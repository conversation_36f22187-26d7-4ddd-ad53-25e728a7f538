export default {
  delay: 1000,
  "GET /_api/api/v1/ybtask/my/assignments": (req, res) => {
    res.json({
      code: 13,
      data: {
        items: [
          {
            startTime: "2018-08-22 15:15:17",
            endTime: "2005-04-29 10:41:22",
            id: "23",
            creatorId: "99",
            type: "Duis",
            creatorName: "法他具性",
            process: [
              {
                id: "21",
                name: "满接派书则",
                type: "nulla in aute",
                assignmentType: "sit elit culpa tempor",
                assignmentId: "37",
              },
              {
                id: "77",
                name: "中任容产",
                type: "deserunt laboris anim pariatur aute",
                assignmentType: "eu sint",
                assignmentId: "70",
              },
              {
                id: "74",
                name: "备器经整科",
                type: "officia fugiat",
                assignmentType: "do aliqua in ut pariatur",
                assignmentId: "69",
              },
              {
                id: "52",
                name: "力战领党",
                type: "ea deserunt laborum commodo tempor",
                assignmentType: "amet",
                assignmentId: "78",
              },
              {
                id: "57",
                name: "把务比",
                type: "nisi",
                assignmentType: "velit anim",
                assignmentId: "89",
              },
            ],
            key: "Duis aliqua",
            title: "引入权存",
            createTime: "1973-09-15 10:48:21",
          },
          {
            id: "24",
            creatorId: "28",
            startTime: "1995-10-21 23:51:06",
            process: [
              {
                id: "49",
                name: "内斯会期",
                type: "consequat occaecat ut",
                assignmentType: "dolore sed",
                assignmentId: "17",
              },
              {
                id: "85",
                name: "务主结统车",
                type: "exercitation commodo",
                assignmentType: "irure",
                assignmentId: "56",
              },
              {
                id: "65",
                name: "包起式是备",
                type: "est",
                assignmentType: "minim",
                assignmentId: "82",
              },
            ],
            type: "in culpa Excepteur",
            endTime: "2019-10-06 00:58:48",
            creatorName: "根取口需群政",
            createTime: "1986-11-26 10:57:08",
            title: "知近只",
            key: "commodo sunt",
          },
          {
            id: "25",
            creatorId: "78",
            key: "in consectetur",
            startTime: "2009-08-23 02:34:12",
            creatorName: "易酸如",
            createTime: "1986-02-08 04:57:29",
            process: [
              {
                id: "58",
                name: "育需习信将许",
                type: "eiusmod sed minim cupidatat sunt",
                assignmentType: "aute",
                assignmentId: "61",
              },
              {
                id: "90",
                name: "路适命集",
                type: "laboris",
                assignmentType: "veniam culpa aliquip laborum",
                assignmentId: "58",
              },
            ],
            type: "proident amet reprehenderit consequat culpa",
            title: "议他维运精",
            endTime: "1974-08-16 22:58:24",
          },
          {
            startTime: "1987-12-09 16:21:37",
            key: "adipisicing anim fugiat est nisi",
            id: "26",
            creatorId: "4",
            endTime: "2014-08-12 01:54:02",
            title: "部石查得",
            creatorName: "及样设却传当",
            process: [
              {
                id: "96",
                name: "低四其带",
                type: "occaecat dolore veniam aliqua velit",
                assignmentType: "est nisi eiusmod",
                assignmentId: "69",
              },
              {
                id: "82",
                name: "究结半资交选",
                type: "non",
                assignmentType: "id elit ipsum in",
                assignmentId: "40",
              },
            ],
            type: "adipisicing",
            createTime: "2012-08-26 13:56:47",
          },
          {
            process: [
              {
                id: "86",
                name: "派学六力构",
                type: "magna",
                assignmentType: "in officia id",
                assignmentId: "95",
              },
              {
                id: "66",
                name: "料易政强展",
                type: "nisi",
                assignmentType: "cillum exercitation nulla",
                assignmentId: "79",
              },
            ],
            type: "officia proident",
            endTime: "1986-07-25 02:52:56",
            key: "elit in",
            startTime: "2013-05-17 01:00:29",
            createTime: "1997-09-20 00:57:58",
            id: "28",
            creatorId: "17",
            title: "体思济政四",
            creatorName: "查性油增求方",
          },
        ],
        totalNum: 39,
      },
    });
  },
  "/api/v1/ybtask/tasks": (req, res) => {
    res.json({
      code: 14,
      data: {
        items: [
          {
            title: "日示书",
            key: "do in id dolore laborum",
            creator: "laboris consequat tempor cupidatat",
            createTime: "2005-08-04 20:46:53",
            process: [
              {
                assignmentType: "dolor",
                assignmentId: "50",
                id: "10",
                type: "commodo do mollit non",
                name: "并常公公三类",
              },
              {
                assignmentType: "Excepteur magna",
                assignmentId: "45",
                id: "91",
                type: "cupidatat",
                name: "你色员流保备和",
              },
            ],
            id: "45",
            endTime: "2023-01-28 06:48:45",
            type: "Ut",
            state: "prepare",
          },
          {
            type: "mollit proident",
            createTime: "2016-05-24 13:45:39",
            key: "ex eiusmod in ut",
            endTime: "1987-03-20 01:14:59",
            title: "干可主",
            id: "78",
            state: "running",
            creator: "nisi reprehenderit elit tempor",
            process: [
              {
                assignmentType: "veniam ipsum qui Duis",
                assignmentId: "78",
                id: "56",
                type: "irure deserunt adipisicing",
                name: "导需海设区",
              },
              {
                assignmentType: "laborum officia",
                assignmentId: "66",
                id: "87",
                type: "nostrud ex minim ut et",
                name: "音手农农",
              },
              {
                assignmentType: "adipisicing eu",
                assignmentId: "95",
                id: "2",
                type: "Excepteur",
                name: "识知也级",
              },
              {
                assignmentType: "eu",
                assignmentId: "27",
                id: "66",
                type: "fugiat",
                name: "格积等眼",
              },
              {
                assignmentType: "sint ex ut",
                assignmentId: "98",
                id: "23",
                type: "dolor",
                name: "保设流说",
              },
            ],
          },
          {
            createTime: "2010-12-26 12:38:31",
            key: "Excepteur culpa Lorem ex quis",
            state: "end",
            creator: "eu nulla in eiusmod qui",
            type: "fugiat tempor ipsum Excepteur veniam",
            title: "龙委业",
            id: "59",
            endTime: "2014-08-15 00:30:08",
            process: [
              {
                assignmentType: "anim incididunt tempor",
                assignmentId: "5",
                id: "2",
                type: "enim cupidatat",
                name: "体处者经",
              },
            ],
          },
          {
            key: "cillum sit fugiat Ut proident",
            state: "running",
            id: "64",
            creator: "exercitation sunt",
            createTime: "2014-12-25 05:11:02",
            endTime: "1988-12-09 17:21:03",
            process: [
              {
                assignmentType: "non qui tempor ex laborum",
                assignmentId: "81",
                id: "81",
                type: "sit amet",
                name: "速按集安出化",
              },
              {
                assignmentType: "ad ipsum",
                assignmentId: "6",
                id: "28",
                type: "enim adipisicing ad nulla",
                name: "快完层斗江华",
              },
              {
                assignmentType: "sint cillum dolore exercitation",
                assignmentId: "8",
                id: "59",
                type: "dolor magna quis pariatur",
                name: "物放样",
              },
              {
                assignmentType: "labore amet sed sit in",
                assignmentId: "56",
                id: "73",
                type: "dolor",
                name: "么信年统",
              },
              {
                assignmentType: "culpa nisi ullamco Ut quis",
                assignmentId: "58",
                id: "66",
                type: "ullamco cillum pariatur aliqua eiusmod",
                name: "又使保",
              },
            ],
            type: "non ut irure",
            title: "始须划斗产",
          },
        ],
        totalNum: 50,
      },
    });
  },
  "DELETE /api/v1/ybtask/task/:id": (req, res) => {
    res.json({
      code: 0,
    });
  },
  "POST /api/v1/ybtask/task/:id": (req, res) => {
    res.json({
      code: 0,
    });
  },
  "GET /api/v1/ybtask/report/dimension": (req, res) => {
    res.json({
      code: 19,
      data: {
        items: [
          {
            id: "1",
            title: "学院",
            single: false,
            sort: [
              "全部",
              "建筑与土木工程学院",
              "机械工程学院",
              "材料工程学院",
              "材料科学与工程学院",
            ],
          },
          {
            id: "2",
            title: "性别",
            single: false,
            sort: ["男", "女"],
          },
          {
            id: "3",
            title: "政治面貌",
            single: false,
            sort: ["群众", "共青团员", "党员"],
          },
          {
            id: "4",
            title: "民族",
            single: false,
            sort: ["汉族", "其他"],
          },
          {
            id: "5",
            title: "贫困生",
            single: true,
          },
          {
            id: "6",
            title: "艺术生",
          },
          {
            id: "7",
            title: "演员",
          },
          {
            id: "8",
            title: "歌手",
          },
        ],
        totalNum: 8,
      },
    });
  },
  "GET /api/v1/ybtask/report/dimension/:id": (req, res) => {
    const { params } = req;
    const arr = {
      1: [
        "全部",
        "建筑与土木工程学院",
        "机械工程学院",
        "材料工程学院",
        "材料科学与工程学院",
      ],
      2: ["男", "女"],
      3: ["群众", "共青团员", "党员"],
    };
    res.json({
      status: 0,
      result: {
        data: {
          id: params.id,
          sorts: arr[params.id],
        },
      },
    });
  },

  "GET /api/v1/ybtask/report/target": (req, res) => {
    res.json({
      status: 0,

      data: [
        {
          id: "1",
          title: "思想引领",
          type: "checkbox",
          selectable: false,
          children: [
            {
              id: "1-1",
              title: "主题教育活动",
            },
            {
              id: "1-2",
              title: "主题宣讲活动",
            },
            {
              id: "1-3",
              title: "党建活动",
            },
          ],
        },
        {
          id: "2",
          title: "学业成绩",
          selectable: false,
          //   disabled: true,
          children: [
            {
              id: "2-1",
              title: "课程成绩",
              type: "filter",
              target: [
                "思想政治",
                "高等数学",
                "马克思主义原理",
                "英语四级",
                "英语六级",
              ],
            },
            {
              id: "2-2",
              title: "平均学分绩点",
              target: ["平均学分绩点"],
            },
          ],
        },
        {
          id: "3",
          title: "健康状况",
        },
        {
          id: "4",
          title: "第二课堂",
        },
        {
          id: "5",
          title: "创新能力",
        },
        {
          id: "6",
          title: "就业深造",
        },
        {
          id: "7",
          title: "荣誉表彰",
        },
      ],
    });
  },

  "GET /api/v1/index/config": (req, res)=> {
    res.json({
      "topPics": [
        {
          "pic": "http://dummyimage.com/400x160",
          "url": "http://xycb.ke/suxus",
          "title": "家且造高价关西",
          "desc": "ut pariatur dolor",
          "sort": "ad"
        },
        {
          "pic": "http://dummyimage.com/400x160",
          "url": "http://aijj.bt/cted",
          "title": "划国外山",
          "desc": "Lorem dolor voluptate amet aliquip",
          "sort": "sed"
        },
        {
          "pic": "http://dummyimage.com/400x160",
          "url": "http://xswhopwfl.pr/wzrtnyu",
          "title": "而影运",
          "desc": "consectetur mollit enim ullamco laborum",
          "sort": "amet dolore sunt exercitation"
        },
        {
          "pic": "http://dummyimage.com/400x160",
          "url": "http://nrx.va/ypteo",
          "title": "持立红己把需",
          "desc": "adipisicing occaecat ut",
          "sort": "mollit consequat non aute ipsum"
        },
        {
          "pic": "http://dummyimage.com/400x160",
          "url": "http://oeeoouh.ai/oce",
          "title": "规身列民争",
          "desc": "Duis",
          "sort": "quis tempor laboris"
        }
      ],
      "apps": [
        {
          "appReleaseId": 65,
          "appName": "什现选集",
          "icon": "http://dummyimage.com/100x100",
          "url": "http://skxwluivoq.ng/ctwrfr",
          "pubSort": 68,
          "appId": "96"
        },
        {
          "appReleaseId": 18,
          "appName": "情状称每与情",
          "icon": "http://dummyimage.com/100x100",
          "url": "http://twslkrqbn.mobi/tnc",
          "pubSort": 25,
          "appId": "71"
        }
      ],
      "tasks": [
        {
          "pic": "http://dummyimage.com/400x400",
          "title": "命很置斗历",
          "desc": "amet fugiat",
          "sort": 31,
          "url": "http://hhvqitwr.tn/lknbuy"
        },
        {
          "pic": "http://dummyimage.com/400x400",
          "title": "已等老平结",
          "desc": "Lorem enim",
          "sort": 84,
          "url": "http://rauyltr.vu/dickpr"
        },
        {
          "pic": "http://dummyimage.com/400x400",
          "title": "作准办形展单",
          "desc": "sunt aliquip dolor",
          "sort": 79,
          "url": "http://uyxhnxjmh.nt/uffat"
        }
      ],
      "taskPics": [
        {
          "title": "变方参今",
          "pic": "http://dummyimage.com/400x160",
          "url": "http://qfhbokgmo.iq/kesgmgaqh",
          "desc": "esse consequat",
          "sort": "sit veniam"
        },
        {
          "title": "建各必证",
          "pic": "http://dummyimage.com/400x160",
          "url": "http://jrfwiz.pe/exmy",
          "desc": "laborum exercitation laboris Excepteur cillum",
          "sort": "labore aliquip consequat"
        }
      ],
      "friendLinks": [
        {
          "pic": "http://dummyimage.com/400x160",
          "url": "http://ymdmdc.中国互联.公司/zhdtrk",
          "title": "常些合正走年水",
          "desc": "magna ex officia",
          "sort": "nulla exercitation eiusmod"
        },
        {
          "pic": "http://dummyimage.com/400x160",
          "url": "http://moxoiduwh.dk/hjbvpjah",
          "title": "也林低上油",
          "desc": "cillum aute veniam",
          "sort": "aute dolore"
        },
        {
          "pic": "http://dummyimage.com/400x160",
          "url": "http://dvvtpd.gu/vmhnd",
          "title": "指区历整做界非",
          "desc": "ex qui",
          "sort": "fugiat dolore adipisicing in"
        }
      ]
    })
  },
  // '/ueditor': /* 前后端通信相关的配置,注释只允许使用多行方式 */
  // {
  //     /* 上传图片配置项 */
  //     "imageActionName": "uploadimage", /* 执行上传图片的action名称 */
  //     "imageFieldName": "upfile", /* 提交的图片表单名称 */
  //     "imageMaxSize": 2048000, /* 上传大小限制，单位B */
  //     "imageAllowFiles": [".png", ".jpg", ".jpeg", ".gif", ".bmp"], /* 上传图片格式显示 */
  //     "imageCompressEnable": true, /* 是否压缩图片,默认是true */
  //     "imageCompressBorder": 1600, /* 图片压缩最长边限制 */
  //     "imageInsertAlign": "none", /* 插入的图片浮动方式 */
  //     "imageUrlPrefix": "", /* 图片访问路径前缀 */
  //     "imagePathFormat": "/easyform-api/attachment/get/temp/ueditor/{uid}", /* 上传保存路径,可以自定义保存路径和文件名格式 */
  //                                 /* {filename} 会替换成原文件名,配置这项需要注意中文乱码问题 */
  //                                 /* {rand:6} 会替换成随机数,后面的数字是随机数的位数 */
  //                                 /* {time} 会替换成时间戳 */
  //                                 /* {yyyy} 会替换成四位年份 */
  //                                 /* {yy} 会替换成两位年份 */
  //                                 /* {mm} 会替换成两位月份 */
  //                                 /* {dd} 会替换成两位日期 */
  //                                 /* {hh} 会替换成两位小时 */
  //                                 /* {ii} 会替换成两位分钟 */
  //                                 /* {ss} 会替换成两位秒 */
  //                                 /* 非法字符 \ : * ? " < > | */
  //                                 /* 具请体看线上文档: fex.baidu.com/ueditor/#use-format_upload_filename */
  
  //     /* 涂鸦图片上传配置项 */
  //     "scrawlActionName": "uploadscrawl", /* 执行上传涂鸦的action名称 */
  //     "scrawlFieldName": "upfile", /* 提交的图片表单名称 */
  //     "scrawlPathFormat": "/ueditor/php/upload/image/{yyyy}{mm}{dd}/{time}{rand:6}", /* 上传保存路径,可以自定义保存路径和文件名格式 */
  //     "scrawlMaxSize": 2048000, /* 上传大小限制，单位B */
  //     "scrawlUrlPrefix": "", /* 图片访问路径前缀 */
  //     "scrawlInsertAlign": "none",
  
  //     /* 截图工具上传 */
  //     "snapscreenActionName": "uploadimage", /* 执行上传截图的action名称 */
  //     "snapscreenPathFormat": "/ueditor/php/upload/image/{yyyy}{mm}{dd}/{time}{rand:6}", /* 上传保存路径,可以自定义保存路径和文件名格式 */
  //     "snapscreenUrlPrefix": "", /* 图片访问路径前缀 */
  //     "snapscreenInsertAlign": "none", /* 插入的图片浮动方式 */
  
  //     /* 抓取远程图片配置 */
  //     "catcherLocalDomain": ["127.0.0.1", "localhost", "img.baidu.com"],
  //     "catcherActionName": "catchimage", /* 执行抓取远程图片的action名称 */
  //     "catcherFieldName": "source", /* 提交的图片列表表单名称 */
  //     "catcherPathFormat": "/ueditor/php/upload/image/{yyyy}{mm}{dd}/{time}{rand:6}", /* 上传保存路径,可以自定义保存路径和文件名格式 */
  //     "catcherUrlPrefix": "", /* 图片访问路径前缀 */
  //     "catcherMaxSize": 2048000, /* 上传大小限制，单位B */
  //     "catcherAllowFiles": [".png", ".jpg", ".jpeg", ".gif", ".bmp"], /* 抓取图片格式显示 */
  
  //     /* 上传视频配置 */
  //     "videoActionName": "uploadvideo", /* 执行上传视频的action名称 */
  //     "videoFieldName": "upfile", /* 提交的视频表单名称 */
  //     "videoPathFormat": "/ueditor/php/upload/video/{yyyy}{mm}{dd}/{time}{rand:6}", /* 上传保存路径,可以自定义保存路径和文件名格式 */
  //     "videoUrlPrefix": "", /* 视频访问路径前缀 */
  //     "videoMaxSize": 102400000, /* 上传大小限制，单位B，默认100MB */
  //     "videoAllowFiles": [
  //         ".flv", ".swf", ".mkv", ".avi", ".rm", ".rmvb", ".mpeg", ".mpg",
  //         ".ogg", ".ogv", ".mov", ".wmv", ".mp4", ".webm", ".mp3", ".wav", ".mid"], /* 上传视频格式显示 */
  
  //     /* 上传文件配置 */
  //     "fileActionName": "uploadfile", /* controller里,执行上传视频的action名称 */
  //     "fileFieldName": "upfile", /* 提交的文件表单名称 */
  //     "filePathFormat": "/ueditor/php/upload/file/{yyyy}{mm}{dd}/{time}{rand:6}", /* 上传保存路径,可以自定义保存路径和文件名格式 */
  //     "fileUrlPrefix": "", /* 文件访问路径前缀 */
  //     "fileMaxSize": 51200000, /* 上传大小限制，单位B，默认50MB */
  //     "fileAllowFiles": [
  //         ".png", ".jpg", ".jpeg", ".gif", ".bmp",
  //         ".flv", ".swf", ".mkv", ".avi", ".rm", ".rmvb", ".mpeg", ".mpg",
  //         ".ogg", ".ogv", ".mov", ".wmv", ".mp4", ".webm", ".mp3", ".wav", ".mid",
  //         ".rar", ".zip", ".tar", ".gz", ".7z", ".bz2", ".cab", ".iso",
  //         ".doc", ".docx", ".xls", ".xlsx", ".ppt", ".pptx", ".pdf", ".txt", ".md", ".xml"
  //     ], /* 上传文件格式显示 */
  
  //     /* 列出指定目录下的图片 */
  //     "imageManagerActionName": "listimage", /* 执行图片管理的action名称 */
  //     "imageManagerListPath": "/ueditor/php/upload/image/", /* 指定要列出图片的目录 */
  //     "imageManagerListSize": 20, /* 每次列出文件数量 */
  //     "imageManagerUrlPrefix": "", /* 图片访问路径前缀 */
  //     "imageManagerInsertAlign": "none", /* 插入的图片浮动方式 */
  //     "imageManagerAllowFiles": [".png", ".jpg", ".jpeg", ".gif", ".bmp"], /* 列出的文件类型 */
  
  //     /* 列出指定目录下的文件 */
  //     "fileManagerActionName": "listfile", /* 执行文件管理的action名称 */
  //     "fileManagerListPath": "/ueditor/php/upload/file/", /* 指定要列出文件的目录 */
  //     "fileManagerUrlPrefix": "", /* 文件访问路径前缀 */
  //     "fileManagerListSize": 20, /* 每次列出文件数量 */
  //     "fileManagerAllowFiles": [
  //         ".png", ".jpg", ".jpeg", ".gif", ".bmp",
  //         ".flv", ".swf", ".mkv", ".avi", ".rm", ".rmvb", ".mpeg", ".mpg",
  //         ".ogg", ".ogv", ".mov", ".wmv", ".mp4", ".webm", ".mp3", ".wav", ".mid",
  //         ".rar", ".zip", ".tar", ".gz", ".7z", ".bz2", ".cab", ".iso",
  //         ".doc", ".docx", ".xls", ".xlsx", ".ppt", ".pptx", ".pdf", ".txt", ".md", ".xml"
  //     ] /* 列出的文件类型 */
  
  // },
  'POST /ueditor': (req, res)=>{
    res.json({
      uid: "@id" 
    })
  }
};
