"use strict";(self.webpackChunkyiban_frontend_admin=self.webpackChunkyiban_frontend_admin||[]).push([[842,485],{12485:function(__unused_webpack_module,__webpack_exports__,__webpack_require__){eval('__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   AppContextProvider: function() { return /* binding */ AppContextProvider; },\n/* harmony export */   ConfigProvider: function() { return /* binding */ ConfigProvider; },\n/* harmony export */   PageContextProvider: function() { return /* binding */ PageContextProvider; },\n/* harmony export */   ProFormContextProvider: function() { return /* binding */ ProFormContextProvider; },\n/* harmony export */   ServicesConfigProvider: function() { return /* binding */ ServicesConfigProvider; },\n/* harmony export */   SuiContextProvider: function() { return /* binding */ SuiContextProvider; },\n/* harmony export */   ThemeContextProvider: function() { return /* binding */ ThemeContextProvider; },\n/* harmony export */   ThemeTag: function() { return /* binding */ ThemeTag; },\n/* harmony export */   getSuiAppClient: function() { return /* binding */ getSuiAppClient; },\n/* harmony export */   useAppContext: function() { return /* binding */ useAppContext; },\n/* harmony export */   useBaseClassName: function() { return /* binding */ useBaseClassName; },\n/* harmony export */   useConfigContext: function() { return /* binding */ useConfigContext; },\n/* harmony export */   usePageContext: function() { return /* binding */ usePageContext; },\n/* harmony export */   useProFormContext: function() { return /* binding */ useProFormContext; },\n/* harmony export */   useServicesConfig: function() { return /* binding */ useServicesConfig; },\n/* harmony export */   useSuiContext: function() { return /* binding */ useSuiContext; },\n/* harmony export */   useThemeContext: function() { return /* binding */ useThemeContext; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(80184);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(78725);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n\n\nfunction _objectWithoutPropertiesLoose(source, excluded) {\n  if (source == null) return {};\n  var target = {};\n  var sourceKeys = Object.keys(source);\n  var key, i;\n  for (i = 0; i < sourceKeys.length; i++) {\n    key = sourceKeys[i];\n    if (excluded.indexOf(key) >= 0) continue;\n    target[key] = source[key];\n  }\n  return target;\n}\nvar _excluded = ["top", "parent", "__client_render_id__", "__top_client", "microApps", "reload", "version", "config", "children", "remotes", "plugin", "models", "dispatcher", "history", "routesState"],\n  _excluded2 = ["children"];\n/**\r\n * \u4e3b\u9898\u6807\u7b7e\r\n */\nvar ThemeTag;\n(function (ThemeTag) {\n  ThemeTag["bright"] = "bright";\n  ThemeTag["compact"] = "compact";\n  ThemeTag["green"] = "green";\n  ThemeTag["red"] = "red";\n  ThemeTag["blue"] = "blue";\n  ThemeTag["yellow"] = "yellow";\n  ThemeTag["orange"] = "orange";\n  ThemeTag["purple"] = "purple";\n  ThemeTag["cyan"] = "cyan";\n  ThemeTag["gray"] = "gray";\n  ThemeTag["dark"] = "dark";\n})(ThemeTag || (ThemeTag = {}));\nvar ThemeContext = /*#__PURE__*/(0,react__WEBPACK_IMPORTED_MODULE_1__.createContext)({});\nfunction useThemeContext() {\n  return (0,react__WEBPACK_IMPORTED_MODULE_1__.useContext)(ThemeContext);\n}\nfunction ThemeContextProvider(props) {\n  var children = props.children,\n    _props$value = props.value,\n    value = _props$value === void 0 ? {} : _props$value;\n  return (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(ThemeContext.Provider, {\n    value: value,\n    children: children\n  }, void 0);\n}\nvar __CLIENT_SUIS_MAP__ = {};\nwindow.__CLIENT_SUIS_MAP__ = __CLIENT_SUIS_MAP__;\nvar SuiContext = /*#__PURE__*/(0,react__WEBPACK_IMPORTED_MODULE_1__.createContext)({\n  version: "",\n  config: {},\n  models: {},\n  remotes: {},\n  history: undefined\n});\nfunction SuiContextProvider(props) {\n  var children = props.children,\n    value = props.value,\n    __CLIENT_SUI_KEY__ = props.__CLIENT_SUI_KEY__;\n  if (!__CLIENT_SUI_KEY__) {\n    var parentSui = useSuiContext();\n    // \u9632\u6b62\u8986\u5199 __CLIENT_SUI_KEY__, version, config\u7b49\u4fdd\u7559\u4fe1\u606f\n    var _value2 = value;\n    _value2.top;\n    _value2.parent;\n    _value2.__client_render_id__;\n    _value2.__top_client;\n    _value2.microApps;\n    _value2.reload;\n    _value2.version;\n    _value2.config;\n    _value2.children;\n    _value2.remotes;\n    _value2.plugin;\n    _value2.models;\n    _value2.dispatcher;\n    _value2.history;\n    _value2.routesState;\n    var _value = _objectWithoutPropertiesLoose(_value2, _excluded);\n    value = Object.assign(parentSui || {}, _value);\n  } else {\n    __CLIENT_SUIS_MAP__[__CLIENT_SUI_KEY__] = value;\n  }\n  return (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(SuiContext.Provider, {\n    value: value,\n    children: children\n  }, void 0);\n}\nfunction useSuiContext() {\n  var context = (0,react__WEBPACK_IMPORTED_MODULE_1__.useContext)(SuiContext);\n  return context;\n}\nvar defaultGetPrefixCls = function defaultGetPrefixCls(suffix, customPrefixCls) {\n  if (customPrefixCls) {\n    return customPrefixCls;\n  }\n  return \'sui\' + (suffix ? "-" + suffix : \'\');\n};\nvar defaultConfig = {\n  getPrefixCls: defaultGetPrefixCls\n};\nvar ConfigContext = /*#__PURE__*/(0,react__WEBPACK_IMPORTED_MODULE_1__.createContext)(defaultConfig);\nfunction ConfigProvider(props) {\n  var children = props.children,\n    config = _objectWithoutPropertiesLoose(props, _excluded2);\n  var value = Object.assign(config, defaultConfig);\n  return (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(ConfigContext.Provider, {\n    value: value,\n    children: children\n  }, void 0);\n}\nfunction useConfigContext() {\n  return (0,react__WEBPACK_IMPORTED_MODULE_1__.useContext)(ConfigContext);\n}\n/*\r\n* \u83b7\u53d6baseclassname\r\n* @param suffix\r\n* @param customPrefixCls\r\n* @returns\r\n*/\nfunction useBaseClassName(suffix, customPrefixCls) {\n  var config = useConfigContext();\n  return config.getPrefixCls(suffix, customPrefixCls);\n}\n/**\r\n * \u83b7\u53d6sui client\r\n * @param clientId\r\n * @returns\r\n */\nfunction getSuiAppClient(clientId) {\n  return __CLIENT_SUIS_MAP__[clientId];\n}\n// \u670d\u52a1\u4e0a\u4e0b\u6587\nvar ServicesConfig = /*#__PURE__*/(0,react__WEBPACK_IMPORTED_MODULE_1__.createContext)([]);\n/**\r\n * \u83b7\u53d6\u670d\u52a1\u914d\u7f6e\r\n * @returns\r\n */\nfunction useServicesConfig() {\n  return (0,react__WEBPACK_IMPORTED_MODULE_1__.useContext)(ServicesConfig) || [];\n}\n/**\r\n * \u670d\u52a1provider\r\n * @param props\r\n * @returns\r\n */\nfunction ServicesConfigProvider(props) {\n  var children = props.children,\n    _props$value2 = props.value,\n    value = _props$value2 === void 0 ? [] : _props$value2;\n  return (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(ServicesConfig.Provider, {\n    value: value,\n    children: children\n  }, void 0);\n}\n// \u5e94\u7528\u4e0a\u4e0b\u6587\nvar AppContext = /*#__PURE__*/(0,react__WEBPACK_IMPORTED_MODULE_1__.createContext)({});\n/**\r\n * \u83b7\u53d6\u5e94\u7528\u4e0a\u4e0b\u6587\u914d\u7f6e\r\n * @param key\r\n * @returns\r\n */\nfunction useAppContext(key) {\n  var config = (0,react__WEBPACK_IMPORTED_MODULE_1__.useContext)(AppContext) || {};\n  return key ? config[key] : config;\n}\n/**\r\n * \u670d\u52a1provider\r\n * @param props\r\n * @returns\r\n */\nfunction AppContextProvider(props) {\n  var children = props.children,\n    _props$value3 = props.value,\n    value = _props$value3 === void 0 ? {} : _props$value3;\n  return (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(AppContext.Provider, {\n    value: value,\n    children: children\n  }, void 0);\n}\n// \u9875\u9762\u4e0a\u4e0b\u6587\nvar PageContext = /*#__PURE__*/(0,react__WEBPACK_IMPORTED_MODULE_1__.createContext)({});\n/**\r\n * \u83b7\u53d6\u9875\u9762\u4e0a\u4e0b\u6587\u914d\u7f6e\r\n * @returns\r\n */\nfunction usePageContext() {\n  return (0,react__WEBPACK_IMPORTED_MODULE_1__.useContext)(PageContext);\n}\n/**\r\n * \u9875\u9762provider\r\n * @param props\r\n * @returns\r\n */\nfunction PageContextProvider(props) {\n  var children = props.children,\n    _props$value4 = props.value,\n    value = _props$value4 === void 0 ? {} : _props$value4;\n  return (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(PageContext.Provider, {\n    value: value,\n    children: children\n  }, void 0);\n}\n// \u9ad8\u7ea7\u8868\u5355\u4e0a\u4e0b\u6587\nvar ProFormContext = /*#__PURE__*/(0,react__WEBPACK_IMPORTED_MODULE_1__.createContext)({});\n/**\r\n * \u83b7\u53d6\u9875\u9762\u4e0a\u4e0b\u6587\u914d\u7f6e\r\n * @returns\r\n */\nfunction useProFormContext() {\n  return (0,react__WEBPACK_IMPORTED_MODULE_1__.useContext)(ProFormContext);\n}\n/**\r\n * \u9875\u9762provider\r\n * @param props\r\n * @returns\r\n */\nfunction ProFormContextProvider(props) {\n  var children = props.children,\n    _props$value5 = props.value,\n    value = _props$value5 === void 0 ? {} : _props$value5;\n  return (0,react_jsx_runtime__WEBPACK_IMPORTED_MODULE_0__.jsx)(ProFormContext.Provider, {\n    value: value,\n    children: children\n  }, void 0);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiMTI0ODUuanMiLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7QUFBQTtBQUNBO0FBRUE7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBRUE7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSIsInNvdXJjZXMiOlsiLi4vbm9kZV9tb2R1bGVzL0BzdWkvcHJvdmlkZXIvZGlzdC9pbmRleC5lc20uanMiXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///12485\n')},66374:function(__unused_webpack_module,exports,__webpack_require__){eval('/** @license React v17.0.2\n * react-jsx-runtime.production.min.js\n *\n * Copyright (c) Facebook, Inc. and its affiliates.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n */\n\n\n__webpack_require__(31725);\nvar f = __webpack_require__(13400),\n  g = 60103;\nexports.Fragment = 60107;\nif ("function" === typeof Symbol && Symbol.for) {\n  var h = Symbol.for;\n  g = h("react.element");\n  exports.Fragment = h("react.fragment");\n}\nvar m = f.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED.ReactCurrentOwner,\n  n = Object.prototype.hasOwnProperty,\n  p = {\n    key: !0,\n    ref: !0,\n    __self: !0,\n    __source: !0\n  };\nfunction q(c, a, k) {\n  var b,\n    d = {},\n    e = null,\n    l = null;\n  void 0 !== k && (e = "" + k);\n  void 0 !== a.key && (e = "" + a.key);\n  void 0 !== a.ref && (l = a.ref);\n  for (b in a) n.call(a, b) && !p.hasOwnProperty(b) && (d[b] = a[b]);\n  if (c && c.defaultProps) for (b in a = c.defaultProps, a) void 0 === d[b] && (d[b] = a[b]);\n  return {\n    $$typeof: g,\n    type: c,\n    key: e,\n    ref: l,\n    props: d,\n    _owner: m.current\n  };\n}\nexports.jsx = q;\nexports.jsxs = q;//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiNjYzNzQuanMiLCJtYXBwaW5ncyI6IkFBQUE7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBOztBQUFBO0FBQUE7QUFBQTtBQUFBO0FBQUE7QUFBQTtBQUFBO0FBQUE7QUFBQTtBQUFBO0FBQUE7QUFBQTtBQUFBO0FBQUE7QUFBQTtBQUFBO0FBQUE7QUFDQTtBQUFBO0FBQUE7QUFBQTtBQUFBO0FBQUE7QUFBQTtBQUFBO0FBQUE7QUFBQTtBQUFBO0FBQUE7QUFBQTtBQUFBO0FBQUE7QUFBQTtBQUFBO0FBQUE7QUFBQTtBQUFBO0FBQUEiLCJzb3VyY2VzIjpbIi4uL25vZGVfbW9kdWxlcy9yZWFjdC9janMvcmVhY3QtanN4LXJ1bnRpbWUucHJvZHVjdGlvbi5taW4uanMiXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///66374\n')},80184:function(module,__unused_webpack_exports,__webpack_require__){eval("\n\nif (true) {\n  module.exports = __webpack_require__(66374);\n} else {}//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiODAxODQuanMiLCJtYXBwaW5ncyI6IkFBQUE7O0FBRUE7QUFDQTtBQUNBIiwic291cmNlcyI6WyIuLi9ub2RlX21vZHVsZXMvcmVhY3QvanN4LXJ1bnRpbWUuanMiXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///80184\n")}}]);