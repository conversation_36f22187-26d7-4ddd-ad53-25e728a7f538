(self.webpackChunkyiban_frontend_admin=self.webpackChunkyiban_frontend_admin||[]).push([[548],{74246:function(__unused_webpack_module,__webpack_exports__,__webpack_require__){"use strict";eval("// ESM COMPAT FLAG\n__webpack_require__.r(__webpack_exports__);\n\n// EXPORTS\n__webpack_require__.d(__webpack_exports__, {\n  \"default\": function() { return /* binding */ bootstrap; }\n});\n\n// NAMESPACE OBJECT: ./src/app.tsx\nvar src_app_namespaceObject = {};\n__webpack_require__.r(src_app_namespaceObject);\n__webpack_require__.d(src_app_namespaceObject, {\n  getAccess: function() { return app_getAccess; },\n  getContextState: function() { return getContextState; },\n  modifyRenderOpts: function() { return modifyRenderOpts; },\n  noAccessRender: function() { return noAccessRender; },\n  rootElement: function() { return rootElement; }\n});\n\n// NAMESPACE OBJECT: ./src/.sui/plugin-model.jsx\nvar _sui_plugin_model_namespaceObject = {};\n__webpack_require__.r(_sui_plugin_model_namespaceObject);\n__webpack_require__.d(_sui_plugin_model_namespaceObject, {\n  dataflowProvider: function() { return dataflowProvider; }\n});\n\n// NAMESPACE OBJECT: ./src/.sui/plugin-access.jsx\nvar plugin_access_namespaceObject = {};\n__webpack_require__.r(plugin_access_namespaceObject);\n__webpack_require__.d(plugin_access_namespaceObject, {\n  accessProvider: function() { return accessProvider; }\n});\n\n// EXTERNAL MODULE: ./node_modules/normalize.css/normalize.css\nvar normalize = __webpack_require__(79908);\n// EXTERNAL MODULE: ./src/global.ts\nvar global = __webpack_require__(81742);\n// EXTERNAL MODULE: consume shared module (default) react@^17.0.2 (singleton) (fallback: ./node_modules/react/index.js) (eager)\nvar index_js_eager_ = __webpack_require__(96985);\nvar index_js_eager_default = /*#__PURE__*/__webpack_require__.n(index_js_eager_);\n// EXTERNAL MODULE: ./node_modules/@sui/sui/dist/index.esm.js\nvar index_esm = __webpack_require__(53273);\n// EXTERNAL MODULE: ./src/.sui/core/runtime.jsx + 3 modules\nvar runtime = __webpack_require__(86642);\n;// CONCATENATED MODULE: ./src/.sui/core/plugin.js\nvar supportHooks=[// \u5e94\u7528\u542f\u52a8\u65f6\n\"onStart\",// \u5e94\u7528\u6210\u529f\u6e32\u67d3\n\"onReady\",// \u6e32\u67d3\u51fd\u6570\u52fe\u5b50\n\"render\",// \u4fee\u6539\u8def\u7531\n\"modifyRoutes\",// \u8fd0\u884c\u65f6\u914d\u7f6e\n\"getRuntimeConfig\",// \u4fee\u6539\u6e32\u67d3\u53c2\u6570\n\"modifyRenderOpts\",// \u4fee\u6539\u9875\u9762props\n\"modifyPageProps\",// \u4fee\u6539\u5b50\u5e94\u7528props\n\"modifyMicroAppProps\",// \u8def\u7531\u6539\u53d8\n\"onRouteChange\",// \u8def\u7531\u6e32\u67d3\u8282\u70b9\u8fc7\u6ee4\u5668\n\"routeElment\",// \u6839\u8282\u70b9\u8fc7\u6ee4\u5668\n\"rootElement\",//\u65e0\u6743\u6743\u9650\u6e32\u67d3\n\"noAccessRender\",//404\u6e32\u67d3\n\"notFoundRender\",// \u8bf7\u6c42\u53d1\u9001\u65f6\n\"requestSend\",// \u8bf7\u6c42\u8fd4\u56de\u540e\n\"requestResponse\",// providers, \u4e0a\u4e0b\u6587\u76f8\u5173\u7684\n'innerProvider',// \u591a\u8bed\u8a00provider\n'i18nProvider',// \u6743\u9650provider\n'accessProvider',// \u6570\u636e\u6d41provider\n'dataflowProvider',// \u5916\u90e8provider\n'outerProvider',// \u6839provider\n'rootContainer',// documentTitle\n'documentTitle',// \u5b50\u5e94\u7528\u8def\u7531\u53d8\u5316\u56de\u8c03\n'onMicroAppRouteChange'];var plugin_plugin=new index_esm/* Plugin */.Sy({supportHooks:supportHooks});/* harmony default export */ var core_plugin = (plugin_plugin);\n// EXTERNAL MODULE: ./node_modules/@babel/runtime/helpers/esm/regeneratorRuntime.js\nvar regeneratorRuntime = __webpack_require__(74165);\n// EXTERNAL MODULE: ./node_modules/@babel/runtime/helpers/esm/asyncToGenerator.js\nvar asyncToGenerator = __webpack_require__(15861);\n// EXTERNAL MODULE: ./node_modules/@babel/runtime/helpers/esm/objectSpread2.js\nvar objectSpread2 = __webpack_require__(1413);\n// EXTERNAL MODULE: ./node_modules/@babel/runtime/helpers/esm/slicedToArray.js + 1 modules\nvar slicedToArray = __webpack_require__(29439);\n;// CONCATENATED MODULE: ./src/.sui/core/plugin-model/index.js\nvar sleep=function sleep(delay){return new Promise(function(resolve){return setTimeout(resolve,delay||0);});};/**\n * \u521b\u5efamodel\n * @param {*} param0 \n * @returns \n */function createContextStateModel(getContextState){return function(){var isMouted=(0,index_js_eager_.useRef)(true);var parentContext,topContext;if((0,index_esm/* useInMicroApp */.Io)()){parentContext=((0,index_esm/* useModel */.tT)('parent.context')||{})['context'];topContext=((0,index_esm/* useModel */.tT)('top.context')||{})['context'];}var hasContextStateCallback=typeof getContextState==='function';var _useState=(0,index_js_eager_.useState)({context:{},topContext:topContext,parentContext:parentContext,loading:hasContextStateCallback,error:undefined}),_useState2=(0,slicedToArray/* default */.Z)(_useState,2),state=_useState2[0],setState=_useState2[1];var refresh=hasContextStateCallback?(0,index_js_eager_.useCallback)(/*#__PURE__*/(0,asyncToGenerator/* default */.Z)(/*#__PURE__*/(0,regeneratorRuntime/* default */.Z)().mark(function _callee(){var asyncFunc,ret;return (0,regeneratorRuntime/* default */.Z)().wrap(function _callee$(_context){while(1)switch(_context.prev=_context.next){case 0:isMouted&&isMouted.current&&setState(function(s){return (0,objectSpread2/* default */.Z)((0,objectSpread2/* default */.Z)({},s),{},{topContext:topContext,parentContext:parentContext,loading:true,error:undefined});});_context.prev=1;asyncFunc=function asyncFunc(){return new Promise(function(res){return res(getContextState(parentContext,topContext));});};_context.next=5;return asyncFunc();case 5:ret=_context.sent;isMouted&&isMouted.current&&setState(function(s){return (0,objectSpread2/* default */.Z)((0,objectSpread2/* default */.Z)({},s),{},{topContext:topContext,parentContext:parentContext,context:ret,error:undefined,loading:false});});_context.next=12;break;case 9:_context.prev=9;_context.t0=_context[\"catch\"](1);isMouted&&isMouted.current&&setState(function(s){return (0,objectSpread2/* default */.Z)((0,objectSpread2/* default */.Z)({},s),{},{topContext:topContext,parentContext:parentContext,error:_context.t0,loading:false});});case 12:_context.next=14;return sleep(10);case 14:case\"end\":return _context.stop();}},_callee,null,[[1,9]]);})),[]):function(){};var setContextState=(0,index_js_eager_.useCallback)(/*#__PURE__*/function(){var _ref2=(0,asyncToGenerator/* default */.Z)(/*#__PURE__*/(0,regeneratorRuntime/* default */.Z)().mark(function _callee2(newContextState){return (0,regeneratorRuntime/* default */.Z)().wrap(function _callee2$(_context2){while(1)switch(_context2.prev=_context2.next){case 0:isMouted&&isMouted.current&&setState(function(s){if(typeof newContextState==='function'){return (0,objectSpread2/* default */.Z)((0,objectSpread2/* default */.Z)({},s),{},{context:newContextState(s.context),topContext:topContext,parentContext:parentContext,error:undefined,loading:false});}return (0,objectSpread2/* default */.Z)((0,objectSpread2/* default */.Z)({},s),{},{context:newContextState,topContext:topContext,parentContext:parentContext,error:undefined,loading:false});});_context2.next=3;return sleep(10);case 3:case\"end\":return _context2.stop();}},_callee2);}));return function(_x){return _ref2.apply(this,arguments);};}(),[]);var setSettingsState=(0,index_js_eager_.useCallback)(/*#__PURE__*/function(){var _ref3=(0,asyncToGenerator/* default */.Z)(/*#__PURE__*/(0,regeneratorRuntime/* default */.Z)().mark(function _callee3(newSettingsState){return (0,regeneratorRuntime/* default */.Z)().wrap(function _callee3$(_context3){while(1)switch(_context3.prev=_context3.next){case 0:isMouted&&isMouted.current&&setState(function(s){if(typeof newSettingsState==='function'){newSettingsState=newSettingsState(s.settings||{});}return (0,objectSpread2/* default */.Z)((0,objectSpread2/* default */.Z)({},s),{},{context:(0,objectSpread2/* default */.Z)((0,objectSpread2/* default */.Z)({},s.context),{},{settings:newSettingsState}),topContext:topContext,parentContext:parentContext,error:undefined,loading:false});});_context3.next=3;return sleep(10);case 3:case\"end\":return _context3.stop();}},_callee3);}));return function(_x2){return _ref3.apply(this,arguments);};}(),[]);var setUserState=(0,index_js_eager_.useCallback)(/*#__PURE__*/function(){var _ref4=(0,asyncToGenerator/* default */.Z)(/*#__PURE__*/(0,regeneratorRuntime/* default */.Z)().mark(function _callee4(newUserState){return (0,regeneratorRuntime/* default */.Z)().wrap(function _callee4$(_context4){while(1)switch(_context4.prev=_context4.next){case 0:isMouted&&isMouted.current&&setState(function(s){if(typeof newUserState==='function'){newUserState=newUserState(s.user||{});}return (0,objectSpread2/* default */.Z)((0,objectSpread2/* default */.Z)({},s),{},{context:(0,objectSpread2/* default */.Z)((0,objectSpread2/* default */.Z)({},s.context),{},{user:newUserState}),topContext:topContext,parentContext:parentContext,error:undefined,loading:false});});_context4.next=3;return sleep(10);case 3:case\"end\":return _context4.stop();}},_callee4);}));return function(_x3){return _ref4.apply(this,arguments);};}(),[]);var setThemeState=(0,index_js_eager_.useCallback)(/*#__PURE__*/function(){var _ref5=(0,asyncToGenerator/* default */.Z)(/*#__PURE__*/(0,regeneratorRuntime/* default */.Z)().mark(function _callee5(newThemeState){return (0,regeneratorRuntime/* default */.Z)().wrap(function _callee5$(_context5){while(1)switch(_context5.prev=_context5.next){case 0:isMouted&&isMouted.current&&setState(function(s){if(typeof newThemeState==='function'){newThemeState=newThemeState(s.theme||{});}return (0,objectSpread2/* default */.Z)((0,objectSpread2/* default */.Z)({},s),{},{context:(0,objectSpread2/* default */.Z)((0,objectSpread2/* default */.Z)({},s.context),{},{theme:newThemeState}),topContext:topContext,parentContext:parentContext,error:undefined,loading:false});});_context5.next=3;return sleep(10);case 3:case\"end\":return _context5.stop();}},_callee5);}));return function(_x4){return _ref5.apply(this,arguments);};}(),[]);var setThemeVars=(0,index_js_eager_.useCallback)(/*#__PURE__*/function(){var _ref6=(0,asyncToGenerator/* default */.Z)(/*#__PURE__*/(0,regeneratorRuntime/* default */.Z)().mark(function _callee6(newThemeVars){return (0,regeneratorRuntime/* default */.Z)().wrap(function _callee6$(_context6){while(1)switch(_context6.prev=_context6.next){case 0:isMouted&&isMouted.current&&setState(function(s){var theme=Object.assign({},s.theme||{});if(typeof newThemeVars==='function'){newThemeVars=newThemeVars(theme.vars||{});}theme.vars=Object.assign({},theme.vars||{},newThemeVars||{});window&&typeof window.modifySuiLessVars==='function'&&window.modifySuiLessVars(theme.vars);return (0,objectSpread2/* default */.Z)((0,objectSpread2/* default */.Z)({},s),{},{context:(0,objectSpread2/* default */.Z)((0,objectSpread2/* default */.Z)({},s.context),{},{theme:theme}),topContext:topContext,parentContext:parentContext,error:undefined,loading:false});});_context6.next=3;return sleep(10);case 3:case\"end\":return _context6.stop();}},_callee6);}));return function(_x5){return _ref6.apply(this,arguments);};}(),[]);var setThemeStyles=(0,index_js_eager_.useCallback)(/*#__PURE__*/function(){var _ref7=(0,asyncToGenerator/* default */.Z)(/*#__PURE__*/(0,regeneratorRuntime/* default */.Z)().mark(function _callee7(newThemeStyles){return (0,regeneratorRuntime/* default */.Z)().wrap(function _callee7$(_context7){while(1)switch(_context7.prev=_context7.next){case 0:isMouted&&isMouted.current&&setState(function(s){var theme=Object.assign({},s.theme||{});if(typeof newThemeStyles==='function'){newThemeStyles=newThemeStyles(theme.styles||{});}theme.styles=Object.assign({},theme.styles||{},newThemeStyles||{});return (0,objectSpread2/* default */.Z)((0,objectSpread2/* default */.Z)({},s),{},{context:(0,objectSpread2/* default */.Z)((0,objectSpread2/* default */.Z)({},s.context),{},{theme:theme}),topContext:topContext,parentContext:parentContext,error:undefined,loading:false});});_context7.next=3;return sleep(10);case 3:case\"end\":return _context7.stop();}},_callee7);}));return function(_x6){return _ref7.apply(this,arguments);};}(),[]);(0,index_js_eager_.useEffect)(function(){isMouted.current=true;refresh&&refresh();return function(){isMouted.current=false;};},[]);return (0,objectSpread2/* default */.Z)((0,objectSpread2/* default */.Z)({},state),{},{setContextState:setContextState,setThemeState:setThemeState,setThemeVars:setThemeVars,setThemeStyles:setThemeStyles,setSettingsState:setSettingsState,setUserState:setUserState,refresh:refresh});};};/**\n * \u589e\u52a0model\n * @param {*} namespace \n * @param {*} model \n */function addModel(models,namespace,model){models=models||{};namespace=model.namespace||namespace;if(models[namespace]){throw new Error(\"Model \".concat(namespace,\" has exists. Please use other namespace.\"));}models[namespace]=model;return models;}\n// EXTERNAL MODULE: ./node_modules/@babel/runtime/helpers/esm/objectWithoutProperties.js\nvar objectWithoutProperties = __webpack_require__(45987);\n// EXTERNAL MODULE: ./node_modules/@babel/runtime/helpers/esm/defineProperty.js\nvar defineProperty = __webpack_require__(4942);\n// EXTERNAL MODULE: ./node_modules/antd/lib/config-provider/style/index.js\nvar style = __webpack_require__(25117);\n// EXTERNAL MODULE: ./node_modules/antd/lib/config-provider/index.js\nvar config_provider = __webpack_require__(55049);\n// EXTERNAL MODULE: ./node_modules/antd/lib/result/style/index.js\nvar result_style = __webpack_require__(28598);\n// EXTERNAL MODULE: ./node_modules/antd/lib/result/index.js\nvar result = __webpack_require__(77114);\n// EXTERNAL MODULE: ./node_modules/antd/es/locale/zh_CN.js + 1 modules\nvar zh_CN = __webpack_require__(25797);\n// EXTERNAL MODULE: ./node_modules/moment/locale/zh-cn.js\nvar zh_cn = __webpack_require__(42173);\n// EXTERNAL MODULE: ./node_modules/@sui/runtime/dist/index.esm.js + 2 modules\nvar dist_index_esm = __webpack_require__(74698);\n;// CONCATENATED MODULE: ./src/index.less\n// extracted by mini-css-extract-plugin\n/* harmony default export */ var src = ({});\n// EXTERNAL MODULE: ./node_modules/antd/lib/message/style/index.js\nvar message_style = __webpack_require__(9430);\n// EXTERNAL MODULE: ./node_modules/antd/lib/message/index.js\nvar message = __webpack_require__(41691);\n;// CONCATENATED MODULE: ./src/utils/util.ts\n// import EventRef from \"@sui/runtime/dist/utils/EventRef\";\nvar YB=dist_index_esm/* utils */.P6.createEventRef();message[\"default\"].config({top:50,maxCount:1,duration:1.8});\n;// CONCATENATED MODULE: ./src/services/user.ts\nvar _APP_CONFIG,_APP_CONFIG$server;var USER_API_PREFIX=((_APP_CONFIG=window.APP_CONFIG)===null||_APP_CONFIG===void 0?void 0:(_APP_CONFIG$server=_APP_CONFIG.server)===null||_APP_CONFIG$server===void 0?void 0:_APP_CONFIG$server.api)+'/user';var request=(0,dist_index_esm/* createRequest */.hG)({prefix:USER_API_PREFIX});var API_USER={/**\n   * \u83b7\u53d6\u4eba\u5458\u4fe1\u606f\n   */getUserInfo:'/getinfo',/**\n   * \u9000\u51fa\u767b\u5f55\n   */getLogout:'/logout'};/**\n * \u83b7\u53d6\u4eba\u5458\u4fe1\u606f\n * @param params\n * @returns\n */function getUserInfo(params){return request(API_USER.getUserInfo,params);}/**\n * \u9000\u51fa\u767b\u5f55\n * @param params\n * @returns\n */function getLogout(_x){return _getLogout.apply(this,arguments);}function _getLogout(){_getLogout=_asyncToGenerator(/*#__PURE__*/_regeneratorRuntime().mark(function _callee(params){return _regeneratorRuntime().wrap(function _callee$(_context){while(1)switch(_context.prev=_context.next){case 0:window.location.replace(\"\".concat(window.EDU_URL_PREFIX||'',\"/logout?service=\").concat(location.origin).concat(window.EDU_URL_PREFIX||''));// const res = await request(API_USER.getLogout, params)\n// if (res && res.code == 200) {\n//   window.location.reload()\n// }\ncase 1:case\"end\":return _context.stop();}},_callee);}));return _getLogout.apply(this,arguments);}function unBind(_x2){return _unBind.apply(this,arguments);}function _unBind(){_unBind=_asyncToGenerator(/*#__PURE__*/_regeneratorRuntime().mark(function _callee2(params){return _regeneratorRuntime().wrap(function _callee2$(_context2){while(1)switch(_context2.prev=_context2.next){case 0:_context2.next=2;return fetch('/v1/wechat/unBindUser').then(function(){getLogout();});case 2:case\"end\":return _context2.stop();}},_callee2);}));return _unBind.apply(this,arguments);}\n// EXTERNAL MODULE: ./node_modules/antd/lib/button/style/index.js\nvar button_style = __webpack_require__(42844);\n// EXTERNAL MODULE: ./node_modules/antd/lib/button/index.js\nvar lib_button = __webpack_require__(59235);\n// EXTERNAL MODULE: ./node_modules/@babel/runtime/helpers/esm/extends.js\nvar esm_extends = __webpack_require__(87462);\n// EXTERNAL MODULE: consume shared module (default) @emotion/react@^11.10.5 (singleton) (fallback: ./node_modules/@emotion/react/dist/emotion-react.browser.esm.js)\nvar emotion_react_browser_esm_js_ = __webpack_require__(94216);\n;// CONCATENATED MODULE: ./src/components/ReloadButtonn/index.tsx\nfunction ReloadButton(props){return (0,emotion_react_browser_esm_js_.jsx)(lib_button[\"default\"],(0,esm_extends/* default */.Z)({type:\"primary\"},props,{onClick:function onClick(){return window.location.reload();}}),\"\\u91CD\\u65B0\\u52A0\\u8F7D\");}\n;// CONCATENATED MODULE: ./src/commons/service.ts\nvar YB_EZFORM_URL=window['YB_EZFORM_URL']||'/easyform/creation';var YB_EZFORM_COMPLETE_URL=window['YB_EZFORM_COMPLETE_URL']||'/easyform/submit';/**\n * \u540e\u53f0\u9759\u6001\u8d44\u6e90\u8bbf\u95ee\u524d\u7f00\n */var YB_STATIC_PREFIX=window.APP_CONFIG.server.api+'/static/images';var HttpStatusCode={Continue:100,SwitchingProtocols:101,Processing:102,EarlyHints:103,Ok:200,Created:201,Accepted:202,NonAuthoritativeInformation:203,NoContent:204,ResetContent:205,PartialContent:206,MultiStatus:207,AlreadyReported:208,ImUsed:226,MultipleChoices:300,MovedPermanently:301,Found:302,SeeOther:303,NotModified:304,UseProxy:305,Unused:306,TemporaryRedirect:307,PermanentRedirect:308,BadRequest:400,Unauthorized:401,PaymentRequired:402,Forbidden:403,NotFound:404,MethodNotAllowed:405,NotAcceptable:406,ProxyAuthenticationRequired:407,RequestTimeout:408,Conflict:409,Gone:410,LengthRequired:411,PreconditionFailed:412,PayloadTooLarge:413,UriTooLong:414,UnsupportedMediaType:415,RangeNotSatisfiable:416,ExpectationFailed:417,ImATeapot:418,MisdirectedRequest:421,UnprocessableEntity:422,Locked:423,FailedDependency:424,TooEarly:425,UpgradeRequired:426,PreconditionRequired:428,TooManyRequests:429,RequestHeaderFieldsTooLarge:431,UnavailableForLegalReasons:451,InternalServerError:500,NotImplemented:501,BadGateway:502,ServiceUnavailable:503,GatewayTimeout:504,HttpVersionNotSupported:505,VariantAlsoNegotiates:506,InsufficientStorage:507,LoopDetected:508,NotExtended:510,NetworkAuthenticationRequired:511};Object.entries(HttpStatusCode).forEach(function(_ref){var _ref2=(0,slicedToArray/* default */.Z)(_ref,2),key=_ref2[0],value=_ref2[1];HttpStatusCode[value]=key;});var getResponseError=function getResponseError(res,defaultMsg){var errMsg=typeof res==='object'?res.msg||res.resultMsg:'',errCode=typeof res==='object'?res.code||res.resultCode:0;if(!res||errCode>=400){var defaultError=errCode?HttpStatusCode[res.code]:undefined;errMsg=errMsg||defaultError||defaultMsg;}if(typeof res==='number'&&HttpStatusCode[res]){errMsg=HttpStatusCode[res];}else if(typeof res==='string'&&res){errMsg=res;}if(/\\{\\\"code\\\"/.test(errMsg)){try{var msg=JSON.parse(errMsg);errMsg=typeof msg==='object'&&msg?msg.msg||msg.message||msg.code&&HttpStatusCode[msg.code]:errMsg;}catch(error){}}return errMsg;};/**\n * \u63d0\u4ea4\u6210\u529f\u5904\u7406\n * @param res\n * @returns\n */function postSuccess(res,successMsg){var errMsg=getResponseError(res,'REQUEST ERROR');errMsg?_message.warning({content:\"\".concat(errMsg)}):_message.success(successMsg||\"\\u64CD\\u4F5C\\u6210\\u529F\");return res;}/**\n * \u4fdd\u5b58\u6210\u529f\u5904\u7406\n * @param res\n * @param successMsg\n * @returns\n */function saveSuccess(res,successMsg){return postSuccess(res,successMsg||'\u4fdd\u5b58\u6210\u529f');}/**\n * \u521b\u5efa\u6210\u529f\n * @param res\n * @param successMsg\n * @returns\n */function createSuccess(res,successMsg){return postSuccess(res,successMsg||'\u521b\u5efa\u6210\u529f');}/**\n * \u66f4\u65b0\u6210\u529f\n * @param res\n * @param successMsg\n * @returns\n */function updateSuccess(res,successMsg){return postSuccess(res,successMsg||'\u66f4\u65b0\u6210\u529f');}/**\n * \u5220\u9664\u6210\u529f\n * @param res\n * @param successMsg\n * @returns\n */function deleteSuccess(res,successMsg){return postSuccess(res,successMsg||'\u5220\u9664\u6210\u529f');}/**\n * \u53d1\u9001\u6210\u529f\n * @param res\n * @param successMsg\n * @returns\n */function sendSuccess(res,successMsg){return postSuccess(res,successMsg||'\u53d1\u9001\u6210\u529f');}/**\n * \u63d0\u4ea4\u5931\u8d25\u5904\u7406\n * @param e\n * @returns\n */function service_postFail(e,errorMsg){var errMsg;var code=e.code;var errData=e.response;if(typeof code==='number'&&HttpStatusCode[code]){errMsg=HttpStatusCode[code];}if(errData){errMsg=getResponseError(errData,errMsg);}_message.warning({content:errMsg||errorMsg||'REQUEST ERROR'});return this;}/**\n * \u652f\u6301\u7684 SQL \u64cd\u4f5c\u7b26\u7c7b\u578b\n */var SQLOder={desc:'DESC',asc:'ASC',descend:'DESC',ascend:'ASC',DESC:'DESC',ASC:'ASC'};/**\n * \u64cd\u4f5c\u7b26\u5b9a\u4e49\n */var SQLOperator={like:\"$n LIKE '%$s%'\",llike:\"$n LIKE '%$s'\",rlike:\"$n LIKE '$s%'\",eq:\"$n='$s'\",lt:'$n < $d',gt:'$n > $d',in:'$n in $a'};/**\n * \u751f\u6210 in \u64cd\u4f5c\u7b26 \u5bf9\u5e94\u7684\u503c\n * @param obj\n * @returns\n */function _getSQLwhereInString(obj){var arr=Array.isArray(obj)?obj:[obj];var str='[';for(var index=0;index<arr.length;index++){var val=arr[index];if(index&&val!==''){str+=',';str+=typeof val==='number'?val:\"'\".concat(val+'',\"'\");}}return str+']';}/**\n * \u751f\u6210 SQL WHERE \u67e5\u8be2\u8bed\u53e5\n * @description \u9ed8\u8ba4\u662f AND \u64cd\u4f5c\uff0c\u7528\u4e8e\u8868\u5355\u67e5\u8be2\n * @param obj\n * @param operator\n * @returns\n */function createSQLQuery(obj,operator){var arr=[];for(var _key in obj){if(Object.prototype.hasOwnProperty.call(obj,_key)){var value=obj[_key];var op=operator&&operator[_key]&&SQLOperator[operator[_key]]?SQLOperator[operator[_key]]:SQLOperator.eq;arr.push(op.replace('$n',_key).replace(/(\\$s|\\$d)/,value).replace('$a',_getSQLwhereInString(value)));}}return arr.join(' AND ');}/**\n * \u521b\u5efaSQL ORDER\n * @param obj\n * @returns\n */function createSQLOder(obj){var arr=[];for(var _key2 in obj){if(Object.prototype.hasOwnProperty.call(obj,_key2)){var value=obj[_key2];if(value&&SQLOder[value]){arr.push(\"\".concat(_key2,\" \").concat(SQLOder[value]));}}}return arr.join(', ');}/**\n * \u521b\u5efa\u67e5\u8be2\u8868\u683c\u5217\u5e76\u8bbe\u7f6e\u663e\u793a\u4f4d\u7f6e\n * @param obj\n * @param show\n * @param hide\n * @returns\n */function createColumn(obj,show,hide){return _objectSpread(_objectSpread({},obj),{},{hideInDescriptions:show&&(hide?show.indexOf('descriptions')>-1:show.indexOf('descriptions')==-1),hideInForm:show&&(hide?show.indexOf('form')>-1:show.indexOf('form')==-1),hideInSetting:show&&(hide?show.indexOf('setting')>-1:show.indexOf('setting')==-1),hideInTable:show&&(hide?show.indexOf('table')>-1:show.indexOf('table')==-1),hideInSearch:show&&(hide?show.indexOf('search')>-1:show.indexOf('search')==-1)});}\n;// CONCATENATED MODULE: ./src/commons/user.ts\n/**\n * \u5224\u65ad\u662f\u5426\u662f\u8f85\u5bfc\u5458\n * @param identity \n * @returns \n */function isFdyUser(identity){return identity==5;}/**\n * \u5224\u65ad\u662f\u5426\u662f\u6821\u9886\u5bfc\n */function isSchoolLeader(identity){return identity===9;}/**\n * \u6821\u7ea7\u7528\u6237\n * @param identity \n * @returns \n */function isSchoolLevelUser(identity){return identity==4||identity==6||identity==9;}/**\n * \u5224\u65ad\u662f\u5426\u662f\u90e8\u95e8\u7ba1\u7406\u5458\n * @param identity \n * @returns \n */function isDepartAdminUser(identity){return identity==3||identity===7||identity===8;}/**\n * \u5224\u65ad\u662f\u5426\u662f\u5b66\u9662\u7ea7\u7528\u6237\n */function isAcademyLevelUser(identity){return identity==3||identity==7||identity==8||identity==11;}/**\n * \u5224\u65ad\u662f\u5426\u662f\u5b66\u5de5\u529e\u4e3b\u4efb\n */function isStudentOfficeDirector(identity){return identity==3;}/**\n * \u5b66\u9662\u526f\u4e66\u8bb0\n * @param identity \n * @returns \n */function isAcademyDeputySecretary(identity){return identity==7;}/**\n * \u5224\u65ad\u662f\u5426\u662f\u5b66\u5de5\u7ba1\u7406\u5458\n * @param identity \n * @returns \n */function isXgAdminUser(identity){return identity==6;}/**\n * \u5224\u65ad\u662f\u5426\u6821\u7ea7\u7ba1\u7406\u5458\n * @param identity \n * @returns \n */function isSchoolAdmin(identity){return identity==6;}/**\n * \u5224\u65ad\u662f\u5426\u662f\u6559\u804c\u5de5\n * @param identity \n * @returns \n */function isStaffUser(identity){return identity==2;}/**\n * \u5224\u65ad\u662f\u5426\u662f\u5b66\u751f\n * @param identity \n * @returns \n */function isStudentUser(identity){return identity==1;}/**\n * \u5224\u65ad\u662f\u5426\u662f\u8d85\u7ea7\u7ba1\u7406\u5458\n * @param identity \n * @returns \n */function isSupperUser(identity){return identity==4;}/**\n * \u83b7\u53d6\u8eab\u4efd\u7684\u89d2\u8272\n * @param identity \n */function getIdentityRole(identity){return\"user\";}/**\n * \u5224\u65ad\u9662\u7cfb\u9886\u5bfc\n * @param identity \n */function isAcademyLeader(identity){return identity==11;}/**\n * \u5224\u65ad\u90e8\u95e8\u9886\u5bfc\n * @param identity \n */function isDepartLeader(identity){return identity==10;}/**\n * \u5b66\u9662\u56e2\u59d4\u4e66\u8bb0\n * @param identity \n * @returns \n */function isAcademyLeagueSecretary(identity){return identity==8;}/**\n * @name \u5176\u4ed6\u7528\u6237\n */function isOtherUser(identity){return identity==12;}/**\n * \u7528\u6237\u5bf9\u8c61\n */var USER={};/**\n * \u66f4\u65b0\u7528\u6237\n * @param user \n * @returns \n */function updateUSER(user){Object.assign(USER,user);return USER;}/**\n * \u83b7\u53d6\u7528\u6237\u6570\u636e\n * @returns \n */function getUser(){return USER;}/**\n * ACCESS\n */var ACCESS={};/**\n * \u66f4\u65b0ACCESS\n * @param access \n * @returns \n */function updateACCESS(access){Object.assign(ACCESS,access);return ACCESS;}/**\n * \u83b7\u53d6access\n * @returns \n */function getAccess(){return ACCESS;}\n;// CONCATENATED MODULE: ./src/commons/releaseApp.ts\nvar RELEASEAPP_ID_KEY=\"appReleaseId\";var RELEASEAPP_IdentityTypesMap={1:\"\u5b66\u751f\",2:\"\u6559\u804c\u5de5\",3:\"\u5b66\u9662\u5b66\u5de5\u529e\u4e3b\u4efb\",4:\"\u7cfb\u7edf\u7ba1\u7406\u5458\",5:\"\u8f85\u5bfc\u5458\",6:\"\u5b66\u5de5\u7ba1\u7406\u5458\",7:\"\u5b66\u9662\u526f\u4e66\u8bb0\"};var RELEASEAPP_StatesMap={0:\"\u672a\u4e0a\u67b6\",1:\"\u5df2\u4e0a\u67b6\"};/**\n * \u53d1\u5e03\u5bf9\u8c61\u9009\u9879\n */var RELEASEAPP_IdentityTypesOptions=Object.keys(RELEASEAPP_IdentityTypesMap).map(function(key){return{label:RELEASEAPP_IdentityTypesMap[key],value:Number(key)};});\n;// CONCATENATED MODULE: ./src/commons/app.ts\nvar APP_ID_KEY='app_id';var APP_STATE=/*#__PURE__*/function(APP_STATE){APP_STATE[APP_STATE[\"unpublish\"]=0]=\"unpublish\";APP_STATE[APP_STATE[\"published\"]=1]=\"published\";return APP_STATE;}({});var APP_StatesMap=[{label:\"\u5df2\u4e0a\u67b6\",value:1},{label:\"\u5df2\u4e0b\u67b6\",value:0}];var Identity={1:{id:1,identityType:1,name:'\u5b66\u751f',role:'user',sort:10},2:{id:2,identityType:2,name:'\u6559\u804c\u5de5',role:'user',sort:20},3:{id:3,identityType:3,name:'\u5b66\u9662\u5b66\u5de5\u529e\u4e3b\u4efb',role:'depAdmin',sort:40},4:{id:4,identityType:4,name:'\u7cfb\u7edf\u7ba1\u7406\u5458',role:'admin',sort:90},5:{id:5,identityType:5,name:'\u8f85\u5bfc\u5458',role:'counselor',sort:30},6:{id:6,identityType:6,name:'\u5b66\u5de5\u7ba1\u7406\u5458',role:'admin',sort:50},7:{id:7,identityType:7,name:'\u5b66\u9662\u526f\u4e66\u8bb0',role:'depAdmin',sort:45}};/**\n * \u66f4\u65b0\u8eab\u4efd\u5217\u8868\u6570\u636e\n * @param items \n */function updateIdentity(items){if(items&&items.length){RELEASEAPP_IdentityTypesOptions.splice(0,RELEASEAPP_IdentityTypesOptions.length);// \u6e05\u7a7a\u539f\u6765\u624b\u52a8\u5b9a\u4e49\u7684\uff0c\u5e76\u4fdd\u5b58\u539f\u6570\u7ec4\u5bf9\u8c61\uff0c\u4fdd\u6301\u5f15\u7528\u5173\u7cfb\nitems.sort(function(a,b){return a.sort-b.sort;});var _loop=function _loop(){var identity=items[index];identity.role=identity.role||getIdentityRole(identity.identityType);if(Identity[identity.identityType]){identity.role=Identity[identity.identityType].role||identity.role;Object.assign(Identity[identity.identityType],identity);}else{Identity[identity.identityType]=identity;}RELEASEAPP_IdentityTypesMap[identity.identityType]=identity.name||RELEASEAPP_IdentityTypesMap[identity.identityType];var idendityOption=RELEASEAPP_IdentityTypesOptions.find(function(item){return item.value===identity.identityType;});if(idendityOption){idendityOption.label=identity.name||idendityOption.label;}else{RELEASEAPP_IdentityTypesOptions.push({label:identity.name,value:identity.identityType});}};for(var index=0;index<items.length;index++){_loop();}}}\n// EXTERNAL MODULE: consume shared module (default) @yiban/system@* (singleton) (fallback: ./node_modules/@yiban/system/es/index.js)\nvar index_js_ = __webpack_require__(2950);\n;// CONCATENATED MODULE: ./src/context/MessageContext/ActionType.ts\nvar Action_Message=/*#__PURE__*/function(Action_Message){Action_Message[Action_Message[\"INIT\"]=0]=\"INIT\";return Action_Message;}({});\n// EXTERNAL MODULE: ./node_modules/immer/dist/immer.esm.mjs\nvar immer_esm = __webpack_require__(92546);\n;// CONCATENATED MODULE: ./src/context/MessageContext/MessageContext.tsx\nvar initState={};function init(url){return{messageClient:new EventSource(url)};}var reducer=function reducer(state,action){var type=action.type,payload=action.payload;return (0,immer_esm/* default */.ZP)(state,function(draft){switch(type){case Action_Message.INIT:{break;}default:}});};var Context=/*#__PURE__*/index_js_eager_default().createContext([initState,function(action){return action;}]);var MessageProvider=function MessageProvider(_ref){var children=_ref.children,url=_ref.url;var _reducer=index_js_eager_default().useReducer(reducer,url,init);return (0,emotion_react_browser_esm_js_.jsx)(Context.Provider,{value:_reducer},children);};var useMessageContext=function useMessageContext(){var _context=React.useContext(Context);if(_context===undefined){throw new Error('must be used within a MessageProvider');}return _context;};var useMessageClient=function useMessageClient(){var _context=React.useContext(Context);if(_context===undefined){throw new Error('must be used within a MessageProvider');}var _context2=_slicedToArray(_context,1),messageClient=_context2[0].messageClient;return messageClient;};\n;// CONCATENATED MODULE: ./src/context/MessageContext/index.tsx\n\n;// CONCATENATED MODULE: ./src/services/cuc.ts\nvar cuc_APP_CONFIG,cuc_APP_CONFIG$server;var CUC_API_PREFIX=((cuc_APP_CONFIG=window.APP_CONFIG)===null||cuc_APP_CONFIG===void 0?void 0:(cuc_APP_CONFIG$server=cuc_APP_CONFIG.server)===null||cuc_APP_CONFIG$server===void 0?void 0:cuc_APP_CONFIG$server.api)+'/cuc';var cuc_request=(0,dist_index_esm/* createRequest */.hG)({prefix:CUC_API_PREFIX});var API_CUC={/**\n   * \u83b7\u53d6CUC\u7684\u4eba\u5458\u6570\u636e\n   */getCucUserInfo:'/user/:id',/**\n   * \u83b7\u53d6\u4eba\u5458\u7ba1\u7406\u7684\u73ed\u7ea7\n   */getLeaderClasses:'/classleader/:id/classes',/**\n   * \u83b7\u53d6\u5b66\u9662\u4e0b\u7684\u8f85\u5bfc\u5458\n   */getAcademyClassLeaders:'/academy/:id/classleader',/**\n   * \u83b7\u53d6\u5b66\u5de5\u5c97\u4f4d\n   */getPositionsetXgbzr:'/positionset/poss_xgbzr/person',/**\n   * \u83b7\u53d6\u73ed\u7ea7\u4e0b\u7684\u5b66\u751f\n   */getClassStudents:'/class/:id/students',/**\n   * \u83b7\u53d6\u4e3b\u4f53\u4e0b\u7684\u6210\u5458\n   */getSubjectMembers:'/subject/members',/**\n   * \u83b7\u53d6\u8eab\u4efd\u5217\u8868\n   */getIdentityList:'/identity/list'};/**\n * \u83b7\u53d6\u4eba\u5458\u4fe1\u606f\n * @param params\n * @returns\n */function getCucUserInfo(params){var req=cuc_request(API_CUC.getCucUserInfo,params);req.catch(postFail);return req;}/**\n * \u83b7\u53d6\u8f85\u5bfc\u5458\u7ba1\u7406\u7684\u73ed\u7ea7\n * @param params\n * @returns\n */function getLeaderClasses(params){var req=cuc_request(API_CUC.getLeaderClasses,params);req.catch(postFail);return req;}/**\n * \u83b7\u53d6\u5b66\u5de5\u5c97\u4f4d\n * @param params\n * @returns\n */function getPositionsetXgbzr(params){var req=cuc_request(API_CUC.getPositionsetXgbzr,params);req.catch(postFail);return req;}/**\n * \u83b7\u53d6\u5b66\u9662\u4e0b\u7684\u8f85\u5bfc\u5458\n * @param params\n * @returns\n */function getAcademyClassLeaders(params){var req=cuc_request(API_CUC.getAcademyClassLeaders,params);req.catch(postFail);return req;}/**\n * \u67e5\u8be2\u73ed\u7ea7\u4e0b\u7684\u5b66\u751f\n * @param params\n * @returns\n */function getClassStudents(params){var req=cuc_request(API_CUC.getClassStudents,params);req.catch(postFail);return req;}/**\n * \u67e5\u8be2\u4e3b\u4f53\u4e0b\u7684\u6210\u5458\n * @param params\n */function getSubjectMembers(params){return cuc_request(API_CUC.getSubjectMembers,params);}/**\n * @name \u83b7\u53d6\u8eab\u4efd\u5217\u8868\n * @param params\n * @returns\n */function getIdentityList(params){return cuc_request(API_CUC.getIdentityList,params);}\n;// CONCATENATED MODULE: ./src/components/BackButton/index.tsx\nvar _excluded=[\"to\",\"onClick\",\"options\",\"children\",\"closeWindow\"];/* harmony default export */ var BackButton = (function(props){var _props$to=props.to,to=_props$to===void 0?-1:_props$to,onClick=props.onClick,options=props.options,_props$children=props.children,children=_props$children===void 0?\"\u8fd4\u56de\":_props$children,closeWindow=props.closeWindow,others=(0,objectWithoutProperties/* default */.Z)(props,_excluded);var navigate=(0,dist_index_esm/* useNavigate */.s0)();var close=function close(){try{window.close();}catch(error){window.history.go(typeof to===\"number\"?to:-1);}};var clickHandler=(0,index_js_eager_.useCallback)(function(e){typeof onClick==='function'&&onClick(e);if(!e.isDefaultPrevented()){if(closeWindow||window.history.length===1&&typeof to!=='string'){close();}else if(typeof to==='number'){navigate(to);}else{navigate(to,options);}}},[onClick,to,closeWindow]);return (0,emotion_react_browser_esm_js_.jsx)(lib_button[\"default\"],(0,esm_extends/* default */.Z)({type:\"primary\",onClick:clickHandler},others),children);});\n// EXTERNAL MODULE: ./node_modules/@yiban/chat/es/index.js + 381 modules\nvar es = __webpack_require__(11698);\n;// CONCATENATED MODULE: ./src/app.tsx\nvar app_excluded=[\"code\",\"identityType\",\"currentIdentity\",\"name\"];window.YB=YB;var httpClient=new es/* HttpClient */.eN({baseURL:window.APP_CONFIG.server.api},{response:function response(_response){if(_response.status===200&&_response.data.code===200){return Promise.resolve(_response.data.data);}return Promise.reject(_response);}});var theme=(0,index_js_.createTheme)({palette:{primary:{main:'#5353EC'},background:{default:'#F2F4F7'}}});function Provider(_ref){var children=_ref.children;var _useContextState=(0,dist_index_esm/* useContextState */.Oz)(),context=_useContextState.context;var errCode=0,errMsg;var err=context&&context.error?context.error:undefined;if(err&&err.code){if(err.code==404){errCode=404;}else if(err.code>=400){errCode=403;}else if(err.code>=500){errCode=500;}}if(!context||err){errMsg=getResponseError(context.error,'\u7f51\u7edc\u6216\u670d\u52a1\u6545\u969c');dist_index_esm/* utils */.P6.setDocumentTitle(errMsg);return (0,emotion_react_browser_esm_js_.jsx)(result/* default */.ZP,{status:errCode||500,subTitle:errMsg,extra:(0,emotion_react_browser_esm_js_.jsx)(ReloadButton,null)});}if(context&&!context.user){dist_index_esm/* utils */.P6.setDocumentTitle('\u8bf7\u68c0\u67e5\u7f51\u7edc\u6216\u767b\u5f55\u72b6\u6001');return (0,emotion_react_browser_esm_js_.jsx)(result/* default */.ZP,{status:errCode||403,subTitle:\"\\u8BF7\\u68C0\\u67E5\\u7F51\\u7EDC\\u6216\\u767B\\u5F55\\u72B6\\u6001\",extra:(0,emotion_react_browser_esm_js_.jsx)(ReloadButton,null)});}return (0,emotion_react_browser_esm_js_.jsx)(config_provider[\"default\"],{locale:zh_CN/* default */.Z},(0,emotion_react_browser_esm_js_.jsx)(MessageProvider,{url:window.APP_CONFIG.server.api+'/stream/events?stream=yibanngpc'},(0,emotion_react_browser_esm_js_.jsx)(index_js_.ThemeProvider,{theme:theme},(0,emotion_react_browser_esm_js_.jsx)(es/* RequestProvider */.zr,{requestMethod:function requestMethod(args){return httpClient.request(args);}},children))));}function rootElement(element){return (0,emotion_react_browser_esm_js_.jsx)(Provider,null,element);}/**\n * \u83b7\u53d6\u4e0a\u4e0b\u6587\n * @returns\n */function getContextState(){return _getContextState.apply(this,arguments);}function _getContextState(){_getContextState=(0,asyncToGenerator/* default */.Z)(/*#__PURE__*/(0,regeneratorRuntime/* default */.Z)().mark(function _callee(){var context,error,identityList,_res1$data,res1,res2,_res2$data,resData,code,identityType,currentIdentity,name,other,userInfo,isAdmin,currentIdentityData,user;return (0,regeneratorRuntime/* default */.Z)().wrap(function _callee$(_context){while(1)switch(_context.prev=_context.next){case 0:context={user:{},userInfo:{}};// \u7528\u6237\u4e2d\u53f0\u7684\u7528\u6237\u4fe1\u606f\u63a5\u53e3\uff0c\u53ef\u4ee5\u53bb\u6389\u3002\n// try {\n//   const res = await axios(getqueryUserRole())\n//   const user = res.result ? res.result.data : res.data;\n//   if ( user ) {\n//     context.user = {\n//       displayName: user.humName,\n//       id: user.humCode,\n//       ...user\n//     }\n//   }\n// } catch (err) {\n//   error = err;\n// }\nidentityList=[];_context.prev=2;_context.next=5;return getIdentityList();case 5:res1=_context.sent;identityList=(res1===null||res1===void 0?void 0:(_res1$data=res1.data)===null||_res1$data===void 0?void 0:_res1$data.items)||[];updateIdentity(identityList);_context.next=12;break;case 10:_context.prev=10;_context.t0=_context[\"catch\"](2);case 12:_context.prev=12;_context.next=15;return getUserInfo();case 15:res2=_context.sent;if(res2&&res2.data){resData=Object.assign({},res2.data, false?0:{});code=resData.code,identityType=resData.identityType,currentIdentity=resData.currentIdentity,name=resData.name,other=(0,objectWithoutProperties/* default */.Z)(resData,app_excluded);userInfo=resData;isAdmin=userInfo.isAdmin||userInfo.adminType?true:false;/**\n       * \u8fd9\u91cc\u517c\u5bb9\u4eba\u5458\u4e2d\u5fc3\u7684\u4ee3\u7801\n       */context.userInfo=userInfo;context.user=Object.assign(context.user||{},userInfo);context.user.id=userInfo.code||context.user.id;context.user.displayName=userInfo.name||context.user.displayName;context.user.auth=context.user.auth||isAdmin;context.user.role=context.user.role||[{roleCode:isAdmin?'admin':'hum'}];/**\n       *\n       */currentIdentityData=Identity[currentIdentity];context.user=(0,objectSpread2/* default */.Z)((0,objectSpread2/* default */.Z)({},context.user),{},{currentIdentity:currentIdentity,departAdmin:isDepartAdminUser(currentIdentity),xgAdmin:isXgAdminUser(currentIdentity),// \u6821\u7ea7\u7ba1\u7406\u5458\uff08\u5b66\u5de5\uff09\nschoolAdmin:isSchoolAdmin(currentIdentity),// \u6821\u7ea7\u7ba1\u7406\u5458\nsupper:isSupperUser(currentIdentity),// \u8d85\u7ea7\u7ba1\u7406\u5458\u5224\u65ad\nfdy:isFdyUser(currentIdentity),// \u8f85\u5bfc\u5458\nstaff:isStaffUser(currentIdentity),// \u6559\u804c\u5de5\nstudent:isStudentUser(currentIdentity),// \u5b66\u751f\nschoolLeader:isSchoolLeader(currentIdentity),// \u6821\u9886\u5bfc\u5224\u65ad\nacademyLeader:isAcademyLeader(currentIdentity),// \u5224\u65ad\u5b66\u9662\u9886\u5bfc\ndepartLeader:isDepartLeader(currentIdentity),// \u5224\u65ad\u90e8\u95e8\u9886\u5bfc\notherUser:isOtherUser(currentIdentity),// \u5176\u4ed6\nschoolLevelUser:isSchoolLevelUser(currentIdentity),//\nacademyLevelUser:isAcademyLevelUser(currentIdentity),// \u5b66\u9662\u7ea7\u9886\u5bfc\u7528\u6237\nacademyDeputySecretary:isAcademyDeputySecretary(currentIdentity),// \u5b66\u9662\u526f\u4e66\u8bb0\nacademyLeagueSecretary:isAcademyLeagueSecretary(currentIdentity),//\u5b66\u9662\u56e2\u59d4\u4e66\u8bb0\nstudentOfficeDirector:isStudentOfficeDirector(currentIdentity),// \u5b66\u5de5\u529e\u4e3b\u4efb\n_role:(0,objectSpread2/* default */.Z)((0,objectSpread2/* default */.Z)({},currentIdentityData),{},{identity:currentIdentity}),// role\u5c5e\u6027\u88ab\u4eba\u5458\u4e2d\u5fc3\u4f7f\u7528\u5360\u7528\uff0c\u6240\u4ee5\u7528_role\u4ee3\u66ff\nidentities:identityType.filter(function(i){return i!==currentIdentity;}).sort(function(a,b){return Identity[b].sort-Identity[a].sort;}).map(function(t){return (0,objectSpread2/* default */.Z)((0,objectSpread2/* default */.Z)({},Identity[t]),{},{identity:t});})});}error=null;_context.next=23;break;case 20:_context.prev=20;_context.t1=_context[\"catch\"](12);error=typeof _context.t1==='object'&&_context.t1.response?_context.t1.response:_context.t1;case 23:if(!error){_context.next=25;break;}return _context.abrupt(\"return\",{error:error});case 25:user=context.user;context.user.canCreateTask=user.supper||user.fdy||user.xgAdmin||user.departAdmin?true:false;// \u521b\u5efa\u4efb\u52a1\u7684\u6743\u9650\u5224\u65ad\ncontext.user.canCreateActivity=context.user.canCreateTask&&!user.fdy;// \u8f85\u5bfc\u5458\u4e0d\u53ef\u4ee5\u521b\u5efa\u6d3b\u52a8\ncontext.user.roleType=context.user.auth?'admin':'user';// \u63d0\u4f9b\u7ed9\u4eba\u5458\u4e2d\u5fc3\u7684\n// fix \u7ba1\u7406\u7684orgs\ncontext.userInfo.manageOrgs=context.userInfo.manageOrgs||(context.user.academyLevelUser?context.userInfo.orgs:[])||[];updateUSER(context.user);return _context.abrupt(\"return\",context);case 32:case\"end\":return _context.stop();}},_callee,null,[[2,10],[12,20]]);}));return _getContextState.apply(this,arguments);}function app_getAccess(_access,context){var _context$user=context.user,user=_context$user===void 0?{}:_context$user;// return { admin: true }\nvar access=Object.assign((0,objectSpread2/* default */.Z)((0,objectSpread2/* default */.Z)({},user),{},(0,defineProperty/* default */.Z)({},\"\".concat(user.currentIdentity),true)),user._role?(0,defineProperty/* default */.Z)({},user._role.role,true):{});updateACCESS(access);return access;}function noAccessRender(){return (0,emotion_react_browser_esm_js_.jsx)(result/* default */.ZP,{status:403,title:\"\\u65E0\\u6743\\u9650\\u8BBF\\u95EE\",subTitle:\"\\u8BF7\\u91CD\\u65B0\\u767B\\u5F55\\u6216\\u5207\\u6362\\u5230\\u6709\\u6743\\u9650\\u8EAB\\u4EFD\",extra:(0,emotion_react_browser_esm_js_.jsx)(BackButton,null)});}function modifyRenderOpts(opts){var _APP_CONFIG;return Object.assign(opts,{defaultTitle:(_APP_CONFIG=window.APP_CONFIG)===null||_APP_CONFIG===void 0?void 0:_APP_CONFIG.title});}\n;// CONCATENATED MODULE: ./src/.sui/model.jsx\nvar _ref=src_app_namespaceObject||{},model_getContextState=_ref.getContextState;function _sui_model_getContextState(_x,_x2){return _getContextState2.apply(this,arguments);}function _getContextState2(){_getContextState2=(0,asyncToGenerator/* default */.Z)(/*#__PURE__*/(0,regeneratorRuntime/* default */.Z)().mark(function _callee(parentContext,topContext){var res;return (0,regeneratorRuntime/* default */.Z)().wrap(function _callee$(_context){while(1)switch(_context.prev=_context.next){case 0:if(!(typeof model_getContextState==='function')){_context.next=6;break;}_context.next=3;return model_getContextState(parentContext,topContext);case 3:res=_context.sent;_context.next=7;break;case 6:res=model_getContextState;case 7:return _context.abrupt(\"return\",res||{});case 8:case\"end\":return _context.stop();}},_callee);}));return _getContextState2.apply(this,arguments);}var models={};addModel(models,'context',createContextStateModel(_sui_model_getContextState));var dispatcher=new index_esm/* Dispatcher */.Up();function Executor(props){var hook=props.hook,onUpdate=props.onUpdate,namespace=props.namespace;var updateRef=(0,index_js_eager_.useRef)(onUpdate);var initialLoad=(0,index_js_eager_.useRef)(false);var data;try{data=hook();}catch(e){console.error(\"plugin-model: Invoking '\".concat(namespace||'unknown',\"' model failed:\"),e);}// \u9996\u6b21\u6267\u884c\u65f6\u7acb\u523b\u8fd4\u56de\u521d\u59cb\u503c\n(0,index_js_eager_.useMemo)(function(){updateRef.current(data);},[]);// React 16.13 \u540e update \u51fd\u6570\u7528 useEffect \u5305\u88f9\n(0,index_js_eager_.useEffect)(function(){if(initialLoad.current){updateRef.current(data);}else{initialLoad.current=true;}});return null;}function ModelsProvider(props){return (0,emotion_react_browser_esm_js_.jsx)((index_js_eager_default()).Fragment,null,Object.keys(models).map(function(namespace){return (0,emotion_react_browser_esm_js_.jsx)(Executor,{key:namespace,namespace:namespace,hook:models[namespace],onUpdate:function onUpdate(val){dispatcher.data[namespace]=val;dispatcher.update(namespace);}});}),props.children);}/* harmony default export */ var model = (models);// generated at 2025/7/17 11:25:30 by sui v3.1.10\n;// CONCATENATED MODULE: ./src/.sui/core/plugin-model/Provider.jsx\nfunction Provider_Provider(props){var children=props.children,loadingElement=props.loading;var appLoaded=(0,index_js_eager_.useRef)(false);var _ref=(0,index_esm/* useModel */.tT)(\"context\")||{},_ref$loading=_ref.loading,loading=_ref$loading===void 0?false:_ref$loading;// \u83b7\u53d6runtime\u914d\u7f6e\nvar useRuntimeConfig=core_plugin.applyPlugins({key:\"getRuntimeConfig\",type:index_esm/* ApplyPluginsType */.Ac.filter,initialValue:{},args:{loading:loading}})||{};(0,index_js_eager_.useEffect)(function(){if(!loading){appLoaded.current=true;}},[loading]);if(loading&&!appLoaded.current){return loadingElement||useRuntimeConfig.loading||null;}return children;};Provider_Provider.displayName='ContextProvider';\n;// CONCATENATED MODULE: ./src/.sui/plugin-model.jsx\nfunction dataflowProvider(element){var args=arguments.length>1&&arguments[1]!==undefined?arguments[1]:{};return (0,emotion_react_browser_esm_js_.jsx)(ModelsProvider,args,(0,emotion_react_browser_esm_js_.jsx)(Provider_Provider,args,element));}// generated at 2025/7/17 11:25:30 by sui v3.1.10\n// EXTERNAL MODULE: ./src/.sui/core/plugin-access/Access.jsx\nvar Access = __webpack_require__(8595);\n;// CONCATENATED MODULE: ./src/.sui/core/plugin-access/Provider.jsx\nfunction plugin_access_Provider_Provider(props){var children=props.children,routes=props.routes,accessStrictMode=props.accessStrictMode,getAccess=props.getAccess;var _ref=(0,index_esm/* useModel */.tT)(\"context\")||{},_ref$context=_ref.context,context=_ref$context===void 0?{}:_ref$context;// \u521d\u59cb\u5316access\nvar _useMemo=(0,index_js_eager_.useMemo)(function(){return{access:getAccess?getAccess({},context):context.access,accessMenus:context.accessMenus};},[context]),access=_useMemo.access,accessMenus=_useMemo.accessMenus;if(accessStrictMode||access){// \u975e\u4e25\u683c\u6a21\u5f0f\uff0c \u53ea\u6709\u8bbe\u7f6e\u4e86access\u72b6\u6001\uff0c\u624d\u8fdb\u884c\u6743\u9650\u6821\u9a8c\n(0,Access/* modifyRoutesByAccess */.Jv)(routes,access,accessStrictMode===true,accessStrictMode===true);// \u4e25\u683c\u6a21\u5f0f\u4e0b\uff0c\u9ed8\u8ba4\u7236\u7ea7unaccessiable\n}if(accessMenus){var accessPaths=(0,Access/* getAccessMenusPaths */.MT)(accessMenus);(0,Access/* modifyRoutesByAccessMenus */.fg)(routes,accessPaths,accessStrictMode===true,accessStrictMode===true);}return (0,emotion_react_browser_esm_js_.jsx)(Access/* AccessProvider */._9,{access:access},children);};plugin_access_Provider_Provider.displayName='AccessRoutesProvider';\n;// CONCATENATED MODULE: ./src/.sui/plugin-access.jsx\nvar plugin_access_ref=src_app_namespaceObject||{},_getAccess=plugin_access_ref.getAccess;var plugin_access_getAccess=function getAccess(access,context){if(typeof _getAccess==='function'){return _getAccess(access,context);}else if(_getAccess&&typeof _getAccess==='object'){return Object.assign({},access,_getAccess);}return access;};var accessStrictMode=false;function accessProvider(element,_ref2){var routes=_ref2.routes;var props={routes:routes,accessStrictMode:accessStrictMode,getAccess:plugin_access_getAccess};return (0,emotion_react_browser_esm_js_.jsx)(plugin_access_Provider_Provider,props,element);}// generated at 2025/7/17 11:25:30 by sui v3.1.10\n// EXTERNAL MODULE: ./src/components/Loading/index.js\nvar Loading = __webpack_require__(53964);\n;// CONCATENATED MODULE: ./src/.sui/render.jsx\nvar render_ref=src_app_namespaceObject||{},microApp=render_ref.microApp;var fallback=(0,emotion_react_browser_esm_js_.jsx)(Loading/* default */.Z,null);var pick=index_esm/* utils */.P6.pick,uuid=index_esm/* utils */.P6.uuid;// \u6ce8\u518c\u63d2\u4ef6\ncore_plugin.register({apply:pick(src_app_namespaceObject,core_plugin.supportHooks),path:'./src/app'});core_plugin.register({apply:plugin_access_namespaceObject,path:'./.sui/plugin-access'});core_plugin.register({apply:_sui_plugin_model_namespaceObject,path:'./.sui/plugin-model'});// \u5bfc\u5165\u5e76\u6ce8\u518cremote\u5e94\u7528\nvar remotes={};//\u521b\u5efa\u8def\u7531\nvar routes=[]||0;// on start\ncore_plugin.applyPlugins({key:\"onStart\",type:index_esm/* ApplyPluginsType */.Ac.action,args:{plugin:core_plugin}});// routes filter\ncore_plugin.applyPlugins({key:\"modifyRoutes\",type:index_esm/* ApplyPluginsType */.Ac.action,args:{routes:routes}});var suiOpts={version:\"3.1.10\",runtimeVersion:\"3.1.4\",pluginsVers:{},routes:routes,plugin:core_plugin,models:model,remotes:remotes,dispatcher:dispatcher,__client_render_id__:uuid()};// \u8fd0\u884c\u65f6\u6e32\u67d3\u914d\u7f6e\u9879\uff0c\u53ef\u7ea7\u8c03\u7528\u8986\u76d6\nvar renderOpts={mountContainer:\"root\",defaultTitle:\"\u80b2\u4eba\u5e73\u53f0\",noAccessTitle:\"\u65e0\u6743\u9650\u8bbf\u95ee\",notFoundTitle:\"\u672a\u627e\u5230\u9875\u9762\",history:{\"type\":\"hash\",\"basename\":\"/\"},loading:fallback};function render(opts){var options=Object.assign({},renderOpts,opts||{},suiOpts);var clientRender=(0,index_esm/* getClientRender */.fO)(options);return clientRender();}// generated at 2025/7/17 11:25:30 by sui v3.1.10\n;// CONCATENATED MODULE: ./src/.sui/bootstrap.js\n/* harmony default export */ var bootstrap = (render());// generated at 2025/7/17 11:25:30 by sui v3.1.10//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///74246\n")},8595:function(__unused_webpack_module,__webpack_exports__,__webpack_require__){"use strict";eval("/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Jv: function() { return /* binding */ modifyRoutesByAccess; },\n/* harmony export */   MT: function() { return /* binding */ getAccessMenusPaths; },\n/* harmony export */   _9: function() { return /* binding */ AccessProvider; },\n/* harmony export */   fg: function() { return /* binding */ modifyRoutesByAccessMenus; }\n/* harmony export */ });\n/* unused harmony exports Access, useAccess, useAccessState */\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(96985);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _sui_sui__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(53273);\n/* harmony import */ var _emotion_react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(94216);\n/* harmony import */ var _emotion_react__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(_emotion_react__WEBPACK_IMPORTED_MODULE_2__);\nvar AccessState=/*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_0___default().createContext({});/**\n * \u6743\u9650\u7ec4\u4ef6\n * @returns \n */function Access(props){var children=props.children,accessible=props.accessible,fallback=props.fallback;return ___EmotionJSX(If,{condition:!!accessible,fallback:fallback},children);}/**\n * access provider\n * @param {*} props \n * @returns \n */function AccessProvider(props){var children=props.children,access=props.access;var value=(0,react__WEBPACK_IMPORTED_MODULE_0__.useMemo)(function(){return{access:access};},[access]);return (0,_emotion_react__WEBPACK_IMPORTED_MODULE_2__.jsx)(AccessState.Provider,{value:value},children);}/**\n * \u83b7\u53d6access\u6743\u9650\n * @returns \n */function useAccess(){var _React$useContext=React.useContext(AccessState),_React$useContext$acc=_React$useContext.access,access=_React$useContext$acc===void 0?{}:_React$useContext$acc;return access;}/**\n * \u83b7\u53d6access\u6743\u9650\u72b6\u6001\n * @returns \n */function useAccessState(){return React.useContext(AccessState);}/**\n * @name \u8bbe\u7f6e\u6743\u9650\u8def\u7531\u4f9d\u8d56\n */function parentRouteAccessDepsNum(parentRoute,hasAccess){parentRoute.__accessDepsNum__=parentRoute.__accessDepsNum__||0;if(hasAccess){parentRoute.__accessDepsNum__++;}if(parentRoute.__parentRoute__){parentRouteAccessDepsNum(parentRoute.__parentRoute__,hasAccess);}}/**\n * \u4fee\u6539\u5e76\u8bbe\u7f6e\u8def\u7531\u6743\u9650\n * @param {*} routes \n * @param {*} access \n * @param {*} parentUnaccessible\n */ /**\n * \u4fee\u6539\u5e76\u8bbe\u7f6e\u8def\u7531\u6743\u9650\n * @param {*} routes \n * @param {*} access \n * @param {*} strictMode\n * @param {*} parentUnaccessible\n */function modifyRoutesByAccess(routes,access,strictMode,parentUnaccessible,parentRoute){return[].concat(routes).map(function(route){// \u9ed8\u8ba4\u60c5\u51b5\u4e0b\uff0c\u521d\u59cb\u6743\u9650\u4e3a\u5df2\u5b9a\u4e49\u6216\u7ee7\u627f\u4e0a\u7ea7\u6743\u9650\nvar accessible=typeof route.unaccessible==='boolean'?!route.unaccessible:parentUnaccessible!==true;if(strictMode){// \u4e25\u683c\u6a21\u5f0f\u4e0b\uff0c\u9ed8\u8ba4\u4e0d\u901a\u8fc7\naccessible=false;}if(route.access){// \u5f53\u8bbe\u7f6e\u4e86\u6743\u9650\uff0c\u9ed8\u8ba4\u4e0d\u901a\u8fc7\uff0c\u53ea\u6709\u6743\u9650\u901a\u8fc7\u624d\u80fd\u8bbf\u95ee\naccessible=false;var accesses=[].concat(route.access);for(var index=0;index<accesses.length;index++){var accessName=accesses[index];if(access&&access[accessName]){accessible=typeof access[accessName]==='function'?access[accessName](route)==true:true;break;}}}route.unaccessible=!accessible;if(!route.unaccessible&&parentRoute&&!strictMode){// \u975e\u4e25\u683c\u6a21\u5f0f\u4e0b\u7684\uff0c\u5904\u7406\u6743\u9650 path \u4f9d\u8d56\u94fe\uff0c\u4ee5\u4fdd\u8bc1\u5728\u5b50\u5c42\u7684\u8def\u7531\u53ef\u8bbf\u95ee\u5230\nparentRouteAccessDepsNum(parentRoute,true);}if(route.children&&route.children.length){route.children=modifyRoutesByAccess(route.children,access,strictMode,route.unaccessible,route);}return route;});}/**\n * \u83b7\u53d6\u83dc\u5355paths\n * @param {*} menus \n * @param {*} paths \n */function getAccessMenusPaths(menus,paths){paths=paths||[];if(!menus||!menus.length){return paths;}menus.forEach(function(menu){menu.path&&paths.push(menu.path);if(menu.children){getAccessMenusPaths(menu.children,paths);}});return paths;}/**\n * \u6839\u636e\u6743\u9650\u83dc\u5355\u5904\u7406\u8def\u7531\u6743\u9650\n * @param {*} routes \n * @param {*} accessPaths \n * @param {*} strictMode \n * @param {*} parentUnaccessible \n */function modifyRoutesByAccessMenus(routes,accessPaths,strictMode,parentUnaccessible){if(accessPaths&&accessPaths.length&&routes&&routes.length){routes.forEach(function(route){// \u9ed8\u8ba4\u60c5\u51b5\u4e0b\uff0c\u521d\u59cb\u6743\u9650\u4e3a\u5df2\u5b9a\u4e49\u6216\u7ee7\u627f\u4e0a\u7ea7\u6743\u9650\nvar accessible=typeof route.unaccessible==='boolean'?!route.unaccessible:parentUnaccessible!==true;if(strictMode){// \u4e25\u683c\u6a21\u5f0f\u4e0b\uff0c\u9ed8\u8ba4\u4e0d\u901a\u8fc7\naccessible=false;}if(accessPaths.indexOf(route.absolutePath)>-1){accessible=true;}route.unaccessible=!accessible;if(route.children&&route.children.length){modifyRoutesByAccessMenus(route.children,accessPaths,strictMode,route.unaccessible);}});}}//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiODU5NS5qcyIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7Ozs7QUFBQTtBQU1BO0FBQ0E7QUFDQTtBQVNBO0FBQ0E7QUFDQTtBQUNBO0FBWUE7QUFDQTtBQUNBO0FBT0E7QUFDQTtBQUNBO0FBTUE7QUFDQTtBQVlBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFFQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFLQTtBQUVBO0FBR0E7QUFhQTtBQVVBO0FBQ0E7QUFDQTtBQUNBO0FBZ0JBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUtBO0FBRUEiLCJzb3VyY2VzIjpbIi5zdWkvY29yZS9wbHVnaW4tYWNjZXNzL0FjY2Vzcy5qc3giXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///8595\n")},86642:function(__unused_webpack_module,__unused_webpack___webpack_exports__,__webpack_require__){"use strict";eval('\n// UNUSED EXPORTS: Access, AccessProvider, NotFoundComponentModule, fallback, fallbackDom, getMFMicroAppModuleName, importMF, importMFApp, importMFComponent, useAccess, useAccessState, withMFModule\n\n// EXTERNAL MODULE: consume shared module (default) react@^17.0.2 (singleton) (fallback: ./node_modules/react/index.js) (eager)\nvar index_js_eager_ = __webpack_require__(96985);\n// EXTERNAL MODULE: ./src/.sui/core/plugin-access/Access.jsx\nvar Access = __webpack_require__(8595);\n;// CONCATENATED MODULE: ./src/.sui/core/plugin-access/runtime.jsx\n\n// EXTERNAL MODULE: ./node_modules/@sui/sui/dist/index.esm.js\nvar index_esm = __webpack_require__(53273);\n// EXTERNAL MODULE: consume shared module (default) @emotion/react@^11.10.5 (singleton) (fallback: ./node_modules/@emotion/react/dist/emotion-react.browser.esm.js)\nvar emotion_react_browser_esm_js_ = __webpack_require__(94216);\n;// CONCATENATED MODULE: ./src/.sui/core/NotFoundComponentModule.jsx\nvar NotFoundComponentModule=function NotFoundComponentModule(_ref){var module=_ref.module,filePath=_ref.filePath,message=_ref.message,_ref$type=_ref.type,type=_ref$type===void 0?"error":_ref$type;if(false){ var typeLabel, bagdeStyle, bgStyle; }else{return null;}};/* harmony default export */ var core_NotFoundComponentModule = ((/* unused pure expression or super */ null && (NotFoundComponentModule)));\n;// CONCATENATED MODULE: ./src/.sui/core/plugin-federated/runtime.js\n// \u8fdc\u7a0bremote container urls\nvar remotesUrls={};/**\n * \u89e3\u6790\u7ec4\u4ef6scope\n * @param {*} name \n * @param {*} prefix \n * @returns \n */function parseModuleName(name,prefix){return(prefix||\'\')+name.replace(/^@/,\'$\').replace(/[@\\/\\\\\\.-]/g,\'_\');}/**\n** \u89e3\u6790\u7ec4\u4ef6\u5305\u540d\n** @param {*} scope\n*/function parsePackageName(scope){return scope?scope.replace(/^\\$/,\'@\').replace(\'_\',\'/\'):"";}/**\n* \u83b7\u53d6 \u8fdc\u7a0b\u5fae\u5e94\u7528\u6a21\u5757\u540d\n**/function getMFMicroAppModuleName(microApp){if(microApp){var name=\'MicroApp\';if(typeof microApp===\'string\'){name=microApp;}else if(typeof microApp===\'object\'){name=microApp.name||name;if(microApp.entry){name=name+\'_Entry_\'+microApp.entry;}}return name;}}/**\n* \u83b7\u53d6\u5bfc\u5165MF\u51fa\u9519\u6e32\u67d3\u903b\u8f91\n**/function getImportMFError(_,inject,errMsg){if(false){}else{inject(errMsg);}}/**\n** \u83b7\u53d6\u8fdc\u7a0b\u5165\u53e3\n**/function getRemoteUrl(scope,packageName,version,filename){var remote_url=scope&&remotesUrls[scope]?remotesUrls[scope]:packageName&&remotesUrls[packageName]?remotesUrls[packageName]:undefined;if(typeof window._sui_webpack_remote_urls===\'object\'){if(packageName&&window._sui_webpack_remote_urls[packageName]){remote_url=window._sui_webpack_remote_urls[packageName];}if(!remote_url&&scope&&window._sui_webpack_remote_urls[scope]){remote_url=window._sui_webpack_remote_urls[scope];}}if(!remote_url&&typeof window._get_sui_webpack_remote_url===\'function\'&&packageName){remote_url=window._get_sui_webpack_remote_url(packageName,null,version,filename);}if(!remote_url&&typeof window._get_sui_webpack_remote_url===\'function\'&&scope&&scope!==packageName){remote_url=window._get_sui_webpack_remote_url(scope,null,version,filename);}return remote_url?remote_url:scope&&remotesUrls[scope]?remotesUrls[scope]:"";}/**\n * \u5bfc\u5165\u8fdc\u7a0bmf\u5bb9\u5668\u6a21\u5757\n * @param {*} modulePath \n * @param {*} packageName \n * @param {*} version \n * @param {*} filename \n * @returns \n */function importMF(modulePath,packageName,version,filename){var index=modulePath.indexOf(\'/\');if(index<0){return new Promise(function(_,inject){getImportMFError(_,inject,"importMF: bad Module Name ".concat(modulePath,", should match pattern \'remoteScope/moduleName"));});}var scope=modulePath.slice(0,index);var module=modulePath.slice(index+1);var remote_url=getRemoteUrl(scope,packageName||parsePackageName(scope),version,filename);if(!remote_url){return new Promise(function(_,inject){getImportMFError(_,inject,"importMF: no remote url for \\"".concat(scope,"\\""));});}return new Promise(function(_,inject){getDynamicModule(remote_url,scope,module).then(_).catch(function(error){console.error(error);getImportMFError(_,inject,"importMF: load remoteModule `".concat(modulePath,"` failed. Please check the remote url ").concat(remote_url));});});}/**\n** \u5bfc\u5165\u8fdc\u7a0bmf \u5fae\u5e94\u7528\n** @param {*} microApp\n**/function importMFApp(scope,microApp){var module=getMFMicroAppModuleName(microApp);return importMF("".concat(scope,"/").concat(module));}/**\n * \u52a0\u8f7d\u8fdc\u7a0b\u7ec4\u4ef6\n * @param {*} componentName \n * @param {*} version \n * @param {*} filename \n * @returns \n */function importMFComponent(componentName,version,filename){var index=componentName.indexOf(\'.\');if(index<0){return new Promise(function(_,inject){getImportMFError(_,inject,"importMFComponent: bad component Name ".concat(componentName,", should match pattern \'remotePackage.componentName"));});}var packageName=componentName.slice(0,index);var scope=parseModuleName(packageName);var component=componentName.slice(index+1);var remote_url=getRemoteUrl(scope,packageName,version,filename);if(!remote_url){return new Promise(function(_,inject){getImportMFError(_,inject,"importMFComponent: no remote url for \\"".concat(packageName,"\\""));});}return new Promise(function(_,inject){getDynamicModule(remote_url,scope,component).then(_).catch(function(error){console.error(error);getImportMFError(_,inject,"importMFComponent: load remoteComponent `".concat(componentName,"` failed. Please check the remote url ").concat(remote_url));});});}/**\n * \u4f9d\u8d56\n * @param mfObjArr \n * @param component \n * @param fallback \n * @returns \n */function withMFModule(mfObjArr,component,fallback){var _mfObjArr=(mfObjArr||[]).map(function(item){return typeof item===\'string\'?importMF(item):item;});return _withMFModule(_mfObjArr,component,fallback);}\n// EXTERNAL MODULE: ./src/components/Loading/index.js\nvar Loading = __webpack_require__(53964);\n;// CONCATENATED MODULE: ./src/.sui/core/runtime.jsx\nvar fallbackDom=(0,emotion_react_browser_esm_js_.jsx)(Loading/* default */.Z,null);var fallback=(0,emotion_react_browser_esm_js_.jsx)(Loading/* default */.Z,null);//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiODY2NDIuanMiLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7Ozs7Ozs7QUNBQTs7QUNBQTtBQUlBO0FBS0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQU1BO0FBQ0E7QUFDQTtBQU1BO0FBQ0E7QUFpQkE7QUFDQTtBQVlBO0FBQ0E7QUF1QkE7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUF5QkE7QUFDQTtBQUNBO0FBT0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBNkJBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTs7OztBQ2pLQSIsInNvdXJjZXMiOlsiLnN1aS9jb3JlL3BsdWdpbi1hY2Nlc3MvcnVudGltZS5qc3giLCIuc3VpL2NvcmUvTm90Rm91bmRDb21wb25lbnRNb2R1bGUuanN4IiwiLnN1aS9jb3JlL3BsdWdpbi1mZWRlcmF0ZWQvcnVudGltZS5qcyIsIi5zdWkvY29yZS9ydW50aW1lLmpzeCJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///86642\n')},53964:function(__unused_webpack_module,__webpack_exports__,__webpack_require__){"use strict";eval('/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Z: function() { return /* binding */ Loading; }\n/* harmony export */ });\n/* harmony import */ var antd_lib_spin_style__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(81753);\n/* harmony import */ var antd_lib_spin_style__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(antd_lib_spin_style__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var antd_lib_spin__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(85965);\n/* harmony import */ var _emotion_styled_base__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(70974);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(96985);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _emotion_react__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(94216);\n/* harmony import */ var _emotion_react__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(_emotion_react__WEBPACK_IMPORTED_MODULE_3__);\nfunction _EMOTION_STRINGIFIED_CSS_ERROR__(){return"You have tried to stringify object returned from `css` function. It isn\'t supposed to be used directly (e.g. as value of the `className` prop), but rather handed to emotion so it can handle it (e.g. as value of `css` prop).";}var Container=/*#__PURE__*/(0,_emotion_styled_base__WEBPACK_IMPORTED_MODULE_1__/* ["default"] */ .Z)(\'div\', true?{target:"e9ui46v0"}:0)( true?{name:"1if2t74",styles:"width:100%;height:100%;position:absolute;top:0;left:0;display:flex;z-index:9999;align-items:center;justify-content:center"}:0);function Loading(_ref){var hide=_ref.hide;return!hide&&(0,_emotion_react__WEBPACK_IMPORTED_MODULE_3__.jsx)(Container,null,(0,_emotion_react__WEBPACK_IMPORTED_MODULE_3__.jsx)(antd_lib_spin__WEBPACK_IMPORTED_MODULE_4__/* ["default"] */ .Z,null));}//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiNTM5NjQuanMiLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7Ozs7QUFBQSIsInNvdXJjZXMiOlsiY29tcG9uZW50cy9Mb2FkaW5nL2luZGV4LmpzIl0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///53964\n')},81742:function(){eval("window.name='YB'// ;(window as any)._sui_webpack_remote_urls = {\n//   '@yiban/graph-template': '/sui/@yiban/graph-template/0.1.0/remoteEntry.js',\n//   '@yiban/message-template': '/sui/@yiban/message-template/0.1.0/remoteEntry.js',\n//   '@yiban/charts': '/sui/@yiban/charts/0.1.0/remoteEntry.js'\n// }\n;window.AjaxRequestFilters={responseFilters:[function(response){if(response.status===401&&\"production\"!=='development'){window.location.reload();}return response;}]};//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiODE3NDIuanMiLCJtYXBwaW5ncyI6IkFBQUE7QUFFQTtBQUNBO0FBQ0E7QUFDQTtBQUFBIiwic291cmNlcyI6WyJnbG9iYWwudHMiXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///81742\n")}}]);