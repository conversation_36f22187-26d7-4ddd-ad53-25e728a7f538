"use strict";(self.webpackChunkyiban_frontend_admin=self.webpackChunkyiban_frontend_admin||[]).push([[465],{8595:function(__unused_webpack_module,__webpack_exports__,__webpack_require__){eval("/* unused harmony exports Access, AccessProvider, useAccess, useAccessState, modifyRoutesByAccess, getAccessMenusPaths, modifyRoutesByAccessMenus */\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(96985);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _sui_sui__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(53273);\n/* harmony import */ var _emotion_react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(94216);\n/* harmony import */ var _emotion_react__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(_emotion_react__WEBPACK_IMPORTED_MODULE_2__);\nvar AccessState=/*#__PURE__*/react__WEBPACK_IMPORTED_MODULE_0___default().createContext({});/**\n * \u6743\u9650\u7ec4\u4ef6\n * @returns \n */function Access(props){var children=props.children,accessible=props.accessible,fallback=props.fallback;return ___EmotionJSX(If,{condition:!!accessible,fallback:fallback},children);}/**\n * access provider\n * @param {*} props \n * @returns \n */function AccessProvider(props){var children=props.children,access=props.access;var value=useMemo(function(){return{access:access};},[access]);return ___EmotionJSX(AccessState.Provider,{value:value},children);}/**\n * \u83b7\u53d6access\u6743\u9650\n * @returns \n */function useAccess(){var _React$useContext=React.useContext(AccessState),_React$useContext$acc=_React$useContext.access,access=_React$useContext$acc===void 0?{}:_React$useContext$acc;return access;}/**\n * \u83b7\u53d6access\u6743\u9650\u72b6\u6001\n * @returns \n */function useAccessState(){return React.useContext(AccessState);}/**\n * @name \u8bbe\u7f6e\u6743\u9650\u8def\u7531\u4f9d\u8d56\n */function parentRouteAccessDepsNum(parentRoute,hasAccess){parentRoute.__accessDepsNum__=parentRoute.__accessDepsNum__||0;if(hasAccess){parentRoute.__accessDepsNum__++;}if(parentRoute.__parentRoute__){parentRouteAccessDepsNum(parentRoute.__parentRoute__,hasAccess);}}/**\n * \u4fee\u6539\u5e76\u8bbe\u7f6e\u8def\u7531\u6743\u9650\n * @param {*} routes \n * @param {*} access \n * @param {*} parentUnaccessible\n */ /**\n * \u4fee\u6539\u5e76\u8bbe\u7f6e\u8def\u7531\u6743\u9650\n * @param {*} routes \n * @param {*} access \n * @param {*} strictMode\n * @param {*} parentUnaccessible\n */function modifyRoutesByAccess(routes,access,strictMode,parentUnaccessible,parentRoute){return[].concat(routes).map(function(route){// \u9ed8\u8ba4\u60c5\u51b5\u4e0b\uff0c\u521d\u59cb\u6743\u9650\u4e3a\u5df2\u5b9a\u4e49\u6216\u7ee7\u627f\u4e0a\u7ea7\u6743\u9650\nvar accessible=typeof route.unaccessible==='boolean'?!route.unaccessible:parentUnaccessible!==true;if(strictMode){// \u4e25\u683c\u6a21\u5f0f\u4e0b\uff0c\u9ed8\u8ba4\u4e0d\u901a\u8fc7\naccessible=false;}if(route.access){// \u5f53\u8bbe\u7f6e\u4e86\u6743\u9650\uff0c\u9ed8\u8ba4\u4e0d\u901a\u8fc7\uff0c\u53ea\u6709\u6743\u9650\u901a\u8fc7\u624d\u80fd\u8bbf\u95ee\naccessible=false;var accesses=[].concat(route.access);for(var index=0;index<accesses.length;index++){var accessName=accesses[index];if(access&&access[accessName]){accessible=typeof access[accessName]==='function'?access[accessName](route)==true:true;break;}}}route.unaccessible=!accessible;if(!route.unaccessible&&parentRoute&&!strictMode){// \u975e\u4e25\u683c\u6a21\u5f0f\u4e0b\u7684\uff0c\u5904\u7406\u6743\u9650 path \u4f9d\u8d56\u94fe\uff0c\u4ee5\u4fdd\u8bc1\u5728\u5b50\u5c42\u7684\u8def\u7531\u53ef\u8bbf\u95ee\u5230\nparentRouteAccessDepsNum(parentRoute,true);}if(route.children&&route.children.length){route.children=modifyRoutesByAccess(route.children,access,strictMode,route.unaccessible,route);}return route;});}/**\n * \u83b7\u53d6\u83dc\u5355paths\n * @param {*} menus \n * @param {*} paths \n */function getAccessMenusPaths(menus,paths){paths=paths||[];if(!menus||!menus.length){return paths;}menus.forEach(function(menu){menu.path&&paths.push(menu.path);if(menu.children){getAccessMenusPaths(menu.children,paths);}});return paths;}/**\n * \u6839\u636e\u6743\u9650\u83dc\u5355\u5904\u7406\u8def\u7531\u6743\u9650\n * @param {*} routes \n * @param {*} accessPaths \n * @param {*} strictMode \n * @param {*} parentUnaccessible \n */function modifyRoutesByAccessMenus(routes,accessPaths,strictMode,parentUnaccessible){if(accessPaths&&accessPaths.length&&routes&&routes.length){routes.forEach(function(route){// \u9ed8\u8ba4\u60c5\u51b5\u4e0b\uff0c\u521d\u59cb\u6743\u9650\u4e3a\u5df2\u5b9a\u4e49\u6216\u7ee7\u627f\u4e0a\u7ea7\u6743\u9650\nvar accessible=typeof route.unaccessible==='boolean'?!route.unaccessible:parentUnaccessible!==true;if(strictMode){// \u4e25\u683c\u6a21\u5f0f\u4e0b\uff0c\u9ed8\u8ba4\u4e0d\u901a\u8fc7\naccessible=false;}if(accessPaths.indexOf(route.absolutePath)>-1){accessible=true;}route.unaccessible=!accessible;if(route.children&&route.children.length){modifyRoutesByAccessMenus(route.children,accessPaths,strictMode,route.unaccessible);}});}}//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiODU5NS5qcyIsIm1hcHBpbmdzIjoiOzs7Ozs7QUFBQTtBQU1BO0FBQ0E7QUFDQTtBQVNBO0FBQ0E7QUFDQTtBQUNBO0FBWUE7QUFDQTtBQUNBO0FBT0E7QUFDQTtBQUNBO0FBTUE7QUFDQTtBQVlBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFFQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFLQTtBQUVBO0FBR0E7QUFhQTtBQVVBO0FBQ0E7QUFDQTtBQUNBO0FBZ0JBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUtBO0FBRUEiLCJzb3VyY2VzIjpbIi5zdWkvY29yZS9wbHVnaW4tYWNjZXNzL0FjY2Vzcy5qc3giXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///8595\n")},86642:function(__unused_webpack_module,__unused_webpack___webpack_exports__,__webpack_require__){eval('\n// UNUSED EXPORTS: Access, AccessProvider, NotFoundComponentModule, fallback, fallbackDom, getMFMicroAppModuleName, importMF, importMFApp, importMFComponent, useAccess, useAccessState, withMFModule\n\n// EXTERNAL MODULE: consume shared module (default) react@^17.0.2 (singleton) (fallback: ./node_modules/react/index.js) (eager)\nvar index_js_eager_ = __webpack_require__(96985);\n// EXTERNAL MODULE: ./src/.sui/core/plugin-access/Access.jsx\nvar Access = __webpack_require__(8595);\n;// CONCATENATED MODULE: ./src/.sui/core/plugin-access/runtime.jsx\n\n// EXTERNAL MODULE: ./node_modules/@sui/sui/dist/index.esm.js\nvar index_esm = __webpack_require__(53273);\n// EXTERNAL MODULE: consume shared module (default) @emotion/react@^11.10.5 (singleton) (fallback: ./node_modules/@emotion/react/dist/emotion-react.browser.esm.js)\nvar emotion_react_browser_esm_js_ = __webpack_require__(94216);\n;// CONCATENATED MODULE: ./src/.sui/core/NotFoundComponentModule.jsx\nvar NotFoundComponentModule=function NotFoundComponentModule(_ref){var module=_ref.module,filePath=_ref.filePath,message=_ref.message,_ref$type=_ref.type,type=_ref$type===void 0?"error":_ref$type;if(false){ var typeLabel, bagdeStyle, bgStyle; }else{return null;}};/* harmony default export */ var core_NotFoundComponentModule = ((/* unused pure expression or super */ null && (NotFoundComponentModule)));\n;// CONCATENATED MODULE: ./src/.sui/core/plugin-federated/runtime.js\n// \u8fdc\u7a0bremote container urls\nvar remotesUrls={};/**\n * \u89e3\u6790\u7ec4\u4ef6scope\n * @param {*} name \n * @param {*} prefix \n * @returns \n */function parseModuleName(name,prefix){return(prefix||\'\')+name.replace(/^@/,\'$\').replace(/[@\\/\\\\\\.-]/g,\'_\');}/**\n** \u89e3\u6790\u7ec4\u4ef6\u5305\u540d\n** @param {*} scope\n*/function parsePackageName(scope){return scope?scope.replace(/^\\$/,\'@\').replace(\'_\',\'/\'):"";}/**\n* \u83b7\u53d6 \u8fdc\u7a0b\u5fae\u5e94\u7528\u6a21\u5757\u540d\n**/function getMFMicroAppModuleName(microApp){if(microApp){var name=\'MicroApp\';if(typeof microApp===\'string\'){name=microApp;}else if(typeof microApp===\'object\'){name=microApp.name||name;if(microApp.entry){name=name+\'_Entry_\'+microApp.entry;}}return name;}}/**\n* \u83b7\u53d6\u5bfc\u5165MF\u51fa\u9519\u6e32\u67d3\u903b\u8f91\n**/function getImportMFError(_,inject,errMsg){if(false){}else{inject(errMsg);}}/**\n** \u83b7\u53d6\u8fdc\u7a0b\u5165\u53e3\n**/function getRemoteUrl(scope,packageName,version,filename){var remote_url=scope&&remotesUrls[scope]?remotesUrls[scope]:packageName&&remotesUrls[packageName]?remotesUrls[packageName]:undefined;if(typeof window._sui_webpack_remote_urls===\'object\'){if(packageName&&window._sui_webpack_remote_urls[packageName]){remote_url=window._sui_webpack_remote_urls[packageName];}if(!remote_url&&scope&&window._sui_webpack_remote_urls[scope]){remote_url=window._sui_webpack_remote_urls[scope];}}if(!remote_url&&typeof window._get_sui_webpack_remote_url===\'function\'&&packageName){remote_url=window._get_sui_webpack_remote_url(packageName,null,version,filename);}if(!remote_url&&typeof window._get_sui_webpack_remote_url===\'function\'&&scope&&scope!==packageName){remote_url=window._get_sui_webpack_remote_url(scope,null,version,filename);}return remote_url?remote_url:scope&&remotesUrls[scope]?remotesUrls[scope]:"";}/**\n * \u5bfc\u5165\u8fdc\u7a0bmf\u5bb9\u5668\u6a21\u5757\n * @param {*} modulePath \n * @param {*} packageName \n * @param {*} version \n * @param {*} filename \n * @returns \n */function importMF(modulePath,packageName,version,filename){var index=modulePath.indexOf(\'/\');if(index<0){return new Promise(function(_,inject){getImportMFError(_,inject,"importMF: bad Module Name ".concat(modulePath,", should match pattern \'remoteScope/moduleName"));});}var scope=modulePath.slice(0,index);var module=modulePath.slice(index+1);var remote_url=getRemoteUrl(scope,packageName||parsePackageName(scope),version,filename);if(!remote_url){return new Promise(function(_,inject){getImportMFError(_,inject,"importMF: no remote url for \\"".concat(scope,"\\""));});}return new Promise(function(_,inject){getDynamicModule(remote_url,scope,module).then(_).catch(function(error){console.error(error);getImportMFError(_,inject,"importMF: load remoteModule `".concat(modulePath,"` failed. Please check the remote url ").concat(remote_url));});});}/**\n** \u5bfc\u5165\u8fdc\u7a0bmf \u5fae\u5e94\u7528\n** @param {*} microApp\n**/function importMFApp(scope,microApp){var module=getMFMicroAppModuleName(microApp);return importMF("".concat(scope,"/").concat(module));}/**\n * \u52a0\u8f7d\u8fdc\u7a0b\u7ec4\u4ef6\n * @param {*} componentName \n * @param {*} version \n * @param {*} filename \n * @returns \n */function importMFComponent(componentName,version,filename){var index=componentName.indexOf(\'.\');if(index<0){return new Promise(function(_,inject){getImportMFError(_,inject,"importMFComponent: bad component Name ".concat(componentName,", should match pattern \'remotePackage.componentName"));});}var packageName=componentName.slice(0,index);var scope=parseModuleName(packageName);var component=componentName.slice(index+1);var remote_url=getRemoteUrl(scope,packageName,version,filename);if(!remote_url){return new Promise(function(_,inject){getImportMFError(_,inject,"importMFComponent: no remote url for \\"".concat(packageName,"\\""));});}return new Promise(function(_,inject){getDynamicModule(remote_url,scope,component).then(_).catch(function(error){console.error(error);getImportMFError(_,inject,"importMFComponent: load remoteComponent `".concat(componentName,"` failed. Please check the remote url ").concat(remote_url));});});}/**\n * \u4f9d\u8d56\n * @param mfObjArr \n * @param component \n * @param fallback \n * @returns \n */function withMFModule(mfObjArr,component,fallback){var _mfObjArr=(mfObjArr||[]).map(function(item){return typeof item===\'string\'?importMF(item):item;});return _withMFModule(_mfObjArr,component,fallback);}\n// EXTERNAL MODULE: ./src/components/Loading/index.js\nvar Loading = __webpack_require__(53964);\n;// CONCATENATED MODULE: ./src/.sui/core/runtime.jsx\nvar fallbackDom=(0,emotion_react_browser_esm_js_.jsx)(Loading/* default */.Z,null);var fallback=(0,emotion_react_browser_esm_js_.jsx)(Loading/* default */.Z,null);//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiODY2NDIuanMiLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7Ozs7Ozs7QUNBQTs7QUNBQTtBQUlBO0FBS0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQU1BO0FBQ0E7QUFDQTtBQU1BO0FBQ0E7QUFpQkE7QUFDQTtBQVlBO0FBQ0E7QUF1QkE7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUF5QkE7QUFDQTtBQUNBO0FBT0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBNkJBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTs7OztBQ2pLQSIsInNvdXJjZXMiOlsiLnN1aS9jb3JlL3BsdWdpbi1hY2Nlc3MvcnVudGltZS5qc3giLCIuc3VpL2NvcmUvTm90Rm91bmRDb21wb25lbnRNb2R1bGUuanN4IiwiLnN1aS9jb3JlL3BsdWdpbi1mZWRlcmF0ZWQvcnVudGltZS5qcyIsIi5zdWkvY29yZS9ydW50aW1lLmpzeCJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///86642\n')},84583:function(__unused_webpack_module,__webpack_exports__,__webpack_require__){eval("/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   CQ: function() { return /* binding */ postFail; },\n/* harmony export */   GC: function() { return /* binding */ YB_EZFORM_URL; },\n/* harmony export */   Mw: function() { return /* binding */ postSuccess; },\n/* harmony export */   vp: function() { return /* binding */ YB_EZFORM_COMPLETE_URL; },\n/* harmony export */   wy: function() { return /* binding */ createColumn; }\n/* harmony export */ });\n/* unused harmony exports YB_STATIC_PREFIX, HttpStatusCode, getResponseError, saveSuccess, createSuccess, updateSuccess, deleteSuccess, sendSuccess, SQLOder, SQLOperator, createSQLQuery, createSQLOder */\n/* harmony import */ var _Users_zhouxiaoyue_work_edu_frontend_admin_node_modules_babel_runtime_helpers_esm_objectSpread2_js__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(1413);\n/* harmony import */ var antd_lib_message_style__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(9430);\n/* harmony import */ var antd_lib_message_style__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(antd_lib_message_style__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var antd_lib_message__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(41691);\n/* harmony import */ var _Users_zhouxiaoyue_work_edu_frontend_admin_node_modules_babel_runtime_helpers_esm_slicedToArray_js__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(29439);\nvar YB_EZFORM_URL=window['YB_EZFORM_URL']||'/easyform/creation';var YB_EZFORM_COMPLETE_URL=window['YB_EZFORM_COMPLETE_URL']||'/easyform/submit';/**\n * \u540e\u53f0\u9759\u6001\u8d44\u6e90\u8bbf\u95ee\u524d\u7f00\n */var YB_STATIC_PREFIX=window.APP_CONFIG.server.api+'/static/images';var HttpStatusCode={Continue:100,SwitchingProtocols:101,Processing:102,EarlyHints:103,Ok:200,Created:201,Accepted:202,NonAuthoritativeInformation:203,NoContent:204,ResetContent:205,PartialContent:206,MultiStatus:207,AlreadyReported:208,ImUsed:226,MultipleChoices:300,MovedPermanently:301,Found:302,SeeOther:303,NotModified:304,UseProxy:305,Unused:306,TemporaryRedirect:307,PermanentRedirect:308,BadRequest:400,Unauthorized:401,PaymentRequired:402,Forbidden:403,NotFound:404,MethodNotAllowed:405,NotAcceptable:406,ProxyAuthenticationRequired:407,RequestTimeout:408,Conflict:409,Gone:410,LengthRequired:411,PreconditionFailed:412,PayloadTooLarge:413,UriTooLong:414,UnsupportedMediaType:415,RangeNotSatisfiable:416,ExpectationFailed:417,ImATeapot:418,MisdirectedRequest:421,UnprocessableEntity:422,Locked:423,FailedDependency:424,TooEarly:425,UpgradeRequired:426,PreconditionRequired:428,TooManyRequests:429,RequestHeaderFieldsTooLarge:431,UnavailableForLegalReasons:451,InternalServerError:500,NotImplemented:501,BadGateway:502,ServiceUnavailable:503,GatewayTimeout:504,HttpVersionNotSupported:505,VariantAlsoNegotiates:506,InsufficientStorage:507,LoopDetected:508,NotExtended:510,NetworkAuthenticationRequired:511};Object.entries(HttpStatusCode).forEach(function(_ref){var _ref2=(0,_Users_zhouxiaoyue_work_edu_frontend_admin_node_modules_babel_runtime_helpers_esm_slicedToArray_js__WEBPACK_IMPORTED_MODULE_1__/* [\"default\"] */ .Z)(_ref,2),key=_ref2[0],value=_ref2[1];HttpStatusCode[value]=key;});var getResponseError=function getResponseError(res,defaultMsg){var errMsg=typeof res==='object'?res.msg||res.resultMsg:'',errCode=typeof res==='object'?res.code||res.resultCode:0;if(!res||errCode>=400){var defaultError=errCode?HttpStatusCode[res.code]:undefined;errMsg=errMsg||defaultError||defaultMsg;}if(typeof res==='number'&&HttpStatusCode[res]){errMsg=HttpStatusCode[res];}else if(typeof res==='string'&&res){errMsg=res;}if(/\\{\\\"code\\\"/.test(errMsg)){try{var msg=JSON.parse(errMsg);errMsg=typeof msg==='object'&&msg?msg.msg||msg.message||msg.code&&HttpStatusCode[msg.code]:errMsg;}catch(error){}}return errMsg;};/**\n * \u63d0\u4ea4\u6210\u529f\u5904\u7406\n * @param res\n * @returns\n */function postSuccess(res,successMsg){var errMsg=getResponseError(res,'REQUEST ERROR');errMsg?antd_lib_message__WEBPACK_IMPORTED_MODULE_2__[\"default\"].warning({content:\"\".concat(errMsg)}):antd_lib_message__WEBPACK_IMPORTED_MODULE_2__[\"default\"].success(successMsg||\"\\u64CD\\u4F5C\\u6210\\u529F\");return res;}/**\n * \u4fdd\u5b58\u6210\u529f\u5904\u7406\n * @param res\n * @param successMsg\n * @returns\n */function saveSuccess(res,successMsg){return postSuccess(res,successMsg||'\u4fdd\u5b58\u6210\u529f');}/**\n * \u521b\u5efa\u6210\u529f\n * @param res\n * @param successMsg\n * @returns\n */function createSuccess(res,successMsg){return postSuccess(res,successMsg||'\u521b\u5efa\u6210\u529f');}/**\n * \u66f4\u65b0\u6210\u529f\n * @param res\n * @param successMsg\n * @returns\n */function updateSuccess(res,successMsg){return postSuccess(res,successMsg||'\u66f4\u65b0\u6210\u529f');}/**\n * \u5220\u9664\u6210\u529f\n * @param res\n * @param successMsg\n * @returns\n */function deleteSuccess(res,successMsg){return postSuccess(res,successMsg||'\u5220\u9664\u6210\u529f');}/**\n * \u53d1\u9001\u6210\u529f\n * @param res\n * @param successMsg\n * @returns\n */function sendSuccess(res,successMsg){return postSuccess(res,successMsg||'\u53d1\u9001\u6210\u529f');}/**\n * \u63d0\u4ea4\u5931\u8d25\u5904\u7406\n * @param e\n * @returns\n */function postFail(e,errorMsg){var errMsg;var code=e.code;var errData=e.response;if(typeof code==='number'&&HttpStatusCode[code]){errMsg=HttpStatusCode[code];}if(errData){errMsg=getResponseError(errData,errMsg);}antd_lib_message__WEBPACK_IMPORTED_MODULE_2__[\"default\"].warning({content:errMsg||errorMsg||'REQUEST ERROR'});return this;}/**\n * \u652f\u6301\u7684 SQL \u64cd\u4f5c\u7b26\u7c7b\u578b\n */var SQLOder={desc:'DESC',asc:'ASC',descend:'DESC',ascend:'ASC',DESC:'DESC',ASC:'ASC'};/**\n * \u64cd\u4f5c\u7b26\u5b9a\u4e49\n */var SQLOperator={like:\"$n LIKE '%$s%'\",llike:\"$n LIKE '%$s'\",rlike:\"$n LIKE '$s%'\",eq:\"$n='$s'\",lt:'$n < $d',gt:'$n > $d',in:'$n in $a'};/**\n * \u751f\u6210 in \u64cd\u4f5c\u7b26 \u5bf9\u5e94\u7684\u503c\n * @param obj\n * @returns\n */function _getSQLwhereInString(obj){var arr=Array.isArray(obj)?obj:[obj];var str='[';for(var index=0;index<arr.length;index++){var val=arr[index];if(index&&val!==''){str+=',';str+=typeof val==='number'?val:\"'\".concat(val+'',\"'\");}}return str+']';}/**\n * \u751f\u6210 SQL WHERE \u67e5\u8be2\u8bed\u53e5\n * @description \u9ed8\u8ba4\u662f AND \u64cd\u4f5c\uff0c\u7528\u4e8e\u8868\u5355\u67e5\u8be2\n * @param obj\n * @param operator\n * @returns\n */function createSQLQuery(obj,operator){var arr=[];for(var _key in obj){if(Object.prototype.hasOwnProperty.call(obj,_key)){var value=obj[_key];var op=operator&&operator[_key]&&SQLOperator[operator[_key]]?SQLOperator[operator[_key]]:SQLOperator.eq;arr.push(op.replace('$n',_key).replace(/(\\$s|\\$d)/,value).replace('$a',_getSQLwhereInString(value)));}}return arr.join(' AND ');}/**\n * \u521b\u5efaSQL ORDER\n * @param obj\n * @returns\n */function createSQLOder(obj){var arr=[];for(var _key2 in obj){if(Object.prototype.hasOwnProperty.call(obj,_key2)){var value=obj[_key2];if(value&&SQLOder[value]){arr.push(\"\".concat(_key2,\" \").concat(SQLOder[value]));}}}return arr.join(', ');}/**\n * \u521b\u5efa\u67e5\u8be2\u8868\u683c\u5217\u5e76\u8bbe\u7f6e\u663e\u793a\u4f4d\u7f6e\n * @param obj\n * @param show\n * @param hide\n * @returns\n */function createColumn(obj,show,hide){return (0,_Users_zhouxiaoyue_work_edu_frontend_admin_node_modules_babel_runtime_helpers_esm_objectSpread2_js__WEBPACK_IMPORTED_MODULE_3__/* [\"default\"] */ .Z)((0,_Users_zhouxiaoyue_work_edu_frontend_admin_node_modules_babel_runtime_helpers_esm_objectSpread2_js__WEBPACK_IMPORTED_MODULE_3__/* [\"default\"] */ .Z)({},obj),{},{hideInDescriptions:show&&(hide?show.indexOf('descriptions')>-1:show.indexOf('descriptions')==-1),hideInForm:show&&(hide?show.indexOf('form')>-1:show.indexOf('form')==-1),hideInSetting:show&&(hide?show.indexOf('setting')>-1:show.indexOf('setting')==-1),hideInTable:show&&(hide?show.indexOf('table')>-1:show.indexOf('table')==-1),hideInSearch:show&&(hide?show.indexOf('search')>-1:show.indexOf('search')==-1)});}//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiODQ1ODMuanMiLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7Ozs7OztBQUVBO0FBSUE7QUFDQTtBQWtHQTtBQUNBO0FBQ0E7QUFDQTtBQVlBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFNQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBTUE7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQU1BO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFNQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBS0E7QUFDQTtBQUNBO0FBQ0E7QUFrQkE7QUFDQTtBQWVBO0FBQ0E7QUFZQTtBQUNBO0FBQ0E7QUFDQTtBQWVBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQXFCQTtBQUNBO0FBQ0E7QUFDQTtBQWlCQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0EiLCJzb3VyY2VzIjpbImNvbW1vbnMvc2VydmljZS50cyJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///84583\n")},62694:function(__unused_webpack_module,__webpack_exports__,__webpack_require__){eval("/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Ah: function() { return /* binding */ TASK_ID_KEY; },\n/* harmony export */   MB: function() { return /* binding */ TASK_PROCESS_ACTIONS_OPTIONS; },\n/* harmony export */   Oh: function() { return /* binding */ StateMap; },\n/* harmony export */   he: function() { return /* binding */ getProcessAssignmentName; },\n/* harmony export */   j: function() { return /* binding */ TASK_PROCESS_OPTIONS; },\n/* harmony export */   kH: function() { return /* binding */ assignmentStateEnum; },\n/* harmony export */   m_: function() { return /* binding */ AssignmentStateMap; },\n/* harmony export */   xe: function() { return /* binding */ parseTaskProcessOptions; },\n/* harmony export */   yB: function() { return /* binding */ TASK_PROCESS_ACTIONS; }\n/* harmony export */ });\n/* unused harmony exports UndoAssignmentStateMap, stateEnum, UndoAssignmentStateEnum, getTaskProcessOption, TASK_TYPES_MAP, TASK_TYPES_OPTIONS */\n/* harmony import */ var _Users_zhouxiaoyue_work_edu_frontend_admin_node_modules_babel_runtime_helpers_esm_objectSpread2_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(1413);\nvar TASK_ID_KEY='id';/**\n * \u4efb\u52a1\u72b6\u6001\u5b9a\u4e49\n */var StateMap={prepare:{status:'default',text:'\u672a\u5f00\u59cb'},running:{status:'processing',text:'\u8fdb\u884c\u4e2d'},end:{status:'error',text:'\u5df2\u7ed3\u675f'}};/**\n * \u59d4\u6d3e\u5f85\u529e\u72b6\u6001\u5b9a\u4e49\n */var AssignmentStateMap={unread:{status:'default',text:'\u672a\u8bfb'},read:{status:'processing',text:'\u5df2\u8bfb'},done:{status:'success',text:'\u5df2\u5b8c\u6210'}};var UndoAssignmentStateMap={unread:{status:'default',text:'\u672a\u8bfb'},read:{status:'processing',text:'\u5df2\u8bfb'}};/**\n * \u4efb\u52a1\u72b6\u6001\u9009\u9879\n */var stateEnum=createOptionsMap(StateMap,false);/**\n * \u59d4\u6d3e\u72b6\u6001\u9009\u9879\n */var assignmentStateEnum=createOptionsMap(AssignmentStateMap,false);/**\n * \u672a\u5904\u7406\u7684\u72b6\u6001\n */var UndoAssignmentStateEnum=createOptionsMap(UndoAssignmentStateMap,false);/**\n * \u521b\u5efa\u4efb\u52a1\u72b6\u6001\u4e0b\u62c9\u63a7\u4ef6\u9009\u9879\n * @param mapObj\n * @param allOption\n * @returns\n */function createOptionsMap(mapObj,allOption){var optionsMap=new Map();if(allOption!==false){if(typeof allOption!=='object'){allOption={value:undefined,label:'\u5168\u90e8'};}optionsMap.set(allOption.value||'',allOption.label||'\u5168\u90e8');}for(var key in mapObj){if(Object.prototype.hasOwnProperty.call(mapObj,key)){var state=mapObj[key];optionsMap.set(key,state.text);}}return optionsMap;}/**\n * \u5904\u7406\u4e0b\u6d3e\u6d41\u7a0b\u9009\u9879\n * @param options\n * @returns\n */function parseTaskProcessOptions(options){return options.map(function(option,index){return (0,_Users_zhouxiaoyue_work_edu_frontend_admin_node_modules_babel_runtime_helpers_esm_objectSpread2_js__WEBPACK_IMPORTED_MODULE_0__/* [\"default\"] */ .Z)({label:option.name},option);});}var TASK_PROCESS_OPTIONS=[{name:'\u9662\u7cfb\u7ba1\u7406\u5458',value:'yxgly'},{name:'\u8f85\u5bfc\u5458',value:'fdy'},{name:'\u672c\u79d1\u751f',value:'bks'}];var TASK_PROCESS_ACTIONS={xp:'\u4e0b\u6d3e',cl:'\u5904\u7406'};var TASK_PROCESS_ACTIONS_OPTIONS={xp:{color:'processing'},cl:{color:'success'}};/**\n * \u83b7\u53d6\u4efb\u52a1\u6d41\u7a0b\u4e00\u9879\n * @param value\n * @returns\n */function getTaskProcessOption(value){return TASK_PROCESS_OPTIONS.find(function(opt){return value.indexOf(opt.value)===0;})||{value:value,name:value};}/**\n * \u83b7\u53d6\u4e0b\u6d3e\u8fc7\u7a0b\u540d\u79f0\n * @param processName\n * @param action\n * @returns\n */function getProcessAssignmentName(processName,action){var assignmentOption=TASK_PROCESS_OPTIONS.find(function(opt){return opt.value===processName;});if(assignmentOption){return assignmentOption.name+TASK_PROCESS_ACTIONS[action];}return processName+TASK_PROCESS_ACTIONS[action];}/**\n * \u4efb\u52a1\u7c7b\u578b\n */var TASK_TYPES_MAP={EZForm:'\u8868\u5355\u4efb\u52a1'};/**\n * \u4efb\u52a1\u7c7b\u578b\u9009\u9879\u5217\u8868\n */var TASK_TYPES_OPTIONS=Object.keys(TASK_TYPES_MAP).map(function(type){return{label:TASK_TYPES_MAP[type],value:type};});//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiNjI2OTQuanMiLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7Ozs7OztBQWNBO0FBRUE7QUFDQTtBQWlCQTtBQUNBO0FBNEJBO0FBQ0E7QUFJQTtBQUNBO0FBSUE7QUFDQTtBQUlBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUF5QkE7QUFDQTtBQUNBO0FBQ0E7QUF3Q0E7QUFDQTtBQUNBO0FBQ0E7QUFNQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBV0E7QUFDQTtBQU1BO0FBQ0EiLCJzb3VyY2VzIjpbImNvbW1vbnMvdGFzay50cyJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///62694\n")},2671:function(__unused_webpack_module,__webpack_exports__,__webpack_require__){eval("/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   U: function() { return /* binding */ parseClassLeaderSet; },\n/* harmony export */   m: function() { return /* binding */ parsePositionSet; }\n/* harmony export */ });\n/* harmony import */ var _Users_zhouxiaoyue_work_edu_frontend_admin_node_modules_babel_runtime_helpers_esm_objectSpread2_js__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(1413);\nfunction parsePositionSet(nodes,disabledKeys,options){var _ref=options||{},parent=_ref.parent,top=_ref.top,childrenAttrs=_ref.childrenAttrs,checkable=_ref.checkable,isLeaf=_ref.isLeaf;disabledKeys=disabledKeys||[];var arr=(nodes||[]).map(function(node){var disabled=disabledKeys&&disabledKeys.indexOf(node.code)>-1?true:false;var _node={code:node.code,name:node.name,isLeaf:typeof isLeaf==='boolean'?isLeaf:true,disabled:disabled,checked:disabled,parentNode:parent,topNode:top,checkable:checkable!==false,_ref:node};if(node.positions&&(!childrenAttrs||childrenAttrs.indexOf('positions')>-1)){_node.children=parsePositionSet(node.positions,disabledKeys,{childrenAttrs:childrenAttrs,checkable:checkable,isLeaf:isLeaf,parent:_node,top:_node.topNode||_node.parentNode||_node});}if(node.persons&&(!childrenAttrs||childrenAttrs.indexOf('persons')>-1)){_node.children=parsePositionSet(node.persons,disabledKeys,{childrenAttrs:childrenAttrs,checkable:checkable,isLeaf:isLeaf,parent:_node,top:_node.topNode||_node.parentNode||_node});}if(_node.children&&_node.children.length){_node.isLeaf=false;_node.checkable=false;}return _node;});return arr;}/**\n   * \u83b7\u53d6\u9662\u7cfb\u4e0b\u7684\u8f85\u5bfc\u5458\u6216\u73ed\u4e3b\u4efb\n   * @param nodes \n   * @param res \n   * @returns \n   */function parseClassLeaderSet(nodes,disabledKeys,options){var _ref2=options||{},types=_ref2.types,res=_ref2.res,topNode=_ref2.topNode,checkable=_ref2.checkable,childrenAttrs=_ref2.childrenAttrs,isLeaf=_ref2.isLeaf;types=types||[2];res=res||[];disabledKeys=disabledKeys||[];(nodes||[]).map(function(node){if((!types||!types.length||types.indexOf(node.type)>-1)&&res.findIndex(function(item){return item.id==node.id;})==-1){var disabled=disabledKeys&&disabledKeys.indexOf(node.code)>-1?true:false;res.push((0,_Users_zhouxiaoyue_work_edu_frontend_admin_node_modules_babel_runtime_helpers_esm_objectSpread2_js__WEBPACK_IMPORTED_MODULE_0__/* [\"default\"] */ .Z)((0,_Users_zhouxiaoyue_work_edu_frontend_admin_node_modules_babel_runtime_helpers_esm_objectSpread2_js__WEBPACK_IMPORTED_MODULE_0__/* [\"default\"] */ .Z)({},node),{},{_ref:node,isLeaf:isLeaf!==false,disabled:disabled,checked:disabled,checkable:checkable!==false,topNode:topNode}));}if(node.leader&&(!childrenAttrs||childrenAttrs.indexOf(\"leader\")>-1)){parseClassLeaderSet(node.leader,disabledKeys,{types:types,res:res,topNode:topNode,childrenAttrs:childrenAttrs,checkable:checkable});}});return res;}//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiMjY3MS5qcyIsIm1hcHBpbmdzIjoiOzs7OztBQWlCQTtBQWtDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBIiwic291cmNlcyI6WyJjb21wb25lbnRzL0Fzc2lnbm1lbnRTZWxlY3Rvci91dGlscy50cyJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///2671\n")},45504:function(__unused_webpack_module,__webpack_exports__,__webpack_require__){eval('// ESM COMPAT FLAG\n__webpack_require__.r(__webpack_exports__);\n\n// EXPORTS\n__webpack_require__.d(__webpack_exports__, {\n  "default": function() { return /* binding */ EasyformTask; },\n  processOptions: function() { return /* binding */ processOptions; },\n  updateTaskData: function() { return /* binding */ updateTaskData; }\n});\n\n// EXTERNAL MODULE: ./node_modules/antd/lib/drawer/style/index.js\nvar style = __webpack_require__(95542);\n// EXTERNAL MODULE: ./node_modules/antd/lib/drawer/index.js\nvar drawer = __webpack_require__(36358);\n// EXTERNAL MODULE: ./node_modules/antd/lib/space/style/index.js\nvar space_style = __webpack_require__(40898);\n// EXTERNAL MODULE: ./node_modules/antd/lib/space/index.js\nvar space = __webpack_require__(68747);\n// EXTERNAL MODULE: ./node_modules/antd/lib/button/style/index.js\nvar button_style = __webpack_require__(42844);\n// EXTERNAL MODULE: ./node_modules/antd/lib/button/index.js\nvar lib_button = __webpack_require__(59235);\n// EXTERNAL MODULE: ./node_modules/antd/lib/notification/style/index.js\nvar notification_style = __webpack_require__(11474);\n// EXTERNAL MODULE: ./node_modules/antd/lib/notification/index.js\nvar notification = __webpack_require__(87073);\n// EXTERNAL MODULE: ./node_modules/@babel/runtime/helpers/esm/regeneratorRuntime.js\nvar regeneratorRuntime = __webpack_require__(74165);\n// EXTERNAL MODULE: ./node_modules/@babel/runtime/helpers/esm/asyncToGenerator.js\nvar asyncToGenerator = __webpack_require__(15861);\n// EXTERNAL MODULE: ./node_modules/antd/lib/message/style/index.js\nvar message_style = __webpack_require__(9430);\n// EXTERNAL MODULE: ./node_modules/antd/lib/message/index.js\nvar message = __webpack_require__(41691);\n// EXTERNAL MODULE: ./node_modules/@babel/runtime/helpers/esm/objectWithoutProperties.js\nvar objectWithoutProperties = __webpack_require__(45987);\n// EXTERNAL MODULE: ./node_modules/@babel/runtime/helpers/esm/slicedToArray.js + 1 modules\nvar slicedToArray = __webpack_require__(29439);\n// EXTERNAL MODULE: ./node_modules/@babel/runtime/helpers/esm/objectSpread2.js\nvar objectSpread2 = __webpack_require__(1413);\n// EXTERNAL MODULE: ./src/services/task.ts\nvar services_task = __webpack_require__(31857);\n// EXTERNAL MODULE: ./node_modules/@sui/runtime/dist/index.esm.js + 2 modules\nvar index_esm = __webpack_require__(74698);\n;// CONCATENATED MODULE: ./src/services/ezform.ts\nvar _excluded=(/* unused pure expression or super */ null && (["id"]));var EZFORM_API_PREFIX=window.APP_CONFIG.server.easyform;var suiRequest=(0,index_esm/* createRequest */.hG)({prefix:EZFORM_API_PREFIX});var request=function request(options){return new Promise(function(resolve,reject){suiRequest(options).then(function(res){if(res.code===\'SUCCESS\'){resolve(res.result);return;}message["default"].error({content:res.msg});reject(res.msg);}).catch(function(err){reject(err);});});};/**\n * \u5e94\u7528\u63a5\u53e3\u5b9a\u4e49\n */var API={activityList:\'/activity/page\',activityFormList:\'/activity/form/page\',getActivityDefine:\'/activity/define\',deleteActivity:\'/activity/del\',toggleActivity:\'/activity/toggleEnable\',deleteInstanceForm:\'/activity/form/del\',// \u6a21\u7248\ntemplateList:\'/template/page\',template:\'/template\',toggleTemplate:\'/template/toggleEnable\',//\u6211\u53c2\u4e0e\u7684\u6d3b\u52a8\njoinedActivity:\'/activity/participate/page\',/**\n   * \u68c0\u67e5\u4efb\u52a1\u662f\u5426\u8d8a\u6743\n   */checkTask:\'/activity/task/checkExceedAuth\',approveTask:\'/activity/task/approve\'};/**\n * \u83b7\u53d6\u6d3b\u52a8\u5b9a\u4e49\n * @param params\n * @returns\n */function getActivityDefine(activityId){// return request.get(`${API.getActivityDefine}/${activityId}`)\nreturn request({url:"".concat(API.getActivityDefine,"/").concat(activityId),method:\'GET\'});}/**\n * \u5220\u9664\u6d3b\u52a8\n */function deleteActivity(activityIds){return request({url:API.deleteActivity,method:\'POST\',data:{ids:activityIds}});}/**\n * \u542f\u7528/\u5173\u95ed\u6d3b\u52a8\n */function toggleActivity(activityId){return request({url:API.toggleActivity+\'/\'+activityId,method:\'POST\'});}/**\n * \u5bfc\u51fa\u6d3b\u52a8\u5b9e\u4f8b\u8868\u5355excel\n */function exportExcelForInstanceForm(instanceId,params){var activityName=arguments.length>2&&arguments[2]!==undefined?arguments[2]:\'\';suiRequest({url:API.activityFormList,method:\'GET\',responseType:\'blob\',headers:{isExport:\'true\'},params:_objectSpread(_objectSpread({},params),{},{page:1,pageSize:999999999,activityInstId:instanceId})}).then(function(res){var blob=new Blob([res]);var url=window.URL.createObjectURL(blob);var link=document.createElement(\'a\');link.href=url;link.setAttribute(\'download\',"\\u4E07\\u80FD\\u8868\\u5355-".concat(activityName).concat(new Date().format(\'MMDDHHmm\'),".xls"));document.body.appendChild(link);link.click();});}/**\n * \u5220\u9664\u5b9e\u4f8b\u8868\u5355\u6570\u636e\n */function deleteInstanceForm(instanceId,formIds){return request({url:API.deleteInstanceForm+\'/\'+instanceId,method:\'POST\',data:{formIds:formIds}});}/**\n * \u6a21\u7248\n */function saveTemplate(parameter){var _ref=parameter||{},id=_ref.id,params=_objectWithoutProperties(_ref,_excluded);return request({url:"".concat(API.template).concat(id?\'/\'+id:\'\'),method:\'POST\',data:params});}function deleteTemplate(id){return request({url:"".concat(API.template,"/del/").concat(id),method:\'POST\'});}function toggleTemplate(id){return request({url:"".concat(API.toggleTemplate,"/").concat(id),method:\'POST\'});}/**\n * \u68c0\u6d4b\u4efb\u52a1\u662f\u5426\u8d8a\u6743\n * @param params\n * @returns\n */function checkTask(params){return request({url:"".concat(API.checkTask),method:\'POST\',data:params});}/**\n * \u5ba1\u6279\u4efb\u52a1\n * @param params\n * @returns\n */function approveTask(taskId,assignmentId,data){return request({url:"".concat(API.approveTask,"/").concat(taskId,"/").concat(assignmentId),method:\'POST\',data:data});}\n// EXTERNAL MODULE: ./src/commons/service.ts\nvar service = __webpack_require__(84583);\n// EXTERNAL MODULE: ./node_modules/@emotion/css/dist/emotion-css.esm.js + 1 modules\nvar emotion_css_esm = __webpack_require__(57847);\n// EXTERNAL MODULE: ./node_modules/@sui/pro-components/es/index.js + 241 modules\nvar es = __webpack_require__(71466);\n// EXTERNAL MODULE: ./node_modules/@sui/pro-layout/es/index.js + 71 modules\nvar pro_layout_es = __webpack_require__(30555);\n// EXTERNAL MODULE: consume shared module (default) react@^17.0.2 (singleton) (fallback: ./node_modules/react/index.js) (eager)\nvar index_js_eager_ = __webpack_require__(96985);\nvar index_js_eager_default = /*#__PURE__*/__webpack_require__.n(index_js_eager_);\n// EXTERNAL MODULE: ./node_modules/antd/lib/alert/style/index.js\nvar alert_style = __webpack_require__(43604);\n// EXTERNAL MODULE: ./node_modules/antd/lib/alert/index.js\nvar lib_alert = __webpack_require__(19713);\n// EXTERNAL MODULE: ./node_modules/antd/lib/switch/style/index.js\nvar switch_style = __webpack_require__(95618);\n// EXTERNAL MODULE: ./node_modules/antd/lib/switch/index.js\nvar lib_switch = __webpack_require__(94233);\n// EXTERNAL MODULE: ./node_modules/antd/lib/timeline/style/index.js\nvar timeline_style = __webpack_require__(15877);\n// EXTERNAL MODULE: ./node_modules/antd/lib/timeline/index.js\nvar timeline = __webpack_require__(91430);\n// EXTERNAL MODULE: ./node_modules/@babel/runtime/helpers/esm/extends.js\nvar esm_extends = __webpack_require__(87462);\n// EXTERNAL MODULE: ./node_modules/antd/lib/select/style/index.js\nvar select_style = __webpack_require__(98352);\n// EXTERNAL MODULE: ./node_modules/antd/lib/select/index.js\nvar lib_select = __webpack_require__(26469);\n// EXTERNAL MODULE: ./node_modules/antd/lib/tag/style/index.js\nvar tag_style = __webpack_require__(59801);\n// EXTERNAL MODULE: ./node_modules/antd/lib/tag/index.js\nvar tag = __webpack_require__(73757);\n// EXTERNAL MODULE: ./node_modules/@babel/runtime/helpers/esm/defineProperty.js\nvar defineProperty = __webpack_require__(4942);\n// EXTERNAL MODULE: ./src/interfaces/task.ts\nvar task = __webpack_require__(64606);\n// EXTERNAL MODULE: ./src/commons/task.ts\nvar commons_task = __webpack_require__(62694);\n// EXTERNAL MODULE: ./node_modules/@ant-design/icons/es/icons/CheckCircleFilled.js\nvar CheckCircleFilled = __webpack_require__(88835);\n// EXTERNAL MODULE: ./node_modules/classnames/index.js\nvar classnames = __webpack_require__(81694);\nvar classnames_default = /*#__PURE__*/__webpack_require__.n(classnames);\n// EXTERNAL MODULE: ./node_modules/antd/lib/dropdown/style/index.js\nvar dropdown_style = __webpack_require__(84802);\n// EXTERNAL MODULE: ./node_modules/antd/lib/dropdown/index.js\nvar dropdown = __webpack_require__(14627);\n// EXTERNAL MODULE: ./node_modules/antd/lib/typography/style/index.js\nvar typography_style = __webpack_require__(19049);\n// EXTERNAL MODULE: ./node_modules/antd/lib/typography/index.js\nvar typography = __webpack_require__(36153);\n;// CONCATENATED MODULE: ./src/services/cuc2.ts\nvar CUC_API_PREFIX=\'/easyform-api/cuc2\';var cuc2_request=(0,index_esm/* createRequest */.hG)({prefix:CUC_API_PREFIX});var API_CUC={/**\n   * \u83b7\u53d6CUC\u7684\u4eba\u5458\u6570\u636e\n   */getCucUserInfo:\'/user/:id\',/**\n   * \u83b7\u53d6\u4eba\u5458\u7ba1\u7406\u7684\u73ed\u7ea7\n   */getLeaderClasses:\'/classleader/:id/classes\',/**\n   * \u83b7\u53d6\u5b66\u9662\u4e0b\u7684\u8f85\u5bfc\u5458\n   */getAcademyClassLeaders:\'/academy/:id/classleader\',/**\n   * \u83b7\u53d6\u5b66\u5de5\u5c97\u4f4d\n   */getPositionsetXgbzr:\'/positionset/poss_xgbzr/person\',/**\n   * \u83b7\u53d6\u73ed\u7ea7\u4e0b\u7684\u5b66\u751f\n   */getClassStudents:\'/class/:id/students\',/**\n   * \u83b7\u53d6\u4e3b\u4f53\u4e0b\u7684\u6210\u5458\n   */getSubjectMembers:\'/subject/members\',/**\n   * \u83b7\u53d6\u8eab\u4efd\u5217\u8868\n   */getIdentityList:\'/identity/list\'};/**\n * \u83b7\u53d6\u4eba\u5458\u4fe1\u606f\n * @param params\n * @returns\n */function getCucUserInfo(params){var req=cuc2_request(API_CUC.getCucUserInfo,params);req.catch(postFail);return req;}/**\n * \u83b7\u53d6\u8f85\u5bfc\u5458\u7ba1\u7406\u7684\u73ed\u7ea7\n * @param params\n * @returns\n */function getLeaderClasses(params){var req=cuc2_request(API_CUC.getLeaderClasses,params);req.catch(postFail);return req;}/**\n * \u83b7\u53d6\u5b66\u5de5\u5c97\u4f4d\n * @param params\n * @returns\n */function getPositionsetXgbzr(params){var req=cuc2_request(API_CUC.getPositionsetXgbzr,params);req.catch(postFail);return req;}/**\n * \u83b7\u53d6\u5b66\u9662\u4e0b\u7684\u8f85\u5bfc\u5458\n * @param params\n * @returns\n */function getAcademyClassLeaders(params){var req=cuc2_request(API_CUC.getAcademyClassLeaders,params);req.catch(postFail);return req;}/**\n * \u67e5\u8be2\u73ed\u7ea7\u4e0b\u7684\u5b66\u751f\n * @param params\n * @returns\n */function getClassStudents(params){var req=cuc2_request(API_CUC.getClassStudents,params);req.catch(postFail);return req;}/**\n * \u67e5\u8be2\u4e3b\u4f53\u4e0b\u7684\u6210\u5458\n * @param params\n */function getSubjectMembers(params){return cuc2_request(API_CUC.getSubjectMembers,params);}/**\n * @name \u83b7\u53d6\u8eab\u4efd\u5217\u8868\n * @param params\n * @returns\n */function getIdentityList(params){return cuc2_request(API_CUC.getIdentityList,params);}\n// EXTERNAL MODULE: ./node_modules/@sui/rc-personnel/es/index.js + 18 modules\nvar rc_personnel_es = __webpack_require__(10900);\n// EXTERNAL MODULE: ./src/components/AssignmentSelector/utils.ts\nvar utils = __webpack_require__(2671);\n// EXTERNAL MODULE: ./node_modules/@ant-design/icons/es/icons/TeamOutlined.js\nvar TeamOutlined = __webpack_require__(5445);\n// EXTERNAL MODULE: consume shared module (default) @emotion/react@^11.10.5 (singleton) (fallback: ./node_modules/@emotion/react/dist/emotion-react.browser.esm.js)\nvar emotion_react_browser_esm_js_ = __webpack_require__(94216);\n;// CONCATENATED MODULE: ./src/components/EasyformTask/components/AssignmentRangePicker.tsx\nvar AssignmentRangePicker_excluded=["type","roleType","title","disabledKeys","disabled","value","style","user","status","onChange","children","manageClasses","manageOrgs","multiple","labelInValue"];function AssignmentRangePicker(props){var type=props.type,roleType=props.roleType,title=props.title,disabledKeys=props.disabledKeys,disabled=props.disabled,value=props.value,style=props.style,user=props.user,status=props.status,onChange=props.onChange,children=props.children,_props$manageClasses=props.manageClasses,manageClasses=_props$manageClasses===void 0?[]:_props$manageClasses,_props$manageOrgs=props.manageOrgs,manageOrgs=_props$manageOrgs===void 0?[]:_props$manageOrgs,multiple=props.multiple,labelInValue=props.labelInValue,others=(0,objectWithoutProperties/* default */.Z)(props,AssignmentRangePicker_excluded);var _useState=(0,index_js_eager_.useState)([]),_useState2=(0,slicedToArray/* default */.Z)(_useState,2),options=_useState2[0],setOptions=_useState2[1];//   const { context = {} } = useContextState();\n//   const user = context.user || {};\nvar userInfo=user||{};var browser=(0,index_esm/* useBrowserClient */.hj)();(0,index_js_eager_.useEffect)(function(){return function(){typeof onChange==="function"&&onChange(undefined,undefined);};},[]);var changeHandler=function changeHandler(values,options){setOptions(options?[].concat(options).map(function(item){var parentNode=item.parentNode||(item.dataRef&&item.dataRef.parentOrg?item.dataRef.parentOrg:undefined);return{value:item.code,title:parentNode?"".concat(parentNode.name,"(").concat(item.name,")"):item.name,label:parentNode?(0,emotion_react_browser_esm_js_.jsx)(index_js_eager_.Fragment,null,parentNode.name," ",(0,emotion_react_browser_esm_js_.jsx)(typography/* default */.Z.Text,{type:"secondary"},"(",item.name,")")):item.name};}):[]);typeof onChange==="function"&&onChange(values,options);};var pickerProps=(0,index_js_eager_.useMemo)(function(){var _pickerProps={};if(type==="academy_yxgly"&&roleType==="SUPER_ADMIN"){_pickerProps={api:CUC_API_PREFIX+API_CUC.getPositionsetXgbzr,title:"\u6307\u5b9a\u9662\u7cfb",successAjax:function successAjax(res){var _res$result;res.data=(0,utils/* parsePositionSet */.m)(((_res$result=res.result)===null||_res$result===void 0?void 0:_res$result.data)||[],disabledKeys,{childrenAttrs:"positions"});},treeNodeTitleRender:function treeNodeTitleRender(node,title){var menus=[];if(node.isLeaf&&node._ref&&node._ref.persons){menus=node._ref.persons.map(function(person){return{label:person.name,key:person.code};});}return (0,emotion_react_browser_esm_js_.jsx)(space["default"],null,title,menus&&menus.length?(0,emotion_react_browser_esm_js_.jsx)(dropdown/* default */.Z,{menu:{items:menus}},(0,emotion_react_browser_esm_js_.jsx)(TeamOutlined/* default */.Z,null)):null);},targetNodeTitleRender:function targetNodeTitleRender(item,defaultTitle,index){return item.parent?(0,emotion_react_browser_esm_js_.jsx)(index_js_eager_.Fragment,null,item.parent.name," ",(0,emotion_react_browser_esm_js_.jsx)(typography/* default */.Z.Text,{type:"secondary"},"(",defaultTitle,")")):defaultTitle;},dataAttr:"data",modalWidth:600,searchOrg:false,search:false};}if(type=="academy_fdy"&&roleType=="SUPER_ADMIN"){var api=CUC_API_PREFIX+API_CUC.getAcademyClassLeaders;_pickerProps={api:CUC_API_PREFIX+API_CUC.getPositionsetXgbzr,title:"\u6307\u5b9a\u8f85\u5bfc\u5458",successAjax:function successAjax(res){var _res$result2;res.data=(0,utils/* parsePositionSet */.m)(((_res$result2=res.result)===null||_res$result2===void 0?void 0:_res$result2.data)||[],disabledKeys,{childrenAttrs:[],checkable:false});},successUserAjax:function successUserAjax(res){var _res$result3;res.data=(0,utils/* parseClassLeaderSet */.U)(((_res$result3=res.result)===null||_res$result3===void 0?void 0:_res$result3.data)||[],disabledKeys);},extraAjaxSettings:{data:null},extraUserAjaxSettings:{data:null},user_api:api,userDataAttr:"data",getUserApiUrl:function getUserApiUrl(defaultUrl,params,isSearch,keyword){if(!params.code){return defaultUrl;}return index_esm/* utils */.P6.add_url_query(index_esm/* utils */.P6.paramsUrlPath(api,{id:params.code}),{type:2});},nodeKeyAttr:"code",userKeyAttr:"code",userTitleAttr:"name",nodeTitleAttr:"name",dataAttr:"data",searchOrg:false,search:false};}if(type==="academy_fdy"&&roleType==="DEPART_ADMIN"){var _api=CUC_API_PREFIX+API_CUC.getAcademyClassLeaders;_pickerProps={api:_api,title:"\u6307\u5b9a\u672c\u9662\u7cfb\u7684\u8f85\u5bfc\u5458",initTreeData:(manageOrgs||[]).map(function(c){return (0,objectSpread2/* default */.Z)((0,objectSpread2/* default */.Z)({},c),{},{isLeaf:false,checkable:false});}),extraAjaxSettings:{data:null,params:{type:2}},successAjax:function successAjax(res){var _res$result4;res.data=(0,utils/* parseClassLeaderSet */.U)(((_res$result4=res.result)===null||_res$result4===void 0?void 0:_res$result4.data)||[],disabledKeys);},defaultExpandAll:true,getApiUrl:function getApiUrl(defaultUrl,params,isSearch,keyword){if(!params.code){return defaultUrl;}return index_esm/* utils */.P6.paramsUrlPath(_api,{id:params.code});},nodeKeyAttr:"code",userKeyAttr:"code",userTitleAttr:"name",nodeTitleAttr:"name",dataAttr:"data",modalWidth:600,searchOrg:false,search:false};}if((type==="academy_class"||type==="academy_student")&&roleType==="DEPART_ADMIN"){var _api2=CUC_API_PREFIX+API_CUC.getAcademyClassLeaders;var userApi=CUC_API_PREFIX+API_CUC.getClassStudents;_pickerProps={api:_api2,title:type=="academy_class"?"\u6307\u5b9a\u672c\u9662\u7cfb\u73ed\u7ea7":"\u6307\u5b9a\u672c\u9662\u7cfb\u5b66\u751f",initTreeData:(manageOrgs||[]).map(function(c){return (0,objectSpread2/* default */.Z)((0,objectSpread2/* default */.Z)({},c),{},{isLeaf:false,checkable:false});}),extraAjaxSettings:{data:null,params:{type:2}},successAjax:function successAjax(res){var _res$result5;res.data=(0,utils/* parseClassLeaderSet */.U)(((_res$result5=res.result)===null||_res$result5===void 0?void 0:_res$result5.data)||[],disabledKeys,{types:[],childrenAttrs:[],checkable:type==="academy_class"}).sort(function(a,b){return a.name<b.name?-1:0;});},getApiUrl:function getApiUrl(defaultUrl,params,isSearch,keyword){if(!params.code){return defaultUrl;}return index_esm/* utils */.P6.paramsUrlPath(_api2,{id:params.code});},nodeKeyAttr:"code",userKeyAttr:"code",userTitleAttr:"name",nodeTitleAttr:"name",dataAttr:"data",modalWidth:600,searchOrg:false,search:false};if(type==="academy_student"){Object.assign(_pickerProps,{extraUserAjaxSettings:{data:null},successUserAjax:function successUserAjax(res){var _res$result6;res.data=(((_res$result6=res.result)===null||_res$result6===void 0?void 0:_res$result6.data)||[]).map(function(d){return (0,objectSpread2/* default */.Z)((0,objectSpread2/* default */.Z)({},d),{},{isLeaf:true});});},user_api:userApi,getUserApiUrl:function getUserApiUrl(defaultUrl,params,isSearch,keyword){if(!params.code){return defaultUrl;}return index_esm/* utils */.P6.paramsUrlPath(userApi,{id:params.code});},modalWidth:undefined});}}if(type==="academy"||type==="class"){var _userApi=CUC_API_PREFIX+API_CUC.getAcademyClassLeaders;_pickerProps={api:CUC_API_PREFIX+API_CUC.getPositionsetXgbzr,title:type==="academy"?"\u6307\u5b9a\u9662\u7cfb":"\u6307\u5b9a\u73ed\u7ea7",successAjax:function successAjax(res){var _res$result7;res.data=(0,utils/* parsePositionSet */.m)(((_res$result7=res.result)===null||_res$result7===void 0?void 0:_res$result7.data)||[],disabledKeys,{childrenAttrs:[]});},nodeKeyAttr:"code",userKeyAttr:"code",userTitleAttr:"name",nodeTitleAttr:"name",dataAttr:"data",modalWidth:600,searchOrg:false,search:false};if(type==="class"){Object.assign(_pickerProps,{user_api:_userApi,successUserAjax:function successUserAjax(res){var _res$result8;res.data=(0,utils/* parseClassLeaderSet */.U)(((_res$result8=res.result)===null||_res$result8===void 0?void 0:_res$result8.data)||[],disabledKeys,{types:[],childrenAttrs:[],checkable:type==="class"}).sort(function(a,b){return a.name<b.name?-1:0;});},extraUserAjaxSettings:{data:null,params:{type:2}},getUserApiUrl:function getUserApiUrl(defaultUrl,params,isSearch,keyword){if(!params.code){return defaultUrl;}return index_esm/* utils */.P6.paramsUrlPath(_userApi,{id:params.code});},modalWidth:undefined});}}if(type==="student"&&roleType==="SUPER_ADMIN"){var classesApi=CUC_API_PREFIX+API_CUC.getAcademyClassLeaders;var _userApi2=CUC_API_PREFIX+API_CUC.getClassStudents;_pickerProps={api:CUC_API_PREFIX+API_CUC.getPositionsetXgbzr,title:"\u6307\u5b9a\u5b66\u751f",successAjax:function successAjax(res,node){var _res$result9,_res$result10;res.data=!node?(0,utils/* parsePositionSet */.m)(((_res$result9=res.result)===null||_res$result9===void 0?void 0:_res$result9.data)||[],disabledKeys,{childrenAttrs:[],checkable:false,isLeaf:false}):(0,utils/* parseClassLeaderSet */.U)(((_res$result10=res.result)===null||_res$result10===void 0?void 0:_res$result10.data)||[],disabledKeys,{types:[],childrenAttrs:[],checkable:false}).sort(function(a,b){return a.name<b.name?-1:0;});},extraAjaxSettings:{data:null},getApiUrl:function getApiUrl(defaultUrl,params,isSearch,keyword){if(!params.code){return defaultUrl;}return index_esm/* utils */.P6.add_url_query(index_esm/* utils */.P6.paramsUrlPath(classesApi,{id:params.code}),{type:2});},user_api:_userApi2,successUserAjax:function successUserAjax(res){var _res$result11;res.data=(((_res$result11=res.result)===null||_res$result11===void 0?void 0:_res$result11.data)||[]).map(function(d){var disabled=disabledKeys&&disabledKeys.indexOf(d.code)>-1?true:false;return (0,objectSpread2/* default */.Z)((0,objectSpread2/* default */.Z)({},d),{},{isLeaf:true,disabled:disabled});});},getUserApiUrl:function getUserApiUrl(defaultUrl,params){if(!params.code){return defaultUrl;}return index_esm/* utils */.P6.paramsUrlPath(_userApi2,{id:params.code});},extraUserAjaxSettings:{data:null},nodeKeyAttr:"code",userKeyAttr:"code",userTitleAttr:"name",nodeTitleAttr:"name",dataAttr:"data",searchOrg:false,search:false};}if(type=="student"&&roleType=="ASSISTANT"){var _api3=CUC_API_PREFIX+API_CUC.getClassStudents;_pickerProps={api:_api3,title:"\u6307\u5b9a\u5b66\u751f",initTreeData:(manageClasses||[]).map(function(c){return (0,objectSpread2/* default */.Z)((0,objectSpread2/* default */.Z)({},c),{},{isLeaf:false,checkable:false});}),extraAjaxSettings:{data:null},successAjax:function successAjax(res){var _res$result12;res.data=(((_res$result12=res.result)===null||_res$result12===void 0?void 0:_res$result12.data)||[]).map(function(d){var disabled=disabledKeys&&disabledKeys.indexOf(d.code)>-1?true:false;return (0,objectSpread2/* default */.Z)((0,objectSpread2/* default */.Z)({},d),{},{isLeaf:true,disabled:disabled});});},getApiUrl:function getApiUrl(defaultUrl,params,isSearch,keyword){if(!params.code){return defaultUrl;}return index_esm/* utils */.P6.paramsUrlPath(_api3,{id:params.code});},nodeKeyAttr:"code",userKeyAttr:"code",userTitleAttr:"name",nodeTitleAttr:"name",dataAttr:"data",modalWidth:600,searchOrg:false,search:false};}return _pickerProps;},[type,roleType,disabledKeys]);var _multiple=typeof multiple==="boolean"?multiple:pickerProps.multiple;return (0,emotion_react_browser_esm_js_.jsx)(rc_personnel_es/* SuiPersonnelPicker */.A,(0,esm_extends/* default */.Z)({},others,{checkedKeys:disabledKeys,multiple:_multiple===true,value:value,disabled:disabled},pickerProps,{key:type,size:browser.isMobile?"mini":undefined,onChange:changeHandler,title:title||pickerProps.title,labelInValue:labelInValue!==false}),children&&typeof children!=="function"?children:function(_,state){var _state$value;return typeof children==="function"?children(_,state):(0,emotion_react_browser_esm_js_.jsx)(lib_select/* default */.Z,{disabled:disabled,status:status,style:Object.assign({width:"100%"},style),placeholder:pickerProps.placeholder||"\u9009\u62e9\u6307\u5b9a\u5bf9\u8c61",onClick:state.open,onClear:state.clear,value:(_state$value=state.value)===null||_state$value===void 0?void 0:_state$value.value,mode:pickerProps.multiple?"multiple":undefined,options:options,open:false,dropdownRender:null});});}\n;// CONCATENATED MODULE: ./src/components/EasyformTask/components/AssignmentTargetSelect.tsx\nvar AssignmentTargetSelect_excluded=["options","emptyText","hiddenEmptyOptionsAlert","onChange"];function AssignmentTargetSelect(props){var options=props.options,emptyText=props.emptyText,hiddenEmptyOptionsAlert=props.hiddenEmptyOptionsAlert,onChange=props.onChange,others=(0,objectWithoutProperties/* default */.Z)(props,AssignmentTargetSelect_excluded);var opts=(0,index_js_eager_.useMemo)(function(){return(options||[]).map(function(item){return{label:item.label||item.name,value:item.value||item.code};});},[options]);var changeHandler=(0,index_js_eager_.useCallback)(function(v,opt){typeof onChange==="function"&&onChange(v,opt);},[onChange]);(0,index_js_eager_.useEffect)(function(){return function(){typeof onChange==="function"&&onChange(undefined,undefined);};},[]);if(!opts.length){return!hiddenEmptyOptionsAlert?(0,emotion_react_browser_esm_js_.jsx)(lib_alert["default"],{message:emptyText||"\u6ca1\u6709\u53ef\u9009\u7684\u5bf9\u8c61",showIcon:true}):(0,emotion_react_browser_esm_js_.jsx)("div",null);}return (0,emotion_react_browser_esm_js_.jsx)(lib_select/* default */.Z,(0,esm_extends/* default */.Z)({},others,{options:opts,onChange:changeHandler,labelInValue:true}));}\n;// CONCATENATED MODULE: ./src/components/EasyformTask/components/index.tsx\nfunction _EMOTION_STRINGIFIED_CSS_ERROR__(){return"You have tried to stringify object returned from `css` function. It isn\'t supposed to be used directly (e.g. as value of the `className` prop), but rather handed to emotion so it can handle it (e.g. as value of `css` prop).";}var CreatorRole=/*#__PURE__*/function(CreatorRole){CreatorRole["SUPER_ADMIN"]="SUPER_ADMIN";CreatorRole["DEPART_ADMIN"]="DEPART_ADMIN";CreatorRole["ASSISTANT"]="ASSISTANT";return CreatorRole;}({});// \u8f85\u5bfc\u5458\nvar jscls=/*#__PURE__*/(0,emotion_css_esm/* css */.iv)( true?{name:"1je3lqt",styles:"padding-top:12px;.ant-timeline-item-head-custom{top:4px;}.ant-switch-small{vertical-align:middle;}.ant-timeline-item{padding-bottom:12px;}.ant-timeline-item-last{padding-bottom:0;& > .ant-timeline-item-content{min-height:auto;}}.ant-tag{float:left;margin:0;}"}:0);var clss=/*#__PURE__*/(0,emotion_css_esm/* css */.iv)( true?{name:"8qcofn",styles:"margin-top:8px;margin-left:32px"}:0);var taskProcessOptions=(0,commons_task/* parseTaskProcessOptions */.xe)(commons_task/* TASK_PROCESS_OPTIONS */.j);function TaskProcessPicker(props){var value=props.value,onChange=props.onChange,className=props.className,process=props.process,user=props.user,options=props.options,disabled=props.disabled,createorRoleType=props.createorRoleType,taskCheckData=props.taskCheckData,onAssignmentRangeChange=props.onAssignmentRangeChange,rangeError=props.rangeError;var cls=classnames_default()(className,jscls);var _useState=(0,index_js_eager_.useState)(),_useState2=(0,slicedToArray/* default */.Z)(_useState,2),valueState=_useState2[0],setValueState=_useState2[1];var _useState3=(0,index_js_eager_.useState)(),_useState4=(0,slicedToArray/* default */.Z)(_useState3,2),rangeState=_useState4[0],setRangeState=_useState4[1];var _useState5=(0,index_js_eager_.useState)(),_useState6=(0,slicedToArray/* default */.Z)(_useState5,2),warningMsg=_useState6[0],setWarningMsg=_useState6[1];var _useState7=(0,index_js_eager_.useState)([]),_useState8=(0,slicedToArray/* default */.Z)(_useState7,2),approvers=_useState8[0],setApprovers=_useState8[1];//\u5e94\u8be5\u662f\u8d8a\u6743\u65f6\u4efb\u52a1\u7684\u5ba1\u6838\u4eba\n//\u59d4\u6d3e\u76ee\u6807\nvar _useState9=(0,index_js_eager_.useState)(),_useState10=(0,slicedToArray/* default */.Z)(_useState9,2),assignmentTarget=_useState10[0],setAssignmentTarget=_useState10[1];//\u8bf7\u6c42\u7684\u53d6\u6d88\u53e5\u67c4\nvar cancelCheckRef=(0,index_js_eager_.useRef)();var timerRef=(0,index_js_eager_.useRef)();//const { context } = useContextState();\nvar userInfo=user||{};//\u6839\u636e\u59d4\u6d3e\u4fe1\u606f\u83b7\u53d6\u8d8a\u6743\u4efb\u52a1\u5ba1\u6838\u4eba\nvar getApprovers=function getApprovers(params){cancelCheckRef.current&&cancelCheckRef.current.silent();cancelCheckRef.current=index_esm/* request */.WY.createCancelToken();getSubjectMembers({cancelToken:cancelCheckRef.current.token,//\u901a\u8fc7token\u53d6\u6d88\u4e0a\u4e00\u6b21\u8bf7\u6c42\nparams:Object.assign({length:10},params)}).then(function(res){if(res&&res.result.data&&res.result.data.persons&&res.result.data.persons.length){setApprovers(res.result.data.persons);}else{setApprovers([]);}}).catch(function(){setApprovers([]);});};/**\n   *\n   * \u5904\u7406\u6d3e\u53d1\u8303\u56f4\u5207\u6362\n   */var changeRangeState=function changeRangeState(name,value,isAutoAssign){if(!isAutoAssign){var warnMsg=false;if(createorRoleType==="ASSISTANT"){if(/^__ASSISTANT__(all|benyuanxi)/.test(value)){warnMsg=true;}}if(createorRoleType==="DEPART_ADMIN"){if(/^__DEPART_ADMIN__(all)/.test(value)){warnMsg=true;}}setRangeState(function(v){return Object.assign({},v,(0,defineProperty/* default */.Z)({},"".concat(name),value));});setWarningMsg(warnMsg?"\u8d85\u51fa\u6743\u9650\u8303\u56f4\u7684\u4efb\u52a1\u5c06\u5728\u5ba1\u6838\u901a\u8fc7\u540e\u53d1\u5e03":"");}else{setRangeState(function(v){return Object.assign({},v,(0,defineProperty/* default */.Z)({},"".concat(name),value));});}};var changeAssignmentTarget=function changeAssignmentTarget(name,value,opts){setAssignmentTarget(value?(0,defineProperty/* default */.Z)({},"".concat(name),value):undefined);};var items=(0,index_js_eager_.useMemo)(function(){var _vals=(typeof value==="undefined"?valueState:value)||[];var opts=(options||taskProcessOptions).filter(function(opt){if(createorRoleType==="ASSISTANT"){return opt.value==="bks";}if(createorRoleType==="DEPART_ADMIN"){return opt.value==="fdy"||opt.value==="bks";}return true;});var vals=opts.filter(function(opt){return _vals.indexOf(opt.value)>-1;});return opts.map(function(option){var _process$filter;var name=option.value;var processDetail=process===null||process===void 0?void 0:(_process$filter=process.filter(function(p){return(p===null||p===void 0?void 0:p.id)==name;}))===null||_process$filter===void 0?void 0:_process$filter[0];var valIndex=vals.findIndex(function(v){return v.value===name;});var checked=valIndex>-1;var action;var options=[];var isAutoAssign=false;var filterType;if(checked){action="xp";if(valIndex===vals.length-1){// \u6700\u540e\u4e00\u4e2a\u5904\u7406\naction="cl";}if(valIndex===0){if(createorRoleType==="SUPER_ADMIN"){if(name==="yxgly"){options=[{label:"\u624b\u52a8\u59d4\u6d3e",value:"manual"},{label:"\u6240\u6709\u9662\u7cfb",value:"__".concat(createorRoleType,"__all_").concat(name)},{label:"\u6307\u5b9a\u9662\u7cfb",value:"__".concat(createorRoleType,"__$zd_").concat(name)}];}if(name==="fdy"){options=[{label:"\u624b\u52a8\u59d4\u6d3e",value:"manual"},{label:"\u5168\u6821\u6240\u6709\u8f85\u5bfc\u5458",value:"__".concat(createorRoleType,"__all_").concat(name)},{label:"\u6307\u5b9a\u9662\u7cfb\u8f85\u5bfc\u5458",value:"__".concat(createorRoleType,"__yx$zd_").concat(name)},{label:"\u6307\u5b9a\u8f85\u5bfc\u5458",value:"__".concat(createorRoleType,"__$zd_").concat(name)}];}if(name=="bks"){options=[{label:"\u624b\u52a8\u59d4\u6d3e",value:"manual"},{label:"\u5168\u6821\u672c\u79d1\u751f",value:"__".concat(createorRoleType,"__all_").concat(name)},{label:"\u6307\u5b9a\u9662\u7cfb",value:"__".concat(createorRoleType,"__yx$zd_").concat(name)},{label:"\u6307\u5b9a\u73ed\u7ea7",value:"__".concat(createorRoleType,"__bj$zd_").concat(name)},{label:"\u6307\u5b9a\u5b66\u751f",value:"__".concat(createorRoleType,"__$zd_").concat(name)}];}}if(createorRoleType==="DEPART_ADMIN"){if(name==="fdy"){options=[{label:"\u624b\u52a8\u59d4\u6d3e",value:"manual"},{label:"\u5168\u6821\u8f85\u5bfc\u5458",value:"__".concat(createorRoleType,"__all_").concat(name)},{label:"\u6307\u5b9a\u672c\u9662\u7cfb\u7684\u8f85\u5bfc\u5458",value:"__".concat(createorRoleType,"__byx$zd_").concat(name)}];}if(name==="bks"){options=[{label:"\u624b\u52a8\u59d4\u6d3e",value:"manual"},{label:"\u5168\u6821\u5b66\u751f",value:"__".concat(createorRoleType,"__all_").concat(name)},{label:"\u672c\u9662\u7cfb\u5b66\u751f",value:"__".concat(createorRoleType,"__benyuanxi$zd_").concat(name)},{label:"\u6307\u5b9a\u672c\u9662\u7cfb\u73ed\u7ea7",value:"__".concat(createorRoleType,"__byxbj$zd_").concat(name)},{label:"\u6307\u5b9a\u672c\u9662\u7cfb\u5b66\u751f",value:"__".concat(createorRoleType,"__byx$zd_").concat(name)}];}}if(createorRoleType==="ASSISTANT"){if(name=="bks"){options=[{label:"\u624b\u52a8\u59d4\u6d3e",value:"manual"},{label:"\u5168\u6821\u5b66\u751f",value:"__".concat(createorRoleType,"__all_").concat(name)},{label:"\u672c\u9662\u7cfb\u5b66\u751f",value:"__".concat(createorRoleType,"__benyuanxi$zd_").concat(name)},{label:"\u672c\u73ed\u7ea7\u5b66\u751f",value:"__".concat(createorRoleType,"__benbanji$zd_").concat(name)},{label:"\u6307\u5b9a\u672c\u73ed\u7ea7\u5b66\u751f",value:"__".concat(createorRoleType,"__bbj$zd_").concat(name)}];}}}else{isAutoAssign=true;if(name=="bks"){options=[{label:"\u624b\u52a8\u59d4\u6d3e",value:"manual"},{label:"\u81ea\u52a8\u59d4\u6d3e",value:"__".concat("_bks"||0,"__auto")}];}if(name=="fdy"){options=[{label:"\u624b\u52a8\u59d4\u6d3e",value:"manual"},{label:"\u81ea\u52a8\u59d4\u6d3e",value:"__".concat("_fdy"||0,"__auto")}];}}}var hasUpdateData=processDetail===null||processDetail===void 0?void 0:processDetail.assignmentTargetName;//\u521b\u5efa\u8005\u662f\u8f85\u5bfc\u5458\uff0c\u6d3e\u53d1\u8303\u56f4\u662f\u672c\u73ed\u7ea7\uff0c\u9700\u8981\u5c55\u793a\u672c\u73ed\u7ea7\u4e0b\u62c9\u9009\u62e9\nvar isClassSelectFilter=checked&&createorRoleType==="ASSISTANT"&&rangeState&&rangeState[name]==="__".concat(createorRoleType,"__benbanji$zd_").concat(name)?true:false;//\u521b\u5efa\u8005\u662f\u9662\u7cfb\u9886\u5bfc\u4e14\u6d3e\u53d1\u8303\u56f4\u662f\u672c\u9662\u7cfb\uff0c\u9700\u8981\u5c55\u793a\u672c\u9662\u7cfb\u4e0b\u62c9\u9009\u62e9\nvar isAcademySelectFilter=checked&&(createorRoleType==="ASSISTANT"||createorRoleType==="DEPART_ADMIN")&&rangeState&&rangeState[name]==="__".concat(createorRoleType,"__benyuanxi$zd_").concat(name)?true:false;//\u5f53\u524d\u9700\u8981\u6307\u5b9a\uff08zd\uff09\u9009\u62e9\u7684\u7fa4\u4f53\uff0c\u9700\u8981\u5c55\u793a\u5bf9\u5e94\u7fa4\u4f53\u7684\u9009\u62e9\u5668\nvar isPickerFilter=rangeState&&rangeState[name]&&/\\$zd/.test(rangeState[name])&&checked&&!isClassSelectFilter&&!isAcademySelectFilter?true:false;if(isPickerFilter){//\u83b7\u53d6\u6307\u5b9a\u7fa4\u4f53\u7684\u7c7b\u578b(\u73ed\u7ea7\uff0c\u9662\u7cfb\uff0c\u8f85\u5bfc\u5458\uff0c\u5b66\u751f..)\nfilterType=getAssignmentRangeFilter(rangeState[name].replace("__".concat(createorRoleType,"__"),""));}return (0,objectSpread2/* default */.Z)((0,objectSpread2/* default */.Z)({},option),{},{label:(0,emotion_react_browser_esm_js_.jsx)(index_js_eager_.Fragment,null,(0,emotion_react_browser_esm_js_.jsx)(space["default"],null,(0,emotion_react_browser_esm_js_.jsx)("span",null,option.label),action&&commons_task/* TASK_PROCESS_ACTIONS */.yB[action]?(0,emotion_react_browser_esm_js_.jsx)(tag/* default */.Z,{color:commons_task/* TASK_PROCESS_ACTIONS_OPTIONS */.MB[action].color},commons_task/* TASK_PROCESS_ACTIONS */.yB[action]):null), false?0:(0,emotion_react_browser_esm_js_.jsx)(index_js_eager_.Fragment,null,options&&options.length?(0,emotion_react_browser_esm_js_.jsx)(index_js_eager_.Fragment,null,(0,emotion_react_browser_esm_js_.jsx)("div",{className:clss},(0,emotion_react_browser_esm_js_.jsx)(lib_select/* default */.Z,{key:name,disabled:disabled,defaultValue:isAutoAssign?"___".concat(name,"__auto"):"manual",placeholder:"\\u9009\\u62E9\\u8303\\u56F4",onChange:function onChange(v){return changeRangeState(name,v,isAutoAssign);},style:{width:"180px"},options:options})),(isPickerFilter||hasUpdateData)&&(0,emotion_react_browser_esm_js_.jsx)("div",{className:clss},(0,emotion_react_browser_esm_js_.jsx)(AssignmentRangePicker,(0,esm_extends/* default */.Z)({},filterType,{disabled:disabled,status:rangeError?"error":undefined,key:name,user:user,onChange:function onChange(v,opts){return changeAssignmentTarget(name,v,opts);},manageOrgs:userInfo.manageOrgs,value:processDetail===null||processDetail===void 0?void 0:processDetail.assignmentTargetName,manageClasses:userInfo.manageclasses,roleType:createorRoleType}))),(isAcademySelectFilter||hasUpdateData)&&(0,emotion_react_browser_esm_js_.jsx)("div",{className:clss},(0,emotion_react_browser_esm_js_.jsx)(AssignmentTargetSelect,{disabled:disabled,hiddenEmptyOptionsAlert:hasUpdateData,placeholder:"\\u9009\\u62E9\\u6307\\u5B9A\\u7684\\u5B66\\u9662",value:processDetail===null||processDetail===void 0?void 0:processDetail.assignmentTargetName,status:rangeError?"error":undefined,key:name,onChange:function onChange(v,opt){return changeAssignmentTarget(name,v,opt);},options:createorRoleType==="ASSISTANT"?userInfo.classLeaderAcademy:userInfo.manageOrgs})),(isClassSelectFilter||hasUpdateData)&&(0,emotion_react_browser_esm_js_.jsx)("div",{className:clss},(0,emotion_react_browser_esm_js_.jsx)(AssignmentTargetSelect,{disabled:disabled,hiddenEmptyOptionsAlert:hasUpdateData,placeholder:"\\u9009\\u62E9\\u6307\\u5B9A\\u7684\\u73ED\\u7EA7",status:rangeError?"error":undefined,key:name,value:processDetail===null||processDetail===void 0?void 0:processDetail.assignmentTargetName,onChange:function onChange(v,opt){return changeAssignmentTarget(name,v,opt);},options:userInfo.manageclasses}))):null)),checked:checked});});},[options,value,valueState,createorRoleType,rangeState,rangeError,disabled]);var changeHandler=function changeHandler(val){setValueState(val);typeof onChange==="function"&&!disabled&&onChange(val);};var checkedChange=function checkedChange(value,checked){var val=(valueState||[]).filter(function(v){return v!==value;});if(checked){val.push(value);}var vals=(options||taskProcessOptions).filter(function(opt){return val.indexOf(opt.value)>-1;});changeHandler(vals.map(function(v){return v.value;}));};(0,index_js_eager_.useEffect)(function(){var range={};if(valueState&&valueState.length){for(var index=0;index<valueState.length;index++){var _k=valueState[index];if(_k&&rangeState&&rangeState[_k]){range[_k]=rangeState[_k];}}}setRangeState(range);},[valueState]);(0,index_js_eager_.useEffect)(function(){timerRef.current&&clearTimeout(timerRef.current);timerRef.current=setTimeout(function(){var assignmentRange={};var _vals=(typeof value==="undefined"?valueState:value)||[];var opts=(options||taskProcessOptions).filter(function(opt){if(createorRoleType==="ASSISTANT"){return opt.value==="bks";}if(createorRoleType==="DEPART_ADMIN"){return opt.value==="fdy"||opt.value==="bks";}return true;});var vals=opts.filter(function(opt){return _vals.indexOf(opt.value)>-1;}).map(function(opt){return opt.value;});for(var index=0;index<vals.length;index++){var name=vals[index];var action=index===vals.length-1?"cl":"xp";var assignmentName=(0,commons_task/* getProcessAssignmentName */.he)(name,action);if(rangeState&&rangeState[name]&&rangeState[name]!=="manual"||index>0&&!rangeState[name]){var needTarget=/\\$zd_/.test(rangeState[name])&&!index;assignmentRange[name]=getAssignmentRange(rangeState[name]?rangeState[name]:"___".concat(name,"__auto"),name,assignmentName,createorRoleType);if(assignmentRange[name]){//\u6307\u5b9a\u4e86\u786e\u5b9a\u7684\u59d4\u6d3e\u76ee\u6807\nif(assignmentTarget&&assignmentTarget[name]){assignmentRange[name].assignmentTargetId=assignmentTarget[name].value;assignmentRange[name].assignmentTargetName=assignmentTarget[name].label;}else{assignmentRange[name].needTarget=needTarget;}}}else{var assignmentData={name:assignmentName,id:name,assignmentTargetFilter:"",assignmentType:"U",assignmentTargetId:"",type:"",assignmentTargetName:"",autoAssign:false,expandRangeAssignment:false};if(name==="yxgly"){assignmentData.assignmentTargetId="poss_xgbzr";assignmentData.assignmentType="PS";}else if(name==="fdy"){assignmentData.assignmentType="U";assignmentData.assignmentTargetFilter=task/* AssignmentTargetFilter */.h.SelectClassAssistantFromAcademy;}else if(name==="bks"){assignmentData.assignmentType="G";assignmentData.assignmentTargetFilter=task/* AssignmentTargetFilter */.h.SelectStudentByCurrentUser;}assignmentRange[name]=assignmentData;}}typeof onAssignmentRangeChange==="function"&&!disabled&&onAssignmentRangeChange(assignmentRange,vals,warningMsg?true:false);},100);},[rangeState,options,onAssignmentRangeChange,warningMsg,value,valueState,disabled,assignmentTarget,createorRoleType]);(0,index_js_eager_.useEffect)(function(){return function(){timerRef.current&&clearTimeout(timerRef.current);};},[]);(0,index_js_eager_.useEffect)(function(){if(taskCheckData&&taskCheckData.approver&&taskCheckData.approver.approverType&&taskCheckData.approver.approverId){getApprovers({subjectType:taskCheckData.approver.approverType,subjectCode:taskCheckData.approver.approverId});}else{setApprovers([]);}},[taskCheckData]);var msg;var avatars=null;if(warningMsg){var approver=taskCheckData&&taskCheckData.approver&&taskCheckData.approver.approverName;var membersNames=(approvers||[]).map(function(i){return i.name;}).filter(Boolean);// \u6700\u591a\u663e\u793a3\u4e2a\nmsg=(0,emotion_react_browser_esm_js_.jsx)(index_js_eager_.Fragment,null,"\\u8D8A\\u6743\\u4EFB\\u52A1\\u5C06\\u5728",approver?(0,emotion_react_browser_esm_js_.jsx)(tag/* default */.Z,{style:{marginLeft:8}},approver,membersNames?"\\uFF08".concat(membersNames.slice(0,3).join("\u3001")).concat(membersNames.length>3?"...":"","\\uFF09"):null):null,"\\u5BA1\\u6838\\u901A\\u8FC7\\u540E\\u53D1\\u5E03 ",avatars);}return (0,emotion_react_browser_esm_js_.jsx)(index_js_eager_.Fragment,null,(0,emotion_react_browser_esm_js_.jsx)(timeline/* default */.Z,{className:cls},(0,emotion_react_browser_esm_js_.jsx)(timeline/* default */.Z.Item,{key:"_create",color:"blue"},"\\u521B\\u5EFA\\u8005"),items.map(function(item,index){return (0,emotion_react_browser_esm_js_.jsx)(timeline/* default */.Z.Item,{key:item.value+index,dot:item.checked?(0,emotion_react_browser_esm_js_.jsx)(CheckCircleFilled/* default */.Z,null):undefined,color:item.checked?"blue":"gray"},(0,emotion_react_browser_esm_js_.jsx)(lib_switch/* default */.Z,{disabled:disabled,onChange:function onChange(checed){return checkedChange(item.value,checed);},size:"small",defaultChecked:item.checked,checked:item.checked})," ",item.label);})),msg&&(0,emotion_react_browser_esm_js_.jsx)(lib_alert["default"],{message:msg,type:"warning",showIcon:true}));}/**\n * \u6307\u5b9a\u6761\u4ef6\u7c7b\u578b\n * @param key\n * @returns\n */function getAssignmentRangeFilter(key){if(key==="$zd_yxgly"){return{type:"academy_yxgly"};}if(key==="$zd_fdy"||key==="byx$zd_fdy"){return{type:"academy_fdy"};}if(key==="byxbj$zd_bks"){return{type:"academy_class"};}if(key==="byx$zd_bks"){return{type:"academy_student"};}if(key==="$zd_bks"||key==="bbj$zd_bks"){return{type:"student"};}if(/^(yx\\$zd)/.test(key)){return{type:"academy"};}if(/^bj\\$zd/.test(key)){return{type:"class"};}}function getAssignmentRange(key,id,name,roleType){if(id){var _ranges;var createAssignmentData=function createAssignmentData(opts){return Object.assign({id:id,name:name,type:"",assignmentType:"",assignmentTargetFilter:"",assignmentTargetId:"",assignmentTargetName:"",autoAssign:true,expandRangeAssignment:true},opts);};var ranges=(_ranges={},(0,defineProperty/* default */.Z)(_ranges,"__".concat(CreatorRole.SUPER_ADMIN,"__all_yxgly"),createAssignmentData({assignmentType:"PS",assignmentTargetId:"poss_xgbzr",assignmentTargetName:"\u5b66\u5de5\u7ba1\u7406\u5458\u5c97\u4f4d\u96c6",expandRangeAssignment:false})),(0,defineProperty/* default */.Z)(_ranges,"__".concat(CreatorRole.SUPER_ADMIN,"__$zd_yxgly"),createAssignmentData({assignmentType:"P",expandRangeAssignment:false})),(0,defineProperty/* default */.Z)(_ranges,"__".concat(CreatorRole.SUPER_ADMIN,"__all_fdy"),createAssignmentData({assignmentTargetFilter:task/* AssignmentTargetFilter */.h.SelectAllClassAssistant})),(0,defineProperty/* default */.Z)(_ranges,"__".concat(CreatorRole.SUPER_ADMIN,"__yx$zd_fdy"),createAssignmentData({assignmentType:"O",assignmentTargetFilter:task/* AssignmentTargetFilter */.h.SelectClassAssistantFromAcademy})),(0,defineProperty/* default */.Z)(_ranges,"__".concat(CreatorRole.SUPER_ADMIN,"__$zd_fdy"),createAssignmentData({assignmentType:"A"})),(0,defineProperty/* default */.Z)(_ranges,"__".concat(CreatorRole.SUPER_ADMIN,"__all_bks"),createAssignmentData({assignmentType:"G",assignmentTargetId:"gro_bks",assignmentTargetName:"\u672c\u79d1\u751f"})),(0,defineProperty/* default */.Z)(_ranges,"__".concat(CreatorRole.SUPER_ADMIN,"__yx$zd_bks"),createAssignmentData({assignmentType:"O",assignmentTargetFilter:task/* AssignmentTargetFilter */.h.SelectStudentFromAcademy})),(0,defineProperty/* default */.Z)(_ranges,"__".concat(CreatorRole.SUPER_ADMIN,"__bj$zd_bks"),createAssignmentData({assignmentType:"C",assignmentTargetFilter:task/* AssignmentTargetFilter */.h.SelectStudentFromClass})),(0,defineProperty/* default */.Z)(_ranges,"__".concat(CreatorRole.SUPER_ADMIN,"__$zd_bks"),createAssignmentData({assignmentType:"U"})),(0,defineProperty/* default */.Z)(_ranges,"__".concat(CreatorRole.DEPART_ADMIN,"__all_fdy"),createAssignmentData({assignmentTargetFilter:task/* AssignmentTargetFilter */.h.SelectAllClassAssistant})),(0,defineProperty/* default */.Z)(_ranges,"__".concat(CreatorRole.DEPART_ADMIN,"__byx$zd_fdy"),createAssignmentData({assignmentType:"A"})),(0,defineProperty/* default */.Z)(_ranges,"__".concat(CreatorRole.DEPART_ADMIN,"__all_bks"),createAssignmentData({assignmentType:"G",assignmentTargetId:"gro_bks",assignmentTargetName:"\u672c\u79d1\u751f"})),(0,defineProperty/* default */.Z)(_ranges,"__".concat(CreatorRole.DEPART_ADMIN,"__benyuanxi$zd_bks"),createAssignmentData({assignmentType:"O",assignmentTargetFilter:task/* AssignmentTargetFilter */.h.SelectStudentFromAcademy})),(0,defineProperty/* default */.Z)(_ranges,"__".concat(CreatorRole.DEPART_ADMIN,"__byxbj$zd_bks"),createAssignmentData({assignmentType:"C",assignmentTargetFilter:task/* AssignmentTargetFilter */.h.SelectStudentFromClass})),(0,defineProperty/* default */.Z)(_ranges,"__".concat(CreatorRole.DEPART_ADMIN,"__byx$zd_bks"),createAssignmentData({assignmentType:"U"})),(0,defineProperty/* default */.Z)(_ranges,"__".concat("_fdy"||0,"__auto"),createAssignmentData({assignmentTargetFilter:task/* AssignmentTargetFilter */.h.SelectClassAssistantFromAcademy,assignmentType:"O",assignmentTargetId:"{authorityScopeId}"})),(0,defineProperty/* default */.Z)(_ranges,"__".concat(CreatorRole.ASSISTANT,"__all_bks"),createAssignmentData({assignmentType:"G",assignmentTargetId:"gro_bks",assignmentTargetName:"\u672c\u79d1\u751f"})),(0,defineProperty/* default */.Z)(_ranges,"__".concat(CreatorRole.ASSISTANT,"__benyuanxi$zd_bks"),createAssignmentData({assignmentType:"O",assignmentTargetFilter:task/* AssignmentTargetFilter */.h.SelectStudentFromAcademy})),(0,defineProperty/* default */.Z)(_ranges,"__".concat(CreatorRole.ASSISTANT,"__benbanji$zd_bks"),createAssignmentData({assignmentType:"C",assignmentTargetFilter:task/* AssignmentTargetFilter */.h.SelectStudentFromClass})),(0,defineProperty/* default */.Z)(_ranges,"__".concat(CreatorRole.ASSISTANT,"__bbj$zd_bks"),createAssignmentData({assignmentType:"U"})),(0,defineProperty/* default */.Z)(_ranges,"__".concat("_bks"||0,"__auto"),createAssignmentData({assignmentType:"C",assignmentTargetFilter:task/* AssignmentTargetFilter */.h.SelectStudentFromClass,assignmentTargetId:"{authorityScopeId}"})),_ranges);return ranges[key];}}\n// EXTERNAL MODULE: ./node_modules/moment/moment.js\nvar moment = __webpack_require__(60438);\nvar moment_default = /*#__PURE__*/__webpack_require__.n(moment);\n;// CONCATENATED MODULE: ./src/components/EasyformTask/index.tsx\nvar EasyformTask_excluded=["_key","process","formId"];function EasyformTask_EMOTION_STRINGIFIED_CSS_ERROR_(){return"You have tried to stringify object returned from `css` function. It isn\'t supposed to be used directly (e.g. as value of the `className` prop), but rather handed to emotion so it can handle it (e.g. as value of `css` prop).";}/**\n * \u5904\u7406\u8868\u5355\u5b9a\u4e49\n * @param data\n * @returns\n */var parsePayloadString=function parsePayloadString(data){return typeof data==="object"&&data?JSON.stringify(data.define||data):"";};var processOptions=commons_task/* TASK_PROCESS_OPTIONS */.j.map(function(v){return (0,objectSpread2/* default */.Z)({id:v.value},v);});var cls=/*#__PURE__*/(0,emotion_css_esm/* css */.iv)( true?{name:"0",styles:""}:0);var updateTaskData=function updateTaskData(values,assignmentRangeRef){if(!values||!assignmentRangeRef)return;var process=values.process||[];var processVals=processOptions.filter(function(opt){return process.indexOf(opt.id)>-1;});values.process=processVals.map(function(opt){var _assignmentRangeRef$c;return(_assignmentRangeRef$c=assignmentRangeRef.current)===null||_assignmentRangeRef$c===void 0?void 0:_assignmentRangeRef$c[opt.id];}).filter(Boolean);console.warn(values);if(moment_default().isMoment(values.startTime)){values.startTime=values.startTime.minute(0).second(0).millisecond(0).toISOString();}if(moment_default().isMoment(values.endTime)){values.endTime=values.endTime.minute(0).second(0).millisecond(0).toISOString();}// return {\n//   ...task,\n//   ...values,\n//   //   creatorId: creatorId,\n//   //   creatorName: creatorName,\n//   //   creatorOrg: values.creatorOrg || orgName,\n//   //   payload: parsePayloadString(formData) || _task.payload,\n// };\nreturn values.process;};/* harmony default export */ var EasyformTask = (/*#__PURE__*/index_js_eager_default().forwardRef(function TaskCreateDrawer2(props,ref){// const { context } = useContextState<{\n//   admin?: boolean;\n//   supper?: boolean;\n//   fdy?: boolean;\n//   departAdmin?: boolean;\n//   system?: boolean;\n//   xgAdmin?: boolean;\n// }>();\nvar user=props.user,_props$creatorId=props.creatorId,creatorId=_props$creatorId===void 0?user.id:_props$creatorId,_props$creatorName=props.creatorName,creatorName=_props$creatorName===void 0?user.displayName:_props$creatorName,creatorOrg=props.creatorOrg,createForm=props.createForm,open=props.open,_props$title=props.title,title=_props$title===void 0?"\u5feb\u901f\u521b\u5efa\u4efb\u52a1":_props$title,task=props.task,onSuccess=props.onSuccess,onCreateTask=props.onCreateTask,onClose=props.onClose,jumpToDetail=props.jumpToDetail,_props$taskTitle=props.taskTitle,taskTitle=_props$taskTitle===void 0?"\u6d3e\u53d1\u8fc7\u7a0b":_props$taskTitle,confirmText=props.confirmText,_props$hiddenCreateBu=props.hiddenCreateButton,hiddenCreateButton=_props$hiddenCreateBu===void 0?false:_props$hiddenCreateBu,jumpFrom=props.jumpFrom,_props$width=props.width,width=_props$width===void 0?"480px":_props$width;var userInfo=user||{};var _useIsMobile=(0,pro_layout_es/* useIsMobile */.dD)(),_useIsMobile2=(0,slicedToArray/* default */.Z)(_useIsMobile,1),isMobile=_useIsMobile2[0];var orgs=(userInfo&&userInfo.orgs?userInfo.orgs:[]).concat([]);var createorRoleType;if(user.fdy){createorRoleType="ASSISTANT";}if(user.departAdmin){createorRoleType="DEPART_ADMIN";}if(user.supper||user.system||user.xgAdmin){createorRoleType="SUPER_ADMIN";}if(creatorOrg){orgs=[{name:creatorOrg}];}var orgName=creatorOrg||(orgs[0]?orgs[0].name:undefined);var _useState=(0,index_js_eager_.useState)(open===true),_useState2=(0,slicedToArray/* default */.Z)(_useState,2),publishPanelVisible=_useState2[0],setPublishPanelVisible=_useState2[1];var _useState3=(0,index_js_eager_.useState)(false),_useState4=(0,slicedToArray/* default */.Z)(_useState3,2),createFormVisible=_useState4[0],setCreateFormVisible=_useState4[1];var isUpdate=task?true:false;var _ref=task||{},_key=_ref._key,_ref$process=_ref.process,process=_ref$process===void 0?[]:_ref$process,formId=_ref.formId,_task=(0,objectWithoutProperties/* default */.Z)(_ref,EasyformTask_excluded);var action=isUpdate?"\u66f4\u65b0":"\u521b\u5efa";var assignmentRangeRef=(0,index_js_eager_.useRef)();var formCount=(0,index_js_eager_.useRef)(0);var _useState5=(0,index_js_eager_.useState)(null),_useState6=(0,slicedToArray/* default */.Z)(_useState5,2),formData=_useState6[0],setFormData=_useState6[1];var _useState7=(0,index_js_eager_.useState)(null),_useState8=(0,slicedToArray/* default */.Z)(_useState7,2),taskData=_useState8[0],setTaskData=_useState8[1];var formRef=(0,index_js_eager_.useRef)();var _useState9=(0,index_js_eager_.useState)(false),_useState10=(0,slicedToArray/* default */.Z)(_useState9,2),createLoading=_useState10[0],setCreateLoading=_useState10[1];var _useState11=(0,index_js_eager_.useState)(""),_useState12=(0,slicedToArray/* default */.Z)(_useState11,2),processRangeError=_useState12[0],setProcessRangeError=_useState12[1];var cancelCheckRef=(0,index_js_eager_.useRef)();var _useState13=(0,index_js_eager_.useState)(),_useState14=(0,slicedToArray/* default */.Z)(_useState13,2),taskCheckData=_useState14[0],setTaskCheckData=_useState14[1];var taskPrivilegeRef=(0,index_js_eager_.useRef)(false);//const navigate = useNavigate();\nvar _React$useState=index_js_eager_default().useState(false),_React$useState2=(0,slicedToArray/* default */.Z)(_React$useState,2),showBack=_React$useState2[0],setShowBack=_React$useState2[1];var _React$useState3=index_js_eager_default().useState(),_React$useState4=(0,slicedToArray/* default */.Z)(_React$useState3,2),iframeNode=_React$useState4[0],setIframeNode=_React$useState4[1];var handleIframeRef=index_js_eager_default().useCallback(function(node){setIframeNode(node);},[]);var closeHandler=function closeHandler(){setPublishPanelVisible(false);setCreateFormVisible(false);setFormData(null);typeof onClose==="function"&&onClose();};var visibleChangeHandler=function visibleChangeHandler(v){setPublishPanelVisible(v);typeof onClose==="function"&&!v&&onClose();};var closeFormDrawerHandler=function closeFormDrawerHandler(){setCreateFormVisible(false);typeof onClose==="function"&&onClose();};var updateTaskData=function updateTaskData(values){var process=values.process||[];var processVals=processOptions.filter(function(opt){return process.indexOf(opt.id)>-1;});values.process=processVals.map(function(opt){var _assignmentRangeRef$c2;return(_assignmentRangeRef$c2=assignmentRangeRef.current)===null||_assignmentRangeRef$c2===void 0?void 0:_assignmentRangeRef$c2[opt.id];}).filter(Boolean);console.warn(values);if(moment_default().isMoment(values.startTime)){values.startTime=values.startTime.minute(0).second(0).millisecond(0).toISOString();}if(moment_default().isMoment(values.endTime)){values.endTime=values.endTime.minute(0).second(0).millisecond(0).toISOString();}return (0,objectSpread2/* default */.Z)((0,objectSpread2/* default */.Z)((0,objectSpread2/* default */.Z)({},task),values),{},{creatorId:creatorId,creatorName:creatorName,creatorOrg:values.creatorOrg||orgName,payload:parsePayloadString(formData)||_task.payload});};var nextHanlder=function nextHanlder(){if(formRef.current){formRef.current.validateFields().then(function(values){setTaskData(updateTaskData(values));if(createForm){setCreateFormVisible(true);setPublishPanelVisible(false);}}).catch(function(reason){console.warn(reason);message["default"].warning("\u8bf7\u68c0\u67e5\u4efb\u52a1\u914d\u7f6e\u662f\u5426\u586b\u5199\u6b63\u786e");});}};var createTaskHandler=/*#__PURE__*/function(){var _ref2=(0,asyncToGenerator/* default */.Z)(/*#__PURE__*/(0,regeneratorRuntime/* default */.Z)().mark(function _callee(getValues){var _values;return (0,regeneratorRuntime/* default */.Z)().wrap(function _callee$(_context){while(1)switch(_context.prev=_context.next){case 0:if(!(getValues===true)){_context.next=15;break;}if(!formRef.current){_context.next=13;break;}_context.prev=2;_context.next=5;return formRef.current.validateFields();case 5:_values=_context.sent;return _context.abrupt("return",updateTaskData(_values));case 9:_context.prev=9;_context.t0=_context["catch"](2);console.warn(_context.t0);message["default"].warning("\u8bf7\u68c0\u67e5\u4efb\u52a1\u914d\u7f6e\u662f\u5426\u586b\u5199\u6b63\u786e");case 13:_context.next=16;break;case 15:if(formRef.current){formRef.current.validateFields().then(function(values){_createTask(updateTaskData(values));}).catch(function(reason){console.warn(reason);message["default"].warning("\u8bf7\u68c0\u67e5\u4efb\u52a1\u914d\u7f6e\u662f\u5426\u586b\u5199\u6b63\u786e");});}case 16:case"end":return _context.stop();}},_callee,null,[[2,9]]);}));return function createTaskHandler(_x){return _ref2.apply(this,arguments);};}();var createTaskRequest=function createTaskRequest(data){setCreateLoading(true);(isUpdate?services_task/* updateTask */.xJ:services_task/* createTask */.vr)({data:data,pathParams:isUpdate?{id:_key}:{}}).then(function(res){if(res&&res.result.data){notification["default"].success({message:action+"\u6210\u529f"});formCount.current++;closeHandler();if(typeof onSuccess!=="function"||onSuccess(res.result.data)!==false){//jumpToDetail && res?.data?._key && navigate(`/task-my/${res.result.data._key}` + (jumpFrom ? `?from=${jumpFrom}` : ""));\n}}else{notification["default"].error({message:action+"\u5931\u8d25",description:res&&res.msg?res.msg:"\u8d85\u65f6\u6216\u670d\u52a1\u51fa\u9519\uff0c\u8bf7\u7a0d\u540e\u91cd\u8bd5"});}}).catch(function(reason){notification["default"].error({message:action+"\u5931\u8d25",description:reason.response&&reason.response.msg?reason.response.msg:"\u7f51\u7edc\u6216\u670d\u52a1\u6545\u969c\uff0c\u8bf7\u7a0d\u540e\u91cd\u8bd5"});}).finally(function(){setCreateLoading(false);});};var _createTask=function _createTask(data){if(formRef.current){formRef.current.validateFields().then(function(){if(typeof onCreateTask==="function"){var res=onCreateTask(data);if(res===false){closeHandler();}else{createTaskRequest(data);}}else{createTaskRequest(data);}}).catch(function(){setPublishPanelVisible(true);setCreateFormVisible(false);});}};var createTaskFormHandler=function createTaskFormHandler(){if(iframeNode&&iframeNode.contentWindow["EASY_FORM"]&&iframeNode.contentWindow["EASY_FORM"].submitActivity&&iframeNode.contentWindow["EASY_FORM"].submitActivity){iframeNode.contentWindow["EASY_FORM"].submitActivity(function(data){_createTask((0,objectSpread2/* default */.Z)((0,objectSpread2/* default */.Z)({},taskData),{},{payload:parsePayloadString(data)||_task.payload}));setFormData(data);});}else if(formData){createTaskHandler();}else{var _iframeNode$contentWi;console.warn("wy:",iframeNode===null||iframeNode===void 0?void 0:(_iframeNode$contentWi=iframeNode.contentWindow)===null||_iframeNode$contentWi===void 0?void 0:_iframeNode$contentWi.EASY_FORM);message["default"].warning("\u8bf7\u68c0\u67e5\u8868\u5355\u662f\u5426\u5df2\u914d\u7f6e");}};var formatCheckData=function formatCheckData(value){if(value){var _value$asignments$,_value$asignments$2,_value$asignments$3,_value$asignments$3$t;return{privilege:value.isExceedAuth,approver:{approverId:(_value$asignments$=value.asignments[0])===null||_value$asignments$===void 0?void 0:_value$asignments$.code,approverName:(_value$asignments$2=value.asignments[0])===null||_value$asignments$2===void 0?void 0:_value$asignments$2.name,approverType:(_value$asignments$3=value.asignments[0])===null||_value$asignments$3===void 0?void 0:(_value$asignments$3$t=_value$asignments$3.type)===null||_value$asignments$3$t===void 0?void 0:_value$asignments$3$t.toLocaleUpperCase()}};}};var checkTaskHandler=function checkTaskHandler(privilege){// \u524d\u7aef\u9884\u5224\u65ad\u662f\u5426\u8d8a\u6743\u7684\u7ed3\u679c\ncancelCheckRef.current&&cancelCheckRef.current.silent();// \u9759\u9ed8\u53d6\u6d88\u4e0a\u6b21\u8bf7\u6c42\ncancelCheckRef.current=index_esm/* request */.WY.createCancelToken();if(privilege){var _formRef$current;var _values2=(_formRef$current=formRef.current)===null||_formRef$current===void 0?void 0:_formRef$current.getFieldsValue();var _taskData=_values2&&updateTaskData(_values2);var hasProcess=_taskData&&_taskData.process&&_taskData.process.length?true:false;hasProcess&&checkTask({//cancelToken: cancelCheckRef.current.token,\nprocess:_taskData===null||_taskData===void 0?void 0:_taskData.process}).then(function(res){if(res&&res.result.data){setTaskCheckData(formatCheckData(res.result.data));}else{setTaskCheckData({privilege:false});}}).catch(function(){setTaskCheckData({privilege:false});});}else{setTaskCheckData({privilege:false});}};var onAssignmentRangeChange=(0,index_js_eager_.useCallback)(function(assignmentRange,vals,privilege){assignmentRangeRef.current=assignmentRange;if(formRef.current){var _process=formRef.current.getFieldValue("process");if(_process&&_process.length){// \u5f53\u8bbe\u7f6e\u4e86\u4e0b\u6d3e\u6d41\u7a0b\uff0c\u65f6\u89e6\u53d1\u68c0\u9a8c\nformRef.current.validateFields(["process"]);}//taskPrivilegeRef.current !== privilege && checkTaskHandler(privilege);\ncheckTaskHandler(privilege);}taskPrivilegeRef.current=privilege===true;},[]);(0,index_js_eager_.useEffect)(function(){setPublishPanelVisible(open===true);if(!open){assignmentRangeRef.current=null;}},[open]);(0,index_js_eager_.useEffect)(function(){if(!createorRoleType&&open){message["default"].warn("\u65e0\u6743\u9650\u521b\u5efa\u4efb\u52a1");visibleChangeHandler(false);}},[createorRoleType,open]);if(!createorRoleType){return null;}var handlePostMessage=index_js_eager_default().useCallback(function(event){if(event.data==="SHOW_BACK"){setShowBack(true);}else if(event.data==="HIDE_BACK"){setShowBack(false);}},[]);index_js_eager_default().useEffect(function(){if(iframeNode){window.addEventListener("message",handlePostMessage);return function(){window.removeEventListener("message",handlePostMessage);};}},[iframeNode]);(0,index_js_eager_.useImperativeHandle)(ref,function(){return{getTaskValue:createTaskHandler};});var _confirmText=confirmText?confirmText:"\\u7ACB\\u5373".concat(action);return (0,emotion_react_browser_esm_js_.jsx)(index_js_eager_.Fragment,null,(0,emotion_react_browser_esm_js_.jsx)(es/* BetaSchemaForm */.laP,{layoutType:"Form"//visible={publishPanelVisible}\n//width={isMobile ? "100vw" : width}\n// drawerProps={{\n//   contentWrapperStyle: isMobile\n//     ? {}\n//     : {\n//         minWidth: 500,\n//       },\n// }}\n,initialValues:Object.assign({type:createForm?"EZForm":"",process:(process||[]).map(function(v){return typeof v==="object"?v.id:v;})},_task),columns:[{dataIndex:"type",title:"\u4efb\u52a1\u7c7b\u578b",valueType:"radio",hideInForm:true,fieldProps:{disabled:isUpdate,options:[{value:"EZForm",label:"\u4e07\u80fd\u8868\u5355"},{value:"",label:"\u666e\u901a\u4efb\u52a1"}],style:{// \u53ea\u6709\u4e00\u4e2a\u9009\u9879\u6682\u65f6\u9690\u85cf\ndisplay:"none"}},formItemProps:{required:createForm?true:false,// \u975e\u666e\u901a\u7c7b\u578b\u65f6\u5fc5\u586b\nrules:[{required:createForm?true:false}],noStyle:true}},{dataIndex:"title",title:"\u6807\u9898",hideInForm:true,formItemProps:{required:false,rules:[{required:false}]}},{dataIndex:"description",title:"\u8bf4\u660e",hideInForm:true,valueType:"textarea"},{dataIndex:"startTime",title:"\u5f00\u59cb\u65f6\u95f4",hideInForm:true,valueType:"dateTime",fieldProps:{format:"YYYY-MM-DD HH:00:00",showTime:{format:"HH:00:00"}}},{dataIndex:"endTime",title:"\u7ed3\u675f\u65f6\u95f4",hideInForm:true,valueType:"dateTime",fieldProps:{format:"YYYY-MM-DD HH:00:00",showTime:{format:"HH:00:00"}}},{dataIndex:"process",title:taskTitle,renderFormItem:function renderFormItem(schema){return (0,emotion_react_browser_esm_js_.jsx)(TaskProcessPicker,{user:user//\u7528\u6237\u4fe1\u606f\n,rangeError:processRangeError,onAssignmentRangeChange:onAssignmentRangeChange//\u5f53\u59d4\u6d3e\u4fe1\u606f\u53d8\u5316\u65f6\n,createorRoleType:createorRoleType//\u521b\u5efa\u8005\u7684\u8eab\u4efd\n,disabled:isUpdate,process:process,taskCheckData:taskCheckData//\u4efb\u52a1\u662f\u5426\u8d8a\u6743\n});},formItemProps:{required:true,rules:[{required:true,message:"\u81f3\u5c11\u9009\u62e9\u4e00\u4e2a\u73af\u8282"},{validator:function validator(rule,value){var err="";if(value&&value[0]&&assignmentRangeRef.current&&assignmentRangeRef.current[value[0]]&&assignmentRangeRef.current[value[0]].needTarget){err="\\u5FC5\\u987B\\u9009\\u62E9\\u81F3\\u5C11\\u4E00\\u4E2A\\u6307\\u5B9A\\u5BF9\\u8C61";}setProcessRangeError(err);return err?Promise.reject(err):Promise.resolve();}}]}},/**\n             * \u5f53\u53ea\u6709\u4e00\u4e2a\u6240\u5c5e\u673a\u6784\u65f6\uff0c\u4e0d\u8ba9\u9009\u62e9\n             */orgs&&orgs.length>1?{dataIndex:"creatorOrg",valueType:"select",title:"\u6240\u5c5e\u673a\u6784",fieldProps:{allowClear:false,clearIcon:false,defaultValue:orgs[0].name,options:orgs.map(function(org){return{label:org.name,value:org.name};})}}:{dataIndex:"creatorOrg",hideInForm:true}],title:title//onVisibleChange={visibleChangeHandler}\n,dateFormatter:function dateFormatter(v){return v.toISOString();},submitter:{render:function render(props){// \u6682\u4e0d\u652f\u6301\u4fee\u6539\u4efb\u52a1\u7684\u8868\u5355\nreturn[createForm?(0,emotion_react_browser_esm_js_.jsx)(lib_button["default"],{key:"createform",type:"primary",ghost:formData||isUpdate||formId?true:false,onClick:nextHanlder},"\\u4E0B\\u4E00\\u6B65\\uFF1A\\u914D\\u7F6E\\u8868\\u5355"):null,(0,emotion_react_browser_esm_js_.jsx)(lib_button["default"],{hidden:isUpdate||hiddenCreateButton,loading:createLoading,key:"create",onClick:isUpdate||!createForm?createTaskHandler:undefined,type:"primary",disabled:createForm&&!formData&&!isUpdate},_confirmText)];}},formRef:formRef,onFinish:/*#__PURE__*/function(){var _ref3=(0,asyncToGenerator/* default */.Z)(/*#__PURE__*/(0,regeneratorRuntime/* default */.Z)().mark(function _callee2(values){return (0,regeneratorRuntime/* default */.Z)().wrap(function _callee2$(_context2){while(1)switch(_context2.prev=_context2.next){case 0:setTaskData(updateTaskData(values));return _context2.abrupt("return",true);case 2:case"end":return _context2.stop();}},_callee2);}));return function(_x2){return _ref3.apply(this,arguments);};}(),className:cls}),(0,emotion_react_browser_esm_js_.jsx)(drawer/* default */.Z,{title:"\\u914D\\u7F6E\\u8868\\u5355",extra:showBack&&(0,emotion_react_browser_esm_js_.jsx)(lib_button["default"],{onClick:function onClick(){return window.history.go(-1);}},"\\u91CD\\u9009\\u6A21\\u7248"),bodyStyle:{padding:0,position:"relative",backgroundColor:"rgb(242, 242, 242)"},footer:(0,emotion_react_browser_esm_js_.jsx)("div",{style:{textAlign:"right"}},(0,emotion_react_browser_esm_js_.jsx)(space["default"],null,(0,emotion_react_browser_esm_js_.jsx)(lib_button["default"],{type:"primary",ghost:true,onClick:function onClick(){setCreateFormVisible(false);setPublishPanelVisible(true);}},"\\u4E0A\\u4E00\\u6B65\\uFF1A\\u586B\\u5199\\u4EFB\\u52A1"),"(",(0,emotion_react_browser_esm_js_.jsx)(lib_button["default"],{hidden:isUpdate||hiddenCreateButton,loading:createLoading,onClick:createTaskFormHandler,type:"primary"},_confirmText),")")),width:isMobile?"100vw":width,maskClosable:false,open:createFormVisible,onClose:closeFormDrawerHandler},(0,emotion_react_browser_esm_js_.jsx)(pro_layout_es/* ProlayoutIframe */.NA,{iframeRef:handleIframeRef,key:"create-form-"+formCount.current,src:service/* YB_EZFORM_URL */.GC+(formId?"?activityId=".concat(formId,"&taskId=").concat(_key,"&usedBy=task"):"?usedBy=task")})));}// export default () => {\n//   const [form] = Form.useForm();\n//   const taskTarget = useRef();\n//   return (\n//     // <Form form={form} initialValues={{ process: ["yxgly"] }}>\n//     //   <Form.Item\n//     //     required\n//     //     rules={[\n//     //       {\n//     //         required: true,\n//     //         message: "\u81f3\u5c11\u9009\u62e9\u4e00\u4e2a\u73af\u8282",\n//     //       },\n//     //       {\n//     //         validator(rule, value) {\n//     //           let err: string = "";\n//     //           if (value && value[0] && taskTarget.current && taskTarget.current[value[0]] && (taskTarget.current[value[0]] as any).needTarget) {\n//     //             err = `\u5fc5\u987b\u9009\u62e9\u81f3\u5c11\u4e00\u4e2a\u6307\u5b9a\u5bf9\u8c61`;\n//     //           }\n//     //           return err ? Promise.reject(err) : Promise.resolve();\n//     //         },\n//     //       },\n//     //     ]}\n//     //     name={"process"}\n//     //     label={"\u6d3e\u53d1\u8fc7\u7a0b"}\n//     //   >\n//     //     <TaskCreateDrawer />\n//     //   </Form.Item>\n//     //   <Form.Item name={"pp"}>\n//     //     <Input></Input>\n//     //   </Form.Item>\n//     //   <Form.Item>\n//     //     <Button\n//     //       onClick={() => {\n//     //         form.validateFields().then(() => console.log(form.getFieldsValue()));\n//     //         console.log("\u8868\u5355\u56de\u586b\u503c====", updateTaskData(form.getFieldsValue(), taskTarget));\n//     //       }}\n//     //     >\n//     //       11\n//     //     </Button>\n//     //   </Form.Item>\n//     // </Form>\n//     <TaskCreateDrawer2\n//       onCreateTask={(value) => {\n//         console.log(value);\n//         return false;\n//       }}\n//     />\n//   );\n// };\n));//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiNDU1MDQuanMiLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7O0FBQ0E7QUE2QkE7QUFDQTtBQVNBO0FBSUE7QUFFQTtBQUNBO0FBTUE7QUFDQTtBQUNBO0FBQ0E7QUFHQTtBQU1BO0FBQ0E7QUFXQTtBQUNBO0FBUUE7QUFDQTtBQXVCQTtBQUNBO0FBWUE7QUFDQTtBQXlCQTtBQUNBO0FBQ0E7QUFDQTtBQVVBO0FBQ0E7QUFDQTtBQUNBOzs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7QUNyS0E7QUFZQTtBQUNBO0FBR0E7QUFDQTtBQUlBO0FBQ0E7QUFJQTtBQUNBO0FBSUE7QUFDQTtBQUlBO0FBQ0E7QUFJQTtBQUNBO0FBS0E7QUFDQTtBQUNBO0FBQ0E7QUFRQTtBQUNBO0FBQ0E7QUFDQTtBQVFBO0FBQ0E7QUFDQTtBQUNBO0FBUUE7QUFDQTtBQUNBO0FBQ0E7QUFRQTtBQUNBO0FBQ0E7QUFDQTtBQWNBO0FBQ0E7QUFDQTtBQVFBO0FBQ0E7QUFDQTtBQUNBOzs7Ozs7Ozs7O0FDekhBO0FBcUNBO0FBQ0E7O0FDckNBOztBQ0RBO0FBZ0RBO0FBeUNBO0FBQ0E7QUFPQTtBQUdBO0FBR0E7QUFNQTtBQW9CQTtBQUNBO0FBQ0E7QUEyREE7QUEwSkE7QUFFQTtBQUdBO0FBR0E7QUFxSUE7QUEyREE7QUFrQ0E7QUFDQTtBQUNBO0FBQ0E7Ozs7O0FDcGtCQTtBQWVBO0FBQ0E7QUFDQTtBQUNBO0FBNEhBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFNQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBMERBO0FBMkdBO0FBMEVBO0FBQ0E7QUFRQTtBQTZCQTtBQUdBO0FBZ0RBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFBQTtBQTRCQTtBQUtBO0FBeURBO0FBRUE7QUFDQTtBQUdBO0FBeUJBO0FBQ0E7QUF3QkE7QUFLQTtBQWdFQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSIsInNvdXJjZXMiOlsic2VydmljZXMvZXpmb3JtLnRzIiwic2VydmljZXMvY3VjMi50cyIsImNvbXBvbmVudHMvRWFzeWZvcm1UYXNrL2NvbXBvbmVudHMvQXNzaWdubWVudFJhbmdlUGlja2VyLnRzeCIsImNvbXBvbmVudHMvRWFzeWZvcm1UYXNrL2NvbXBvbmVudHMvQXNzaWdubWVudFRhcmdldFNlbGVjdC50c3giLCJjb21wb25lbnRzL0Vhc3lmb3JtVGFzay9jb21wb25lbnRzL2luZGV4LnRzeCIsImNvbXBvbmVudHMvRWFzeWZvcm1UYXNrL2luZGV4LnRzeCJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///45504\n')},53964:function(__unused_webpack_module,__webpack_exports__,__webpack_require__){eval('/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Z: function() { return /* binding */ Loading; }\n/* harmony export */ });\n/* harmony import */ var antd_lib_spin_style__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(81753);\n/* harmony import */ var antd_lib_spin_style__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(antd_lib_spin_style__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var antd_lib_spin__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(85965);\n/* harmony import */ var _emotion_styled_base__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(70974);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(96985);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var _emotion_react__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(94216);\n/* harmony import */ var _emotion_react__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(_emotion_react__WEBPACK_IMPORTED_MODULE_3__);\nfunction _EMOTION_STRINGIFIED_CSS_ERROR__(){return"You have tried to stringify object returned from `css` function. It isn\'t supposed to be used directly (e.g. as value of the `className` prop), but rather handed to emotion so it can handle it (e.g. as value of `css` prop).";}var Container=/*#__PURE__*/(0,_emotion_styled_base__WEBPACK_IMPORTED_MODULE_1__/* ["default"] */ .Z)(\'div\', true?{target:"e9ui46v0"}:0)( true?{name:"1if2t74",styles:"width:100%;height:100%;position:absolute;top:0;left:0;display:flex;z-index:9999;align-items:center;justify-content:center"}:0);function Loading(_ref){var hide=_ref.hide;return!hide&&(0,_emotion_react__WEBPACK_IMPORTED_MODULE_3__.jsx)(Container,null,(0,_emotion_react__WEBPACK_IMPORTED_MODULE_3__.jsx)(antd_lib_spin__WEBPACK_IMPORTED_MODULE_4__/* ["default"] */ .Z,null));}//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiNTM5NjQuanMiLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7Ozs7QUFBQSIsInNvdXJjZXMiOlsiY29tcG9uZW50cy9Mb2FkaW5nL2luZGV4LmpzIl0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///53964\n')},64606:function(__unused_webpack_module,__webpack_exports__,__webpack_require__){eval('/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   h: function() { return /* binding */ AssignmentTargetFilter; }\n/* harmony export */ });\n/* unused harmony export CreatorRole */\nvar CreatorRole=/*#__PURE__*/function(CreatorRole){CreatorRole["SUPER_ADMIN"]="SUPER_ADMIN";CreatorRole["DEPART_ADMIN"]="DEPART_ADMIN";CreatorRole["ASSISTANT"]="ASSISTANT";return CreatorRole;}({});// \u4e0b\u6d3e \u548c \u5904\u7406 \u64cd\u4f5c\n/**\n * \u4efb\u52a1\u6d41\u7a0b\u9009\u62e9\u4eba\u5458\u8fc7\u6ee4\u5668\n */var AssignmentTargetFilter=/*#__PURE__*/function(AssignmentTargetFilter){AssignmentTargetFilter["SelectClassAssistantFromAcademy"]="SelectClassAssistantFromAcademy";AssignmentTargetFilter["SelectStudentFromClassAssistant"]="SelectStudentFromClassAssistant";AssignmentTargetFilter["SelectAllClassAssistant"]="SelectAllClassAssistant";AssignmentTargetFilter["SelectStudentFromAcademy"]="SelectStudentFromAcademy";AssignmentTargetFilter["SelectStudentFromClass"]="SelectStudentFromClass";AssignmentTargetFilter["SelectStudentByCurrentUser"]="SelectStudentByCurrentUser";AssignmentTargetFilter["SelectAssistantByCurrentAcademyLeader"]="SelectAssistantByCurrentAcademyLeader";AssignmentTargetFilter["SelectStudentByCurrentAcademyLeader"]="SelectStudentByCurrentAcademyLeader";return AssignmentTargetFilter;}({});// \u6839\u636e\u5f53\u524d\u767b\u9646\u9662\u7cfb\u7ba1\u7406\u5458\u7b5b\u9009\u81ea\u5df1\u9662\u7cfb\u7684\u5b66\u751f\n/**\n * ITaskProcess\n */ /**\n * task state\n */ /**\n * \u4efb\u52a1\n */ /**\n * \u521b\u5efa\u6570\u636e\u9879\n */ /**\n * \u5f85\u529e\u4e8b\u9879\n */ /**\n * \u59d4\u6d3e\u4eba\n */ /**\n * \u59d4\u6d3e\n */ /**\n * \u4efb\u52a1\u72b6\u6001\u9009\u9879\n */ /**\n * \u4e0b\u6d3e\u6d41\u7a0b\u9009\u9879\u5b9a\u4e49\n */ /**\n * \u4efb\u52a1\n */ /**\n * \u4efb\u52a1\u68c0\u6d4b\u7ed3\u679c\n *///# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiNjQ2MDYuanMiLCJtYXBwaW5ncyI6Ijs7OztBQUVBO0FBb0NBO0FBQ0E7QUFDQTtBQWVBO0FBQ0E7QUFDQTtBQWtCQTtBQUNBO0FBSUE7QUFDQTtBQU1BO0FBQ0E7QUFpQ0E7QUFDQTtBQWlCQTtBQUNBO0FBYUE7QUFDQTtBQWdCQTtBQUNBO0FBU0E7QUFDQTtBQVVBO0FBQ0E7QUFrQ0E7QUFDQSIsInNvdXJjZXMiOlsiaW50ZXJmYWNlcy90YXNrLnRzIl0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///64606\n')},31857:function(__unused_webpack_module,__webpack_exports__,__webpack_require__){eval("/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   $s: function() { return /* binding */ postActiveTask; },\n/* harmony export */   Ib: function() { return /* binding */ postTaskAssignments; },\n/* harmony export */   Uo: function() { return /* binding */ getAssignmentChain; },\n/* harmony export */   We: function() { return /* binding */ postAassignmentRead; },\n/* harmony export */   ZM: function() { return /* binding */ API_TASK; },\n/* harmony export */   _X: function() { return /* binding */ getTask; },\n/* harmony export */   _s: function() { return /* binding */ TASK_API_PREFIX; },\n/* harmony export */   r_: function() { return /* binding */ postAssignments; },\n/* harmony export */   vr: function() { return /* binding */ createTask; },\n/* harmony export */   xJ: function() { return /* binding */ updateTask; }\n/* harmony export */ });\n/* unused harmony exports getTasks, endTask, delTask, getMyTasks, getMyUndo, getMyTasksCount, getTaskAssignments, getTaskSubAssignments, getMyTaskCount, checkTask */\n/* harmony import */ var _commons_service__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(84583);\n/* harmony import */ var _sui_runtime__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(74698);\nvar _APP_CONFIG,_APP_CONFIG$server;var TASK_API_PREFIX=((_APP_CONFIG=window.APP_CONFIG)===null||_APP_CONFIG===void 0?void 0:(_APP_CONFIG$server=_APP_CONFIG.server)===null||_APP_CONFIG$server===void 0?void 0:_APP_CONFIG$server.api)+'/ybtask';var request=(0,_sui_runtime__WEBPACK_IMPORTED_MODULE_1__/* .createRequest */ .hG)({prefix:TASK_API_PREFIX});/**\n * \u4efb\u52a1\u63a5\u53e3\u5b9a\u4e49\n */var API_TASK={/**\n   * \u83b7\u53d6\u4efb\u52a1\n   */getTask:'/task/:id',/**\n   * \u83b7\u53d6\u4efb\u52a1\u5217\u8868\n   */getTasks:'/tasks',/**\n   * \u521b\u5efa\u4efb\u52a1\n   */postTask:'/task',/**\n   * \u5220\u9664\u4efb\u52a1\n   */deleteTask:'/task/:id/delete',/**\n   * \u83b7\u53d6\u6211\u7684\u4efb\u52a1\n   */getMyTasks:'/my/tasks',/**\n   * \u83b7\u53d6\u6211\u7684\u4efb\u52a1\u5f85\u529e\uff08\u59d4\u6d3e\uff09\n   */getMyAssignments:'/my/assignments',/**\n   * \u83b7\u53d6\u6211\u7684\u4efb\u52a1\u7edf\u8ba1\n   */getMyTaskCount:'/my/task/count',/**\n   * \u83b7\u53d6\u4efb\u52a1\u4e0b\u7684\u6240\u6709\u59d4\u6d3e\n   */getTaskAssignments:'/task/:id/assignments',/**\n   * \u83b7\u53d6\u4efb\u52a1\u59d4\u6d3e\u4e0b\u7684\u5b50\u59d4\u6d3e\n   */getTaskSubAssignments:'/assignment/:id/assignments',/**\n   * \u521b\u5efa\u4efb\u52a1\u7684\u59d4\u6d3e\n   */postTaskAssignments:'/task/:id/assignment',/**\n   * \u521b\u5efa\u4efb\u52a1\u59d4\u6d3e\u7684\u5b50\u59d4\u6d3e\n   */postAssignments:'/assignment/:id/assignment',/**\n   * \u8bbe\u7f6e\u59d4\u6d3e\u5df2\u8bfb\u72b6\u6001\n   */postAassignmentRead:'/my/assignment/:id/read',/**\n   * \u66f4\u65b0\u4efb\u52a1\n   */updateTask:'/task/:id/update',/**\n   * \u8bbe\u7f6e\u4efb\u52a1\u4e3a\u5b8c\u6210\n   */endTask:'/task/:id/end',/**\n   * \u83b7\u53d6\u59d4\u6d3e\u94fe\n   */getAssignmentChain:'/assignment/:id/chain',/**\n   * \u6fc0\u6d3b\u4efb\u52a1\n   */postActiveTask:'/task/:id/active',/**\n   * \u68c0\u67e5\u4efb\u52a1\u662f\u5426\u8d8a\u6743\n   */checkTask:'/task/check'};/**\n * \u63a5\u53e3 PATH Parmas\n */ /**\n * \u83b7\u53d6\u4efb\u52a1\u8be6\u60c5\n * @param params\n * @returns\n */function getTask(params){return request(API_TASK.getTask,params);}/**\n * \u83b7\u53d6\u4efb\u52a1\u5217\u8868\n * @param params\n * @returns\n */function getTasks(params){return request(API_TASK.getTasks,params);}/**\n * \u521b\u5efa\u4efb\u52a1\n * @param params\n * @returns\n */function createTask(params){return request.post(API_TASK.postTask,params);}/**\n * \u66f4\u65b0\u4efb\u52a1\n * @param params\n * @returns\n */function updateTask(params){return request.post(API_TASK.updateTask,params);}/**\n * \u8bbe\u7f6e\u4efb\u52a1\u72b6\u6001\u4e3a\u5b8c\u6210\n * @param params\n * @returns\n */function endTask(params){var req=request.post(API_TASK.endTask,params).then(postSuccess);req.catch(postFail);return req;}/**\n * \u5220\u9664\u4efb\u52a1\n * @param params\n * @returns\n */function delTask(params){var req=request.post(API_TASK.deleteTask,params).then(postSuccess);req.catch(postFail);return req;}/**\n * \u83b7\u53d6\u6211\u521b\u5efa\u7684\u4efb\u52a1\n * @param params\n * @returns\n */function getMyTasks(params){return request(API_TASK.getMyTasks,params);}/**\n * \u83b7\u53d6\u6211\u7684\u5f85\u529e\n * @param params\n */function getMyUndo(params){return request(API_TASK.getMyAssignments,params);}/**\n * \u83b7\u53d6\u5f85\u529e\u6570\u91cf\n * @param params\n * @returns\n */function getMyTasksCount(params){return request(API_TASK.getMyTaskCount,params);}/**\n * \u83b7\u53d6\u4efb\u52a1\u7684\u6240\u6709\u59d4\u6d3e\n * @param params\n * @returns\n */function getTaskAssignments(params){return request(API_TASK.getTaskAssignments,params);}/**\n * \u83b7\u53d6\u4efb\u52a1\u7684\u4e00\u5c42\u59d4\u6d3e\n * @param params\n */function getTaskSubAssignments(params){return request(API_TASK.getTaskSubAssignments,params);}/**\n * \u67e5\u8be2\u6211\u7684\u5f85\u529e\u6570\u91cf\n * @param params\n */function getMyTaskCount(params){return request(API_TASK.getMyTaskCount,params);}/**\n * \u59d4\u6d3e\n * @param params\n * @returns\n */function postTaskAssignments(params){var req=request.post(API_TASK.postTaskAssignments,params).then(_commons_service__WEBPACK_IMPORTED_MODULE_0__/* .postSuccess */ .Mw);req.catch(_commons_service__WEBPACK_IMPORTED_MODULE_0__/* .postFail */ .CQ);return req;}/**\n * \u59d4\u6d3e\n * @param params\n * @returns\n */function postAssignments(params){var req=request.post(API_TASK.postAssignments,params).then(_commons_service__WEBPACK_IMPORTED_MODULE_0__/* .postSuccess */ .Mw);req.catch(_commons_service__WEBPACK_IMPORTED_MODULE_0__/* .postFail */ .CQ);return req;}/**\n * \u5df2\u8bfb\u59d4\u6d3e\n * @param params\n * @returns\n */function postAassignmentRead(params){// \u65e0\u4ea4\u4e92\u7684\u9759\u9ed8\u8bf7\u6c42\uff0c\u4e0d\u63d0\u9192\nreturn request.post(API_TASK.postAassignmentRead,params);}/**\n * \u83b7\u53d6\u59d4\u6d3e\u94fe\n * @param params\n * @returns\n */function getAssignmentChain(params){return request(API_TASK.getAssignmentChain,params);}/**\n * \u6fc0\u6d3b\u4efb\u52a1\n * @param params\n * @returns\n */function postActiveTask(params){var req=request.post(API_TASK.postActiveTask,params).then(_commons_service__WEBPACK_IMPORTED_MODULE_0__/* .postSuccess */ .Mw);req.catch(_commons_service__WEBPACK_IMPORTED_MODULE_0__/* .postFail */ .CQ);return req;}/**\n * \u68c0\u6d4b\u4efb\u52a1\u662f\u5426\u8d8a\u6743\n * @param params\n * @returns\n */function checkTask(params){return request.post(API_TASK.checkTask,params);}//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiMzE4NTcuanMiLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7Ozs7Ozs7O0FBRUE7QUFVQTtBQUNBO0FBR0E7QUFDQTtBQUdBO0FBQ0E7QUFHQTtBQUNBO0FBR0E7QUFDQTtBQUdBO0FBQ0E7QUFHQTtBQUNBO0FBR0E7QUFDQTtBQUdBO0FBQ0E7QUFHQTtBQUNBO0FBR0E7QUFDQTtBQUdBO0FBQ0E7QUFHQTtBQUNBO0FBSUE7QUFDQTtBQUlBO0FBQ0E7QUFJQTtBQUNBO0FBSUE7QUFDQTtBQUlBO0FBQ0E7QUFLQTtBQUNBO0FBU0E7QUFDQTtBQUNBO0FBQ0E7QUFNQTtBQUNBO0FBQ0E7QUFDQTtBQU1BO0FBQ0E7QUFDQTtBQUNBO0FBTUE7QUFDQTtBQUNBO0FBQ0E7QUFNQTtBQUNBO0FBQ0E7QUFDQTtBQVFBO0FBQ0E7QUFDQTtBQUNBO0FBUUE7QUFDQTtBQUNBO0FBQ0E7QUFNQTtBQUNBO0FBQ0E7QUFNQTtBQUNBO0FBQ0E7QUFDQTtBQU1BO0FBQ0E7QUFDQTtBQUNBO0FBTUE7QUFDQTtBQUNBO0FBTUE7QUFDQTtBQUNBO0FBTUE7QUFDQTtBQUNBO0FBQ0E7QUFRQTtBQUNBO0FBQ0E7QUFDQTtBQVFBO0FBQ0E7QUFDQTtBQUNBO0FBR0E7QUFJQTtBQUNBO0FBQ0E7QUFDQTtBQU1BO0FBQ0E7QUFDQTtBQUNBO0FBUUE7QUFDQTtBQUNBO0FBQ0EiLCJzb3VyY2VzIjpbInNlcnZpY2VzL3Rhc2sudHMiXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///31857\n")}}]);