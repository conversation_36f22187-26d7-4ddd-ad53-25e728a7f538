"use strict";(self.webpackChunkyiban_frontend_admin=self.webpackChunkyiban_frontend_admin||[]).push([[683,864],{68864:function(__unused_webpack_module,__webpack_exports__,__webpack_require__){eval('\n// EXPORTS\n__webpack_require__.d(__webpack_exports__, {\n  Z: function() { return /* binding */ es_useSize; }\n});\n\n// EXTERNAL MODULE: ./node_modules/tslib/tslib.es6.mjs\nvar tslib_es6 = __webpack_require__(75971);\n// EXTERNAL MODULE: ./node_modules/resize-observer-polyfill/dist/ResizeObserver.es.js\nvar ResizeObserver_es = __webpack_require__(90474);\n// EXTERNAL MODULE: consume shared module (default) react@^16.8.0 || ^17.0.0 || ^18.0.0 (singleton) (fallback: ./node_modules/react/index.js) (eager)\nvar index_js_eager_ = __webpack_require__(27015);\n// EXTERNAL MODULE: ./node_modules/ahooks/es/useUnmount/index.js\nvar useUnmount = __webpack_require__(43030);\n;// CONCATENATED MODULE: ./node_modules/ahooks/es/useRafState/index.js\n\n\n\nfunction useRafState(initialState) {\n  var ref = (0,index_js_eager_.useRef)(0);\n  var _a = (0,tslib_es6/* __read */.CR)((0,index_js_eager_.useState)(initialState), 2),\n    state = _a[0],\n    setState = _a[1];\n  var setRafState = (0,index_js_eager_.useCallback)(function (value) {\n    cancelAnimationFrame(ref.current);\n    ref.current = requestAnimationFrame(function () {\n      setState(value);\n    });\n  }, []);\n  (0,useUnmount/* default */.Z)(function () {\n    cancelAnimationFrame(ref.current);\n  });\n  return [state, setRafState];\n}\n/* harmony default export */ var es_useRafState = (useRafState);\n// EXTERNAL MODULE: ./node_modules/ahooks/es/utils/domTarget.js\nvar domTarget = __webpack_require__(41586);\n// EXTERNAL MODULE: ./node_modules/ahooks/es/utils/isBrowser.js\nvar isBrowser = __webpack_require__(1226);\n// EXTERNAL MODULE: ./node_modules/ahooks/es/utils/useEffectWithTarget.js\nvar useEffectWithTarget = __webpack_require__(28143);\n// EXTERNAL MODULE: ./node_modules/ahooks/es/utils/createEffectWithTarget.js\nvar createEffectWithTarget = __webpack_require__(33931);\n;// CONCATENATED MODULE: ./node_modules/ahooks/es/utils/useLayoutEffectWithTarget.js\n\n\nvar useLayoutEffectWithTarget_useEffectWithTarget = (0,createEffectWithTarget/* default */.Z)(index_js_eager_.useLayoutEffect);\n/* harmony default export */ var useLayoutEffectWithTarget = (useLayoutEffectWithTarget_useEffectWithTarget);\n;// CONCATENATED MODULE: ./node_modules/ahooks/es/utils/useIsomorphicLayoutEffectWithTarget.js\n\n\n\nvar useIsomorphicLayoutEffectWithTarget = isBrowser/* default */.Z ? useLayoutEffectWithTarget : useEffectWithTarget/* default */.Z;\n/* harmony default export */ var utils_useIsomorphicLayoutEffectWithTarget = (useIsomorphicLayoutEffectWithTarget);\n;// CONCATENATED MODULE: ./node_modules/ahooks/es/useSize/index.js\n\n\n\n\n\nfunction useSize(target) {\n  var _a = (0,tslib_es6/* __read */.CR)(es_useRafState(function () {\n      var el = (0,domTarget/* getTargetElement */.n)(target);\n      return el ? {\n        width: el.clientWidth,\n        height: el.clientHeight\n      } : undefined;\n    }), 2),\n    state = _a[0],\n    setState = _a[1];\n  utils_useIsomorphicLayoutEffectWithTarget(function () {\n    var el = (0,domTarget/* getTargetElement */.n)(target);\n    if (!el) {\n      return;\n    }\n    var resizeObserver = new ResizeObserver_es["default"](function (entries) {\n      entries.forEach(function (entry) {\n        var _a = entry.target,\n          clientWidth = _a.clientWidth,\n          clientHeight = _a.clientHeight;\n        setState({\n          width: clientWidth,\n          height: clientHeight\n        });\n      });\n    });\n    resizeObserver.observe(el);\n    return function () {\n      resizeObserver.disconnect();\n    };\n  }, [], target);\n  return state;\n}\n/* harmony default export */ var es_useSize = (useSize);//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiNjg4NjQuanMiLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7Ozs7Ozs7O0FBQUE7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTs7Ozs7Ozs7OztBQ25CQTtBQUNBO0FBQ0E7QUFDQTs7QUNIQTtBQUNBO0FBQ0E7QUFDQTtBQUNBOztBQ0pBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSIsInNvdXJjZXMiOlsiLi4vbm9kZV9tb2R1bGVzL2Fob29rcy9lcy91c2VSYWZTdGF0ZS9pbmRleC5qcyIsIi4uL25vZGVfbW9kdWxlcy9haG9va3MvZXMvdXRpbHMvdXNlTGF5b3V0RWZmZWN0V2l0aFRhcmdldC5qcyIsIi4uL25vZGVfbW9kdWxlcy9haG9va3MvZXMvdXRpbHMvdXNlSXNvbW9ycGhpY0xheW91dEVmZmVjdFdpdGhUYXJnZXQuanMiLCIuLi9ub2RlX21vZHVsZXMvYWhvb2tzL2VzL3VzZVNpemUvaW5kZXguanMiXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///68864\n')},33931:function(__unused_webpack_module,__webpack_exports__,__webpack_require__){eval('/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(27015);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _useUnmount__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(43030);\n/* harmony import */ var _depsAreSame__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(48179);\n/* harmony import */ var _domTarget__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(41586);\n\n\n\n\nvar createEffectWithTarget = function createEffectWithTarget(useEffectType) {\n  /**\n   *\n   * @param effect\n   * @param deps\n   * @param target target should compare ref.current vs ref.current, dom vs dom, ()=>dom vs ()=>dom\n   */\n  var useEffectWithTarget = function useEffectWithTarget(effect, deps, target) {\n    var hasInitRef = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)(false);\n    var lastElementRef = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)([]);\n    var lastDepsRef = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)([]);\n    var unLoadRef = (0,react__WEBPACK_IMPORTED_MODULE_0__.useRef)();\n    useEffectType(function () {\n      var _a;\n      var targets = Array.isArray(target) ? target : [target];\n      var els = targets.map(function (item) {\n        return (0,_domTarget__WEBPACK_IMPORTED_MODULE_1__/* .getTargetElement */ .n)(item);\n      });\n      // init run\n      if (!hasInitRef.current) {\n        hasInitRef.current = true;\n        lastElementRef.current = els;\n        lastDepsRef.current = deps;\n        unLoadRef.current = effect();\n        return;\n      }\n      if (els.length !== lastElementRef.current.length || !(0,_depsAreSame__WEBPACK_IMPORTED_MODULE_2__/* ["default"] */ .Z)(els, lastElementRef.current) || !(0,_depsAreSame__WEBPACK_IMPORTED_MODULE_2__/* ["default"] */ .Z)(deps, lastDepsRef.current)) {\n        (_a = unLoadRef.current) === null || _a === void 0 ? void 0 : _a.call(unLoadRef);\n        lastElementRef.current = els;\n        lastDepsRef.current = deps;\n        unLoadRef.current = effect();\n      }\n    });\n    (0,_useUnmount__WEBPACK_IMPORTED_MODULE_3__/* ["default"] */ .Z)(function () {\n      var _a;\n      (_a = unLoadRef.current) === null || _a === void 0 ? void 0 : _a.call(unLoadRef);\n      // for react-refresh\n      hasInitRef.current = false;\n    });\n  };\n  return useEffectWithTarget;\n};\n/* harmony default export */ __webpack_exports__.Z = (createEffectWithTarget);//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiMzM5MzEuanMiLCJtYXBwaW5ncyI6Ijs7Ozs7QUFBQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBIiwic291cmNlcyI6WyIuLi9ub2RlX21vZHVsZXMvYWhvb2tzL2VzL3V0aWxzL2NyZWF0ZUVmZmVjdFdpdGhUYXJnZXQuanMiXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///33931\n')},41586:function(__unused_webpack_module,__webpack_exports__,__webpack_require__){eval("/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   n: function() { return /* binding */ getTargetElement; }\n/* harmony export */ });\n/* harmony import */ var _index__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(667);\n/* harmony import */ var _isBrowser__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(1226);\n\n\nfunction getTargetElement(target, defaultElement) {\n  if (!_isBrowser__WEBPACK_IMPORTED_MODULE_0__/* [\"default\"] */ .Z) {\n    return undefined;\n  }\n  if (!target) {\n    return defaultElement;\n  }\n  var targetElement;\n  if ((0,_index__WEBPACK_IMPORTED_MODULE_1__/* .isFunction */ .mf)(target)) {\n    targetElement = target();\n  } else if ('current' in target) {\n    targetElement = target.current;\n  } else {\n    targetElement = target;\n  }\n  return targetElement;\n}//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiNDE1ODYuanMiLCJtYXBwaW5ncyI6Ijs7Ozs7QUFBQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFDQSIsInNvdXJjZXMiOlsiLi4vbm9kZV9tb2R1bGVzL2Fob29rcy9lcy91dGlscy9kb21UYXJnZXQuanMiXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///41586\n")},28143:function(__unused_webpack_module,__webpack_exports__,__webpack_require__){eval('/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(27015);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var _createEffectWithTarget__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(33931);\n\n\nvar useEffectWithTarget = (0,_createEffectWithTarget__WEBPACK_IMPORTED_MODULE_1__/* ["default"] */ .Z)(react__WEBPACK_IMPORTED_MODULE_0__.useEffect);\n/* harmony default export */ __webpack_exports__.Z = (useEffectWithTarget);//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiMjgxNDMuanMiLCJtYXBwaW5ncyI6Ijs7O0FBQUE7QUFDQTtBQUNBO0FBQ0EiLCJzb3VyY2VzIjpbIi4uL25vZGVfbW9kdWxlcy9haG9va3MvZXMvdXRpbHMvdXNlRWZmZWN0V2l0aFRhcmdldC5qcyJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///28143\n')},80184:function(module,__unused_webpack_exports,__webpack_require__){eval("\n\nif (true) {\n  module.exports = __webpack_require__(66374);\n} else {}//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiODAxODQuanMiLCJtYXBwaW5ncyI6IkFBQUE7O0FBRUE7QUFDQTtBQUNBIiwic291cmNlcyI6WyIuLi9ub2RlX21vZHVsZXMvcmVhY3QvanN4LXJ1bnRpbWUuanMiXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///80184\n")}}]);