"use strict";(self.webpackChunkyiban_frontend_admin=self.webpackChunkyiban_frontend_admin||[]).push([[516],{14522:function(__unused_webpack_module,__webpack_exports__,__webpack_require__){eval('// ESM COMPAT FLAG\n__webpack_require__.r(__webpack_exports__);\n\n// EXPORTS\n__webpack_require__.d(__webpack_exports__, {\n  "default": function() { return /* binding */ TaskDetailPage; }\n});\n\n// EXTERNAL MODULE: ./node_modules/@babel/runtime/helpers/esm/objectWithoutProperties.js\nvar objectWithoutProperties = __webpack_require__(45987);\n// EXTERNAL MODULE: ./node_modules/antd/lib/space/style/index.js\nvar style = __webpack_require__(40898);\n// EXTERNAL MODULE: ./node_modules/antd/lib/space/index.js\nvar space = __webpack_require__(68747);\n// EXTERNAL MODULE: ./node_modules/antd/lib/button/style/index.js\nvar button_style = __webpack_require__(42844);\n// EXTERNAL MODULE: ./node_modules/antd/lib/button/index.js\nvar lib_button = __webpack_require__(59235);\n// EXTERNAL MODULE: ./node_modules/antd/lib/alert/style/index.js\nvar alert_style = __webpack_require__(43604);\n// EXTERNAL MODULE: ./node_modules/antd/lib/alert/index.js\nvar lib_alert = __webpack_require__(19713);\n// EXTERNAL MODULE: ./node_modules/antd/lib/badge/style/index.js\nvar badge_style = __webpack_require__(56597);\n// EXTERNAL MODULE: ./node_modules/antd/lib/badge/index.js\nvar badge = __webpack_require__(58620);\n// EXTERNAL MODULE: ./node_modules/antd/lib/result/style/index.js\nvar result_style = __webpack_require__(28598);\n// EXTERNAL MODULE: ./node_modules/antd/lib/result/index.js\nvar result = __webpack_require__(77114);\n// EXTERNAL MODULE: ./node_modules/antd/lib/tooltip/style/index.js\nvar tooltip_style = __webpack_require__(63933);\n// EXTERNAL MODULE: ./node_modules/antd/lib/tooltip/index.js\nvar tooltip = __webpack_require__(23195);\n// EXTERNAL MODULE: ./node_modules/antd/lib/typography/style/index.js\nvar typography_style = __webpack_require__(19049);\n// EXTERNAL MODULE: ./node_modules/antd/lib/typography/index.js\nvar typography = __webpack_require__(36153);\n// EXTERNAL MODULE: ./node_modules/antd/lib/notification/style/index.js\nvar notification_style = __webpack_require__(11474);\n// EXTERNAL MODULE: ./node_modules/antd/lib/notification/index.js\nvar notification = __webpack_require__(87073);\n// EXTERNAL MODULE: ./node_modules/@babel/runtime/helpers/esm/objectSpread2.js\nvar objectSpread2 = __webpack_require__(1413);\n// EXTERNAL MODULE: ./node_modules/@babel/runtime/helpers/esm/toConsumableArray.js + 2 modules\nvar toConsumableArray = __webpack_require__(93433);\n// EXTERNAL MODULE: ./node_modules/antd/lib/message/style/index.js\nvar message_style = __webpack_require__(9430);\n// EXTERNAL MODULE: ./node_modules/antd/lib/message/index.js\nvar message = __webpack_require__(41691);\n// EXTERNAL MODULE: ./node_modules/@babel/runtime/helpers/esm/slicedToArray.js + 1 modules\nvar slicedToArray = __webpack_require__(29439);\n// EXTERNAL MODULE: ./src/services/task.ts\nvar task = __webpack_require__(31857);\n// EXTERNAL MODULE: ./node_modules/@sui/pro-layout/es/index.js + 71 modules\nvar es = __webpack_require__(30555);\n// EXTERNAL MODULE: ./node_modules/@sui/runtime/dist/index.esm.js + 2 modules\nvar index_esm = __webpack_require__(74698);\n// EXTERNAL MODULE: ./node_modules/@sui/sui/dist/index.esm.js\nvar dist_index_esm = __webpack_require__(53273);\n// EXTERNAL MODULE: ./node_modules/antd/lib/spin/style/index.js\nvar spin_style = __webpack_require__(81753);\n// EXTERNAL MODULE: ./node_modules/antd/lib/spin/index.js\nvar spin = __webpack_require__(85965);\n// EXTERNAL MODULE: consume shared module (default) react@^17.0.2 (singleton) (fallback: ./node_modules/react/index.js) (eager)\nvar index_js_eager_ = __webpack_require__(96985);\n// EXTERNAL MODULE: ./src/components/Loading/index.js\nvar Loading = __webpack_require__(53964);\n// EXTERNAL MODULE: consume shared module (default) @emotion/react@^11.10.5 (singleton) (fallback: ./node_modules/@emotion/react/dist/emotion-react.browser.esm.js)\nvar emotion_react_browser_esm_js_ = __webpack_require__(94216);\n;// CONCATENATED MODULE: ./src/components/PageLoading/index.jsx\nfunction PageLoading(){return (0,emotion_react_browser_esm_js_.jsx)(spin/* default */.Z,null,(0,emotion_react_browser_esm_js_.jsx)("div",{style:{height:"70vh",maxHeight:400}}));}\n// EXTERNAL MODULE: ./node_modules/@sui/pro-table/es/index.js + 271 modules\nvar pro_table_es = __webpack_require__(83229);\n// EXTERNAL MODULE: ./node_modules/antd/lib/steps/style/index.js\nvar steps_style = __webpack_require__(21107);\n// EXTERNAL MODULE: ./node_modules/antd/lib/steps/index.js\nvar steps = __webpack_require__(20089);\n// EXTERNAL MODULE: ./node_modules/antd/lib/descriptions/style/index.js\nvar descriptions_style = __webpack_require__(87034);\n// EXTERNAL MODULE: ./node_modules/antd/lib/descriptions/index.js\nvar descriptions = __webpack_require__(51277);\n// EXTERNAL MODULE: ./node_modules/moment/moment.js\nvar moment = __webpack_require__(60438);\nvar moment_default = /*#__PURE__*/__webpack_require__.n(moment);\n// EXTERNAL MODULE: ./src/commons/task.ts\nvar commons_task = __webpack_require__(62694);\n// EXTERNAL MODULE: ./node_modules/@emotion/css/dist/emotion-css.esm.js + 1 modules\nvar emotion_css_esm = __webpack_require__(57847);\n;// CONCATENATED MODULE: ./src/commons/user.ts\n/**\n * \u5224\u65ad\u662f\u5426\u662f\u8f85\u5bfc\u5458\n * @param identity \n * @returns \n */function isFdyUser(identity){return identity==5;}/**\n * \u5224\u65ad\u662f\u5426\u662f\u6821\u9886\u5bfc\n */function isSchoolLeader(identity){return identity===9;}/**\n * \u6821\u7ea7\u7528\u6237\n * @param identity \n * @returns \n */function isSchoolLevelUser(identity){return identity==4||identity==6||identity==9;}/**\n * \u5224\u65ad\u662f\u5426\u662f\u90e8\u95e8\u7ba1\u7406\u5458\n * @param identity \n * @returns \n */function isDepartAdminUser(identity){return identity==3||identity===7||identity===8;}/**\n * \u5224\u65ad\u662f\u5426\u662f\u5b66\u9662\u7ea7\u7528\u6237\n */function isAcademyLevelUser(identity){return identity==3||identity==7||identity==8||identity==11;}/**\n * \u5224\u65ad\u662f\u5426\u662f\u5b66\u5de5\u529e\u4e3b\u4efb\n */function isStudentOfficeDirector(identity){return identity==3;}/**\n * \u5b66\u9662\u526f\u4e66\u8bb0\n * @param identity \n * @returns \n */function isAcademyDeputySecretary(identity){return identity==7;}/**\n * \u5224\u65ad\u662f\u5426\u662f\u5b66\u5de5\u7ba1\u7406\u5458\n * @param identity \n * @returns \n */function isXgAdminUser(identity){return identity==6;}/**\n * \u5224\u65ad\u662f\u5426\u6821\u7ea7\u7ba1\u7406\u5458\n * @param identity \n * @returns \n */function isSchoolAdmin(identity){return identity==6;}/**\n * \u5224\u65ad\u662f\u5426\u662f\u6559\u804c\u5de5\n * @param identity \n * @returns \n */function isStaffUser(identity){return identity==2;}/**\n * \u5224\u65ad\u662f\u5426\u662f\u5b66\u751f\n * @param identity \n * @returns \n */function isStudentUser(identity){return identity==1;}/**\n * \u5224\u65ad\u662f\u5426\u662f\u8d85\u7ea7\u7ba1\u7406\u5458\n * @param identity \n * @returns \n */function isSupperUser(identity){return identity==4;}/**\n * \u83b7\u53d6\u8eab\u4efd\u7684\u89d2\u8272\n * @param identity \n */function user_getIdentityRole(identity){return"user";}/**\n * \u5224\u65ad\u9662\u7cfb\u9886\u5bfc\n * @param identity \n */function isAcademyLeader(identity){return identity==11;}/**\n * \u5224\u65ad\u90e8\u95e8\u9886\u5bfc\n * @param identity \n */function isDepartLeader(identity){return identity==10;}/**\n * \u5b66\u9662\u56e2\u59d4\u4e66\u8bb0\n * @param identity \n * @returns \n */function isAcademyLeagueSecretary(identity){return identity==8;}/**\n * @name \u5176\u4ed6\u7528\u6237\n */function isOtherUser(identity){return identity==12;}/**\n * \u7528\u6237\u5bf9\u8c61\n */var USER={};/**\n * \u66f4\u65b0\u7528\u6237\n * @param user \n * @returns \n */function updateUSER(user){Object.assign(USER,user);return USER;}/**\n * \u83b7\u53d6\u7528\u6237\u6570\u636e\n * @returns \n */function getUser(){return USER;}/**\n * ACCESS\n */var ACCESS={};/**\n * \u66f4\u65b0ACCESS\n * @param access \n * @returns \n */function updateACCESS(access){Object.assign(ACCESS,access);return ACCESS;}/**\n * \u83b7\u53d6access\n * @returns \n */function getAccess(){return ACCESS;}\n;// CONCATENATED MODULE: ./src/commons/releaseApp.ts\nvar RELEASEAPP_ID_KEY="appReleaseId";var releaseApp_RELEASEAPP_IdentityTypesMap={1:"\u5b66\u751f",2:"\u6559\u804c\u5de5",3:"\u5b66\u9662\u5b66\u5de5\u529e\u4e3b\u4efb",4:"\u7cfb\u7edf\u7ba1\u7406\u5458",5:"\u8f85\u5bfc\u5458",6:"\u5b66\u5de5\u7ba1\u7406\u5458",7:"\u5b66\u9662\u526f\u4e66\u8bb0"};var RELEASEAPP_StatesMap={0:"\u672a\u4e0a\u67b6",1:"\u5df2\u4e0a\u67b6"};/**\n * \u53d1\u5e03\u5bf9\u8c61\u9009\u9879\n */var releaseApp_RELEASEAPP_IdentityTypesOptions=Object.keys(releaseApp_RELEASEAPP_IdentityTypesMap).map(function(key){return{label:releaseApp_RELEASEAPP_IdentityTypesMap[key],value:Number(key)};});\n;// CONCATENATED MODULE: ./src/commons/app.ts\nvar APP_ID_KEY=\'app_id\';var APP_STATE=/*#__PURE__*/function(APP_STATE){APP_STATE[APP_STATE["unpublish"]=0]="unpublish";APP_STATE[APP_STATE["published"]=1]="published";return APP_STATE;}({});var APP_StatesMap=[{label:"\u5df2\u4e0a\u67b6",value:1},{label:"\u5df2\u4e0b\u67b6",value:0}];var Identity={1:{id:1,identityType:1,name:\'\u5b66\u751f\',role:\'user\',sort:10},2:{id:2,identityType:2,name:\'\u6559\u804c\u5de5\',role:\'user\',sort:20},3:{id:3,identityType:3,name:\'\u5b66\u9662\u5b66\u5de5\u529e\u4e3b\u4efb\',role:\'depAdmin\',sort:40},4:{id:4,identityType:4,name:\'\u7cfb\u7edf\u7ba1\u7406\u5458\',role:\'admin\',sort:90},5:{id:5,identityType:5,name:\'\u8f85\u5bfc\u5458\',role:\'counselor\',sort:30},6:{id:6,identityType:6,name:\'\u5b66\u5de5\u7ba1\u7406\u5458\',role:\'admin\',sort:50},7:{id:7,identityType:7,name:\'\u5b66\u9662\u526f\u4e66\u8bb0\',role:\'depAdmin\',sort:45}};/**\n * \u66f4\u65b0\u8eab\u4efd\u5217\u8868\u6570\u636e\n * @param items \n */function updateIdentity(items){if(items&&items.length){RELEASEAPP_IdentityTypesOptions.splice(0,RELEASEAPP_IdentityTypesOptions.length);// \u6e05\u7a7a\u539f\u6765\u624b\u52a8\u5b9a\u4e49\u7684\uff0c\u5e76\u4fdd\u5b58\u539f\u6570\u7ec4\u5bf9\u8c61\uff0c\u4fdd\u6301\u5f15\u7528\u5173\u7cfb\nitems.sort(function(a,b){return a.sort-b.sort;});var _loop=function _loop(){var identity=items[index];identity.role=identity.role||getIdentityRole(identity.identityType);if(Identity[identity.identityType]){identity.role=Identity[identity.identityType].role||identity.role;Object.assign(Identity[identity.identityType],identity);}else{Identity[identity.identityType]=identity;}RELEASEAPP_IdentityTypesMap[identity.identityType]=identity.name||RELEASEAPP_IdentityTypesMap[identity.identityType];var idendityOption=RELEASEAPP_IdentityTypesOptions.find(function(item){return item.value===identity.identityType;});if(idendityOption){idendityOption.label=identity.name||idendityOption.label;}else{RELEASEAPP_IdentityTypesOptions.push({label:identity.name,value:identity.identityType});}};for(var index=0;index<items.length;index++){_loop();}}}\n;// CONCATENATED MODULE: ./src/components/TaskDetail/index.tsx\nfunction _EMOTION_STRINGIFIED_CSS_ERROR__(){return"You have tried to stringify object returned from `css` function. It isn\'t supposed to be used directly (e.g. as value of the `className` prop), but rather handed to emotion so it can handle it (e.g. as value of `css` prop).";}var cls=/*#__PURE__*/(0,emotion_css_esm/* css */.iv)( true?{name:"1uxq3aj",styles:".ant-steps-icon-dot{background-color:#faad14!important;}"}:0);/**\n * \u83b7\u53d6\u672a\u6fc0\u6d3b\u4efb\u52a1\u72b6\u6001\n */function getNotActivityedTaskState(privilegeApprove){if(!privilegeApprove){return{status:\'warning\',text:\'\u672a\u6fc0\u6d3b\'};}return{status:privilegeApprove.done?privilegeApprove.accept?\'success\':\'error\':\'processing\',text:privilegeApprove.done?privilegeApprove.accept?\'\u5df2\u901a\u8fc7\':\'\u672a\u901a\u8fc7\':"\\u5F85".concat(privilegeApprove.approverName,"\\u5BA1\\u6279")};}/* harmony default export */ var TaskDetail = (function(props){var data=props.data,stepPropsFilter=props.stepPropsFilter,stepsStyle=props.stepsStyle,current=props.current,readOnly=props.readOnly,footer=props.footer;var title=data.title,startTime=data.startTime,active=data.active,endTime=data.endTime,creatorName=data.creatorName,creatorOrg=data.creatorOrg,state=data.state,createTime=data.createTime,creatorIdentity=data.creatorIdentity,process=data.process,description=data.description,privilegeApprove=data.privilegeApprove;var actived=active!==false;var _style=Object.assign({maxWidth:process.length*240,marginTop:10},stepsStyle);var stateProps=state&&commons_task/* StateMap */.Oh[state];if(!actived){readOnly=true;stateProps=getNotActivityedTaskState(privilegeApprove);}var customDot=function customDot(dot,_ref){var status=_ref.status,index=_ref.index;return index==current+1&&!readOnly&&actived?(0,emotion_react_browser_esm_js_.jsx)(typography/* default */.Z.Text,{className:cls,type:"warning"},dot):dot;};var _creatorOrg=(creatorOrg||"")+(creatorIdentity&&Identity[creatorIdentity]?" - ".concat(Identity[creatorIdentity].name):"");var processArr=[{id:\'create\',name:\'\u521b\u5efa\u8005\',createTime:createTime,creatorOrg:_creatorOrg,userName:creatorName,subName:creatorName}].concat(process);var cur=typeof current===\'number\'?current:-1;if(readOnly){cur=-1;}return (0,emotion_react_browser_esm_js_.jsx)(index_js_eager_.Fragment,null,(0,emotion_react_browser_esm_js_.jsx)(descriptions["default"],{column:1},(0,emotion_react_browser_esm_js_.jsx)(descriptions["default"].Item,{label:"\\u4EFB\\u52A1\\u6807\\u9898"},title),description&&(0,emotion_react_browser_esm_js_.jsx)(descriptions["default"].Item,{label:"\\u4EFB\\u52A1\\u8BF4\\u660E"},description),(0,emotion_react_browser_esm_js_.jsx)(descriptions["default"].Item,{label:"\\u521B\\u5EFA\\u65F6\\u95F4"},moment_default()(createTime).format(\'YYYY-MM-DD HH:mm:ss\')),(0,emotion_react_browser_esm_js_.jsx)(descriptions["default"].Item,{label:"\\u5F00\\u59CB\\u65F6\\u95F4"},startTime?moment_default()(startTime).format(\'YYYY-MM-DD HH:mm:ss\'):\' - \'),(0,emotion_react_browser_esm_js_.jsx)(descriptions["default"].Item,{label:"\\u7ED3\\u675F\\u65F6\\u95F4"},endTime?moment_default()(endTime).format(\'YYYY-MM-DD HH:mm:ss\'):\' - \'),stateProps&&(0,emotion_react_browser_esm_js_.jsx)(descriptions["default"].Item,{label:"\\u4EFB\\u52A1\\u72B6\\u6001"},(0,emotion_react_browser_esm_js_.jsx)(badge/* default */.Z,stateProps))),(0,emotion_react_browser_esm_js_.jsx)(steps/* default */.Z,{style:_style,progressDot:customDot,current:cur,items:processArr.map(function(item,index){var filterName;if(privilegeApprove&&index===1){if(item.assignmentTargetFilter==="SelectAllClassAssistant"){filterName="\u5168\u6821\u8f85\u5bfc\u5458";}else if(item.assignmentTargetId==="gro_bks"&&item.assignmentType==="G"){filterName="\u5168\u6821\u672c\u79d1\u751f";}else if(item.assignmentTargetFilter==="SelectStudentFromAcademy"&&item.assignmentType=="O"){filterName="\u672c\u9662\u7cfb\u672c\u79d1\u751f";}}return typeof stepPropsFilter===\'function\'&&!readOnly?stepPropsFilter(item,index):{title:item.name,subTitle:item.subName&&!index?(0,emotion_react_browser_esm_js_.jsx)("div",null,item.subName,item.creatorOrg?(0,emotion_react_browser_esm_js_.jsx)("div",null,"(",item.creatorOrg,")"):\'\'):index==1&&privilegeApprove&&filterName?(0,emotion_react_browser_esm_js_.jsx)("div",null,(0,emotion_react_browser_esm_js_.jsx)(typography/* default */.Z.Text,{type:"warning"},"(",filterName,")")):undefined};})}),footer);});\n// EXTERNAL MODULE: ./src/interfaces/task.ts\nvar interfaces_task = __webpack_require__(64606);\n// EXTERNAL MODULE: ./src/commons/service.ts\nvar service = __webpack_require__(84583);\n// EXTERNAL MODULE: ./node_modules/antd/lib/modal/style/index.js\nvar modal_style = __webpack_require__(25783);\n// EXTERNAL MODULE: ./node_modules/antd/lib/modal/index.js\nvar modal = __webpack_require__(60110);\n// EXTERNAL MODULE: ./node_modules/antd/lib/drawer/style/index.js\nvar drawer_style = __webpack_require__(95542);\n// EXTERNAL MODULE: ./node_modules/antd/lib/drawer/index.js\nvar drawer = __webpack_require__(36358);\n// EXTERNAL MODULE: ./node_modules/@babel/runtime/helpers/esm/extends.js\nvar esm_extends = __webpack_require__(87462);\n// EXTERNAL MODULE: ./node_modules/antd/lib/select/style/index.js\nvar select_style = __webpack_require__(98352);\n// EXTERNAL MODULE: ./node_modules/antd/lib/select/index.js\nvar lib_select = __webpack_require__(26469);\n// EXTERNAL MODULE: ./node_modules/@sui/rc-personnel/es/index.js + 18 modules\nvar rc_personnel_es = __webpack_require__(10900);\n;// CONCATENATED MODULE: ./src/services/cuc.ts\nvar _APP_CONFIG,_APP_CONFIG$server;var CUC_API_PREFIX=((_APP_CONFIG=window.APP_CONFIG)===null||_APP_CONFIG===void 0?void 0:(_APP_CONFIG$server=_APP_CONFIG.server)===null||_APP_CONFIG$server===void 0?void 0:_APP_CONFIG$server.api)+\'/cuc\';var request=(0,index_esm/* createRequest */.hG)({prefix:CUC_API_PREFIX});var API_CUC={/**\n   * \u83b7\u53d6CUC\u7684\u4eba\u5458\u6570\u636e\n   */getCucUserInfo:\'/user/:id\',/**\n   * \u83b7\u53d6\u4eba\u5458\u7ba1\u7406\u7684\u73ed\u7ea7\n   */getLeaderClasses:\'/classleader/:id/classes\',/**\n   * \u83b7\u53d6\u5b66\u9662\u4e0b\u7684\u8f85\u5bfc\u5458\n   */getAcademyClassLeaders:\'/academy/:id/classleader\',/**\n   * \u83b7\u53d6\u5b66\u5de5\u5c97\u4f4d\n   */getPositionsetXgbzr:\'/positionset/poss_xgbzr/person\',/**\n   * \u83b7\u53d6\u73ed\u7ea7\u4e0b\u7684\u5b66\u751f\n   */getClassStudents:\'/class/:id/students\',/**\n   * \u83b7\u53d6\u4e3b\u4f53\u4e0b\u7684\u6210\u5458\n   */getSubjectMembers:\'/subject/members\',/**\n   * \u83b7\u53d6\u8eab\u4efd\u5217\u8868\n   */getIdentityList:\'/identity/list\'};/**\n * \u83b7\u53d6\u4eba\u5458\u4fe1\u606f\n * @param params\n * @returns\n */function getCucUserInfo(params){var req=request(API_CUC.getCucUserInfo,params);req.catch(service/* postFail */.CQ);return req;}/**\n * \u83b7\u53d6\u8f85\u5bfc\u5458\u7ba1\u7406\u7684\u73ed\u7ea7\n * @param params\n * @returns\n */function getLeaderClasses(params){var req=request(API_CUC.getLeaderClasses,params);req.catch(service/* postFail */.CQ);return req;}/**\n * \u83b7\u53d6\u5b66\u5de5\u5c97\u4f4d\n * @param params\n * @returns\n */function getPositionsetXgbzr(params){var req=request(API_CUC.getPositionsetXgbzr,params);req.catch(postFail);return req;}/**\n * \u83b7\u53d6\u5b66\u9662\u4e0b\u7684\u8f85\u5bfc\u5458\n * @param params\n * @returns\n */function getAcademyClassLeaders(params){var req=request(API_CUC.getAcademyClassLeaders,params);req.catch(postFail);return req;}/**\n * \u67e5\u8be2\u73ed\u7ea7\u4e0b\u7684\u5b66\u751f\n * @param params\n * @returns\n */function getClassStudents(params){var req=request(API_CUC.getClassStudents,params);req.catch(postFail);return req;}/**\n * \u67e5\u8be2\u4e3b\u4f53\u4e0b\u7684\u6210\u5458\n * @param params\n */function getSubjectMembers(params){return request(API_CUC.getSubjectMembers,params);}/**\n * @name \u83b7\u53d6\u8eab\u4efd\u5217\u8868\n * @param params\n * @returns\n */function getIdentityList(params){return request(API_CUC.getIdentityList,params);}\n// EXTERNAL MODULE: ./src/components/AssignmentSelector/utils.ts\nvar utils = __webpack_require__(2671);\n;// CONCATENATED MODULE: ./src/components/EasyformTaskDetail/components/AssignmentSelector.tsx\nvar _excluded=["title","width","prevProcessId","processId","assignmentData","assignmentTargetFilter","onChange","onCancel","creatorRoleType","user","userInfo","assigneeData","disabledKeys"],_excluded2=["groups","onGroupChange"];var userModalCls={padding:"10px 16px"};var userDrawerCls={padding:"10px"};function AssignmentSelector(props){var _props$title=props.title,title=_props$title===void 0?"\u9009\u62e9\u59d4\u6d3e\u4eba":_props$title,_props$width=props.width,width=_props$width===void 0?640:_props$width,prevProcessId=props.prevProcessId,processId=props.processId,assignmentData=props.assignmentData,assignmentTargetFilter=props.assignmentTargetFilter,onChange=props.onChange,onCancel=props.onCancel,creatorRoleType=props.creatorRoleType,_props$user=props.user,user=_props$user===void 0?{}:_props$user,userInfoProp=props.userInfo,assigneeData=props.assigneeData,_props$disabledKeys=props.disabledKeys,disabledKeys=_props$disabledKeys===void 0?[]:_props$disabledKeys,others=(0,objectWithoutProperties/* default */.Z)(props,_excluded);var _useState=(0,index_js_eager_.useState)(),_useState2=(0,slicedToArray/* default */.Z)(_useState,2),groupId=_useState2[0],setGroupId=_useState2[1];//const { context = {} } = useContextState();\nvar _useIsMobile=(0,es/* useIsMobile */.dD)(),_useIsMobile2=(0,slicedToArray/* default */.Z)(_useIsMobile,1),isMobile=_useIsMobile2[0];var userInfo=userInfoProp||user||{};var _useState3=(0,index_js_eager_.useState)(false),_useState4=(0,slicedToArray/* default */.Z)(_useState3,2),loading=_useState4[0],setLoading=_useState4[1];var _useState5=(0,index_js_eager_.useState)([]),_useState6=(0,slicedToArray/* default */.Z)(_useState5,2),academies=_useState6[0],setAcademies=_useState6[1];var isDepartAdminSelector=!prevProcessId&&creatorRoleType==="DEPART_ADMIN";var isSupperAdminSelector=!prevProcessId&&creatorRoleType==="SUPER_ADMIN";(0,index_js_eager_.useEffect)(function(){if(isSupperAdminSelector){setLoading(true);(0,index_esm/* request */.WY)(CUC_API_PREFIX+API_CUC.getPositionsetXgbzr).then(function(res){res.data=(0,utils/* parsePositionSet */.m)(res.data||[],disabledKeys,{childrenAttrs:[],checkable:false}).sort();setAcademies(res.data);}).finally(function(){setLoading(false);});}else{setAcademies([]);}},[isSupperAdminSelector]);var isSelectStudents=function isSelectStudents(){return /^(bks)/.test(processId)||["SelectStudentFromClassAssistant","SelectStudentFromAcademy","SelectStudentFromClass","SelectStudentByCurrentAssistant","SelectStudentByCurrentAcademyLeader"].includes(assignmentTargetFilter);};var isSelectClassLeaders=function isSelectClassLeaders(){return /^(fdy)/.test(processId)||["SelectAllClassAssistant","SelectClassAssistantFromAcademy","SelectAssistantByCurrentAcademyLeader"].includes(assignmentTargetFilter);};var userPickerProps=(0,index_js_eager_.useMemo)(function(){var assigneeGroups=assigneeData&&assigneeData.authorityScopeId?[{code:assigneeData.authorityScopeId,name:assigneeData.authorityScope}]:undefined;if(/^(create|yxgly)/.test(processId)){var api="".concat(CUC_API_PREFIX,"/positionset/poss_xgbzr/person");return{api:api,successAjax:function successAjax(res){res.data=(0,utils/* parsePositionSet */.m)(res.data||[],disabledKeys).sort();},nodeKeyAttr:"code",nodeTitleAttr:"name"};}else if(isSelectClassLeaders()){var _groups=assigneeGroups||userInfo.manageOrgs||userInfo.orgs||[];if(isSupperAdminSelector){_groups=academies;}if(assignmentData&&assignmentData.assignmentType==="O"){// \u6307\u5b9a\u5b66\u9662\n_groups=[{code:assignmentData.assignmentTargetId,name:assignmentData.assignmentTargetName}];}if(!_groups[0]){return null;}var _groupId2=groupId||_groups[0].code;var orgNode=_groups.find(function(g){return g.code==_groupId2;});var _api="".concat(CUC_API_PREFIX,"/academy/").concat(_groupId2,"/classleader");return{api:_api,groups:_groups,onGroupChange:function onGroupChange(val){setGroupId(val);},extraParams:{type:2},groupIdAttr:"code",groupNameAttr:"name",defaultGroupId:_groupId2,nodeKeyAttr:"code",nodeTitleAttr:"name",successAjax:function successAjax(res){res.data=(0,utils/* parseClassLeaderSet */.U)(res.data||[],disabledKeys,{topNode:orgNode}).sort();},beforeAjax:function beforeAjax(data){index_esm/* utils */.P6.emptyObject(data);},orgHeadTitle:"\u9662\u7cfb\u8f85\u5bfc\u5458"};}else if(isSelectStudents()){var _groups2=userInfo.manageclasses||[];if(isDepartAdminSelector){_groups2=userInfo.manageOrgs&&userInfo.manageOrgs.length?userInfo.manageOrgs:_groups2;}if(isSupperAdminSelector){_groups2=academies;}if(assignmentData&&assignmentData.assignmentType==="C"){// \u6307\u5b9a\u73ed\u7ea7\n_groups2=[{code:assignmentData.assignmentTargetId,name:assignmentData.assignmentTargetName}];}if(!_groups2[0]){return null;}var _groupId3=groupId||_groups2[0].code;var _orgNode=_groups2.find(function(g){return g.code==_groupId3;});var _api2="".concat(CUC_API_PREFIX,"/class/").concat(_groupId3,"/students");var getClassApi=index_esm/* utils */.P6.paramsUrlPath(CUC_API_PREFIX+API_CUC.getAcademyClassLeaders,{id:_groupId3});if(isDepartAdminSelector||isSupperAdminSelector){_api2=getClassApi;}var parseClassResult=function parseClassResult(res){res.data=(0,utils/* parseClassLeaderSet */.U)(res.data||[],disabledKeys,{types:[],childrenAttrs:[],checkable:false,isLeaf:false}).sort();};var parseUserResult=function parseUserResult(res){res.data=(res.data||[]).map(function(item){item.isLeaf=true;item.disabled=disabledKeys&&disabledKeys.indexOf(item.code)>-1?true:false;item.checked=item.disabled;item.topNode=_orgNode;item.parentNode=_orgNode;return item;}).sort();};var _pickerProps={api:_api2,groups:_groups2,onGroupChange:function onGroupChange(val){setGroupId(val);},groupIdAttr:"code",groupNameAttr:"name",defaultGroupId:_groupId3,nodeKeyAttr:"code",nodeTitleAttr:"name",userKeyAttr:"code",userTitleAttr:"name",successAjax:parseUserResult,extraAjaxSettings:{data:null},extraUserAjaxSettings:{data:null},search:false,orgHeadTitle:"\u73ed\u7ea7\u5b66\u751f"};if(isDepartAdminSelector||isSupperAdminSelector){_pickerProps.successAjax=function(res,node){return!node?parseClassResult(res):parseUserResult(res);};_pickerProps.orgHeadTitle="\u5b66\u9662\u73ed\u7ea7";_pickerProps.getApiUrl=function(defaultUrl,params,isSearch,keyword){if(!params.code){return index_esm/* utils */.P6.add_url_query(defaultUrl,{type:2});}return index_esm/* utils */.P6.paramsUrlPath("".concat(CUC_API_PREFIX,"/class/:id/students"),{id:params.code});};}return _pickerProps;}return null;},[processId,groupId,disabledKeys,assigneeData,creatorRoleType,prevProcessId,academies,isDepartAdminSelector,isSupperAdminSelector]);var cancelHandler=(0,index_js_eager_.useCallback)(function(e){if(typeof onCancel==="function"){onCancel(e);}setGroupId(null);},[onCancel]);var _ref=userPickerProps||{},_ref$groups=_ref.groups,groups=_ref$groups===void 0?[]:_ref$groups,onGroupChange=_ref.onGroupChange,_userPickerProps=(0,objectWithoutProperties/* default */.Z)(_ref,_excluded2);var _groupId=groupId||(groups&&groups[0]?groups[0].code:undefined);var pickerTop=groups&&groups.length?(0,emotion_react_browser_esm_js_.jsx)(lib_select/* default */.Z,{style:{width:"100%",marginBottom:12},defaultValue:_groupId,options:groups.map(function(group){return{label:group.name,value:group.code};}),onChange:onGroupChange}):null;var dom=(0,emotion_react_browser_esm_js_.jsx)(index_js_eager_.Fragment,{key:_userPickerProps.api},pickerTop,(0,emotion_react_browser_esm_js_.jsx)(rc_personnel_es/* default */.Z,(0,esm_extends/* default */.Z)({},_userPickerProps,{key:_userPickerProps.api,size:"mini",onPickerChange:onChange,checkedKeys:disabledKeys,search:false})));return isMobile?(0,emotion_react_browser_esm_js_.jsx)(drawer/* default */.Z,{destroyOnClose:true,headerStyle:userDrawerCls,bodyStyle:userDrawerCls,open:others.open,height:570,placement:"bottom",extra:(0,emotion_react_browser_esm_js_.jsx)(space["default"],null,(0,emotion_react_browser_esm_js_.jsx)(lib_button["default"],{onClick:cancelHandler},"\\u53D6\\u6D88"),(0,emotion_react_browser_esm_js_.jsx)(lib_button["default"],{onClick:others.onOk,type:"primary"},"\\u786E\\u5B9A")),onClose:cancelHandler,title:title},dom):(0,emotion_react_browser_esm_js_.jsx)(modal/* default */.Z,(0,esm_extends/* default */.Z)({okText:"\u786e\u8ba4",cancelText:"\u53d6\u6d88",destroyOnClose:true,onCancel:cancelHandler,width:width,title:title,bodyStyle:userModalCls},others),loading?(0,emotion_react_browser_esm_js_.jsx)(spin/* default */.Z,{spinning:true},(0,emotion_react_browser_esm_js_.jsx)("div",{style:{height:200}})):dom);}\n// EXTERNAL MODULE: ./node_modules/@ant-design/icons/es/icons/RollbackOutlined.js + 1 modules\nvar RollbackOutlined = __webpack_require__(53834);\n;// CONCATENATED MODULE: ./src/components/DetailPageContainer/index.tsx\nfunction DetailPageContainer(props){return (0,emotion_react_browser_esm_js_.jsx)(es/* PageContainer */._z,(0,esm_extends/* default */.Z)({backLink:(0,emotion_react_browser_esm_js_.jsx)(index_esm/* BackLink */.hb,null,(0,emotion_react_browser_esm_js_.jsx)(lib_button["default"],{icon:(0,emotion_react_browser_esm_js_.jsx)(RollbackOutlined/* default */.Z,null)},"\\u8FD4\\u56DE"))},props));}\n// EXTERNAL MODULE: ./src/components/EasyformTask/index.tsx + 5 modules\nvar EasyformTask = __webpack_require__(45504);\n// EXTERNAL MODULE: ./node_modules/classnames/index.js\nvar classnames = __webpack_require__(81694);\nvar classnames_default = /*#__PURE__*/__webpack_require__.n(classnames);\n;// CONCATENATED MODULE: ./src/components/Margin/index.tsx\nvar Margin_excluded=["children","className"];function Margin(props){var children=props.children,className=props.className,others=(0,objectWithoutProperties/* default */.Z)(props,Margin_excluded);var _useIsMobile=(0,es/* useIsMobile */.dD)(),_useIsMobile2=(0,slicedToArray/* default */.Z)(_useIsMobile,1),isMobile=_useIsMobile2[0];var clss=/*#__PURE__*/(0,emotion_css_esm/* css */.iv)(isMobile?{margin:16}:{}, true?"":0, true?"":0);var cls=classnames_default()(\'sui-ui-margin\',className,clss);return (0,emotion_react_browser_esm_js_.jsx)("div",(0,esm_extends/* default */.Z)({},others,{className:cls}),children);}\n;// CONCATENATED MODULE: ./src/components/EasyformTaskDetail/index.tsx\nvar EasyformTaskDetail_excluded=["pageNo","limit","_createTime"];function TaskDetailPage(_ref){var match=_ref.match,location=_ref.location,assignmentId=_ref.assignmentId,taskId=_ref.taskId,todo=_ref.todo,approve=_ref.approve,user=_ref.user,mine=_ref.mine,_ref$title=_ref.title,title=_ref$title===void 0?"\u4efb\u52a1\u8be6\u60c5":_ref$title,delegate=_ref.delegate,backLink=_ref.backLink,formTaskAssignmentState=_ref.formTaskAssignmentState,breadcrumb=_ref.breadcrumb,className=_ref.className,shared=_ref.shared;var _useState=(0,index_js_eager_.useState)(formTaskAssignmentState||{}),_useState2=(0,slicedToArray/* default */.Z)(_useState,2),taskAssignmentState=_useState2[0],setTaskAssignmentState=_useState2[1];var id=taskId;var _useIsMobile=(0,es/* useIsMobile */.dD)(),_useIsMobile2=(0,slicedToArray/* default */.Z)(_useIsMobile,1),isMobile=_useIsMobile2[0];//   const { context = {} } = useContextState<{\n//     currentIdentity?: string | number;\n//   }>();\n//const user = context.user || {};\nvar _useState3=(0,index_js_eager_.useState)(false),_useState4=(0,slicedToArray/* default */.Z)(_useState3,2),userModalVisible=_useState4[0],setUserModalVisible=_useState4[1];var _useState5=(0,index_js_eager_.useState)(false),_useState6=(0,slicedToArray/* default */.Z)(_useState5,2),confirmLoading=_useState6[0],setConfirmLoading=_useState6[1];var _useState7=(0,index_js_eager_.useState)(delegate?0:undefined),_useState8=(0,slicedToArray/* default */.Z)(_useState7,2),processIndex=_useState8[0],setProcessIndex=_useState8[1];//\u4ece\u6700\u65b0\u7684\u59d4\u6d3e\u4fe1\u606f\u4e2d\u89e3\u6790\u6570\u636e\nvar processId=taskAssignmentState.processId,_taskAssignmentState$=taskAssignmentState.assignmentKey,assignmentKey=_taskAssignmentState$===void 0?assignmentId:_taskAssignmentState$,assignmentState=taskAssignmentState.assignmentState,assignmentUserName=taskAssignmentState.assignmentUserName,taskActiveId=taskAssignmentState.taskActiveId,assigneeState=taskAssignmentState.assignee;var _useState9=(0,index_js_eager_.useState)(delegate?[]:undefined),_useState10=(0,slicedToArray/* default */.Z)(_useState9,2),assigneeHistory=_useState10[0],setAssigneeHistory=_useState10[1];var _useState11=(0,index_js_eager_.useState)(false),_useState12=(0,slicedToArray/* default */.Z)(_useState11,2),userInfoUpdating=_useState12[0],setUserInfoUpdating=_useState12[1];var _useState13=(0,index_js_eager_.useState)([]),_useState14=(0,slicedToArray/* default */.Z)(_useState13,2),disabledAssignees=_useState14[0],setDisabledAssignees=_useState14[1];var tableRef=(0,index_js_eager_.useRef)();assignmentKey=assignmentKey||assignmentId;taskActiveId=taskActiveId||taskId;var _useRequest=(0,dist_index_esm/* useRequest */.QT)(function(){return (0,task/* getTask */._X)({pathParams:{id:id}});},{manual:true}),res=_useRequest.data,runGetTask=_useRequest.run,loadingTask=_useRequest.loading;var _useRequest2=(0,dist_index_esm/* useRequest */.QT)(function(){return (0,task/* getAssignmentChain */.Uo)({pathParams:{id:id}});},{manual:true}),resChain=_useRequest2.data,runGetAssignmentChain=_useRequest2.run,loadingChain=_useRequest2.loading;var _useRequest3=(0,dist_index_esm/* useRequest */.QT)(function(_ref2){var _id=_ref2._id,_key=_ref2._key;return (0,task/* postActiveTask */.$s)({pathParams:{id:_id||taskActiveId},data:{assignmentId:_key||assignmentKey}});},{manual:true}),activeTaskRes=_useRequest3.data,doActiveTask=_useRequest3.run,activing=_useRequest3.loading;var loading=loadingTask||loadingChain;(0,index_js_eager_.useEffect)(function(){if(id){if(todo&&!approve){runGetAssignmentChain();}else{runGetTask();}}},[id,todo,approve]);var assignmentChain=(resChain===null||resChain===void 0?void 0:resChain.data)||[];var taskData=todo&&!approve?assignmentChain[0]?assignmentChain[0].rootTask:undefined:res===null||res===void 0?void 0:res.data;var assignee=assignmentChain[0]?assignmentChain[0].assignee:assigneeState;var assignmentData=assignmentChain[0];var processUrl=assignmentData&&assignmentData.url?assignmentData.url:taskData&&taskData.url?taskData.url:"";var processFormId=taskData===null||taskData===void 0?void 0:taskData.formId;var taskActived=(taskData===null||taskData===void 0?void 0:taskData.active)!==false;var process=(taskData===null||taskData===void 0?void 0:taskData.process)||[];var _useState15=(0,index_js_eager_.useState)(),_useState16=(0,slicedToArray/* default */.Z)(_useState15,2),targetUsers=_useState16[0],setTargetUsers=_useState16[1];var detailReadonly=approve||!taskActived;(0,index_js_eager_.useEffect)(function(){if(todo&&assignmentData){setTaskAssignmentState({processId:assignmentData.processId,assignmentKey:assignmentData.id,assignmentState:assignmentData.state,assignmentUserName:assignmentData.assignee.assigneeName,authorityScopeId:assignmentData.assignee.authorityScopeId,authorityScope:assignmentData.assignee.authorityScope});}},[assignmentData,todo]);/**\n   * \u8bbe\u7f6e\u5df2\u8bfb\u72b6\u6001\n   * @description \u5f85\u529e\u9875\u9762\u6709\u6548\n   */(0,index_js_eager_.useEffect)(function(){if(assignmentState==="unread"&&assignmentKey&&!delegate){(0,task/* postAassignmentRead */.We)({pathParams:{id:assignmentKey}});}},[assignmentState,assignmentKey,delegate]);var updateUserInfo=function updateUserInfo(_processId,_userId,fn){if(userInfoUpdating||!delegate){return;}//\u81ea\u52a8\u59d4\u6d3e\u4e0d\u9700\u8981\u8bf7\u6c42\u7528\u6237\u4fe1\u606f\nif(nextAssignment!==null&&nextAssignment!==void 0&&nextAssignment.autoAssign){fn({});return;}var hideMsg=message["default"].loading("\u52a0\u8f7d\u4e2d...",0);setUserInfoUpdating(true);//\u9ed8\u8ba4\u8c03\u7528\u83b7\u53d6\u7528\u6237\u4fe1\u606f\u7684\u63a5\u53e3\nvar serviceCall=getCucUserInfo;var callback=function callback(res){if(res&&res.data&&res.data.orgs){fn(res.data);}};//\u6839\u636eprocessId\u83b7\u53d6\u5230\u8eab\u4efd\u6620\u5c04\u5173\u7cfb\nvar processData=EasyformTask.processOptions.find(function(p){return p.id==_processId;});var filter=processData&&processData.assignmentTargetFilter;//\u6b64\u5904filter\u6052\u4e3aundefined\n//\u5982\u679c\u662f\u59d4\u6d3e\u7ed9\u8f85\u5bfc\u5458\uff0c\u5219\u8fd4\u56de\u8f85\u5bfc\u5458\u7684\u73ed\u7ea7\nif(/^(fdy)/.test(_processId)||filter===interfaces_task/* AssignmentTargetFilter */.h.SelectStudentFromClassAssistant){//\u8bbe\u7f6e\u8c03\u7528\u63a5\u53e3\uff0c\u67e5\u8be2\u8f85\u5bfc\u5458\u7ba1\u7406\u7684\u73ed\u7ea7\nserviceCall=getLeaderClasses;callback=function callback(res){if(res&&res.data&&Array.isArray(res.data)){var _userInfo={manageclasses:res.data};fn(_userInfo);}};}//\u89e6\u53d1\u63a5\u53e3\u8c03\u7528\nserviceCall({pathParams:{id:_userId}}).then(callback)//\u6210\u529f\u5219\u5c06\u7ed3\u679c\u4f20\u9012\u7ed9\u4e0a\u5c42\u56de\u8c03\n.finally(function(){setUserInfoUpdating(false);hideMsg();});};/**\n   * \u5173\u95ed\u9009\u62e9\u59d4\u6d3e\u4eba\u7a97\u53e3\n   */var closeUserModal=function closeUserModal(){setUserModalVisible(false);setConfirmLoading(false);setTargetUsers([]);};/**\n   * \u66f4\u65b0\u9009\u4e2d\u7684\u4eba\u5458\n   * @param users\n   */var userDataChangeHandler=function userDataChangeHandler(users){setTargetUsers(users);};var activeTaskHanlder=(0,index_js_eager_.useCallback)(function(){doActiveTask({_id:taskActiveId,_key:assignmentKey});},[taskActiveId,assignmentKey]);/**\n   * \u5b8c\u6210\u529e\u7406\u64cd\u4f5c\n   * @description \u8df3\u8f6c\u5230\u6d3b\u52a8\u53c2\u4e0e\u9875\u9762\n   */var completeHanlder=(0,index_js_eager_.useCallback)(function(){if(processUrl){window.open(processUrl);}else if(assignmentKey&&processFormId){window.open(service/* YB_EZFORM_COMPLETE_URL */.vp+"?activityId="+processFormId+"&assignmentId="+assignmentKey);}},[processFormId,assignmentKey,processUrl]);/**\n   * \u7ee7\u7eed\u4e0b\u6d3e\n   * @param record\n   */var delegateHandler=function delegateHandler(record){if(record&&record.assignee&&record.processId){updateUserInfo(record.processId,record.assignee.assigneeId,function(userInfo){//userInfo  \u83b7\u53d6\u5230\u7684\u7528\u6237\u4fe1\u606f\n//\u8bb0\u5f55\u59d4\u6d3e\u7684\u5386\u53f2\u8bb0\u5f55\uff0c\u65b9\u4fbf\u56de\u9000\nsetAssigneeHistory(function(old){var newArr=(0,toConsumableArray/* default */.Z)(old||[]);newArr.push((0,objectSpread2/* default */.Z)((0,objectSpread2/* default */.Z)({},record),{},{userInfo:userInfo}));return newArr;});});}};var prevDelegateHandler=function prevDelegateHandler(index){setAssigneeHistory(function(old){var oldArr=(0,toConsumableArray/* default */.Z)(old||[]);oldArr.splice(index);return (0,toConsumableArray/* default */.Z)(oldArr);});};/**\n   * \u65b0\u5efa\u59d4\u6d3e\u4eba\n   */var addAssignments=(0,index_js_eager_.useCallback)(function(){if(!targetUsers||!targetUsers.length){return message["default"].info("\u8bf7\u9009\u62e9\u59d4\u6d3e\u4eba");}setConfirmLoading(true);// \u5224\u65ad\u662f\u5426\u662f\u59d4\u6d3e\u7684\u59d4\u6d3e\uff0c\u8c03\u7528\u4e0d\u540c\u7684\u63a5\u53e3\u670d\u52a1\n(assignmentKey?task/* postAssignments */.r_:task/* postTaskAssignments */.Ib)({pathParams:{// \u6839\u59d4\u6d3e\nid:assignmentKey||id},data:targetUsers.map(function(node){var user=node.dataRef||node;return{assigneeId:user.key,assigneeName:user.name,authorityScope:user.topNode?user.topNode.name:"",authorityScopeId:user.topNode?user.topNode.code:"",bizId:"",type:"U"// \u9ed8\u8ba4\u4e0b\u6d3e\u5230\u4eba\n};})}).then(function(res){if(res){notification["default"].success({message:"\u65b0\u589e\u59d4\u6d3e\u6210\u529f"});setUserModalVisible(false);setTargetUsers([]);tableRef.current&&tableRef.current.reload(true);}else{message["default"].error("\u65b0\u589e\u59d4\u6d3e\u5931\u8d25");}}).finally(function(){setConfirmLoading(false);});},[confirmLoading,targetUsers,assignmentKey,id]);(0,index_js_eager_.useEffect)(function(){var _processIndex=process.findIndex(function(item){return item.id===processId&&processId?true:false;});setProcessIndex(_processIndex+1);},[process,processId]);(0,index_js_eager_.useEffect)(function(){if(delegate&&assigneeHistory){//\u9ed8\u8ba4\u53d6\u6700\u65b0\u7684\u59d4\u6d3e\u8bb0\u5f55\nvar record=assigneeHistory[assigneeHistory.length-1];if(record&&record.assignee){//\u66f4\u65b0\u6700\u65b0\u7684\u59d4\u6d3e\u72b6\u6001\nsetTaskAssignmentState({processId:record.processId,assignmentKey:record.id,assignmentState:record.state,assignmentUserName:record.assignee.assigneeName,authorityScopeId:record.assignee.authorityScopeId,authorityScope:record.assignee.authorityScope});}else{setTaskAssignmentState({});}}},[assigneeHistory,delegate]);/**\n   * \u67e5\u8be2\u5df2\u59d4\u6d3e\u7684\u4eba\u5458\n   */var apiUrl=(0,index_js_eager_.useMemo)(function(){if(assignmentKey){//\u5982\u679c\u5b58\u5728\u59d4\u6d3eid\u5219\u8bc1\u660e\u662f\u59d4\u6d3e\u7684\u59d4\u6d3e\uff0c\u6839\u636e\u5f53\u524d\u59d4\u6d3eid\u67e5\u8be2\u59d4\u6d3e\u7684\u59d4\u6d3e\u7ed3\u679c\nreturn index_esm/* utils */.P6.paramsUrlPath(task/* API_TASK */.ZM.getTaskSubAssignments,{id:assignmentKey});}//\u5426\u5219\u6839\u636e\u4efb\u52a1id\u67e5\u8be2\u5f53\u524d\u4efb\u52a1\u7684\u59d4\u6d3e\u7ed3\u679c\nreturn index_esm/* utils */.P6.paramsUrlPath(task/* API_TASK */.ZM.getTaskAssignments,{id:id});},[assignmentKey,id]);/**\n   * \u6d41\u7a0b\n   */var stepPropsFilter=(0,index_js_eager_.useCallback)(function(process,index){var isCur=processIndex===index;var isNext=index===processIndex+1;var subName=process.subName||assignmentUserName;var curAssignee=index>0&&assigneeHistory&&assigneeHistory.length&&assigneeHistory[index-1]?assigneeHistory[index-1]:undefined;var curUserInfo=curAssignee?curAssignee.userInfo:undefined;var curAssigneeScope=curAssignee&&curAssignee.assignee&&curAssignee.assignee.authorityScope?curAssignee.assignee.authorityScope:undefined;var curAssigneeScopeId=curAssignee&&curAssignee.assignee&&curAssignee.assignee.authorityScopeId?curAssignee.assignee.authorityScopeId:undefined;if(isCur){subName=delegate?subName:"\u6211";}else{if(index&&index<=processIndex){subName=curUserInfo?curUserInfo.name:subName;}else if(index>processIndex){subName="";}}if(todo){var curAssignmentChain=assignmentChain.find(function(item){return item.processId==process.id;});if(curAssignmentChain){subName=curAssignmentChain.assignee.assigneeName||subName;curAssigneeScope=curAssignmentChain.assignee.authorityScope||curAssigneeScope;curAssigneeScopeId=curAssignmentChain.assignee.authorityScopeId||curAssigneeScopeId;}}if(!subName){var _curAssignee$assignee;subName=curAssignee===null||curAssignee===void 0?void 0:(_curAssignee$assignee=curAssignee.assignee)===null||_curAssignee$assignee===void 0?void 0:_curAssignee$assignee.assigneeName;}return{title:detailReadonly?process.name:(0,emotion_react_browser_esm_js_.jsx)(index_js_eager_.Fragment,null,isCur?(0,emotion_react_browser_esm_js_.jsx)(typography/* default */.Z.Link,{style:{cursor:"text"}},process.name):index<processIndex&&delegate?(0,emotion_react_browser_esm_js_.jsx)("a",{onClick:function onClick(){return prevDelegateHandler(index);}},process.name):isNext?(0,emotion_react_browser_esm_js_.jsx)(typography/* default */.Z.Text,{type:"warning"},process.name):process.name),subTitle:(0,emotion_react_browser_esm_js_.jsx)(index_js_eager_.Fragment,null,curAssigneeScope&&(0,emotion_react_browser_esm_js_.jsx)(tooltip["default"],{title:curAssigneeScope},(0,emotion_react_browser_esm_js_.jsx)("div",{style:{whiteSpace:"nowrap",width:140,overflow:" hidden",textOverflow:"ellipsis"}},curAssigneeScope)),subName&&"(".concat(subName,")"))};},[processIndex,assignmentUserName,assigneeHistory,delegate]);if(loading){return (0,emotion_react_browser_esm_js_.jsx)(PageLoading,null);}if(!taskData&&!loading){return (0,emotion_react_browser_esm_js_.jsx)(result/* default */.ZP,{status:404,title:"\\u672A\\u627E\\u5230\\u76F8\\u5173\\u4EFB\\u52A1"});}if(approve&&!assignmentKey){return (0,emotion_react_browser_esm_js_.jsx)(result/* default */.ZP,{status:404,title:"\\u672A\\u77E5\\u7684\\u5F85\\u529EID"});}if(approve&&activeTaskRes&&(!activeTaskRes.code||activeTaskRes.code==200)&&!activing){return (0,emotion_react_browser_esm_js_.jsx)(result/* default */.ZP,{status:"success",title:"\\u4EFB\\u52A1\\u5BA1\\u6838\\u901A\\u8FC7"});}var userInfo=delegate&&assigneeHistory&&assigneeHistory.length?assigneeHistory[assigneeHistory.length-1].userInfo:undefined;var assigneeData=delegate?assigneeHistory&&assigneeHistory.length?assigneeHistory[assigneeHistory.length-1].assignee:undefined:assignee;var taskEnded=(taskData===null||taskData===void 0?void 0:taskData.state)==="end";var _processIndex=process.findIndex(function(p){return p.id===processId;});var nextAssignment=process[_processIndex===-1?0:_processIndex+1];var nextAssignmentId=nextAssignment===null||nextAssignment===void 0?void 0:nextAssignment.id;var nextAssignmentTargetFilter=nextAssignment===null||nextAssignment===void 0?void 0:nextAssignment.assignmentTargetFilter;var creatorIdentity=!processId&&taskData!==null&&taskData!==void 0&&taskData.creatorIdentity?taskData===null||taskData===void 0?void 0:taskData.creatorIdentity:user.currentIdentity;var assignmentType;if(nextAssignmentId==="yxgly"){assignmentType="academy_yxgly";}if(nextAssignmentId==="fdy"){assignmentType="academy_fdy";}if(nextAssignmentId==="bks"){assignmentType="student";}var creatorRoleType;if(isFdyUser(creatorIdentity)){creatorRoleType="ASSISTANT";}if(isDepartAdminUser(creatorIdentity)){creatorRoleType="DEPART_ADMIN";if(assignmentType==="student"&&!processId){assignmentType="academy_student";}}if(isSupperUser(creatorIdentity)||isXgAdminUser(creatorIdentity)){creatorRoleType="SUPER_ADMIN";}/**\n   * \u59d4\u6d3e\u5217\u8868\u5b9a\u4e49\n   */var columns=[(0,service/* createColumn */.wy)({dataIndex:"assignee",title:"\u59d4\u6d3e\u4eba",fieldProps:{placeholder:"\u8bf7\u8f93\u5165\u59d3\u540d\u6216\u5b66/\u5de5\u53f7"}},"search"),{dataIndex:"assigneeName",title:nextAssignment!==null&&nextAssignment!==void 0&&nextAssignment.expandRangeAssignment?"\u59d3\u540d":"\u5c97\u4f4d",hideInSearch:true,render:function render(item,record){return record.assignee&&record.assignee.assigneeName;},width:140},{dataIndex:"assigneeId",title:"\u5b66/\u5de5\u53f7",hideInSearch:true,render:function render(item,record){return record.assignee&&record.assignee.assigneeId;},hideInTable:!(nextAssignment!==null&&nextAssignment!==void 0&&nextAssignment.expandRangeAssignment),width:160},{dataIndex:"authorityScope",title:"\u90e8\u95e8",hideInSearch:true,render:function render(item,record){return record.assignee&&record.assignee.authorityScope;}},{dataIndex:"createTime",valueType:"dateTime",title:"\u4e0b\u6d3e\u65f6\u95f4",hideInSearch:true,width:180},{dataIndex:"state",title:"\u72b6\u6001",valueType:"select",valueEnum:commons_task/* assignmentStateEnum */.kH,render:function render(item,record){if(!record||!record.state){return null;}var state=commons_task/* AssignmentStateMap */.m_[record.state];return (0,emotion_react_browser_esm_js_.jsx)(badge/* default */.Z,state);},width:80},{dataIndex:"completeTime",valueType:"dateTime",title:"\u5b8c\u6210\u65f6\u95f4",hideInSearch:true}];return (0,emotion_react_browser_esm_js_.jsx)(DetailPageContainer,{className:className,backLink:backLink,title:title,content:(0,emotion_react_browser_esm_js_.jsx)(index_js_eager_.Fragment,null,approve&&taskData&&!taskActived?(0,emotion_react_browser_esm_js_.jsx)(lib_alert["default"],{style:{marginBottom:10},message:"".concat(taskData.creatorOrg||"").concat(taskData.creatorName,"\\u521B\\u5EFA\\u7684\\u4EFB\\u52A1\\u9700\\u8981\\u60A8\\u5BA1\\u6279"),showIcon:true,type:"info"}):null,(0,emotion_react_browser_esm_js_.jsx)(TaskDetail,{readOnly:detailReadonly||!taskActived,current:processIndex,data:taskData,stepPropsFilter:stepPropsFilter})),breadcrumb:breadcrumb!==false?true:undefined},(0,emotion_react_browser_esm_js_.jsx)(index_js_eager_.Fragment,null,todo&&!approve&&processIndex===process.length&&assignmentKey?(0,emotion_react_browser_esm_js_.jsx)(Margin,null,assignmentState=="done"?(0,emotion_react_browser_esm_js_.jsx)(result/* default */.ZP,{status:"success",title:"\\u5DF2\\u5B8C\\u6210"}):(0,emotion_react_browser_esm_js_.jsx)(space["default"],null,(0,emotion_react_browser_esm_js_.jsx)(lib_button["default"],{onClick:completeHanlder,type:"primary",disabled:!taskActived},"\\u7ACB\\u5373\\u529E\\u7406"))):approve&&assignmentKey?(0,emotion_react_browser_esm_js_.jsx)(Margin,null,(0,emotion_react_browser_esm_js_.jsx)(space["default"],null,(0,emotion_react_browser_esm_js_.jsx)(lib_button["default"],{disabled:taskActived,loading:activing,onClick:activeTaskHanlder,type:"primary"},taskActived?"\u4efb\u52a1\u5df2\u901a\u8fc7\u5ba1\u6838":"\u5ba1\u6838\u901a\u8fc7"))):(0,emotion_react_browser_esm_js_.jsx)(pro_table_es/* default */.ZP,{getActionRef:function getActionRef(ref){return tableRef.current=ref;},key:"key"+processIndex,idKey:commons_task/* TASK_ID_KEY */.Ah,columns:columns,requestOptions:{url:apiUrl,prefix:task/* TASK_API_PREFIX */._s},onGetActionParams:function onGetActionParams(params){var _params$pageNo=params.pageNo,pageNo=_params$pageNo===void 0?1:_params$pageNo,_params$limit=params.limit,limit=_params$limit===void 0?10:_params$limit,_createTime=params._createTime,others=(0,objectWithoutProperties/* default */.Z)(params,EasyformTaskDetail_excluded);return (0,objectSpread2/* default */.Z)({begin:limit*(pageNo-1),length:limit},others);},onGetActionSuccess:function onGetActionSuccess(res){var data=res.data&&res.data.items?res.data.items:[];//\u5b58\u50a8\u5b58\u5728\u59d4\u6d3eid(\u5df2\u7ecf\u59d4\u6d3e\u8fc7)\u7684\u59d4\u6d3e\u6570\u636e\nsetDisabledAssignees(data.map(function(item){return item.assignee.assigneeId;}));return{data:data,total:res.data&&res.data.totalNum?res.data.totalNum:0};},headerButtonsRender:function headerButtonsRender(){return[(0,emotion_react_browser_esm_js_.jsx)(lib_button["default"],{disabled:userInfoUpdating||taskEnded||nextAssignment!==null&&nextAssignment!==void 0&&nextAssignment.autoAssign||!taskActived?true:false,key:"create",type:"primary",onClick:function onClick(){return setUserModalVisible(true);}},"\\u65B0\\u5EFA\\u59D4\\u6D3E")];},headerTitle:false,hideCreateActionButton:true,batchActionsPlacement:false,rowSelection:false,actionsColumnConfig:{width:60},hideActionsColumn:!delegate||!taskActived||typeof processIndex==="number"&&processIndex>=process.length-1,actionsColumnRender:delegate?function(_ref3){var record=_ref3.record;return[(0,emotion_react_browser_esm_js_.jsx)("a",{key:"delegate",onClick:function onClick(){return delegateHandler(record);}},"\\u67E5\\u770B")];}:undefined,fixedActionsColumn:true,scroll:{x:960},pagination:isMobile?{size:"small"}:undefined,dateFormatter:function dateFormatter(v){return v.toISOString();}}),(0,emotion_react_browser_esm_js_.jsx)(AssignmentSelector,{disabledKeys:disabledAssignees,userInfo:userInfo,prevProcessId:processId,user:user,assigneeData:assigneeData,processId:nextAssignmentId,onChange:userDataChangeHandler,confirmLoading:confirmLoading&&userModalVisible,onOk:addAssignments,open:userModalVisible,onCancel:closeUserModal,assignmentTargetFilter:nextAssignmentTargetFilter,assignmentData:nextAssignment,creatorRoleType:creatorRoleType})));}//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiMTQ1MjIuanMiLCJtYXBwaW5ncyI6Ijs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7QUFBQTs7Ozs7Ozs7Ozs7Ozs7Ozs7OztBQ0VBO0FBQ0E7QUFDQTtBQUNBO0FBQ0E7QUFNQTtBQUNBO0FBTUE7QUFDQTtBQUNBO0FBQ0E7QUFNQTtBQUNBO0FBQ0E7QUFDQTtBQU1BO0FBQ0E7QUFNQTtBQUNBO0FBTUE7QUFDQTtBQUNBO0FBQ0E7QUFNQTtBQUNBO0FBQ0E7QUFDQTtBQU1BO0FBQ0E7QUFDQTtBQUNBO0FBTUE7QUFDQTtBQUNBO0FBQ0E7QUFNQTtBQUNBO0FBQ0E7QUFDQTtBQU1BO0FBQ0E7QUFDQTtBQUNBO0FBTUE7QUFDQTtBQUNBO0FBTUE7QUFDQTtBQUNBO0FBTUE7QUFDQTtBQUNBO0FBTUE7QUFDQTtBQUNBO0FBQ0E7QUFNQTtBQUNBO0FBTUE7QUFDQTtBQUlBO0FBQ0E7QUFDQTtBQUNBO0FBT0E7QUFDQTtBQUNBO0FBTUE7QUFDQTtBQUlBO0FBQ0E7QUFDQTtBQUNBO0FBT0E7QUFDQTtBQUNBOztBQ3pMQTtBQWtCQTtBQUNBOztBQ2xCQTtBQTRFQTtBQUNBO0FBQ0E7QUFJQTs7QUNsRkE7QUF3QkE7QUFDQTs7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OztBQzFCQTtBQVlBO0FBQ0E7QUFHQTtBQUNBO0FBSUE7QUFDQTtBQUlBO0FBQ0E7QUFJQTtBQUNBO0FBSUE7QUFDQTtBQUlBO0FBQ0E7QUFLQTtBQUNBO0FBQ0E7QUFDQTtBQVFBO0FBQ0E7QUFDQTtBQUNBO0FBUUE7QUFDQTtBQUNBO0FBQ0E7QUFRQTtBQUNBO0FBQ0E7QUFDQTtBQVFBO0FBQ0E7QUFDQTtBQUNBO0FBY0E7QUFDQTtBQUNBO0FBUUE7QUFDQTtBQUNBO0FBQ0E7Ozs7QUN6SEE7QUFxREE7QUFvRUE7QUErQ0E7Ozs7QUN2S0E7Ozs7Ozs7QUNEQTs7QUNBQTtBQXdFQTtBQUNBO0FBQ0E7QUFDQTtBQUlBO0FBNEVBO0FBQ0E7QUFDQTtBQWdCQTtBQU9BO0FBT0E7QUFFQTtBQUNBO0FBRUE7QUFXQTtBQUtBO0FBUUE7QUFDQTtBQVFBO0FBQ0E7QUFDQTtBQVVBO0FBQ0E7QUFDQTtBQVVBO0FBQ0E7QUFDQTtBQU1BO0FBQ0E7QUFrQkE7QUFDQTtBQVNBO0FBR0E7QUFXQTtBQTRCQTtBQUdBO0FBZUE7QUFDQTtBQUlBO0FBR0E7QUFJQTtBQUNBO0FBNkdBO0FBQ0E7QUEySEEiLCJzb3VyY2VzIjpbImNvbXBvbmVudHMvUGFnZUxvYWRpbmcvaW5kZXguanN4IiwiY29tbW9ucy91c2VyLnRzIiwiY29tbW9ucy9yZWxlYXNlQXBwLnRzIiwiY29tbW9ucy9hcHAudHMiLCJjb21wb25lbnRzL1Rhc2tEZXRhaWwvaW5kZXgudHN4Iiwic2VydmljZXMvY3VjLnRzIiwiY29tcG9uZW50cy9FYXN5Zm9ybVRhc2tEZXRhaWwvY29tcG9uZW50cy9Bc3NpZ25tZW50U2VsZWN0b3IudHN4IiwiY29tcG9uZW50cy9EZXRhaWxQYWdlQ29udGFpbmVyL2luZGV4LnRzeCIsImNvbXBvbmVudHMvTWFyZ2luL2luZGV4LnRzeCIsImNvbXBvbmVudHMvRWFzeWZvcm1UYXNrRGV0YWlsL2luZGV4LnRzeCJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///14522\n')}}]);