
<!DOCTYPE html>
<html>
<head>
  <meta http-equiv="Content-Type" content="text/html; charset=utf-8"/>
  <title>135编辑器</title>
  <style>
    html, body {
      padding: 0;
      margin: 0;
    }
    #editor135 {
      position: absolute;
      width: 100%;
      height: 100%;
      border: none;
      box-sizing: border-box;
    }
  </style>
</head>
<body>
<iframe id="editor135" src="//135editor.com/simple_editor.html?callback=true&appkey="></iframe>
<script type="text/javascript" src="../internal.js"></script>
<script>
  var editor135 = document.getElementById('editor135');
  var postUrl =  window.location.protocol + '//www.135editor.com'
  editor135.onload = function () {
    editor135.contentWindow.postMessage('','*');
  };
  document.addEventListener("mousewheel", function (event) {
    event.preventDefault();
    event.stopPropagation();
  });
  window.addEventListener('message', function (event) {
      if (typeof event.data !== 'string') return;
      if (event.origin == postUrl) {
        editor.execCommand('insertHtml', event.data);
        editor.fireEvent("catchRemoteImage");
        dialog.close();
      } else {
        throw Error('135编辑器返回origin根域名匹配不对')
      }
  }, false);
</script>
</body>
</html>