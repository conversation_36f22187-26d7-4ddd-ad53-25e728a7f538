

<!DOCTYPE html>
<html>

<head>
    <meta http-equiv="Content-Type" content="text/html; charset=utf-8" />
    <title>秀米编辑器</title>
    <style>
        html,
        body {
            padding: 0;
            margin: 0;
        }

        #xiumi {
            position: absolute;
            width: 100%;
            height: 100%;
            border: none;
            box-sizing: border-box;
        }
    </style>
</head>

<body>
    <iframe id="xiumi" src="//xiumi.us/studio/v5#/paper">
    </iframe>
    <script type="text/javascript" src="../internal.js"></script>
    <script>
        var xiumi = document.getElementById('xiumi');
        var postUrl =  "https://xiumi.us";
        xiumi.onload = function () {
            // "XIUMI:3rdEditor:Connect" 是特定标识符，不能修改，大小写敏感
            xiumi.contentWindow.postMessage('XIUMI:3rdEditor:Connect', postUrl);
        };
        document.addEventListener("mousewheel", function (event) {
            event.preventDefault();
            event.stopPropagation();
        });
        window.addEventListener('message', function (event) {
            if (event.origin == postUrl) {
                console.log("Inserting html");
                editor.execCommand('insertHtml', event.data);
                console.log("Xiumi dialog is closing");
                dialog.close();
            } else{
                throw Error('xiumi编辑器返回origin根域名匹配不对')
            }
        }, false);
    </script>
</body>

</html>
