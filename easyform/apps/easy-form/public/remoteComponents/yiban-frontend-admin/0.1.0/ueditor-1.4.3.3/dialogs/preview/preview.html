<!DOCTYPE HTML PUBLIC "-//W3C//DTD HTML 4.01 Transitional//EN"
    "http://www.w3.org/TR/html4/loose.dtd">
<html>
    <head>
        <meta http-equiv="Content-Type" content="text/html; charset=utf-8"/>
        <style>
            html,body{
                height:100%;
                width:100%;
                padding:0;
                margin:0;
            }
            #preview {
                font-family:sans-serif;font-size:16px;
                width: 560px;
                padding: 8px;
                margin: 0 auto;
                background-color: #fff;
                box-sizing: border-box;
            }

            .article_wrap,.article_content,.article_head,.article_head *,body {
            margin: 0;
            padding: 0;
        }
        .article_wrap {
            min-height: 100vh;
            margin: 0 auto;
            max-width: 750px;
            padding: 6px;
            box-sizing: border-box;
        }
        .article_head {
            padding: 10px;
        }
        .article_content {
            margin: 0 10px;
        }
        .article_title {
            line-height: 1.2em;
            margin-top: 10px;
            font-size: 22px;
            font-weight: 400;
        }
        .article_meta {
            margin-top: 10px;
            line-height: 1.2em;
        }
        .article_meta span {
            margin-right: 16px;
            color: #777;
            font-size: 14px;
        }
        .article_meta span.article_creator {
            color: #45bcda;
        }
       .selectTdClass{background-color:#edf5fa !important}table.noBorderTable td,table.noBorderTable th,table.noBorderTable caption{border:1px dashed #ddd !important}table{margin-bottom:10px;border-collapse:collapse;display:table;}td,th{padding: 5px 10px;border: 1px solid #DDD;}caption{border:1px dashed #DDD;border-bottom:0;padding:3px;text-align:center;}th{border-top:1px solid #BBB;background-color:#F7F7F7;}table tr.firstRow th{border-top-width:2px;}.ue-table-interlace-color-single{ background-color: #fcfcfc; } .ue-table-interlace-color-double{ background-color: #f7faff; }td p{margin:0;padding:0;}
       
       .article_content img,.article_content table, .article_content pre {
           max-width: 100%!important;
       }
       .article_content table {
          width: 100%;
       }
       .article_content img {
        height: auto !important;
       }

        </style>
        <script type="text/javascript" src="../internal.js"></script>
        <script src="../../ueditor.parse.js"></script>
        <title></title>
    </head>
    <body class="view">
        <div id="preview" class="article_content">

        </div>
    </body>
    <script>
        document.getElementById('preview').innerHTML = editor.getContent();
        uParse('#preview',{
            rootPath : '../../',
            chartContainerHeight:500
        })
        dialog.oncancel = function(){
            document.getElementById('preview').innerHTML = '';
        }
    </script>
</html>