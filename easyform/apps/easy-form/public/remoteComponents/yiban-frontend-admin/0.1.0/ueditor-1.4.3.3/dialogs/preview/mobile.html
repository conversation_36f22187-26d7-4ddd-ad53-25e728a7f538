<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>预览</title>
    <style>
        .article_wrap,.article_content,.article_head,.article_head *,body {
            margin: 0;
            padding: 0;
        }
        .article_wrap {
            min-height: 100vh;
            margin: 0 auto;
            max-width: 750px;
            padding: 6px;
            box-sizing: border-box;
        }
        .article_head {
            padding: 10px;
        }
        .article_content {
            margin: 0 10px;
        }
        .article_title {
            line-height: 1.2em;
            margin-top: 10px;
            font-size: 22px;
            font-weight: 400;
        }
        .article_meta {
            margin-top: 10px;
            line-height: 1.2em;
        }
        .article_meta span {
            margin-right: 16px;
            color: #777;
            font-size: 14px;
        }
        .article_meta span.article_creator {
            color: #45bcda;
        }
       .selectTdClass{background-color:#edf5fa !important}table.noBorderTable td,table.noBorderTable th,table.noBorderTable caption{border:1px dashed #ddd !important}table{margin-bottom:10px;border-collapse:collapse;display:table;}td,th{padding: 5px 10px;border: 1px solid #DDD;}caption{border:1px dashed #DDD;border-bottom:0;padding:3px;text-align:center;}th{border-top:1px solid #BBB;background-color:#F7F7F7;}table tr.firstRow th{border-top-width:2px;}.ue-table-interlace-color-single{ background-color: #fcfcfc; } .ue-table-interlace-color-double{ background-color: #f7faff; }td p{margin:0;padding:0;}
       
       .article_content img,.article_content table, .article_content pre {
           max-width: 100%!important;
       }
       .article_content table {
          width: 100%;
       }
       .article_content img {
        height: auto !important;
       }
    </style>
    <script src="../../ueditor.parse.js"></script>
</head>
<body>
    <div class="article_wrap">
        <div class="article_head">
            <h1 class="article_title" id="_title"></h1>
            <div class="article_meta">
                <span class="article_date" id="_date"><!--article_date--></span>
                <span class="article_creator" id="_creator"><!--article_creator--></span>
            </div>
        </div>
        <div class="article_content" id="_conetent">
        
        </div>
    </div>
</body>
<script>
    uParse('#_conetent',{
        rootPath : '../../',
        chartContainerHeight:500
    })
    function changeTitle(){
        var editor = parent.g_getEditor()
        document.getElementById('_title').innerHTML = editor.getTitle();
        changeDate()
    }
    function changeAuthor(){
        var editor = parent.g_getEditor()
        document.getElementById("_creator").innerHTML = editor.getCreator();
        changeDate()
    }
    function changeDate(){
        var editor = parent.g_getEditor()
        document.getElementById("_date").innerHTML = editor.getDate();
    }
    function changeContent(){
        var editor = parent.g_getEditor()
        document.getElementById('_conetent').innerHTML = editor.getContent();
        changeDate()
    }
    changeContent()
    changeTitle()
    changeAuthor()
    
</script>
</html>