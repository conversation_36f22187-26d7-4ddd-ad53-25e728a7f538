{"name": "@yiban/easyform", "version": "0.1.0", "private": true, "scripts": {"start": "craco start", "dev": "PORT=3000 craco start", "build": "craco build", "test": "craco test", "lint": "eslint \"**/*.{ts,tsx,js,jsx}\"", "clean": "rm -rf build"}, "dependencies": {"@ant-design/icons": "^5.2.6", "@ant-design/pro-layout": "^7.20.2", "@craco/craco": "^6.4.3", "@emotion/react": "^11.10.5", "@emotion/styled": "^11.10.5", "@mui/system": "^5.10.13", "@testing-library/jest-dom": "^5.11.4", "@testing-library/react": "^12.1.2", "@testing-library/user-event": "^13.5.0", "@types/jest": "^27.0.3", "@types/node": "^17.0.2", "@types/react": "^17.0.0", "@types/react-dom": "^17.0.0", "@types/react-transition-group": "^4.4.5", "@yiban/common-mobile": "^0.1.1", "@yiban/easyform-picker": "0.1.1", "@yiban/participator-picker": "0.1.4", "@yiban/system": "^0.1.0", "ahooks": "^3.7.2", "antd": "^5.6.2", "antd-mobile": "^5.25.1", "antd-mobile-icons": "^0.3.0", "axios": "^1.4.0", "dayjs": "^1.11.6", "echarts": "^5.4.1", "echarts-for-react": "^3.0.2", "immer": "^9.0.7", "jsonpath": "^1.1.1", "moment": "^2.30.1", "next-qrcode": "^2.5.1", "react": "^17.0.2", "react-d3-cloud": "^1.0.6", "react-dnd": "14", "react-dnd-html5-backend": "14", "react-dom": "^17.0.2", "react-router-dom": "6.2.1", "react-scripts": "5.0.0", "react-transition-group": "^4.4.5", "react-virtualized": "^9.22.3", "react-virtuoso": "^4.0.3", "shortid": "^2.2.16", "tinycolor2": "^1.6.0", "typescript": "^4.1.2", "web-vitals": "^2.1.2"}, "devDependencies": {"autoprefixer": "^10.4.0", "babel-plugin-import": "^1.13.3", "eslint-config-custom": "*", "postcss": "^8.4.5", "prettier": "^2.5.1", "prop-types": "^15.8.1", "tailwind-config": "*", "tailwindcss": "^3.0.5", "tsconfig": "*"}, "eslintConfig": {"extends": ["react-app", "react-app/jest"]}, "eslintIgnore": ["build/*", "node_modules/*"], "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}, "dev": {"prefixUrl": "/edu", "serverHost": "https://edu.sudytech.cn"}}