{"name": "@yiban/easyform", "version": "0.1.0", "private": true, "scripts": {"start": "craco start", "dev": "PORT=3000 craco start", "build": "craco build", "test": "craco test", "lint": "eslint \"**/*.{ts,tsx,js,jsx}\"", "clean": "rm -rf build"}, "dependencies": {"@ant-design/icons": "^4.8.0", "@ant-design/pro-layout": "^6.5.0", "@craco/craco": "^6.4.3", "@emotion/css": "^11.13.5", "@emotion/react": "^11.10.5", "@emotion/styled": "^11.10.5", "@mui/system": "^5.10.13", "@sui/pro-components": "3.0.2", "@sui/pro-layout": "3.0.29", "@sui/rc-personnel": "3.0.42", "@sui/runtime": "3.0.55", "@sui/sui": "^3.1.120", "@testing-library/jest-dom": "^5.11.4", "@testing-library/react": "^12.1.2", "@testing-library/user-event": "^13.5.0", "@types/jest": "^27.0.3", "@types/node": "^17.0.2", "@types/react": "^17.0.0", "@types/react-dom": "^17.0.0", "@types/react-transition-group": "^4.4.5", "@yiban/common-mobile": "^0.1.1", "@yiban/easyform-picker": "0.1.1", "@yiban/participator-picker": "0.1.3", "@yiban/system": "^0.1.0", "ahooks": "^3.7.2", "antd": "^5.26.5", "antd-mobile": "^5.25.1", "antd-mobile-icons": "^0.3.0", "axios": "^1.4.0", "dayjs": "^1.11.6", "echarts": "^5.4.1", "echarts-for-react": "^3.0.2", "immer": "^9.0.7", "jsonpath": "^1.1.1", "moment": "^2.30.1", "next-qrcode": "^2.5.1", "react": "^17.0.2", "react-d3-cloud": "^1.0.6", "react-dnd": "14", "react-dnd-html5-backend": "14", "react-dom": "^17.0.2", "react-router-dom": "6.2.1", "react-scripts": "5.0.0", "react-transition-group": "^4.4.5", "react-virtualized": "^9.22.3", "react-virtuoso": "^4.0.3", "shortid": "^2.2.16", "tinycolor2": "^1.6.0", "typescript": "^4.1.2", "web-vitals": "^2.1.2"}, "devDependencies": {"autoprefixer": "^10.4.0", "babel-plugin-import": "^1.13.3", "eslint-config-custom": "*", "postcss": "^8.4.5", "prettier": "^2.5.1", "prop-types": "^15.8.1", "tailwind-config": "*", "tailwindcss": "^3.0.5", "tsconfig": "*"}, "eslintConfig": {"extends": ["react-app", "react-app/jest"]}, "eslintIgnore": ["build/*", "node_modules/*"], "browserslist": {"production": [">0.2%", "not dead", "not op_mini all"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}, "dev": {"prefixUrl": "/", "serverHost": "http://my.coreplustest.sudytech.com"}}