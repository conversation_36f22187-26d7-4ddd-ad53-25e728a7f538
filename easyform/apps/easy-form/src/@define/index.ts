/**
 * 万能表单管理员类型
 */
import { PageSettings } from '@/editor-kit/context'
export const ADMIN_TYPE = {
  SUPER: 'super', //超管
  ORG: 'org', //院系管理员
  COUNSELOR: 'counselor' //辅导员
}

export const IDENTITY_TYPE = {
  STUDENT: 1, //学生
  SCHOOL_WORKER: 2, //教职工
  DEPT_MANAGER: 3, //部门管理员
  ADMIN: 4, // 系统管理员
  COUNSELOR: 5, // 辅导员
  XG_ADMIN: 6, //学工管理员
  DEPT_CLERK: 7 // 学院副书记
}

export const IdentityTypes: Record<string, any> = {
  1: {
    name: '学生',
    id: 'user',
    sort: 10
  },
  2: {
    name: '教职工',
    id: 'user',
    sort: 20
  },
  3: {
    name: '学院学工办主任',
    id: 'depAdmin',
    sort: 40
  },
  4: {
    name: '系统管理员',
    id: 'admin',
    sort: 90
  },
  5: {
    name: '辅导员',
    id: 'counselor',
    sort: 30
  },
  6: {
    name: '学工管理员',
    id: 'admin',
    sort: 50
  },
  7: {
    name: '学院副书记',
    id: 'deptClerk',
    sort: 60
  }
}

type Identity = typeof IDENTITY_TYPE
export type TIdentityValue = Identity[keyof Identity]

export const DefaultInitValue = [
  { label: '无', value: '' },
  { label: '登录人姓名', value: '{{userName}}' },
  { label: '登录人学工号', value: '{{code}}' },
  { label: '登录人手机号', value: '{{phone}}' },
  { label: '登录人机构', value: '{{org}}' },
  { label: '登录人专业', value: '{{speciality}}' },
  { label: '登录人班级', value: '{{class}}' }
]

export const DefaultInitValueMap = {
  userName: '登录人姓名',
  code: '登录人学工号',
  phone: '登录人手机号',
  org: '登录人机构',
  speciality: '登录人专业',
  class: '登录人班级'
}

export const ParticipantCat = [
  // {
  //   code: 'student_in_org:ALL',
  //   name: '所有学生'
  // },
  // {
  //   code: 'techer_in_org:ALL',
  //   name: '所有老师'
  // },
  {
    code: 'techer_in_org',
    name: '当前机构下所有老师'
  },
  {
    code: 'student_in_org',
    name: '当前机构下所有学生'
  }
]

export const CandidateCat = [
  {
    code: 'expr:counselor',
    name: '辅导员'
  },
  {
    code: 'expr:classteacher',
    name: '班主任'
  },
  {
    code: 'expr:orgadmin',
    name: '院系管理员'
  }
]

export const USED_BY_PLATEFORM = {
  TASK: 'task',
  EXT: 'EXT'
}

export const FORM_CONFIG = {
  APPLY: {
    name: { label: '活动名称', placeholder: '点击输入标题' },
    desc: { label: '活动说明文本', placeholder: '输入描述说明' },
    imgs: { label: '活动配图' },
    daterange: { label: '活动时间' },
    activitylocation: { label: '活动地点', placeholder: '输入活动地点' },
    participantDetail: { label: '参与者' },
    maxParticipantNum: { label: '最大参与人数' },
    isTicket: { label: '开启入场凭证' },
    ticketWriteOffUserScopeDetail: { label: '验票者' },
    nametype: { label: '填写方式' },
    type: { label: '报名方式' }
  },
  INFO: {
    name: { label: '名称', placeholder: '点击输入标题' },
    desc: { label: '说明文本', placeholder: '输入描述说明' },
    imgs: { label: '配图' },
    daterange: { label: '收集时间' },
    activitylocation: { label: '活动地点', placeholder: '输入活动地点' },
    participantDetail: { label: '发布范围' },
    maxParticipantNum: { label: '最大参与人数' },
    isTicket: { label: '开启入场凭证' },
    ticketWriteOffUserScopeDetail: { label: '验票者' },
    nametype: { label: '填写方式' },
    type: { label: '报名方式' }
  },
  PSQ: {
    name: { label: '问卷名称', placeholder: '点击输入问卷名称' },
    desc: { label: '问卷说明文本', placeholder: '输入问卷描述说明' },
    imgs: { label: '问卷配图' },
    daterange: { label: '问卷时间' },
    activitylocation: { label: '地点', placeholder: '输入地点' },
    participantDetail: { label: '发布范围' },
    maxParticipantNum: { label: '最大参与人数' },
    isTicket: { label: '开启入场凭证' },
    ticketWriteOffUserScopeDetail: { label: '验票者' },
    nametype: { label: '填写方式' },
    type: { label: '报名方式' }
  },
  NOTICE: {
    name: { label: '公告名称', placeholder: '点击输入公告标题' },
    desc: { label: '公告说明文本', placeholder: '输入公告描述说明' },
    imgs: { label: '公告配图' },
    daterange: { label: '公告时间' },
    activitylocation: { label: '地点', placeholder: '输入地点' },
    participantDetail: { label: '公告范围' },
    maxParticipantNum: { label: '最大参与人数' },
    isTicket: { label: '开启入场凭证' },
    ticketWriteOffUserScopeDetail: { label: '验票者' },
    nametype: { label: '填写方式' },
    type: { label: '报名方式' }
  }
}
export type themetype =
  | 'YSQD'
  | 'YQYY'
  | 'YFXS'
  | 'YYXY'
  | 'CXCT'
  | 'YTXY'
  | 'JSQS'
  | 'QFLY'
  | 'RCDF'
  | 'HSBJ'
  | 'DEFAULT'
export const THEMESETTINGS: Record<themetype, PageSettings> = {
  DEFAULT: {
    cover: { value: ['/easyform/template/default.jpg'] },
    background: { value: ['/easyform/pc-bg.jpeg'] },
    hideMobileBKG: true,
    themeColor: '#5763E0'
  },
  YSQD: {
    cover: { value: ['/easyform/assets/BKGs/1.png'] },
    background: { color: ['rgba(55, 93, 115, 0.3)'] },
    hideMobileBKG: true,
    themeColor: '#5763E0'
  },
  YQYY: {
    cover: { value: ['/easyform/assets/BKGs/2.png'] },
    background: { color: ['rgba(143, 85, 155, 0.3)'] },
    hideMobileBKG: true,
    themeColor: '#5763E0'
  },
  YFXS: {
    cover: { value: ['/easyform/assets/BKGs/3.png'] },
    hideMobileBKG: true,
    background: { color: ['rgba(209, 219, 229, 0.3)'] },
    themeColor: '#5763E0'
  },
  YYXY: {
    cover: { value: ['/easyform/assets/BKGs/4.png'] },
    hideMobileBKG: true,
    background: { color: ['rgba(33, 85, 153, 0.5)'] },
    themeColor: '#5763E0'
  },
  CXCT: {
    cover: { value: ['/easyform/assets/BKGs/5.png'] },
    hideMobileBKG: true,
    background: { color: ['rgba(0, 31, 69, 0.8)'] },
    themeColor: '#5763E0'
  },
  YTXY: {
    cover: { value: ['/easyform/assets/BKGs/6.png'] },
    hideMobileBKG: true,
    background: { color: ['rgba(213, 121, 95, 0.6)'] },
    themeColor: '#5763E0'
  },
  JSQS: {
    cover: { value: ['/easyform/assets/BKGs/7.png'] },
    hideMobileBKG: true,
    background: { color: ['rgba(227, 227, 255, 0.7)'] },
    themeColor: '#5763E0'
  },
  QFLY: {
    cover: { value: ['/easyform/assets/BKGs/8.png'] },
    hideMobileBKG: true,
    background: { color: ['rgba(227, 227, 255, 0.7)'] },
    themeColor: '#5763E0'
  },
  RCDF: {
    cover: { value: ['/easyform/assets/BKGs/9.png'] },
    hideMobileBKG: true,
    background: { color: ['rgba(255, 237, 173, 0.5)'] },
    themeColor: '#5763E0'
  },
  HSBJ: {
    cover: { value: ['/easyform/assets/BKGs/10.png'] },
    hideMobileBKG: true,
    background: { color: ['rgba(226, 185, 53, 0.5)'] },
    themeColor: '#5763E0'
  }
}
export type theme = {
  label: string
  description?: string
  value: themetype
  cover?: string
  [key: string]: any
}
export const THEME: theme[] = [
  { label: '默认', description: '默认', value: 'DEFAULT', cover: '/easyform/template/default.jpg' },
  { label: '远山青黛', description: '远山青黛', value: 'YSQD', cover: '/easyform/assets/BKGs/1.png' },
  { label: '影泉映月', description: '影泉映月', value: 'YQYY', cover: '/easyform/assets/BKGs/2.png' },
  { label: '月伏西山', value: 'YFXS', description: '月伏西山', cover: '/easyform/assets/BKGs/3.png' },
  { label: '云掩星月', value: 'YYXY', description: '云掩星月', cover: '/easyform/assets/BKGs/4.png' },
  { label: '赤显潮褪', value: 'CXCT', description: '赤显潮褪', cover: '/easyform/assets/BKGs/5.png' },
  { label: '雁塔西映', value: 'YTXY', description: '雁塔西映', cover: '/easyform/assets/BKGs/6.png' },
  { label: '技术巧思', value: 'JSQS', description: '技术巧思', cover: '/easyform/assets/BKGs/7.png' },
  { label: '启发乐园', value: 'QFLY', description: '启发乐园', cover: '/easyform/assets/BKGs/8.png' },
  { label: '日出东方', value: 'RCDF', description: '日出东方', cover: '/easyform/assets/BKGs/9.png' },
  { label: '红色背景', value: 'HSBJ', description: '红色背景', cover: '/easyform/assets/BKGs/10.png' }
]

export const THEMECOLORS = [
  '#0095ff',
  '#005eff',
  '#7e42f5',
  '#f55200',
  '#40a999',
  '#f343c8',
  '#1aa66d',
  '#f5ab31',
  '#f5495d',
  '#5763E0'
]
