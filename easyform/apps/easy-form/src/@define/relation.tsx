export const RelationDesc: any = {
  INCLUDE: (
    <span>
      当<span style={{ color: 'red' }}>选中</span>其中<span style={{ color: 'red' }}>任意一个时</span>出现当前表单项
    </span>
  ),
  EXCLUDE: (
    <span>
      当<span style={{ color: 'red' }}>未选中</span>其中<span style={{ color: 'red' }}>任意一个时</span>出现当前表单项
    </span>
  ),
  INCLUDE_ALL: (
    <span>
      当<span style={{ color: 'red' }}>选中</span>其中<span style={{ color: 'red' }}>全部选项时</span>出现当前表单项
    </span>
  ),
  EXCLUDE_ALL: (
    <span>
      当<span style={{ color: 'red' }}>未选中</span>其中<span style={{ color: 'red' }}>全部选项时</span>出现当前表单项
    </span>
  )
}
const Relation: any[] = [
  {
    label: RelationDesc.INCLUDE,
    value: 'INCLUDE'
  },
  {
    label: RelationDesc.EXCLUDE,
    value: 'EXCLUDE'
  },
  {
    label: RelationDesc.INCLUDE_ALL,
    value: 'INCLUDE_ALL'
  },
  {
    label: RelationDesc.EXCLUDE_ALL,
    value: 'EXCLUDE_ALL'
  }
]

export default Relation
