export const ActivityType = {
  NOTIFY: 'NOTIFY'
}
export const ActivityTab = {
  RUNNING: 'started',
  PART_IN: 'participate',
  CREATED: 'CREATED'
}

/**
 * 活动状态定义
 */

export const Activity_State = {
  START_WAIT: 'start_wait', // 未开始
  START_WILL: 'start_will', //即将开始
  START: 'start', // 进行中
  END_WILL: 'end_will', // 即将结束
  END: 'end' // 已结束
  //   JOINED: 'joined' //已参加
}

export type TActivityType = {
  id: string
  title: string
  color?: string
  meta?: any
}

export const ActivityTypes: TActivityType[] = [
  {
    id: 'INFO',
    title: '信息填报',
    color: '#00A4FF',
    meta: {
      prefixTitle: '填报',
      settings: {
        exclude: []
      }
    }
  },
  {
    id: 'PSQ',
    title: '问卷调查',
    color: '#4DBE9F',
    meta: {
      prefixTitle: '问卷',
      settings: {
        exclude: []
      }
    }
  },
  {
    id: 'VOTE',
    title: '投票',
    color: 'magenta'
  },
  {
    id: 'APPLY',
    title: '报名',
    color: '#FFC45B'
  },
  {
    id: 'NOTICE',
    title: '通知公告',
    color: '#FF629C'
  }
  // {
  //   id: 'qd',
  //   title: '签到打卡',
  //   icon: '/easyform/assets/icon/dk.png'
  // },
  // {
  //   id: 'jl',
  //   title: '接龙',
  //   icon: '/easyform/assets/icon/jl.png'
  // }
]

export const ActivityTypeTitleMap = ActivityTypes.reduce((p, c) => ({ ...p, [c.id]: c.title }), {})
export const ActivityTypeColorMap = ActivityTypes.reduce((p, c) => ({ ...p, [c.id]: c.color }), {})

// type _ActivityState = typeof Activity_State
// export type TActivityState = _ActivityState[keyof _ActivityState]

export const ActivityState = {
  NORMAL: 'NORMAL',
  NOT_FOUND: 'NOT_FOUND',
  CLOSED: 'CLOSED',
  NOT_START: 'NOT_START',
  FINISHED: 'FINISHED',
  PARTICIPATED: 'PARTICIPATED',
  GT_MAX_COUNT: 'GT_MAX_COUNT',
  NOT_ACCESS: 'NOT_ACCESS',
  RE_SUBMIT: 'RE_SUBMIT'
}

export const ActivityStateText = {
  NOT_FOUND: '很抱歉，您访问的活动不存在',
  CLOSED: '您访问的活动已关闭',
  NOT_START: '活动尚未开始哦',
  FINISHED: '活动已结束，敬请期待下期活动！',
  GT_MAX_COUNT: '当前报名人数已满，敬请期待下期活动！',
  NOT_ACCESS: '本活动尚未对您开放哦'
}

type _ActivityState = typeof ActivityState
export type TActivityState = _ActivityState[keyof _ActivityState]
