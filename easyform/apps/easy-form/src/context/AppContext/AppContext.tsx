import React from 'react'
import produce from 'immer'
import { IDENTITY_TYPE, TIdentityValue } from '@/@define'
import { Action } from './ActionType'

type TUser = {
  id: string | number
  name: string
  icon?: string
  remark?: string
  mobile?: string
  category: TIdentityValue //1:教职工,2:本专科生,4:研究生
  isCounselor: boolean
  extra?: any
  adminType?: string
  orgs?: any[]
  // identityType: number[]
  // currentIdentity: TIdentityValue
  // identities: any[]
}

export type TAppState = {
  user: TUser
  ybUser?: any
}

interface IAction {
  type: Action
  payload?: any
}
interface IProvider {
  data: TAppState
  children?: React.ReactNode
}

const initState: TAppState = {
  user: { id: '', name: '', category: 2, isCounselor: false }
}

function init(data: TAppState) {
  return data
}

type TContext = [TAppState, React.Dispatch<IAction>]

const reducer = (state: TAppState, action: IAction) => {
  const { type, payload } = action
  return produce(state, (draft) => {
    switch (type) {
      case Action.INIT: {
        break
      }
      default:
    }
  })
}

const Context = React.createContext<TContext>([initState, (action) => action])

const AppProvider: React.FC<IProvider> = ({ children, data }) => {
  const _reducer = React.useReducer(reducer, data, init)
  return <Context.Provider value={_reducer}>{children}</Context.Provider>
}

const useAppContext = () => {
  const _context = React.useContext(Context)
  if (_context === undefined) {
    throw new Error('must be used within a MessageProvider')
  }
  return _context
}

export { useAppContext, AppProvider }
