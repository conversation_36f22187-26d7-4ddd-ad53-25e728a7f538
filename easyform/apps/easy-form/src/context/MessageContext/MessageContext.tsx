import React from 'react'
import produce from 'immer'
import { Action_Message } from './ActionType'
import { getUniqueId } from '@/utils'
import { getComponentName } from '@/helper'

interface IState {
  messageClient?: any
}

interface IAction {
  type: Action_Message
  payload?: any
}
interface IProvider {
  children?: React.ReactNode
  url: string
}

const initState: IState = {}

function init(url: string) {
  console.log('run init messageContext:', url)
  return {
    messageClient: new EventSource(url)
  }
}

type TContext = [IState, React.Dispatch<IAction>]

const reducer = (state: IState, action: IAction) => {
  const { type, payload } = action
  return produce(state, (draft) => {
    switch (type) {
      case Action_Message.INIT: {
        break
      }
      default:
    }
  })
}

const Context = React.createContext<TContext>([initState, (action) => action])

const MessageProvider: React.FC<IProvider> = ({ children, url }) => {
  const _reducer = React.useReducer(reducer, url, init)
  return <Context.Provider value={_reducer}>{children}</Context.Provider>
}

const useMessageContext = () => {
  const _context = React.useContext(Context)
  if (_context === undefined) {
    throw new Error('must be used within a MessageProvider')
  }
  return _context
}
const useMessageClient = () => {
  const _context = React.useContext(Context)
  if (_context === undefined) {
    throw new Error('must be used within a MessageProvider')
  }
  const [{ messageClient }] = _context
  return messageClient
}

export { useMessageContext, MessageProvider, useMessageClient }
