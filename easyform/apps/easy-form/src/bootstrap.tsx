import ReactDOM from 'react-dom'
import { HashRouter as Router } from 'react-router-dom'
import { Suspense } from 'react'
// import 'antd-mobile/es/global'
import ThemeProvider from '@/ThemeProvider'
import { MessageProvider } from '@/context/MessageContext'
import themes from '@/themes'
import { PageLoading } from '@/components/Loading'

import App from './App.tsx'
ReactDOM.render(<App />, document.getElementById('root'))
