/* eslint-disable */
// @ts-nocheck
import { OptionType } from '@/interfaces'
import {
  AssignmentType,
  TaskStateType,
  TaskOptionsType,
  ITaskProcessOption,
  TaskProcessActions,
  TaskProcessActionOptions,
  TaskProcessNameType,
  TaskProcessActionType,
  TaskTypeMaps,
  TaskType
} from '@/interfaces/task'

export const TASK_ID_KEY = 'id'
/**
 * 任务状态定义
 */
export const StateMap: TaskOptionsType<TaskStateType> = {
  prepare: {
    status: 'default',
    text: '未开始'
  },
  running: {
    status: 'processing',
    text: '进行中'
  },
  end: {
    status: 'error',
    text: '已结束'
  }
}

/**
 * 委派待办状态定义
 */
export const AssignmentStateMap: TaskOptionsType<AssignmentType> = {
  unread: {
    status: 'default',
    text: '未读'
  },
  read: {
    status: 'processing',
    text: '已读'
  },
  done: {
    status: 'success',
    text: '已完成'
  }
}

export const UndoAssignmentStateMap: Partial<TaskOptionsType<AssignmentType>> = {
  unread: {
    status: 'default',
    text: '未读'
  },
  read: {
    status: 'processing',
    text: '已读'
  }
}

/**
 * 任务状态选项
 */
export const stateEnum = createOptionsMap<TaskStateType>(StateMap, false)

/**
 * 委派状态选项
 */
export const assignmentStateEnum = createOptionsMap<AssignmentType>(AssignmentStateMap, false)

/**
 * 未处理的状态
 */
export const UndoAssignmentStateEnum = createOptionsMap<AssignmentType>(UndoAssignmentStateMap, false)

/**
 * 创建任务状态下拉控件选项
 * @param mapObj
 * @param allOption
 * @returns
 */
function createOptionsMap<T extends string>(
  mapObj: Partial<TaskOptionsType<T>>,
  allOption?: { label: any; value?: string } | boolean
) {
  const optionsMap = new Map<string, string>()
  if (allOption !== false) {
    if (typeof allOption !== 'object') {
      allOption = {
        value: undefined,
        label: '全部'
      }
    }
    optionsMap.set(allOption.value || '', allOption.label || '全部')
  }
  for (const key in mapObj) {
    if (Object.prototype.hasOwnProperty.call(mapObj, key)) {
      const state = mapObj[key]
      optionsMap.set(key, state.text)
    }
  }
  return optionsMap
}

/**
 * 处理下派流程选项
 * @param options
 * @returns
 */
export function parseTaskProcessOptions<T = string>(options: ITaskProcessOption<T>[]) {
  return options.map((option, index) => {
    return {
      label: option.name,
      ...option
    }
  })
}

export const TASK_PROCESS_OPTIONS: ITaskProcessOption<TaskProcessNameType>[] = [
  {
    name: '院系管理员',
    value: 'yxgly'
  },
  {
    name: '辅导员',
    value: 'fdy'
  },
  {
    name: '本科生',
    value: 'bks'
  }
]

export const TASK_PROCESS_ACTIONS: TaskProcessActions = {
  xp: '下派',
  cl: '处理'
}

export const TASK_PROCESS_ACTIONS_OPTIONS: TaskProcessActions<TaskProcessActionOptions> = {
  xp: {
    color: 'processing'
  },
  cl: {
    color: 'success'
  }
}

/**
 * 获取任务流程一项
 * @param value
 * @returns
 */
export function getTaskProcessOption(value: string) {
  return TASK_PROCESS_OPTIONS.find((opt) => value.indexOf(opt.value) === 0) || { value, name: value }
}

/**
 * 获取下派过程名称
 * @param processName
 * @param action
 * @returns
 */
export function getProcessAssignmentName(processName: TaskProcessNameType, action: TaskProcessActionType) {
  console.log(processName, action)
  const assignmentOption = TASK_PROCESS_OPTIONS.find((opt) => opt.value === processName)
  if (assignmentOption) {
    return assignmentOption.name + TASK_PROCESS_ACTIONS[action]
  }
  return processName + TASK_PROCESS_ACTIONS[action]
}

/**
 * 任务类型
 */
export const TASK_TYPES_MAP: TaskTypeMaps = {
  EZForm: '表单任务'
}

/**
 * 任务类型选项列表
 */
export const TASK_TYPES_OPTIONS: OptionType<TaskType | ''>[] = Object.keys(TASK_TYPES_MAP).map((type: TaskType) => {
  return {
    label: TASK_TYPES_MAP[type],
    value: type
  }
})
