/* eslint-disable */
// @ts-nocheck
import { message } from 'antd'

export const YB_EZFORM_URL = window['YB_EZFORM_URL'] || '/easyform/creation'
export const YB_EZFORM_COMPLETE_URL = window['YB_EZFORM_COMPLETE_URL'] || '/easyform/submit'

/**
 * 后台静态资源访问前缀
 */
export const YB_STATIC_PREFIX = (window as any).APP_CONFIG.server.api + '/static/images'

const HttpStatusCode = {
  Continue: 100,
  SwitchingProtocols: 101,
  Processing: 102,
  EarlyHints: 103,
  Ok: 200,
  Created: 201,
  Accepted: 202,
  NonAuthoritativeInformation: 203,
  NoContent: 204,
  ResetContent: 205,
  PartialContent: 206,
  MultiStatus: 207,
  AlreadyReported: 208,
  ImUsed: 226,
  MultipleChoices: 300,
  MovedPermanently: 301,
  Found: 302,
  SeeOther: 303,
  NotModified: 304,
  UseProxy: 305,
  Unused: 306,
  TemporaryRedirect: 307,
  PermanentRedirect: 308,
  BadRequest: 400,
  Unauthorized: 401,
  PaymentRequired: 402,
  Forbidden: 403,
  NotFound: 404,
  MethodNotAllowed: 405,
  NotAcceptable: 406,
  ProxyAuthenticationRequired: 407,
  RequestTimeout: 408,
  Conflict: 409,
  Gone: 410,
  LengthRequired: 411,
  PreconditionFailed: 412,
  PayloadTooLarge: 413,
  UriTooLong: 414,
  UnsupportedMediaType: 415,
  RangeNotSatisfiable: 416,
  ExpectationFailed: 417,
  ImATeapot: 418,
  MisdirectedRequest: 421,
  UnprocessableEntity: 422,
  Locked: 423,
  FailedDependency: 424,
  TooEarly: 425,
  UpgradeRequired: 426,
  PreconditionRequired: 428,
  TooManyRequests: 429,
  RequestHeaderFieldsTooLarge: 431,
  UnavailableForLegalReasons: 451,
  InternalServerError: 500,
  NotImplemented: 501,
  BadGateway: 502,
  ServiceUnavailable: 503,
  GatewayTimeout: 504,
  HttpVersionNotSupported: 505,
  VariantAlsoNegotiates: 506,
  InsufficientStorage: 507,
  LoopDetected: 508,
  NotExtended: 510,
  NetworkAuthenticationRequired: 511
}
Object.entries(HttpStatusCode).forEach(([key, value]) => {
  HttpStatusCode[value] = key
})

export { HttpStatusCode }

export const getResponseError = (res: any, defaultMsg?: string) => {
  let errMsg: string = typeof res === 'object' ? res.msg || res.resultMsg : '',
    errCode: number = typeof res === 'object' ? res.code || res.resultCode : 0

  if (!res || errCode >= 400) {
    const defaultError = errCode ? HttpStatusCode[res.code] : undefined
    errMsg = errMsg || defaultError || defaultMsg
  }
  if (typeof res === 'number' && HttpStatusCode[res]) {
    errMsg = HttpStatusCode[res]
  } else if (typeof res === 'string' && res) {
    errMsg = res
  }
  if (/\{\"code\"/.test(errMsg)) {
    try {
      const msg = JSON.parse(errMsg)
      errMsg =
        typeof msg === 'object' && msg ? msg.msg || msg.message || (msg.code && HttpStatusCode[msg.code]) : errMsg
    } catch (error) {}
  }
  return errMsg
}

/**
 * 提交成功处理
 * @param res
 * @returns
 */
export function postSuccess(res: any, successMsg?: string) {
  let errMsg: string = getResponseError(res, 'REQUEST ERROR')
  errMsg
    ? message.warning({
        content: `${errMsg}`
      })
    : message.success(successMsg || `操作成功`)
  return res
}

/**
 * 保存成功处理
 * @param res
 * @param successMsg
 * @returns
 */
export function saveSuccess(res: any, successMsg?: string) {
  return postSuccess(res, successMsg || '保存成功')
}

/**
 * 创建成功
 * @param res
 * @param successMsg
 * @returns
 */
export function createSuccess(res: any, successMsg?: string) {
  return postSuccess(res, successMsg || '创建成功')
}

/**
 * 更新成功
 * @param res
 * @param successMsg
 * @returns
 */
export function updateSuccess(res: any, successMsg?: string) {
  return postSuccess(res, successMsg || '更新成功')
}

/**
 * 删除成功
 * @param res
 * @param successMsg
 * @returns
 */
export function deleteSuccess(res: any, successMsg?: string) {
  return postSuccess(res, successMsg || '删除成功')
}

/**
 * 发送成功
 * @param res
 * @param successMsg
 * @returns
 */
export function sendSuccess(res: any, successMsg?: string) {
  return postSuccess(res, successMsg || '发送成功')
}
/**
 * 提交失败处理
 * @param e
 * @returns
 */
export function postFail(e: any, errorMsg?: string) {
  let errMsg: string
  let code = e.code
  let errData = e.response
  if (typeof code === 'number' && HttpStatusCode[code]) {
    errMsg = HttpStatusCode[code]
  }
  if (errData) {
    errMsg = getResponseError(errData, errMsg)
  }
  message.warning({
    content: errMsg || errorMsg || 'REQUEST ERROR'
  })
  return this
}

/**
 * 支持的 SQL 操作符类型
 */
type SQLOperatorType = 'like' | 'llike' | 'rlike' | 'eq' | 'lt' | 'gt' | 'in'

type SQLOderType = 'descend' | 'ascend' | 'DESC' | 'ASC' | 'desc' | 'asc'

export const SQLOder: { [key in SQLOderType]: 'DESC' | 'ASC' } = {
  desc: 'DESC',
  asc: 'ASC',
  descend: 'DESC',
  ascend: 'ASC',
  DESC: 'DESC',
  ASC: 'ASC'
}

/**
 * 操作符定义
 */
export const SQLOperator: { [key in SQLOperatorType]: string } = {
  like: "$n LIKE '%$s%'",
  llike: "$n LIKE '%$s'",
  rlike: "$n LIKE '$s%'",
  eq: "$n='$s'",
  lt: '$n < $d',
  gt: '$n > $d',
  in: '$n in $a'
}

/**
 * 生成 in 操作符 对应的值
 * @param obj
 * @returns
 */
function _getSQLwhereInString(obj: any) {
  let arr = Array.isArray(obj) ? obj : [obj]
  let str = '['
  for (let index = 0; index < arr.length; index++) {
    const val = arr[index]
    if (index && val !== '') {
      str += ','
      str += typeof val === 'number' ? val : `'${val + ''}'`
    }
  }
  return str + ']'
}

/**
 * 生成 SQL WHERE 查询语句
 * @description 默认是 AND 操作，用于表单查询
 * @param obj
 * @param operator
 * @returns
 */
export function createSQLQuery(obj: any, operator?: { [key: string]: SQLOperatorType }) {
  let arr = []
  for (const key in obj) {
    if (Object.prototype.hasOwnProperty.call(obj, key)) {
      const value = obj[key]
      const op = operator && operator[key] && SQLOperator[operator[key]] ? SQLOperator[operator[key]] : SQLOperator.eq

      arr.push(
        op
          .replace('$n', key)
          .replace(/(\$s|\$d)/, value)
          .replace('$a', _getSQLwhereInString(value))
      )
    }
  }

  return arr.join(' AND ')
}

/**
 * 创建SQL ORDER
 * @param obj
 * @returns
 */
export function createSQLOder(obj: { [key: string]: SQLOderType }) {
  let arr = []
  for (const key in obj) {
    if (Object.prototype.hasOwnProperty.call(obj, key)) {
      const value = obj[key]
      if (value && SQLOder[value]) {
        arr.push(`${key} ${SQLOder[value]}`)
      }
    }
  }
  return arr.join(', ')
}

type ColumnHideInType = 'descriptions' | 'form' | 'setting' | 'table' | 'search'

/**
 * 创建查询表格列并设置显示位置
 * @param obj
 * @param show
 * @param hide
 * @returns
 */
export function createColumn<T = any>(obj: T, show?: ColumnHideInType | ColumnHideInType[] | string, hide?: boolean) {
  return {
    ...obj,
    hideInDescriptions: show && (hide ? show.indexOf('descriptions') > -1 : show.indexOf('descriptions') == -1),
    hideInForm: show && (hide ? show.indexOf('form') > -1 : show.indexOf('form') == -1),
    hideInSetting: show && (hide ? show.indexOf('setting') > -1 : show.indexOf('setting') == -1),
    hideInTable: show && (hide ? show.indexOf('table') > -1 : show.indexOf('table') == -1),
    hideInSearch: show && (hide ? show.indexOf('search') > -1 : show.indexOf('search') == -1)
  }
}
