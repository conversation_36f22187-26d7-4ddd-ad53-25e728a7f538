import { properties } from '../FormItemWrapper/def'
export const DefaultInitValue = [
  {
    label: '图片',
    value: 'image/*'
  },
  {
    label: '视频',
    value: 'video/*'
  },
  {
    label: '音频',
    value: 'audio/*'
  },
  {
    label: 'PDF',
    value: '.pdf'
  },
  {
    label: 'DOC文档',
    value: '.doc,.docx'
  },
  {
    label: 'PPT文档',
    value: '.ppt,.pptx'
  },
  {
    label: 'EXCEL文档',
    value: '.xls,.xlsx'
  }
]

export default {
  properties: {
    ...properties,
    acceptType: {
      label: '文件类型',
      component: 'Checkbox',
      props: {
        options: DefaultInitValue
      }
    },
    disabled: {
      label: '禁用',
      component: 'Switch'
    },
    allowDownloadWhenDisabled: {
      label: '禁用时允许下载',
      component: 'Switch',
      defaultValue: true
    }
  }
}
