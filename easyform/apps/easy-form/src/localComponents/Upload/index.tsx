import React, { forwardRef, useEffect, useCallback } from 'react'
import { Upload as AUpload, Button, UploadProps as AUploadProps } from 'antd'
import { UploadOutlined } from '@ant-design/icons'
import { Box } from '@mui/system'
import FormItemWrapper, { FormItemProps } from '../FormItemWrapper'
import { RcFile, UploadFile } from 'antd/lib/upload'

interface UploadFileInfo extends Partial<UploadFile> {
  _fileName?: string
  _name?: string
  fileName?: string
  fileKey?: string
  loading?: boolean
}

export type UploadProps = {
  uploadUrl?: string
  imageUrlPrefix?: string
  onChange?: (value: string[] | string, fileList?: UploadFile[], e?: any) => void
  fileList?: any
  value?: any
  acceptType?: string | string[]
} & Partial<Omit<AUploadProps, 'uploadUrl' | 'fileList' | 'onChange'>> &
  FormItemProps

function getFileValue(file: any) {
  let res = file.fileKey || file.uid
  if (file.status === 'done' && file.response && file.response.Data && file.name) {
    res = file.response.Data[file.name] || res || undefined
  }
  return res
}
function getFileListValue(list: any) {
  const valueList = (list || [])
    .map((file: any) => {
      return getFileValue(file)
    })
    .filter(Boolean)
  return valueList
}

const Upload = React.forwardRef<any, UploadProps>(
  ({ uploadUrl='/easyform-api/attachment/upload/easyform',imageUrlPrefix='/easyform-api/attachment/get/temp/easyform/', onChange, fileList, value, acceptType, ...other }, ref) => {
    console.log('value=---',value)
    const [file, setFile] = React.useState<any[]>([])
    const fetchFileInfo = async (fileId: string) => {
      try {
        const response = await fetch(`/easyform-api/attachment/info/temp/easyform/${fileId}`)
        const data = await response.json()
        const [key, id] = data.name.split('/')
        return {
          status: 'done',
          ...data,
          uid: id,
          name: data.userMetadata.Originalfilename,
          fileName: data.userMetadata.Originalfilename,
          fileKey: id,
          default: true,
          url: `${imageUrlPrefix}${id}`
        }
      } catch (error) {
        console.error('error:', error)
      }
    }

    const fetchAllFileInfo = async (value: string | string[]) => {
      if (Array.isArray(value)) {
        const fileInfos = await Promise.all(value.map(fetchFileInfo))
        setFile(fileInfos)
      } else if (typeof value === 'string') {
        const fileInfo = await fetchFileInfo(value)
        setFile([fileInfo])
      }
      return []
    }
    useEffect(() => {
      if (value) {
        fetchAllFileInfo(value)
      }
    }, [])

    const handleChange = useCallback(
      (e) => {
        const list = (Array.isArray(e) ? e : e.fileList) || []
        const file = e.file
        const _list = list.filter((file: UploadFileInfo) => {
          if (file && file.status === 'done' && file.response && !file.response.Data) {
            return false
          }
          file.fileName = file.fileKey = getFileValue(file)
          if (!file.url && file.fileName && file.status === 'done') {
            file.url = `${imageUrlPrefix}${Object.values(file?.response?.Data)[0]}`
          }
          return true
        })
        if (onChange && file && ((file.status === 'done' && !file.error) || file.status === 'removed')) {
          const value = getFileListValue(list)
          onChange(value, list, e)
        }
        setFile(_list)
      },
      [onChange]
    )
    
    const customRequest = useCallback(
      ({ file, onSuccess, onError, onProgress }: any) => {
        if (!uploadUrl) {
          onError(new Error('uploadUrl未定义'))
          return
        }
        
        const formData = new FormData()
        formData.append('file', file)
        
        fetch(uploadUrl, {
          method: 'POST',
          body: formData
        })
          .then((res) => res.json())
          .then((data) => {
            if (data.Code === 1) {
              const fileKey = Object.values(data.Data)[0] as string
              const newFile = {
                uid: file.uid,
                name: file.name,
                status: 'done',
                url: `${imageUrlPrefix}${fileKey}`,
                fileKey: fileKey,
                fileName: fileKey
              }
              onSuccess(newFile)
            } else {
              onError(new Error(data.Msg))
            }
          })
          .catch((error) => {
            onError(error)
          })
      },
      [imageUrlPrefix, uploadUrl]
    )
    return (
      <AUpload
        onChange={handleChange}
        // customRequest={customRequest}
        action={uploadUrl}
        fileList={file}
        accept={Array.isArray(acceptType) ? acceptType.join(',') : acceptType}
        {...other}
      >
        <Button icon={<UploadOutlined />}>上传</Button>
      </AUpload>
    )
  }
)

export default React.forwardRef<HTMLDivElement, UploadProps>(({ children, ...props }, ref) => {
  console.log('children===', children)
  return (
    <FormItemWrapper {...props}>
      <Upload>{children}</Upload>
    </FormItemWrapper>
  )
})
export { default as def } from './def'
