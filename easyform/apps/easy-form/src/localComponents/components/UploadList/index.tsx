import React, { forwardRef, useEffect, useCallback, useState } from 'react'
import { Box } from '@mui/system'
import { RcFile, UploadFile } from 'antd/lib/upload'

export interface UploadListProps {
  imageUrlPrefix?: string
  value?: any
}


export default React.forwardRef<HTMLDivElement, UploadListProps>(
  ({ imageUrlPrefix = '/easyform-api/attachment/get/temp/easyform/', value }, ref) => {
    const [files, setFiles] = useState<Array<{
      status: string,
      uid: string,
      name: string,
      fileName: string,
      fileKey: string,
      default: boolean,
      url: string
    }>>([])
    
    const fetchFileInfo = async (fileId: string) => {
      try {
        const response = await fetch(`/easyform-api/attachment/info/temp/easyform/${fileId}`)
        const data = await response.json()
        const [key, id] = data.name.split('/')
        return {
          status: 'done',
          ...data,
          uid: id,
          name: data.userMetadata.Originalfilename,
          fileName: data.userMetadata.Originalfilename,
          fileKey: id,
          default: true,
          url: `${imageUrlPrefix}${id}`
        }
      } catch (error) {
        console.error('error:', error)
        return null
      }
    }

    const fetchAllFileInfo = async (value: string | string[]) => {
      if (Array.isArray(value)) {
        const fileInfos = await Promise.all(value.map(fetchFileInfo))
        setFiles(fileInfos.filter(Boolean))
      } else if (typeof value === 'string') {
        const fileInfo = await fetchFileInfo(value)
        if (fileInfo) {
          setFiles([fileInfo])
        }
      }
    }
    
    useEffect(() => {
      if (value) {
        fetchAllFileInfo(value)
      }
    }, [value, imageUrlPrefix])
    
    return (
      <div style={{paddingLeft: '16px', display:'flex', flexDirection:'column',gap:'4px'}} ref={ref}>
        {files.map((file:any) => (
          <a key={file.uid} onClick={()=>window.open(file.url)}>{file.name}</a>
        ))}
      </div>
    )
  }
)
