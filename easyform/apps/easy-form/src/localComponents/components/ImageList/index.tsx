import React, { useState } from 'react'
import { Image, ImageViewer } from 'antd-mobile'
import { Box, styled } from '@mui/system'

export interface ImageListProps {
  imgs?: string[]
  imageUrlPrefix?: string
  singleStyle?: { [k: string]: string | number | undefined }
}
/**
 * 1.自定义css样式采用 jss 方案 + css 变量 + tailwind原子化css,根据场景自行灵活使用
 * 2.使用主题相关颜色时注意主题定义方案，@/themes/default.ts
 *
 *  注意： 仅仅 styled创建的组件以及@mui/system中的Box组件 支持sx属性
 */

//参照朋友圈展示图片
//一张图原图展示
//两张及两张以上写布局---3x3布局点开显示原图---√

const Img = styled(Image)(({ theme }) => ({
  borderRadius: 8,
  '& .adm-image-tip': {
    minHeight: '100px',
    '&>svg': {
      width: '50%',
      height: '50%'
    }
  }
}))

export default React.forwardRef<HTMLDivElement, ImageListProps>(({ imgs, singleStyle, imageUrlPrefix = '' }, ref) => {
  const [previewImage, setPreviewImage] = React.useState<boolean>(false)
  const previewRef = React.useRef<any>(null)
  const handleClick = (image: string, i: number) => {
    console.log(image, i)
    setPreviewImage(true)
    previewRef.current.swipeTo(i)
  }

  return (
    <>
      <Box ref={ref}>
        {imgs && imgs.length > 0 ? (
          imgs.length > 1 ? (
            <Box className='grid grid-cols-3 gap-4'>
              {imgs?.map((item, i) => (
                <Img
                  key={i}
                  fit='contain'
                  src={imageUrlPrefix + item}
                  lazy
                  onClick={() => {
                    handleClick(item, i)
                  }}
                />
              ))}
            </Box>
          ) : (
            <Img
              fit='contain'
              src={imageUrlPrefix + imgs[0]}
              lazy
              sx={{
                maxWidth: '72%',
                ...singleStyle
              }}
              onClick={() => {
                handleClick(imgs[0], 0)
              }}
            />
          )
        ) : null}
      </Box>
      <ImageViewer.Multi
        ref={previewRef}
        getContainer={() => document.body}
        images={imgs?.map((img) => imageUrlPrefix + img) || []}
        visible={previewImage}
        onClose={() => {
          setPreviewImage(false)
        }}
      />
    </>
  )
})
