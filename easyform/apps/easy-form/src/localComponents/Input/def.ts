import { properties } from '../FormItemWrapper/def'
export const DefaultInitValue = [
  { label: '无', value: '' },
  { label: '登录人姓名', value: '{{userName}}' },
  { label: '登录人学工号', value: '{{code}}' },
  { label: '登录人手机号', value: '{{phone}}' },
  { label: '登录人机构', value: '{{org}}' },
  { label: '登录人专业', value: '{{speciality}}' },
  { label: '登录人班级', value: '{{class}}' }
]
export default {
  properties: {
    ...properties,
    placeholder: {
      label: '输入提示',
      component: 'Input'
    },
    ruleType: {
      label: '校验规则',
      component: 'Select',
      props: {
        options: [
          {
            label: '无',
            value: ''
          },
          {
            label: '手机号',
            value: 'mobile'
          },
          {
            label: '座机号',
            value: 'tel'
          },
          {
            label: '邮箱',
            value: 'email'
          },
          {
            label: '身份证号',
            value: 'id'
          },
          {
            label: '车牌号',
            value: 'license'
          },
          {
            label: 'ip地址',
            value: 'ip'
          },
          {
            label: '数字',
            value: 'number'
          },
          {
            label: '整数',
            value: 'integer'
          }
        ]
      }
    },
    // readOnly: {
    //   label: '只读',
    //   component: 'Switch'
    // },
    maxLength: {
      label: '最大输入长度',
      component: 'Input',
      props: {
        type: 'number'
      }
    },
    initialValue: {
      label: '初始值',
      component: 'Select',
      props: {
        options: DefaultInitValue
      }
    }
  }
}
