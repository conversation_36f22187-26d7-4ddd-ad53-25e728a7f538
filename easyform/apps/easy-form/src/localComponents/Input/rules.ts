const Rules = {
  //密码  // 以字母开头，长度在6~18之间，只能包含字母、数字和下划线
  password: {
    reg: /^[a-zA-Z]\w{5,17}$/,
    message: '字母开头，包含字母、数字和下划线的6-18个字符'
  },
  //手机号
  mobile: {
    reg: /^1[3-9]\d{9}$/,
    message: '手机号不合法'
  },
  //固定电话
  tel: {
    reg: /^(\d{3,4}-)?\d{7,8}$/,
    message: '电话不合法'
  },
  //中文字符
  chinese: {
    reg: /^[\u4e00-\u9fa5]{0,}$/,
    message: '请输入中文字符'
  },
  //url
  url: {
    reg: /^(?:(http|https|ftp):\/\/)?((|[\w-]+\.)+[a-z0-9]+)(?:(\/[^/?#]+)*)?(\?[^#]+)?(#.+)?$/i,
    message: '非法url'
  },
  email: {
    reg: /^[\w-]+(\.[\w-]+)*@[\w-]+(\.[\w-]+)+$/,
    message: '非法邮箱'
  },
  //IP
  ip: {
    reg: /^((\\d|[1-9]\\d|1\\d\\d|2[0-4]\\d|25[0-5]|[*])\\.){3}(\\d|[1-9]\\d|1\\d\\d|2[0-4]\\d|25[0-5]|[*])$/,
    message: '非法IP'
  },
  //MAC
  mac: {
    reg: /^[A-F0-9]{2}(-[A-F0-9]{2}){5}$/,
    message: '非法MAC地址'
  },
  //纯数字
  digit: {
    reg: /^\d+$/,
    message: '请输入纯数字'
  },
  //数字（整数或小数）
  number: {
    reg: /^-?\d+(\.{1}\d+)?$/,
    message: '请输入合法数字'
  },
  //整数
  integer: {
    reg: /^-?\d+$/,
    message: '请输入整数'
  },
  //正整数
  posinteger: {
    reg: /^[1-9]\d*$/,
    message: '请输入正整数'
  },
  //正数（含小数）
  posnumber: {
    reg: /^(0|[1-9][0-9]*)(\.\d+)?$/,
    message: '请输入正数'
  },
  //浮点型
  float: {
    reg: /^(-?\d+)(\.\d+)?$/,
    message: '请输入浮点树'
  },
  //身份证号
  id: {
    reg: /^[1-9](\d{13}|\d{16})[0-9Xx]$/,
    message: '身份证号不合法'
  },
  //邮政编码
  zipcode: {
    reg: /^[0-9]\d{5}$/,
    message: '邮政编码不合法'
  },
  //QQ
  qq: {
    reg: /^[1-9][0-9]{4,9}$/,
    message: '请输入正确QQ'
  },
  //车牌号
  license: {
    reg: /^[京津冀晋蒙辽吉黑沪苏浙皖闽赣鲁豫鄂湘粤桂琼川贵云渝藏陕甘青宁新]{1}[ABCDEFGHJKLMNOPQRSTUVWXY]{1}[0-9A-Z]{5,6}$/u,
    message: '车牌号不合法'
  }
}

export type TRuleType = keyof typeof Rules

export default Rules
