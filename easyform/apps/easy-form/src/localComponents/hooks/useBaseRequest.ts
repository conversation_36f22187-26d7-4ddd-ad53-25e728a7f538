import { easyformAxios, yibanAxios } from '@/utils/axios'
import { useRequest } from 'ahooks'

type TParamFn = (...args: any[]) => any

export default function useBaseRequest(paramFn: TParamFn, options?: Record<string, any>) {
  return useRequest<any, any>((...args: any[]) => yibanAxios(paramFn.apply(undefined, [...args])), options)
}

export function useEasyFormRequest(paramFn: TParamFn, options?: Record<string, any>) {
  return useRequest<any, any>((...args: any[]) => easyformAxios(paramFn.apply(undefined, [...args])), options)
}
