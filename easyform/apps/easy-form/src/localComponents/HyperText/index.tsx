import React, { forwardRef } from 'react'
import { Typography } from 'antd'
import { Box } from '@mui/system'
const { Title, Paragraph, Text, Link } = Typography
export interface HyperTextProps {
  text?: string
  copyable?: boolean
  textAlign?: 'center' | 'left' | 'right'
  href?: string
  target?: '_blank' | '_self'
  textColor?: any
  textSize?: any
}
export default forwardRef((prop: HyperTextProps, ref) => {
  const {
    text = '',
    textAlign = 'left',
    textColor,
    href,
    textSize,
    copyable = false,
    target = '_blank',
    ...props
  } = prop
  return (
    <>
      <Box sx={{ textAlign: textAlign }} ref={ref} className={'baseComponent-hyperText'}>
        {text ? (
          <Link href={href} target={target} style={{ color: textColor, fontSize: textSize?.value }} copyable={copyable}>
            {text}
          </Link>
        ) : (
          <Box sx={{ color: textColor ?? 'text.hint' }}>在此编辑超链接</Box>
        )}
      </Box>
    </>
  )
})

export { default as def } from './def'
