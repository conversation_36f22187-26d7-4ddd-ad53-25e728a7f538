export default {
  properties: {
    text: {
      label: '超链接文本',
      component: 'TextArea'
    },
    href: {
      label: '超链接地址',
      component: 'TextArea'
    },
    textSize: {
      label: '文本大小',
      component: 'Slider',
      props: {
        unitOptions: ['px'],
        unitRanges: [{ min: 0, max: 50 }]
        //disableSliderUnit: ['px']
      }
    },
    textColor: {
      label: '链接颜色',
      component: 'ColorPicker',
      props: {
        value: 'rgb(22, 119, 255)', //超链接的颜色
        valueType: 'toRgbString'
      }
    },
    textAlign: {
      label: '对齐方式',
      component: 'Select',
      props: {
        value: 'left',
        options: [
          {
            label: '居中',
            value: 'center'
          },
          {
            label: '左对齐',
            value: 'left'
          },
          {
            label: '右对齐',
            value: 'right'
          }
        ]
      }
    },
    target: {
      label: '超链接地址打开方式',
      component: 'Select',
      props: {
        value: '_blank',
        options: [
          {
            label: '新页面打开',
            value: '_blank'
          },
          {
            label: '当前页面打开',
            value: '_self'
          }
        ]
      }
    }
    // copyable: {
    //   label: '文本可复制',
    //   component: 'Switch'
    // },
  }
}
