import OrgView from './OrgView'
import ListView from './ListView'
import GroupView from './GroupView'
import PositionsetView from './PositionsetView'
import ClazzUserView from './ClazzUserView'
import ClazzView from './ClazzView'
export { OrgView, ListView, GroupView, PositionsetView, ClazzUserView, ClazzView }

export type TSelectedItem = {
  name: string
  code: string
}
export interface IViewProps {
  // onDestroyView?: (index: number) => void
  onEnterView?: (extra?: any, type?: string) => void
  onCheck?: (type: string, itemData: TSelectedItem, checked?: boolean) => void
  selected?: any
  // index?: number
  viewType: string
}
export type breadcrumbsDataItem = {
  id: string
  name: string
}
