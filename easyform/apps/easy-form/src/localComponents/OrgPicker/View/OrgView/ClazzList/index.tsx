import React from 'react'
import { Box, styled } from '@mui/system'
import { <PERSON>ton, <PERSON>bs, DotLoading, InfiniteScroll, Avatar, SearchBar } from 'antd-mobile'
import List from '../../../List'
import { Collapse, TabPanel, Typography } from '@/components'
import { useEasyFormRequest } from '@/hooks'
import { loadClazz } from '../../../api'
import { IconClazz } from '../../../icon'
import { InfiniteScrollContent } from '../index'
import { BtnEnter } from '../OrgList'
import { useMemoizedFn } from 'ahooks'

interface IListProps {
  orgCode: string
  orgName?: string
  onCheck?: (type: string, itemData: any, checked?: boolean) => void
  selected?: any
  onEnterView: (code: string, name?: string, viewType?: string) => void
}

export default function ({ orgCode, onCheck, selected, orgName, onEnterView }: IListProps) {
  const [{ pageNo, pageSize }, setPagination] = React.useState({ pageNo: 1, pageSize: 20 })
  const [hasMore, setHasMore] = React.useState(true)
  const [list, setList] = React.useState<any>([])
  const [keywords, setKeywords] = React.useState<string>()
  const { runAsync: request } = useEasyFormRequest(loadClazz, {
    manual: true
  })
  const loadMore = useMemoizedFn(async () => {
    const _data = await request(orgCode, { pageNo, pageSize, name: keywords })
    console.log('run load more:', pageNo, pageSize)
    const _hasMore = _data.total > pageSize * pageNo
    setPagination((prev) => ({ ...prev, pageNo: prev.pageNo + 1 }))
    setHasMore(_hasMore)
    setList([...list, ..._data.data])
  })
  const handleCheck = useMemoizedFn((itemData: any, checked?: boolean) => {
    if (typeof onCheck === 'function') {
      onCheck('clazz', itemData, checked)
    }
  })

  const handleChangeView = useMemoizedFn((code: string, name?: string) => {
    if (typeof onEnterView === 'function') {
      onEnterView(code, name, 'clazzUser')
    }
  })
  const handleSearch = async (sv: string) => {
    setKeywords(sv)
    const _data = await request(orgCode, { pageNo: 1, pageSize: 20, name: sv, orgCode: orgCode })
    const _hasMore = _data.total > pageSize * pageNo
    setHasMore(_hasMore)
    setList([..._data.data])
    setPagination((prev) => ({ ...prev, pageNo: _hasMore ? 1 + 1 : 1 }))
  }
  const handleClearSearch = async () => {
    setKeywords(void 0)
    const _data = await request(orgCode, { pageNo: 1, pageSize: 20, name: void 0, orgCode: orgCode })
    const _hasMore = _data.total > pageSize * pageNo
    setHasMore(_hasMore)
    setList([..._data.data])
    setPagination((prev) => ({ ...prev, pageNo: _hasMore ? 1 + 1 : 1 }))
  }

  return (
    <Box>
      <Box>
        <SearchBar onSearch={handleSearch} onClear={handleClearSearch} placeholder='请输入关键字搜索' />
      </Box>
      <List>
        {list.length === 0 ? (
          <Typography sx={{ mt: 2 }} align='center' variant='subtitle2'>
            当前机构暂无班级数据
          </Typography>
        ) : (
          <>
            {list.map((l: any) => (
              <List.Item
                checked={!!selected?.clazz?.find((n: any) => n.code === l.code)}
                onCheck={(c) =>
                  handleCheck({ code: l.code, name: `${l.name}${orgName ? '(' + orgName + ')' : ''}` }, c)
                }
                key={l.code}
                prefix={<IconClazz style={{ color: '#999' }} width='16px' height='16px' />}
                checkable
                arrow={
                  <BtnEnter
                    text='进入班级'
                    disabled={!!selected?.clazz?.find((n: any) => n.code === l.code)}
                    onClick={() => handleChangeView(l.code, l.name)}
                  />
                }
                title={l.name}
              />
            ))}
          </>
        )}
        <InfiniteScroll hasMore={hasMore} loadMore={loadMore}>
          <InfiniteScrollContent hasMore={hasMore} />
        </InfiniteScroll>
      </List>
    </Box>
  )
}
