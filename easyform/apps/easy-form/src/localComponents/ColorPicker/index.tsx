import React, { forwardRef, useMemo } from 'react'
import { ColorPicker, ColorPickerProps } from 'antd'
import type { Color } from 'antd/es/color-picker'
import { Box } from '@mui/system'
import { SliderValue } from '@/Slider'
import { ValueType } from './def'
import FormItemWrapper, { FormItemProps } from '../FormItemWrapper'
//export interface ColorPickerProps {}

/**
 *
 * @param valueType 返回的颜色string的格式，antd的组件默认返回的是Color对象
 * @returns
 */
const _ColorPicker = ({ onChange, valueType, ...others }: ColorPickerProps & { valueType: ValueType }) => {
  return (
    <ColorPicker
      defaultValue={'rgb(0,0,0)'}
      {...others}
      onChange={(value, hex: string) => {
        if (!valueType) {
          onChange && onChange(value, hex)
        } else {
          const trsfunc: any = value[valueType]
          if (typeof trsfunc === 'function') {
            onChange && onChange(trsfunc.call(value), hex)
          } else {
            console.log('非法的颜色转换类型')
          }
        }
      }}
    ></ColorPicker>
  )
}
export default forwardRef(({ valueType, ...others }: any, ref) => {
  return (
    <>
      <FormItemWrapper {...others}>
        <_ColorPicker valueType={valueType}></_ColorPicker>
      </FormItemWrapper>
    </>
  )
})

export { default as def } from './def'
