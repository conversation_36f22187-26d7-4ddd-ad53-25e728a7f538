import React from 'react'
import Form<PERSON>temWrapper, { FormItemProps } from '../FormItemWrapper'
import { Checkbox as AMCheckbox, CheckboxGroupProps as AMCheckboxGroupProps, Space, Image, Input } from 'antd-mobile'
import { Box, styled } from '@mui/system'
import { CheckboxValue } from 'antd-mobile/es/components/checkbox'

type Option = {
  label: string
  value: string | number
  image?: string
  inputAble?: boolean // 支持输入;value: 其他:我不
}

export type CheckboxProps = {
  options?: Option[]
  imageUrlPrefix?: string
  placeholder?: string
  valueType?: 'string' | 'array' //定义值为字符串（,分割）还是数组
  onChange?: (value: string | CheckboxValue[]) => void
  value?: string | CheckboxValue[]
} & Omit<AMCheckboxGroupProps, 'onChange' | 'value'> &
  FormItemProps

const OptionWrapper = styled(Box)({
  display: 'flex',
  flexDirection: 'column',
  margin: '4px auto'
})

export const Checkbox = ({
  options,
  imageUrlPrefix = '',
  valueType = 'array',
  onChange,
  value,
  placeholder,
  disabled,
  ...other
}: CheckboxProps) => {
  const [inputValueMap, setInputValueMap] = React.useState<any>({})
  const handleChange = (value: CheckboxValue[]) => {
    const _value = value.map((v) => {
      const o = options?.find((op) => op.value === v)
      if (o?.inputAble && inputValueMap[v]) {
        return `${v}:${inputValueMap[v]}`
      }
      return v
    })
    // console.log(_value)
    if (typeof onChange === 'function') {
      if (valueType === 'string') {
        onChange(_value.join(','))
      } else {
        onChange(_value)
      }
    }
  }

  const innerValue: any = React.useMemo(() => {
    if (!value) return []
    const [_optionValue, inputMap] = getRealValue(typeof value === 'string' ? value.split(',') : value, options)
    setInputValueMap(inputMap)
    return _optionValue
  }, [options, value])

  // const innerValue = React.useMemo(() => (typeof value === 'string' ? value.split(',') : value || []), [value])

  const handleInputChange = (v: string, ov: string | number) => {
    const _v = v.replaceAll(',', '，')
    setInputValueMap((prev: any) => ({ ...prev, [ov]: _v }))
    const i = innerValue?.findIndex((v: any) => v.toString() === ov.toString())
    if (i > -1) {
      const originValue = [...innerValue]
      originValue.splice(i, 1, ov + ':' + _v)
      handleChange(originValue)
    }
  }

  // console.log('===innervalue:', innerValue, inputValueMap)

  return (
    <AMCheckbox.Group disabled={disabled} onChange={handleChange} value={innerValue} {...other}>
      {options?.map((o) => (
        <OptionWrapper key={o.value}>
          <AMCheckbox value={o.value}>{o.label}</AMCheckbox>
          {o.image && <Image style={{ margin: '4px auto' }} src={`${imageUrlPrefix}${o.image}`} />}
          {o.inputAble && checkInputOption(innerValue, o.value) && (
            <Input
              disabled={disabled}
              placeholder={placeholder}
              style={{ marginTop: 12, border:'1px solid #d9d9d9', borderRadius:'6px', padding:'0 11px' }}
              value={inputValueMap[o.value]}
              onChange={(v) => {
                handleInputChange(v, o.value)
              }}
            />
          )}
        </OptionWrapper>
      ))}
    </AMCheckbox.Group>
  )
}

export default React.forwardRef<HTMLDivElement, CheckboxProps>((props, ref) => {
  return (
    <FormItemWrapper {...props}>
      <Checkbox />
    </FormItemWrapper>
  )
})

export { default as def } from './def'

function checkInputOption(innerValue?: any[], optionValue?: string | number) {
  return (
    innerValue &&
    optionValue &&
    innerValue.findIndex((v: string | number) => v.toString() === optionValue.toString()) !== -1
  )
}
function getRealValue(value: any[], options: Option[] = []) {
  const map: any = {}
  const optionValue = value.map((v) => {
    if (options?.findIndex((o) => o.value.toString() === v.toString()) === -1) {
      const [ov, ...rest] = v.toString().split(':')
      map[ov] = rest.join(':')
      return ov
    }
    return v
  })
  return [optionValue, map]
}
