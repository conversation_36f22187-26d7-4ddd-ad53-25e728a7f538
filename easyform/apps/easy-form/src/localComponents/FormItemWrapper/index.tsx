import React from 'react'
import { Form, FormItemProps as AMFormItemProps } from 'antd-mobile'
import { Rule } from 'rc-field-form/lib/interface'
import def from './def'

type FormItemProps = AMFormItemProps & {
  noFormItem?: boolean //不使用FormItem包裹
}

export type { FormItemProps }
export { def }

export default function (props: FormItemProps) {
  const {
    label,
    noFormItem,
    disabled,
    help,
    name,
    required,
    dependencies,
    getValueFromEvent,
    getValueProps,
    hasFeedback,
    initialValue,
    normalize,
    onClick,
    hidden,
    trigger,
    validateTrigger,
    valuePropName,
    rules,
    layout,
    children,
    ...other
  } = props
  const finallyRules: any = React.useMemo<Rule[] | undefined>(() => {
    return required ? ([{ required: true }] as any[]).concat(rules || []) : rules
  }, [required, rules])
  return noFormItem ? (
    React.cloneElement(children as any, { ...props })
  ) : (
    <Form.Item
      getValueFromEvent={getValueFromEvent}
      getValueProps={getValueProps}
      dependencies={dependencies}
      hasFeedback={hasFeedback}
      label={label}
      onClick={onClick}
      initialValue={initialValue}
      normalize={normalize}
      hidden={hidden}
      disabled={disabled}
      help={help}
      trigger={trigger}
      validateTrigger={validateTrigger}
      valuePropName={valuePropName}
      name={name}
      required={required}
      rules={finallyRules}
      layout={layout}
    >
      {React.cloneElement(children as any, { ...other })}
    </Form.Item>
  )
}
