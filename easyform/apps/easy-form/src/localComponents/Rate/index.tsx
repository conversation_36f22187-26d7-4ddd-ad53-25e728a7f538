import React from 'react'
import { Rate as AMRate, RateProps as AMRateProps } from 'antd-mobile'
import FormItemWrapper, { FormItemProps } from '../FormItemWrapper'

export type RateProps = FormItemProps & AMRateProps
export default React.forwardRef<HTMLInputElement, RateProps>((props, ref) => {
  return (
    <FormItemWrapper {...props}>
      <AMRate />
    </FormItemWrapper>
  )
})
export { default as def } from './def'
