import React from 'react'
import { <PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON> } from 'antd-mobile'
import { Box, styled } from '@mui/system'
import { Typography, ImageList, TabPanel, History } from '@/components'
import { RemoteComponent } from '@/helper'
import { IconSuccess } from '../icon'
import { useAppContext } from '@/context/AppContext'
import { useEditorContext } from '@/editor-kit/context'

interface ITemplateProps {
  isPC?: boolean
  title?: string
  bgImg?: string
  children?: React.ReactNode
  desc?: string
  imgs?: string[]
  formSettings?: any
  defaultViewIndex?: number
  isApplied?: boolean
  btnText?: string
  onSubmit?: () => void
  submitting?: boolean
  isEnd?: boolean
  imageUrlPrefix?: string
  workflow?: any
  processHistory?: Record<string, any>
  formData?: any
  type?: string
  hideSubmitButton?: boolean
  hideDefaultCover?: boolean
}
const Root = styled(Box, { shouldForwardProp: (prop) => prop !== 'isApplied' })<any>(({ isApplied }) => ({
  display: 'flex',
  alignItems: 'center'
  //backgroundColor: 'white'
}))
const ContentRoot = styled(Box)({
  //padding: '16px 16px'
})

const Footer = styled(Box)({
  //   position: 'sticky',
  zIndex: 9999,
  padding: '8px 32px',
  //   bottom: 0,
  height: 72,
  //   left: 0,
  //   right: 0,
  backgroundColor: '#F8F8F840',

  boxShadow: '0px 0px 10px rgba(0, 0, 0, 0.1)'
  //   boxShadow: '0 -10px 15px -3px rgb(0 0 0 / 0.1), 0 -4px 6px -4px rgb(0 0 0 / 0.1)'
})

const defaultTopBanner = (window as any).PUBLIC_URL + '/template/default.jpg'

const defaultText =
  '西华易班将抽取30位同学与费凌副校长面对面交流。入选情况会通过西华易班消息推送和学生服务热线87729000进行通知，敬请关注！'
const defaultEndText = '本期活动报名已经结束,敬请期待下期活动!'

const processAction: any = {
  同意: 1,
  不同意: 0
}

export default React.forwardRef<any, ITemplateProps>(
  (
    {
      children,
      desc,
      imgs,
      defaultViewIndex = 0,
      imageUrlPrefix = '',
      isApplied,
      btnText = '提交',
      onSubmit,
      formSettings = {},
      submitting,
      isEnd,
      workflow,
      processHistory,
      formData,
      isPC,
      type,
      hideSubmitButton,
      hideDefaultCover
    },
    ref
  ) => {
    const [{ user }] = useAppContext()
    const [{ pageSettinngs }] = useEditorContext()
    const swiperRef = React.useRef<any>(null)
    const handleStart = () => {
      swiperRef.current?.swipeNext()
    }
    const postRef = React.useRef()
    React.useImperativeHandle(ref, () => ({
      swipeTo: (index = 1) => {
        swiperRef.current?.swipeTo(index)
      }
    }))

    const descByRows = React.useMemo(() => {
      return desc?.split('\n') || []
    }, [desc])
    const historyData: any = React.useMemo(() => {
      return Object.keys(workflow || {}).length === 0
        ? void 0
        : Object.keys(workflow)
            .filter((k) => !['start', 'agree-end', 'refuse-end'].includes(k))
            .map((k) => ({
              title: workflow[k].name,
              name: workflow[k]?.participantDetail
                ? Object.entries(workflow[k]?.participantDetail)
                    .map(([k, v]: any) => v.name)
                    .flat()
                    .join(',')
                : '',
              // name: workflow[k]?.participantDetail?.user.map((d: any) => d.name).join(',') || '',
              status: processHistory ? processAction[processHistory[k]?.action] : void 0,
              date: processHistory ? processHistory[k]?.updateAt : void 0
            }))
    }, [processHistory, workflow])
    const finalHistoryData: any = React.useMemo(
      () =>
        historyData
          ? [{ title: '申请', name: user.name, status: 9, date: formData?.createAt }, ...historyData]
          : void 0,
      [formData?.createAt, historyData, user.name]
    )
    const { width, height } = formSettings
    //当海报宽度大于屏幕宽度时进行缩放
    const zoomValue = React.useMemo(() => {
      if (window.innerWidth < width) {
        return (window.innerWidth / width).toFixed(2)
      }
      if (!isPC) {
        return (343 / width).toFixed(2)
      }
    }, [isPC, width])
    return (
      <Root className='postTemplateRoot' isApplied={isApplied} ref={ref}>
        <Box
          ref={postRef}
          sx={{
            position: 'relative',
            zoom: zoomValue,
            width: 'fit-content',
            margin: '0 auto'
          }}
        >
          {children}
        </Box>
      </Root>
    )
  }
)
