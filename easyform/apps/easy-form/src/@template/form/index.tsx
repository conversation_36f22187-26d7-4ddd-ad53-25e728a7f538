import React from 'react'
import { Box, styled } from '@mui/system'
import { Typography } from '@/components'
import { Avatar, Form, Button } from 'antd-mobile'
interface IDefaultTemplate {
  title?: string
  bgImg?: string
  children?: React.ReactNode
}
const Root = styled('div')({
  display: 'flex',
  flexDirection: 'column',
  margin: '0 auto',
  height: '100%'
})
const Header = styled('div')({
  backgroundSize: 'cover',
  padding: '16px 16px 0px 16px'
})
const CircleAvatar = styled(Avatar)({
  '--border-radius': '100%',
  '--size': '60px',
  border: '1px solid #FFF'
})
const Footer = styled('div')({
  boxShadow: '5px 5px 8px 5px #dddfde'
})
const Btn = styled(Button)({
  '--border-color': ' #35a269',
  '--border-radius': '24px',
  width: '180px'
})
export default React.forwardRef<any, IDefaultTemplate>(
  ({ children, bgImg = '/easyform/assets/bg-header.png' }, ref) => {
    return (
      <Root className='h-screen' ref={ref}>
        <Header sx={{ backgroundImage: `url(${bgImg})`, backgroundSize: 'cover' }}>
          <Box sx={{ p: 1, mt: 4 }} className='flex '>
            <CircleAvatar lazy src={''} />
            <Box sx={{ p: 1 }}>
              <Typography variant='h6'>用户00d28</Typography>
              <Typography variant='subtitle1' sx={{ py: 1 }}>
                2022-12-21 10:03:36
              </Typography>
            </Box>
          </Box>
        </Header>

        <Box sx={{ bgcolor: '#FFF', p: 2, flex: 1, overflowY: 'scroll' }}>
          <Box>{children}</Box>
        </Box>
        <Footer className='flex justify-center items-center'>
          <Box>
            <Form
              layout='horizontal'
              footer={
                <Btn block type='submit' size='large'>
                  <Typography color='#35a269'>提交</Typography>
                </Btn>
              }
            ></Form>
          </Box>
        </Footer>
      </Root>
    )
  }
)
