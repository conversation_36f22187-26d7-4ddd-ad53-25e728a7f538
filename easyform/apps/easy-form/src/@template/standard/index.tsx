import React from 'react'
import { <PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON> } from 'antd-mobile'
import { Box, styled } from '@mui/system'
import { Typography, ImageList, TabPanel, History } from '@/components'
import { RemoteComponent } from '@/helper'
import { IconSuccess } from '../icon'
import { useAppContext } from '@/context/AppContext'
import { useEditorContext } from '@/editor-kit/context'

interface ITemplateProps {
  isPC?: boolean
  title?: string
  bgImg?: string
  children?: React.ReactNode
  desc?: string
  imgs?: string[]
  defaultViewIndex?: number
  isApplied?: boolean
  btnText?: string
  onSubmit?: () => void
  submitting?: boolean
  isEnd?: boolean
  imageUrlPrefix?: string
  workflow?: any
  processHistory?: Record<string, any>
  formData?: any
  type?: string
  hideSubmitButton?: boolean
  hideDefaultCover?: boolean
}
const Root = styled(Box, { shouldForwardProp: (prop) => prop !== 'isApplied' })<any>(({ isApplied }) => ({
  height: '100%',
  backgroundColor: 'white'
}))
const ContentRoot = styled(Box)({
  padding: '16px 16px'
})

const Footer = styled(Box)({
  //   position: 'sticky',
  zIndex: 9999,
  padding: '8px 32px',
  //   bottom: 0,
  height: 72,
  //   left: 0,
  //   right: 0,
  backgroundColor: '#F8F8F840',
  display: 'flex',
  alignItems: 'center',
  justifyContent: 'center',
  boxShadow: '0px 0px 10px rgba(0, 0, 0, 0.1)'
  //   boxShadow: '0 -10px 15px -3px rgb(0 0 0 / 0.1), 0 -4px 6px -4px rgb(0 0 0 / 0.1)'
})

const defaultTopBanner = (window as any).PUBLIC_URL + '/template/default.jpg'

const defaultText =
  '西华易班将抽取30位同学与费凌副校长面对面交流。入选情况会通过西华易班消息推送和学生服务热线87729000进行通知，敬请关注！'
const defaultEndText = '本期活动报名已经结束,敬请期待下期活动!'

const processAction: any = {
  同意: 1,
  不同意: 0
}

export default React.forwardRef<any, ITemplateProps>(
  (
    {
      children,
      desc,
      imgs,
      defaultViewIndex = 0,
      imageUrlPrefix = '',
      isApplied,
      btnText = '提交',
      onSubmit,
      submitting,
      isEnd,
      workflow,
      processHistory,
      formData,
      isPC,
      type,
      hideSubmitButton,
      hideDefaultCover
    },
    ref
  ) => {
    const [{ user }] = useAppContext()
    const swiperRef = React.useRef<any>(null)
    const handleStart = () => {
      swiperRef.current?.swipeNext()
    }
    React.useImperativeHandle(ref, () => ({
      swipeTo: (index = 1) => {
        swiperRef.current?.swipeTo(index)
      }
    }))

    const descByRows = React.useMemo(() => {
      return desc?.split('\n') || []
    }, [desc])
    const historyData: any = React.useMemo(() => {
      return Object.keys(workflow || {}).length === 0
        ? void 0
        : Object.keys(workflow)
            .filter((k) => !['start', 'agree-end', 'refuse-end'].includes(k))
            .map((k) => ({
              title: workflow[k].name,
              name: workflow[k]?.participantDetail
                ? Object.entries(workflow[k]?.participantDetail)
                    .map(([k, v]: any) => v.name)
                    .flat()
                    .join(',')
                : '',
              // name: workflow[k]?.participantDetail?.user.map((d: any) => d.name).join(',') || '',
              status: processHistory ? processAction[processHistory[k]?.action] : void 0,
              date: processHistory ? processHistory[k]?.updateAt : void 0
            }))
    }, [processHistory, workflow])
    const finalHistoryData: any = React.useMemo(
      () =>
        historyData
          ? [{ title: '申请', name: user.name, status: 9, date: formData?.createAt }, ...historyData]
          : void 0,
      [formData?.createAt, historyData, user.name]
    )
    return (
      <Root isApplied={isApplied} ref={ref}>
        <Swiper
          defaultIndex={isApplied ? 1 : isEnd ? 2 : defaultViewIndex}
          ref={swiperRef}
          indicator={() => null}
          allowTouchMove={false}
          style={{ height: '100%' }}
        >
          <Swiper.Item>
            <Box className='h-full flex flex-col'>
              <Box
                className='flex-grow overflow-auto'
                sx={{
                  ' ::-webkit-scrollbar': {
                    width: 5,
                    backgroundColor: 'transparent'
                  },
                  '::-webkit-scrollbar-thumb': {
                    backgroundColor: '#c0c0c0',
                    width: 5,
                    borderRadius: 2
                  }
                }}
              >
                {!hideDefaultCover ? (
                  <Image
                    style={{ maxHeight: 320 }}
                    src={Array.isArray(imgs) && imgs.length > 0 ? imageUrlPrefix + imgs[0] : defaultTopBanner}
                  />
                ) : (
                  ''
                )}

                <ContentRoot>
                  {descByRows.map((d, i) => (
                    <Typography
                      indent={/^\s+/.test(d)}
                      key={i}
                      sx={{
                        whiteSpace: 'pre-wrap',
                        lineHeight: 1.5,
                        letterSpacing: '1px',
                        fontSize: '16px',
                        minHeight: '1em'
                      }}
                    >
                      {d?.replace(/^\s+/, '')}
                    </Typography>
                  ))}

                  <Box sx={{ bgcolor: '#FFF', mt: 3 }}>{children}</Box>
                  {isPC && !hideSubmitButton && (
                    <Box sx={{ mt: 4 }}>
                      <Button disabled={submitting} onClick={onSubmit} block color='primary'>
                        {btnText}
                      </Button>
                    </Box>
                  )}
                </ContentRoot>
              </Box>

              {!isPC && !hideSubmitButton && Boolean(onSubmit) && (
                <Footer>
                  <Button disabled={submitting} onClick={onSubmit} block shape='rounded' color='primary'>
                    {btnText}
                  </Button>
                </Footer>
              )}
            </Box>
          </Swiper.Item>
          <Swiper.Item></Swiper.Item>
          <Swiper.Item>
            <Box sx={{ px: 6, paddingTop: '25%' }} className='h-screen flex items-center flex-col'>
              <Image width='80%' src='/assets/end.png' />
              <Box sx={{ mt: 1 }} className='flex items-center flex-col'>
                <Box className='flex justify-center' sx={{ mt: 1 }}>
                  <Typography sx={{ textIndent: '2em', fontSize: 16, letterSpacing: '1px' }} variant='subtitle1'>
                    {defaultEndText}
                  </Typography>
                </Box>
              </Box>
            </Box>
          </Swiper.Item>
        </Swiper>
      </Root>
    )
  }
)
