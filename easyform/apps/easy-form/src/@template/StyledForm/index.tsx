import { Form } from 'antd-mobile'
import { styled } from '@mui/system'

const StyledForm = styled(Form, { shouldForwardProp: (prop) => prop !== 'disabledShowNormal' && prop !== 'isPC' })<any>(
  ({ disabledShowNormal, isPC }) => ({
    '& .adm-list-item-disabled.adm-list-item-disabled > .adm-list-item-content > *': {
      opacity: disabledShowNormal ? 1 : 0.6
    },
    '& .adm-list-item-content': {
      borderTop: isPC ? 'none' : void 0
    },
    '& .adm-list-body': {
      borderTop: isPC ? 'none' : void 0,
      borderBottom: isPC ? 'none' : void 0
    }
  })
)
export const PostForm = styled(Form, {
  shouldForwardProp: (prop) => prop !== 'disabledShowNormal' && prop !== 'isPC'
})<any>(({ disabledShowNormal, isPC }) => ({
  '& .adm-list-item-disabled.adm-list-item-disabled > .adm-list-item-content > *': {
    opacity: disabledShowNormal ? 1 : 0.6
  },
  '& .adm-list-item-content': {
    borderTop: isPC ? 'none' : void 0
  },
  '& .adm-list-body': {
    borderTop: isPC ? 'none' : void 0,
    borderBottom: isPC ? 'none' : void 0,
    backgroundColor: 'transparent'
  }
}))
export default StyledForm
