import React from 'react'
import { Box, styled } from '@mui/system'
import { Button } from 'antd-mobile'
import { useSize } from 'ahooks'
import { useEditorContext } from '@/editor-kit/context'

const Container = styled(Box)({
  background: '#FFF',
  borderRadius: 4,
  height: '100%',
  boxShadow: '0 1px 2px 0 rgb(0 0 0 / 0.05)',
  overflow: 'hidden'
})

const Wrapper = ({ children }: any) => {
  return (
    <Box
      className='h-full'
      sx={{
        // backgroundImage: `url(${(window as any).APP_CONFIG?.server?.fileUrlPrefix || ''})`,
        backgroundSize: 'cover',
        backgroundPosition: 'center'
      }}
    >
      <Container>{children}</Container>
    </Box>
  )
}
const MobileWrapper = ({ children }: any) => {
  return (
    <Box sx={{ borderRadius: '32px', border: '15px solid rgb(234, 234, 234)', height: '100%', overflow: 'hidden' }}>
      {children}
    </Box>
  )
}
export type PageWrapperProps = {
  children?: (isPC: boolean) => React.ReactNode | React.ReactNode
  isPC?: boolean
}

export default function ({ children, isPC: _isPC, isRunning }: any) {
  const ref = React.useRef(null)
  //   const size = useSize(ref)
  //   const isPC = React.useMemo(() => size?.width && size.width > 767, [size?.width])
  const isPC = React.useMemo(() => (_isPC === void 0 ? window.innerWidth > 767 : _isPC), [_isPC])
  const newChildren = React.useMemo(() => {
    console.log(typeof children)
    if (typeof children === 'function') {
      return isPC ? (
        <Wrapper>{children(isPC)}</Wrapper>
      ) : !isRunning ? (
        <MobileWrapper>{children(isPC)}</MobileWrapper>
      ) : (
        children(isPC)
      )
    }
    return isPC ? <Wrapper>{children}</Wrapper> : !isRunning ? <MobileWrapper>{children}</MobileWrapper> : children
  }, [children, isPC, isRunning])
  return (
    <Box ref={ref} className='h-full'>
      {newChildren}
    </Box>
  )
}
