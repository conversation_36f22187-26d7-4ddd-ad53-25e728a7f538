import React, { useMemo, useRef } from 'react'
import { <PERSON><PERSON><PERSON>, <PERSON><PERSON>, Image } from 'antd-mobile'
import { Box, styled } from '@mui/system'
import { Typography, ImageList, TabPanel, History } from '@/components'
import { Skeleton } from 'antd'
interface ITemplateProps {
  isPC?: boolean
  title?: string
  bgImg?: string
  children?: React.ReactNode
  desc?: string
  imgs?: string[]
  defaultViewIndex?: number
  isApplied?: boolean
  settings?: any
  btnText?: string
  onSubmit?: () => void
  submitting?: boolean
  isEnd?: boolean
  height?: any
  imageUrlPrefix?: string
  workflow?: any
  processHistory?: Record<string, any>
  formData?: any
  type?: string
  hideSubmit?: boolean
  pageSettings?: any
}
const Root = styled(Box, { shouldForwardProp: (prop) => prop !== 'isApplied' })<any>(({ isApplied }) => ({
  //height: '100%',
  backgroundColor: 'white',
  maxWidth: '800px',
  margin: '0 auto'
}))
const ContentRoot = styled(Box)({
  padding: '16px 16px'
})

const Footer = styled(Box)({
  position: 'sticky',
  bottom: 0,
  zIndex: 9999,
  padding: '8px 32px',
  //   bottom: 0,
  height: 72,
  //   left: 0,
  //   right: 0,
  backgroundColor: '#FFFFFF',
  display: 'flex',
  alignItems: 'center',
  justifyContent: 'center',
  boxShadow: '0px 0px 10px rgba(0, 0, 0, 0.1)'
  //   boxShadow: '0 -10px 15px -3px rgb(0 0 0 / 0.1), 0 -4px 6px -4px rgb(0 0 0 / 0.1)'
})

const defaultTopBanner = (window as any).PUBLIC_URL + '/template/default.jpg'

const defaultEndText = '本期活动报名已经结束,敬请期待下期活动!'

export const BKGWrapper = (props: any) => {
  const { src, isPC, bkgColor, children, height, settings, hideMobileBKG } = props
  return (
    <>
      {(hideMobileBKG && !isPC) || (!src && !bkgColor) ? (
        <Box
          className={'editor-BKG-wrapper'}
          sx={{
            height: height ?? 'inherit',
            overflow: 'auto',
            ' ::-webkit-scrollbar': {
              width: 5,
              backgroundColor: 'transparent'
            },
            '::-webkit-scrollbar-thumb': {
              backgroundColor: '#c0c0c0',
              width: 5,
              borderRadius: 2
            }
          }}
        >
          {children}
        </Box>
      ) : (
        <Box
          className={'editor-BKG-wrapper'}
          sx={{
            ' ::-webkit-scrollbar': {
              width: 5,
              backgroundColor: 'transparent'
            },
            '::-webkit-scrollbar-thumb': {
              backgroundColor: '#c0c0c0',
              width: 5,
              borderRadius: 2
            },
            background: src ? `url(${src})` : bkgColor,
            backgroundSize: 'cover',
            //display: 'flex',
            alignItems: 'center',
            justifyContent: 'center',
            overflow: 'auto',
            height: height ?? 'inherit',
            backgroundPosition: 'center',
            p: 2,
            pt: 6,
            pb: 0
          }}
        >
          {children}
        </Box>
      )}
    </>
  )
}
export default React.forwardRef<any, ITemplateProps>(
  (
    {
      children,
      defaultViewIndex = 0,
      imageUrlPrefix = '',
      settings,
      isApplied,
      onSubmit,
      hideSubmit,
      submitting,
      pageSettings,
      height,
      isPC
    },
    ref
  ) => {
    const swiperRef = React.useRef<any>(null)
    const { desc, name } = settings
    React.useImperativeHandle(ref, () => ({
      swipeTo: (index = 1) => {
        swiperRef.current?.swipeTo(index)
      }
    }))

    const descByRows = React.useMemo(() => {
      return desc?.split('\n') || []
    }, [desc])
    const coverUrl = React.useMemo(() => {
      console.log(2120, 'Align')
      if (!pageSettings?.hideCover) {
        if (pageSettings) {
          if (pageSettings?.cover) {
            return (
              (pageSettings?.cover?.src?.[0] && imageUrlPrefix + pageSettings?.cover?.src?.[0]) ||
              pageSettings?.cover?.value?.[0]
            )
          } else {
            return defaultTopBanner
          }
        }
      }
    }, [pageSettings, imageUrlPrefix])
    const showSubmit = useMemo(() => {
      return !hideSubmit && !pageSettings?.hideSubmitButton && settings.type !== 'NOTICE'
    }, [hideSubmit, pageSettings?.hideSubmitButton, settings.type])
    const imgRef = useRef()
    return (
      <BKGWrapper
        isPC={isPC}
        height={height}
        src={
          (pageSettings?.background?.src?.[0] &&
            ((window as any).APP_CONFIG?.server?.fileUrlPrefix || '') + pageSettings?.background?.src?.[0]) ||
          pageSettings?.background?.value?.[0]
        }
        bkgColor={pageSettings?.background?.color?.[0]}
        hideMobileBKG={pageSettings?.hideMobileBKG}
      >
        <Root isApplied={isApplied} ref={ref}>
          <Box className='h-full flex flex-col'>
            <Box
              className='flex-grow overflow-auto'
              sx={{
                '.adm-image-img': {
                  objectFit: 'cover!important',
                  'aspect-ratio': '16 / 9',
                  objectPosition: 'center center'
                }
              }}
            >
              {!pageSettings?.hideCover ? (
                <Image
                  placeholder={
                    <Box sx={{ width: '100%', '.ezf-skeleton-element': { width: '100%' } }}>
                      <Skeleton.Image style={{ width: '100%' }} active />
                    </Box>
                  }
                  style={{ display: 'flex', alignItems: 'center', justifyContent: 'center' }}
                  src={coverUrl}
                />
              ) : (
                ''
              )}
              {!pageSettings?.hideTitle && name ? (
                <Typography
                  sx={{
                    textAlign: 'center',
                    fontSize: 'x-large',
                    fontWeight: 'bold',
                    pt: 5,
                    color: pageSettings?.titleColor || undefined
                  }}
                >
                  {name}
                </Typography>
              ) : (
                ''
              )}
              <ContentRoot>
                {pageSettings?.hideDescription
                  ? ''
                  : descByRows.map((d: any, i: any) => (
                      <Typography
                        indent={/^\s+/.test(d)}
                        key={i}
                        sx={{
                          whiteSpace: 'pre-wrap',
                          lineHeight: 1.5,
                          letterSpacing: '1px',
                          fontSize: '16px',
                          minHeight: '1em',
                          px: 1.5,
                          color: pageSettings?.descriptionColor || undefined
                        }}
                      >
                        {d?.replace(/^\s+/, '')}
                      </Typography>
                    ))}

                <Box sx={{ bgcolor: '#FFF', mt: 3 }}>{children}</Box>
                {!pageSettings?.stickySubmit && showSubmit && (
                  <Box sx={{ mt: 4 }}>
                    <Button
                      style={{
                        '--background-color': pageSettings?.themeColor,
                        '--border-color': pageSettings?.themeColor
                      }}
                      disabled={submitting}
                      onClick={onSubmit}
                      block
                      color='primary'
                    >
                      {pageSettings?.submitText || '提交'}
                    </Button>
                  </Box>
                )}
              </ContentRoot>
              <Box
                sx={{
                  fontSize: 14,
                  display: pageSettings?.footerText ? 'flex' : 'none',
                  justifyContent: 'center',
                  height: 50,
                  alignItems: 'center',
                  backgroundColor: '#f9f9f9'
                }}
              >
                <Typography sx={{ color: '#8c8c8c', pr: 1 }}>{pageSettings?.footerText || ''}</Typography>
                {/* <Typography sx={{ color: '#bebebe' }}>提供技术支持</Typography> */}
              </Box>
            </Box>

            {pageSettings?.stickySubmit && showSubmit && (
              <Footer>
                <Button
                  style={{ '--background-color': pageSettings?.themeColor, '--border-color': pageSettings?.themeColor }}
                  disabled={submitting}
                  onClick={onSubmit}
                  block
                  shape='rounded'
                  color='primary'
                >
                  {pageSettings?.submitText || '提交'}
                </Button>
              </Footer>
            )}
          </Box>
        </Root>
      </BKGWrapper>
    )
  }
)
