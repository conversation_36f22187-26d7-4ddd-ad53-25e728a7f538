import React from 'react'
import { Box, styled } from '@mui/system'
import { Form, Input, TextArea, Button, Popup, CenterPopup } from 'antd-mobile'
import { CheckOutline as IconPass, CloseOutline as IconNoPass, LoopOutline as IconReturn } from 'antd-mobile-icons'
import { useBoolean } from 'ahooks'

export type TBaseData = {
  activity: {
    name: string
    desc?: string
    date: string
  }
  applicat: {
    name: string
    code: string
    phone: string
    org: string
  }
  applicatFormData: {
    [k: string]: any
  }
  nexts: any
}

interface IApproveProps {
  children?: React.ReactNode
  baseData: TBaseData
  onSubmit?: (values: any) => void
}

const StyledForm = styled(Form, { shouldForwardProp: (prop) => prop !== 'disabledShowNormal' })<any>(
  ({ disabledShowNormal }) => ({
    '& .adm-list-item-disabled.adm-list-item-disabled > .adm-list-item-content > *': {
      opacity: disabledShowNormal ? 1 : 0.6
    }
  })
)

const Root = styled(Box)(({ theme }) => ({
  display: 'flex',
  height: '100%',
  flexDirection: 'column',
  backgroundColor: theme.palette.background.default
}))

const Footer = styled(Box)({
  zIndex: 99,
  padding: '8px 16px 48px 16px',
  display: 'flex',
  alignItems: 'center',
  justifyContent: 'space-between',
  flexShrink: 0
})

export default function ({
  children,
  baseData: { activity, applicat, applicatFormData, nexts },
  onSubmit
}: IApproveProps) {
  const [action, setAction] = React.useState<string | null>(null)
  const nextProcess = React.useMemo(() => nexts.find((f: any) => f.action === '同意'), [nexts])
  const handleAprove = (values: any) => {
    if (typeof onSubmit === 'function') {
      onSubmit({ opionion: values.opinion, action })
    }
  }
  return (
    <>
      <Root>
        <Box className='flex-grow overflow-auto'>
          <StyledForm initialValues={activity} layout='horizontal' disabled mode='card'>
            <Form.Header>活动信息</Form.Header>
            <Form.Item name='name' label='活动名称'>
              <Input />
            </Form.Item>
            {/* <Form.Item name='desc' label='活动描述'>
              <TextArea />
            </Form.Item> */}
            <Form.Item name='date' label='活动时间'>
              <Input />
            </Form.Item>
          </StyledForm>
          <StyledForm initialValues={applicat} layout='horizontal' disabled mode='card'>
            <Form.Header>基本信息</Form.Header>
            <Form.Item name='name' label='申请人'>
              <Input />
            </Form.Item>
            <Form.Item name='code' label='学工号'>
              <Input />
            </Form.Item>
            <Form.Item name='phone' label='联系电话'>
              <Input />
            </Form.Item>
            <Form.Item name='org' label='部门/院系'>
              <Input />
            </Form.Item>
          </StyledForm>
          <StyledForm initialValues={applicatFormData} disabled mode='card'>
            <Form.Header>表单信息</Form.Header>
            {children}
          </StyledForm>
        </Box>
        {nexts.length > 0 && (
          <Footer>
            <Button onClick={() => setAction('退回')} style={{ marginRight: 24 }} color='warning' block shape='rounded'>
              <span style={{ marginRight: '8px' }}>退回</span>
              <IconReturn />
            </Button>
            <Button
              onClick={() => setAction('不同意')}
              style={{ marginRight: 24 }}
              color='danger'
              block
              shape='rounded'
            >
              <span style={{ marginRight: '8px' }}>不同意</span>
              <IconNoPass />
            </Button>
            <Button onClick={() => setAction('同意')} color='success' block shape='rounded'>
              <span style={{ marginRight: '8px' }}>同意</span>
              <IconPass />
            </Button>
          </Footer>
        )}
      </Root>
      <CenterPopup
        // getContainer={null}
        // showCloseButton
        destroyOnClose
        onClose={() => setAction(null)}
        onMaskClick={() => setAction(null)}
        bodyStyle={{
          minWidth: 320
          // borderTopLeftRadius: '8px',
          // borderTopRightRadius: '8px',
          // maxWidth: 800,
        }}
        visible={Boolean(action)}
      >
        <Box>
          <StyledForm
            onFinish={handleAprove}
            initialValues={{
              opinion: action,
              next: nextProcess?.name || ''
            }}
            footer={
              <Button shape='rounded' type='submit' block color='primary' size='large'>
                提交
              </Button>
            }
            sx={{ mt: 2, mb: 4 }}
            mode='card'
            disabledShowNormal
          >
            <Form.Item rules={[{ required: true }]} name='opinion' label='意见'>
              <TextArea />
            </Form.Item>
            {action === '同意' && nextProcess?.name !== '结束' && (
              <Form.Item disabled name='next' label='下一步：'>
                <Input />
              </Form.Item>
            )}
          </StyledForm>
        </Box>
      </CenterPopup>
    </>
  )
}
