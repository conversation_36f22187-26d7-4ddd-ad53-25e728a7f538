import React from 'react'
import { <PERSON>wiper, <PERSON><PERSON> } from 'antd-mobile'
import { Box, styled } from '@mui/system'
import { Typography, ImageList, TabPanel } from '@/components'

interface IDefaultTemplate {
  title?: string
  bgImg?: string
  children?: React.ReactNode
  desc?: string
  imgs?: string[]
  defaultViewIndex?: number
  readonly?: boolean
}
const Root = styled(Box)<any>(({ readonly }) => ({
  '.adm-list-item-disabled.adm-list-item-disabled > .adm-list-item-content > *': {
    opacity: readonly ? 0.4 : 1
  }
}))

export default React.forwardRef<any, IDefaultTemplate>(
  ({ title, children, desc, imgs, bgImg = './template/7.jpg', defaultViewIndex = 1, readonly }, ref) => {
    const swiperRef = React.useRef<any>(null)
    const handleStart = () => {
      swiperRef.current?.swipeNext()
    }
    return (
      <Root readonly={readonly} ref={ref}>
        <Swiper defaultIndex={defaultViewIndex} ref={swiperRef} indicator={() => null} allowTouchMove={false}>
          <Swiper.Item>
            <Box
              className='h-screen overflow-auto relative'
              sx={{
                py: 3,
                px: 2,
                backgroundImage: `url(${bgImg})`,
                backgroundSize: 'cover'
              }}
            >
              <Typography sx={{ mb: 2, color: '#FFF' }} variant='h1' align='center' color='primary'>
                {title || ''}
              </Typography>
              {desc && (
                <Typography sx={{ mb: 2, whiteSpace: 'pre-wrap', lineHeight: 1.8, color: '#FFF' }} variant='subtitle1'>
                  {desc}
                </Typography>
              )}
              {/* {imgs && (
          <Box sx={{ mb: 1.5 }}>
            <ImageList maxWidth='100%' imgs={imgs} />
          </Box>
        )} */}
              <Box className='absolute bottom-16 left-8 right-8'>
                <Button onClick={handleStart} color='primary' fill='solid' block>
                  开始报名
                </Button>
              </Box>
            </Box>
          </Swiper.Item>
          <Swiper.Item>
            <Box
              className='h-screen overflow-auto relative'
              sx={{
                py: 3,
                px: 2,
                backgroundImage: `url(${bgImg})`,
                backgroundSize: 'cover'
              }}
            >
              <Typography sx={{ mb: 2, color: '#FFF' }} variant='h1' align='center' color='primary'>
                {title || ''}
              </Typography>
              {desc && (
                <Typography sx={{ mb: 2, whiteSpace: 'pre-wrap', lineHeight: 1.8, color: '#FFF' }} variant='subtitle1'>
                  {desc}
                </Typography>
              )}
              {/* {imgs && (
          <Box sx={{ mb: 1.5 }}>
            <ImageList maxWidth='100%' imgs={imgs} />
          </Box>
        )} */}
              <Box sx={{ bgcolor: '#FFF' }}>{children}</Box>
            </Box>
          </Swiper.Item>
        </Swiper>
      </Root>
    )
  }
)
