import React from 'react'
import { useLocation } from 'react-router-dom'
import { PullToRefresh } from 'antd-mobile'
import { Box } from '@mui/system'
import { getUrlParam } from '@/utils'
import ParticipateDetail from '@/pages/components/ParticipateDetail'
import PageWrapper from '@/@template/PageWrapper'

export default function () {
  const { state }: any = useLocation()
  const [[activityId, activityInstanceId]] = React.useState([
    getUrlParam('activityId') || state?.activityId,
    getUrlParam('instanceId') || state?.activityInstanceId
  ])
  return (
    <PullToRefresh>
      <Box className='h-screen'>
        <PageWrapper isRunning>
          <ParticipateDetail activityId={activityId} activityInstanceId={activityInstanceId} />
        </PageWrapper>
      </Box>
    </PullToRefresh>
  )
}
