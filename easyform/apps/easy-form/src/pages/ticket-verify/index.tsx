import React from 'react'
import { useNavigate } from 'react-router-dom'
import { getUrlParam, formatDate } from '@/utils'
import { Box } from '@mui/system'
import { LoadingWrapper } from '@/components'
import { useTicketRequest } from '@/hooks'
import { verifyTicket } from '@/api/ticket'

export default function () {
  const navigate = useNavigate()
  const [[token]] = React.useState([getUrlParam('token')])
  useTicketRequest(() => verifyTicket(token || ''), {
    ready: <PERSON>ole<PERSON>(token),
    onSuccess: (res: any) => {
      if (res?.info?.detailPageUrl) {
        window.open(`${res.info.detailPageUrl}&token=${res.token}`, '_self')
      }
    },
    onError: (err: any) => {
      navigate('/noaccess', { state: { reason: err } })
    }
  })
  React.useEffect(() => {
    if (!token) {
      navigate('/noaccess', { state: { reason: '非法token' } })
    }
  }, [navigate, token])
  return (
    <Box className='h-screen'>
      <LoadingWrapper loading={true}></LoadingWrapper>
    </Box>
  )
}
