import React, { useState } from 'react'
import { useNavigate } from 'react-router-dom'
import { Box, styled } from '@mui/system'
import { InfiniteScroll, Toast } from 'antd-mobile'
import { useEasyFormRequest, useBaseRequest } from '@/hooks'
import { getActivityList, toggleActivityEnable, deleteActivity, getActivity, saveActivity } from '@/api/easyform'
import { deleteActivity as deleteYbActivity } from '@/api'
import { getRemainTime, formatDate } from '@/utils'
import CreatedCard from '../components/CreateCard'
import produce from 'immer'

const defaultPagination = { pageSize: 10, page: 1 }

export default React.forwardRef((props, ref) => {
  const navigate = useNavigate()
  const [list, setList] = React.useState<any[]>([])
  const [hasMore, setHasMore] = React.useState(true)
  const [keyWs, setKeyWs] = useState<any>()
  const { runAsync: fetchData } = useEasyFormRequest(getActivityList, {
    manual: true
  })
  const { runAsync: fetchDefine } = useEasyFormRequest((id) => getActivity(id), {
    manual: true
  })
  const { runAsync: buildForm } = useEasyFormRequest((data: any) => saveActivity(data), {
    manual: true
  })
  const [{ page, pageSize }, setPagination] = React.useState(defaultPagination)

  const { runAsync: publishRun } = useEasyFormRequest(toggleActivityEnable, {
    manual: true
  })
  const { runAsync: deleteActivityRun } = useEasyFormRequest(deleteActivity, {
    manual: true
  })
  /**
   * 删除接口（易班）
   */
  // const { runAsync: deleteActivityRun } = useBaseRequest(deleteYbActivity, {
  //   manual: true
  // })

  const loadMore = React.useCallback(
    async (isRetry) => {
      const res = await fetchData({
        pageNo: page,
        pageSize: pageSize,
        isMy: true,
        keywords: keyWs
      })
      setList((prev: any) => [...prev, ...(res.data || [])])
      const _hasMore = res.total > page * pageSize
      setHasMore(_hasMore)
      if (_hasMore) {
        setPagination((prev) => ({ ...prev, page: prev.page + 1 }))
      }
    },
    [fetchData, page, pageSize, keyWs]
  )

  const handleStatistics = (activityInstId: string, activityId: string) => {
    navigate('/statistics', { state: { instanceId: activityInstId, activityId: activityId } })
  }

  React.useImperativeHandle(ref, () => ({
    refresh: async () => {
      const res = await fetchData({
        pageNo: defaultPagination.page,
        pageSize: defaultPagination.pageSize,
        isMy: true,
        keywords: keyWs
      })
      setList(res.data)
      setPagination({ ...defaultPagination })
    },
    setSearch: async (keyWords: string) => {
      setKeyWs(keyWords)
      const res = await fetchData({
        pageNo: defaultPagination.page,
        pageSize: defaultPagination.pageSize,
        isMy: true,
        keywords: keyWords
      })
      setList(res.data)
      setHasMore(res?.total > res?.data?.length)
      setPagination({ ...defaultPagination })
    }
  }))
  const handlePublish = async (id: string) => {
    try {
      Toast.show({
        icon: 'loading',
        content: ''
      })
      await publishRun(id)
      Toast.clear()
      Toast.show({
        icon: 'success',
        content: '操作成功'
      })
      setList(
        produce(list, (draft: any) => {
          const _index = list.findIndex((item: any) => item.id === id)
          draft[_index].enable = Number(!draft[_index].enable)
        })
      )
    } catch (e) {
      Toast.show({
        icon: 'fail',
        content: '操作失败'
      })
    }
  }
  const handleDelete = async (id: string) => {
    try {
      await deleteActivityRun(id)
      setList(
        produce(list, (draft: any) => {
          const _index = list.findIndex((item: any) => item.id === id)
          draft.splice(_index, 1)
        })
      )
      Toast.show({
        icon: 'success',
        content: '删除成功'
      })
    } catch (e) {
      Toast.show({
        icon: 'fail',
        content: '删除失败'
      })
    }
  }
  const handleFormCopy = async ({ id, name }: any) => {
    try {
      if (!id) throw new Error('')
      const { data: define } = await fetchDefine(id)
      const cpoyName = name || (define as any)?.settings?.name + '【复制】'
      Object.assign(define.settings, { name: cpoyName })
      const result = await buildForm({ define })
      Toast.show({ icon: 'success', content: '复制成功！' })
      //复制当前表单定义并创建
      const res = await fetchData({
        pageNo: defaultPagination.page,
        pageSize: defaultPagination.pageSize,
        isMy: true,
        keywords: keyWs
      })
      setList(res.data)
      setPagination({ ...defaultPagination })
    } catch {
      Toast.show({
        content: '复制失败！',
        icon: 'fail'
      })
    }
  }
  return (
    <Box sx={{ display: 'grid', gridTemplateColumns: '1fr', rowGap: 1.5 }}>
      {list.map((item) => (
        <CreatedCard
          onCopyForm={handleFormCopy}
          onStatistics={handleStatistics}
          onPublish={handlePublish}
          data={item}
          key={item.id}
          onDelete={handleDelete}
        />
      ))}
      <InfiniteScroll hasMore={hasMore} loadMore={loadMore} />
    </Box>
  )
})
