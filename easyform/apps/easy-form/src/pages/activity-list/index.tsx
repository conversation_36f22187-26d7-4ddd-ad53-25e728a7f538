import React, { useState } from 'react'
import { useNavigate } from 'react-router-dom'
import { Box, styled } from '@mui/system'
import { InfiniteScroll, PullToRefresh, SearchBar, Tabs } from 'antd-mobile'
import { AddOutline as IconAdd } from 'antd-mobile-icons'

import { TabPanel, Typography } from '@/components'
import { ActivityTab } from '@/@define/Activity'
import { IDENTITY_TYPE } from '@/@define'
import { Header, HeaderContent, ContentWrapper } from '@/pages/components'
import RunningList from './running'
import JoinedList from './joined'
import CreatedList from './created'
import { useAppContext } from '@/context/AppContext'

const defaultTabs = {
  [ActivityTab.RUNNING]: '进行中',
  [ActivityTab.PART_IN]: '已参与',
  [ActivityTab.CREATED]: '我创建的'
}

const StyledTabs = styled(Tabs)({
  '& .adm-tabs-header': {
    borderBottom: 'none'
  },
  '& .adm-tabs-tab': {
    padding: '0 0 4px 0'
  },
  '--active-title-color': '#FFF',
  '--active-line-color': '#FFF',
  '--title-font-size': 14,
  color: '#eee'
})

const StyledTab = styled(Tabs.Tab)({
  padding: '0 0 4px 0'
})

export default function () {
  const navigate = useNavigate()
  const [
    {
      user: { adminType }
    }
  ] = useAppContext()
  const [serachFocused, setSearchFocused] = useState(false)
  const [currentTab, setCurrentTab] = React.useState(ActivityTab.CREATED)
  const [refs] = React.useState<Record<string, any>>({
    [ActivityTab.RUNNING]: React.createRef(),
    [ActivityTab.PART_IN]: React.createRef(),
    [ActivityTab.CREATED]: React.createRef()
  })
  const handleTabChange = (newTab: string) => {
    setCurrentTab(newTab)
  }
  const handleRefresh = async () => {
    await refs[currentTab].current?.refresh()
  }

  const createAble = React.useMemo(() => Boolean(adminType), [adminType])

  // const tabs = React.useMemo(
  //   () =>
  //     createAble
  //       ? {
  //           [ActivityTab.RUNNING]: '进行中',
  //           [ActivityTab.PART_IN]: '已参与',
  //           [ActivityTab.CREATED]: '我创建的'
  //         }
  //       : {
  //           [ActivityTab.RUNNING]: '进行中',
  //           [ActivityTab.PART_IN]: '已参与'
  //         },
  //   [createAble]
  // )

  const tabs = React.useMemo(
    () => ({
      [ActivityTab.CREATED]: '我创建的'
    }),
    []
  )

  return (
    <Box sx={{ bgcolor: 'background.default', height: '100%' }}>
      <Header>
        <HeaderContent>
          <Box
            sx={{
              flexFlow: 1,
              height: 30,
              flexGrow: 1,
              pr: 2,
              '.adm-search-bar-cancel-button': {
                color: 'white!important'
              }
            }}
          >
            <SearchBar
              onSearch={(val: string) => {
                refs[currentTab].current?.setSearch(val)
              }}
              onClear={() => {
                refs[currentTab].current?.setSearch(undefined)
              }}
              onlyShowClearWhenFocus={false}
              style={{
                '--border-radius': '100px',
                '--background': '#ffffff',
                '--height': '32px',
                '--padding-left': '12px'
              }}
              placeholder={serachFocused ? '输入关键词搜索' : '搜索问卷'}
              onBlur={() => {
                setSearchFocused(false)
              }}
              onFocus={() => {
                setSearchFocused(true)
              }}
              showCancelButton
            />
          </Box>
          {createAble && !serachFocused ? (
            <Box
              onClick={() => {
                navigate('/creation2')
              }}
              sx={{
                border: 1,
                borderRadius: 3,
                px: '8px',
                borderColor: '#FFF',
                color: '#FFF',
                fontSize: '12px',
                flexShrink: 0
              }}
              className='flex items-center'
            >
              <IconAdd style={{ fontSize: 16 }} />
              <Typography sx={{ ml: 0.5 }}>新建</Typography>
            </Box>
          ) : (
            <div />
          )}
          {/* {!serachFocused ? (
            <StyledTabs style={{ flexShrink: 0 }} activeKey={currentTab} onChange={handleTabChange}>
              {Object.entries(tabs)?.map(([k, v]) => {
                return <StyledTab key={k} title={v} />
              })}
            </StyledTabs>
          ) : (
            ''
          )} */}
        </HeaderContent>
      </Header>
      <ContentWrapper>
        <PullToRefresh onRefresh={handleRefresh}>
          {/* <TabPanel value={ActivityTab.RUNNING} showValue={currentTab}>
            <RunningList ref={refs[ActivityTab.RUNNING]} />
          </TabPanel>
          <TabPanel value={ActivityTab.PART_IN} showValue={currentTab}>
            <JoinedList ref={refs[ActivityTab.PART_IN]} />
          </TabPanel> */}
          <TabPanel value={ActivityTab.CREATED} showValue={currentTab}>
            <CreatedList ref={refs[ActivityTab.CREATED]} />
          </TabPanel>
        </PullToRefresh>
      </ContentWrapper>
    </Box>
  )
}
