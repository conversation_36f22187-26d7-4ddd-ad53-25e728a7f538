import React from 'react'
import { Box, styled } from '@mui/system'
import { TeamOutline as IconTeam } from 'antd-mobile-icons'
import { Typography } from '@/components'
import { formatDate } from '@/utils'

const Root = styled(Box)({
  borderRadius: 8,
  padding: 12,
  backgroundColor: '#FFF'
})

const Tag = styled(Box, { shouldForwardProp: (prop) => prop !== 'color' })<{ color?: string }>(({ theme, color }) => ({
  borderRadius: 4,
  padding: '2px 4px',
  color: color || theme.palette.secondary.main,
  border: `1px solid ${color || theme.palette.secondary.main}`,
  transform: 'scale(0.8)',
  fontSize: 12
}))

export type CardProps = {
  title: string
  tag?: {
    text: string
    color?: string
  }
  joinedCount?: number
  info?: string
  onClick?: () => void
}

export default function ({ title, tag, joinedCount, info, onClick }: CardProps) {
  return (
    <Root onClick={onClick}>
      <Box className='flex'>
        <Typography className='flex-1' variant='h6' sx={{ fontSize: 15 }}>
          {title}
        </Typography>
        {tag && (
          <Tag color={tag.color} className='flex-shrink-0'>
            {tag.text}
          </Tag>
        )}
      </Box>
      <Box sx={{ mt: 1 }} className='flex items-center justify-between'>
        <Typography variant='subtitle2'>{info}</Typography>
        {joinedCount !== void 0 && (
          <Typography variant='subtitle2'>
            <IconTeam style={{ marginRight: 4 }} />
            {joinedCount}人
          </Typography>
        )}
      </Box>
    </Root>
  )
}
