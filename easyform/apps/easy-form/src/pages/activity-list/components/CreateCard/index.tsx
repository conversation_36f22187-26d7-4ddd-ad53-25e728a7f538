import React, { ReactNode, useState } from 'react'
import { Card, Popup, Dialog, Tag, Form, Space, Toast, Modal, Input } from 'antd-mobile'
import { useTheme, styled, Box } from '@mui/system'
import Typography from '@/components/Typography'
import { useNavigate } from 'react-router-dom'
import { Badge } from 'antd'
import { formatDate, getAcitvityDate } from '@/utils'
import { ActivityTypes } from '@/@define/Activity'

const StyledCard = styled(Card)({
  marginBottom: 16,
  padding: 0,
  '& .adm-card-header': {
    padding: '13px 12px 0px'
  },
  '& .adm-card-header:not(:last-child)': {
    border: 'none'
  },
  '& .adm-card-body': {
    paddingTop: '6px',
    paddingBottom: '0px'
  }
  // ' &:hover': {
  //   borderColor: 'transparent',
  //   boxShadow: ' 0 1px 2px -2px #00000029, 0 3px 6px #0000001f, 0 5px 12px 4px #00000017'
  // }
})
const StyledHeaderDiv = styled('div')({})
const StyledDivi = styled('span')({
  width: 1,
  height: 10,
  background: '#ccc',
  margin: '0px 6px'
})
const StyledContent = styled('div')({
  padding: '0 14px 17px '
})
const StyledContentDiv = styled('div')({
  font: '12px/20px ""',
  display: 'flex',
  alignItems: 'center'
})
const StyledContentDivStatus = styled('div')<any>(({ theme, isPublished }) => ({
  color: isPublished ? theme.palette.success.main : theme.palette.error.main
}))
const StyledContentPop = styled('div')(({ theme }) => ({
  background: '#f8f8f8',
  height: '100%',
  padding: '8px'
}))
const StyledContentPopOpe = styled('div')(({ theme }) => ({
  display: 'flex',
  padding: '16px 8px'
}))
const StyledManage = styled('div')({
  marginRight: 12,
  '.styledManageTitle': {
    marginTop: 8
  },
  '& .imgDiv': {
    background: '#fff',
    borderRadius: '10px',
    width: '50px',
    height: '50px',
    display: 'flex',
    justifyContent: 'center',
    alignItems: 'center',
    img: {
      width: 22
    }
  }
})
const StyledContentDivFlex = styled('div')({
  display: 'flex',
  justifyContent: 'space-between',
  fontSize: 13,
  marginTop: 8
})
const StyledExtre = styled('div')(({ theme }) => ({
  fontSize: '12px',
  //color: 'rgba(0,0,0,0.5)',
  textAlign: 'center'
}))
const StyledExtreFont = styled('div')(({ theme }) => ({
  fontSize: '13px',
  color: 'rgba(0,0,0,0.5)',
  display: 'flex',
  justifyContent: 'center',
  alignItems: 'center'
}))
const StyledBottomPopup = styled('div')(({ theme }) => ({
  background: '#fff',
  position: 'absolute',
  bottom: 0,
  width: '100vw',
  left: 0,
  textAlign: 'center',
  height: 75,
  p: {
    marginTop: 18
  }
}))

interface CardPrps {
  data: any
  onPublish: (id: string) => void
  onDelete: (id: string) => void
  onClick?: () => void
  onCopyForm: (id: string) => void
  onStatistics: (instId: string, activityId: string) => void
  //   status: string
}
const manageData = [
  {
    name: '表单操作',
    children: [
      {
        icon: '/easyform/assets/icon/publish.png',
        title: '发布',
        key: 'publish',
        render: (isPublished: boolean, existInst?: boolean) => !isPublished
      },
      {
        icon: '/easyform/assets/icon/cancelPublish.png',
        title: '取消发布',
        key: 'cancelPublish',
        render: (isPublished: boolean, existInst?: boolean) => isPublished
      },
      {
        icon: '/easyform/assets/icon/copyform.svg',
        title: '复制',
        key: 'copy'
      },
      {
        icon: '/easyform/assets/icon/delete.png',
        title: '删除',
        key: 'delete'
      }
    ]
  },
  {
    name: '设计表单',
    children: [
      {
        icon: '/easyform/assets/icon/edit.png',
        title: '编辑',
        key: 'designer'
      },
      {
        icon: '/easyform/assets/icon/settingform.svg',
        title: '设置',
        key: 'settings'
      }
    ]
  },
  {
    name: '发送问卷',
    children: [
      {
        icon: '/easyform/assets/icon/shareform.svg',
        title: '分享',
        key: 'share',
        beforeEmit: ({ enable }: any) => {
          if (!enable) {
            Toast.show({
              icon: 'fail',
              content: '若要分享请先发布该表单。'
            })
          } else {
            return true
          }
        }
      }
    ]
  },
  {
    name: '数据统计',
    children: [
      {
        icon: '/easyform/assets/icon/taskDetail.svg',
        title: '进度跟踪',
        key: 'taskDetail',
        render: (isPublished: boolean, existInst?: boolean, taskId?: any) => taskId
      },
      {
        icon: '/easyform/assets/icon/statisic.png',
        title: '数据统计',
        key: 'statistics',
        render: (isPublished: boolean, existInst?: boolean, taskId?: any, hide?: boolean) => Boolean(existInst) && hide
      }
    ]
  }
]

export default function CardCom(props: CardPrps) {
  const {
    data: {
      name,
      id,
      enable,
      type,
      startDateTime,
      taskId,
      endDateTime,
      pvCount,
      latestInstId,
      latestInstParticipateCount,
      createAt
    },
    onPublish,
    onDelete,
    onClick,
    onStatistics,
    onCopyForm,
    ...other
  } = props
  const navigate = useNavigate()
  const [visible, setVisible] = useState(false)
  const [copyForm] = Form.useForm()
  const clickFun = (item: any) => {
    if (item?.beforeEmit && !item?.beforeEmit({ enable })) return
    switch (item.key) {
      case 'publish': {
        onPublish(id)
        setVisible(false)
        break
      }
      case 'cancelPublish': {
        onPublish(id)
        setVisible(false)
        break
      }
      case 'copy': {
        copyForm.setFieldsValue({
          name: name + '【复制】',
          id
        })
        Modal.confirm({
          title: '复制表单',
          content: (
            <Form form={copyForm}>
              <Form.Item label={'表单名称'} name={'name'}>
                <Input clearable></Input>
              </Form.Item>
              <Form.Item label={'表单id'} hidden name={'id'}>
                <Input></Input>
              </Form.Item>
            </Form>
          ),
          onConfirm: () => {
            copyForm.validateFields().then((res) => {
              onCopyForm(copyForm.getFieldsValue())
            })
          }
        })
        setVisible(false)
        break
      }
      case 'designer': {
        // if (enable) {
        //   Dialog.confirm({
        //     content: (
        //       <Box>
        //         <Typography sx={{ textAlign: 'center', fontWeight: 'bold', py: 1 }}>系统提示</Typography>
        //         <Box>修改问卷不会改变问卷链接，但会暂停发布问卷，修改完后请重新发布问卷。</Box>
        //       </Box>
        //     ),
        //     onConfirm: () => {
        //       navigate('/designer', { state: { id: id, pageTo: 1 } })
        //     }
        //   })
        // } else {
        navigate('/designer', { state: { id: id, pageTo: 2 } })
        //}

        break
      }
      case 'settings': {
        // if (enable) {
        //   Dialog.confirm({
        //     content: (
        //       <Box>
        //         <Typography sx={{ textAlign: 'center', fontWeight: 'bold', py: 1 }}>系统提示</Typography>
        //         <Box>修改问卷不会改变问卷链接，但会暂停发布问卷，修改完后请重新发布问卷。</Box>
        //       </Box>
        //     ),
        //     onConfirm: () => {
        //       navigate('/designer', { state: { id: id } })
        //     }
        //   })
        // } else {
        navigate('/designer', { state: { id: id } })
        //}

        break
      }
      case 'share': {
        navigate('/share', { state: { id: id } })
        break
      }
      case 'taskDetail': {
        navigate('/taskDetail', { state: { id: id, taskId } })
        break
      }
      case 'statistics': {
        onStatistics(latestInstId, id)
        break
      }
      case 'delete': {
        Dialog.confirm({
          title: '提示',
          content: '是否确定删除此活动？',
          // closeOnAction: true,
          onConfirm: async () => {
            onDelete(id)
            setVisible(false)
          }
        })
        break
      }
    }
  }
  return (
    <Badge.Ribbon text={`访问量：${pvCount}`} color=''>
      <StyledCard
        onClick={() => {
          setVisible(true)
          onClick && onClick()
        }}
        title={<StyledHeaderDiv>{name}</StyledHeaderDiv>}
        key={id}
        // extra={<StyledExtre> {getFriendlyTime(startDateTime)}</StyledExtre>}
      >
        <StyledContent>
          <StyledContentDiv>
            <StyledExtre>{formatDate(createAt)}</StyledExtre>
            <StyledDivi></StyledDivi>
            <StyledExtre>{latestInstParticipateCount || 0}条记录</StyledExtre>
          </StyledContentDiv>
          <StyledContentDivFlex>
            <StyledContentDivStatus isPublished={enable}>{enable ? '已发布' : '未发布'}</StyledContentDivStatus>
            <StyledExtreFont
              onClick={(e) => {
                e.stopPropagation()
                setVisible(true)
              }}
            >
              {/* <img src='/easyform/assets/icon/setting.png' width={'20'}></img> 管理 */}
              <Tag
                fill='outline'
                style={{ width: 54, textAlign: 'center' }}
                color={ActivityTypes.find((item) => item.id == type)?.color || ''}
              >
                {ActivityTypes.find((item) => item.id == type)?.title}
              </Tag>
            </StyledExtreFont>
          </StyledContentDivFlex>
        </StyledContent>
        <Popup
          visible={visible}
          onMaskClick={() => {
            setVisible(false)
          }}
          bodyStyle={{ height: '75vh' }}
        >
          {/* <StyledManage key={item.key} onClick={() => clickFun(item)}>
                <div className='imgDiv'>
                  <img src={item?.icon}></img>
                </div>
                <StyledExtre className='styledManageTitle'> {item?.title}</StyledExtre>
              </StyledManage> */}
          <StyledContentPop>
            <Box sx={{ overflow: 'auto', height: 'calc(100% - 75px)' }}>
              {manageData.map((group: { name: string; children: any[] }, index) => (
                <Box key={index} sx={{ display: 'flex', flexDirection: 'column' }}>
                  {group?.children?.filter(
                    (child) =>
                      !child?.render ||
                      child.render(
                        Boolean(enable),
                        Boolean(latestInstId),
                        Boolean(type == 'INFO'),
                        Boolean(type !== 'NOTICE')
                      )
                  )?.length > 0 ? (
                    <Box sx={{ color: '#8C8C8C', fontSize: 14, pl: 1 }}>{group?.name}</Box>
                  ) : (
                    ''
                  )}
                  <StyledContentPopOpe>
                    {group?.children?.map((item, key) =>
                      item.render ? (
                        item.render(
                          Boolean(enable),
                          Boolean(latestInstId),
                          Boolean(type == 'INFO'),
                          Boolean(type !== 'NOTICE')
                        ) && (
                          <StyledManage key={item.key} onClick={() => clickFun(item)}>
                            <div className='imgDiv'>
                              <img src={item?.icon}></img>
                            </div>
                            <StyledExtre className='styledManageTitle'> {item?.title}</StyledExtre>
                          </StyledManage>
                        )
                      ) : (
                        <StyledManage key={item.key} onClick={() => clickFun(item)}>
                          <div className='imgDiv'>
                            <img src={item?.icon}></img>
                          </div>
                          <StyledExtre className='styledManageTitle'> {item?.title}</StyledExtre>
                        </StyledManage>
                      )
                    )}
                  </StyledContentPopOpe>
                </Box>
              ))}
            </Box>
            <StyledBottomPopup>
              <Typography
                onClick={() => {
                  setVisible(false)
                }}
              >
                取消
              </Typography>
            </StyledBottomPopup>
          </StyledContentPop>
        </Popup>
      </StyledCard>
    </Badge.Ribbon>
  )
}
