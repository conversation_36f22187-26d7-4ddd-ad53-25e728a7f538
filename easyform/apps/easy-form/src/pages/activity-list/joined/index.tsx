import React from 'react'
import { Box, styled } from '@mui/system'
import { InfiniteScroll } from 'antd-mobile'
import { useEasyFormRequest } from '@/hooks'
import { getMyActivity } from '@/api/easyform'
import { getRemainTime, formatDate } from '@/utils'
import Card from '../components/Card'

const defaultPagination = { pageSize: 10, page: 1 }

export default React.forwardRef((props, ref) => {
  const [list, setList] = React.useState<any[]>([])
  const [hasMore, setHasMore] = React.useState(true)
  const { runAsync: fetchData } = useEasyFormRequest(getMyActivity, {
    manual: true
  })
  const [{ page, pageSize }, setPagination] = React.useState(defaultPagination)
  const loadMore = React.useCallback(
    async (isRetry) => {
      const res = await fetchData({
        pageNo: page,
        pageSize: pageSize,
        status: 'Already'
      })
      setList((prev: any) => [...prev, ...(res.data || [])])
      const _hasMore = res.total > page * pageSize
      setHasMore(_hasMore)
      if (_hasMore) {
        setPagination((prev) => ({ ...prev, page: prev.page + 1 }))
      }
    },
    [fetchData, page, pageSize]
  )
  const handleClick = (id: string, instanceId: string) => {
    window.open(`${(window as any).PUBLIC_URL}/submit?activityId=${id}&instanceId=${instanceId}`)
  }
  React.useImperativeHandle(ref, () => ({
    refresh: async () => {
      const res = await fetchData({
        pageNo: defaultPagination.page,
        pageSize: defaultPagination.pageSize,
        status: 'Already'
      })
      setList(res.data)
      setPagination({ ...defaultPagination })
    }
  }))
  return (
    <Box sx={{ display: 'grid', gridTemplateColumns: '1fr', rowGap: 1.5 }}>
      {list.map((item) => (
        <Card
          key={item.id}
          title={item.name}
          onClick={() => handleClick(item.id, item.latestInstId)}
          info={`最近提交：${formatDate(item.myLatestInstCreateAt, 'MM-DD HH:mm')}`}
        />
      ))}
      <InfiniteScroll hasMore={hasMore} loadMore={loadMore} />
    </Box>
  )
})
