import React from 'react'
import { Form, Button, Input, Toast, DotLoading } from 'antd-mobile'
import { Box, styled } from '@mui/system'
import { useNavigate, useLocation } from 'react-router-dom'
import { useComponents, PageRender, useLocalComponents } from '@/helper'
import { PageLoading } from '@/components/Loading'
import DefaultTemeplate from '@/@template/default'
import StandardTemplate from '@/@template/FormTemplate'
import dayjs from 'dayjs'
import PageWrapper from '@/@template/PageWrapper'

import { useEasyFormRequest } from '@/hooks'
import {
  getActivityDefine,
  submitGroupFormData,
  // read,
  getMyFormData,
  // loadFormData,
  checkAllowJoin,
  getProcessHistory
} from '@/api/easyform'
import { getHashUrlParam, getUrlParam, filterSpecialChar } from '@/utils'
import { useAppContext } from '@/context/AppContext'
import { ActivityState, TActivityState, ActivityStateText } from '@/@define/Activity'
import StyledForm from '@/@template/StyledForm'

// const StyledForm = styled(Form, { shouldForwardProp: (prop) => prop !== 'disabledShowNormal' && prop !== 'isPC' })<any>(
//   ({ disabledShowNormal, isPC }) => ({
//     '& .adm-list-item-disabled.adm-list-item-disabled > .adm-list-item-content > *': {
//       opacity: disabledShowNormal ? 1 : 0.6
//     },
//     '& .adm-list-item-content': {
//       borderTop: isPC ? 'none' : void 0
//     }
//   })
// )

export default function () {
  const [
    {
      user: { name, id, extra: userData = {} }
    }
  ] = useAppContext()
  const [form] = Form.useForm()
  const location: any = useLocation()
  const templateRef = React.useRef<any>()
  const [define, setDefine] = React.useState<any>({})
  const navigate = useNavigate()
  const [[activityId, activityInstanceId, assignmentId, groupId]] = React.useState([
    getUrlParam('activityId'),
    getUrlParam('instanceId'),
    getUrlParam('assignmentId'),
    getUrlParam('groupId')
  ])
  const [joinState, setJoinState] = React.useState<TActivityState>()

  const { data: formData } = useEasyFormRequest(() => getMyFormData(activityId, activityInstanceId), {
    ready: Boolean(activityId) || Boolean(activityInstanceId)
  })

  const { data } = useEasyFormRequest(() => getActivityDefine(activityId || '', '?isCounter=true'), {
    ready: Boolean(activityId),
    onError: () => {
      navigate('/noaccess', {
        replace: true,
        state: { reason: '您访问的活动不存在' }
      })
    }
  })

  const { data: historyData } = useEasyFormRequest(() => getProcessHistory(formData?.data?.instFormId), {
    ready: Boolean(formData?.data?.instFormId)
  })

  const { loading: checkLoading } = useEasyFormRequest(() => checkAllowJoin(activityId || '', activityInstanceId), {
    ready: Boolean(data),
    onSuccess: (res: any) => {
      switch (res.data?.status) {
        case ActivityState.NORMAL:
        case ActivityState.PARTICIPATED: {
          if (res.data?.status === ActivityState.PARTICIPATED) {
            if (location.state?.submitFormId) {
              setJoinState(ActivityState.RE_SUBMIT)
            } else {
              setJoinState(ActivityState.PARTICIPATED)
              //   navigate('/submited', {
              //     state: {
              //       activityId: activityId,
              //       activityInstanceId: activityInstanceId
              //     },
              //     replace: true
              //   })
            }
          } else {
            setJoinState(res.data?.status)
          }

          break
        }
        default: {
          navigate('/noaccess', {
            replace: true,
            state: { reason: (ActivityStateText as any)[res.data.status] || ActivityStateText.NOT_ACCESS }
          })
        }
      }
    }
  })

  const { loading: saveLoading, run: save } = useEasyFormRequest(submitGroupFormData, {
    loadingDelay: 200,
    manual: true,
    onSuccess: () => {
      Toast.show({
        icon: 'success',
        content: '提交成功'
      })
      setTimeout(() => {
        navigate(-1)
        // navigate('/submited', {
        //   state: {
        //     activityId: activityId,
        //     activityInstanceId: activityInstanceId
        //   },
        //   replace: true
        // })
      }, 1000)
    }
  })

  React.useEffect(() => {
    if (data) {
      setDefine(data.data)
    }
  }, [data])
  const { loadComplete, components } = useLocalComponents(define.page?.componentTree, define.page?.remoteDependencies)
  const btnText = React.useMemo(
    () => define?.settings?.templateSettings?.btnText || '提交',
    [define?.settings?.templateSettings?.btnText]
  )
  const BaseData = React.useMemo(() => {
    return {
      userName: userData.name,
      phone: userData.mobile,
      org: Array.isArray(userData.orgs) ? userData.orgs[0]?.name : void 0,
      speciality: userData.speciality?.name,
      class: userData.class?.name,
      code: userData.code
    }
  }, [userData.class, userData.code, userData.mobile, userData.name, userData.orgs, userData.speciality])
  const handleSubmit = React.useCallback(() => {
    form.submit()
  }, [form])
  const handleFinish = React.useCallback(
    (values) => {
      save(
        activityId,
        { ...values, assignmentId: assignmentId, id: location.state?.submitFormId },
        groupId,
        formData?.data?.id
      )
    },
    [save, activityId, assignmentId, location.state?.submitFormId, groupId, formData?.data?.id]
  )
  const isReady = React.useMemo(() => {
    return joinState !== void 0 && Boolean(components) && Boolean(define?.page?.componentTree)
  }, [components, define?.page?.componentTree, joinState])

  const isEnd = React.useMemo(() => {
    if (define?.settings?.endDateTime) {
      return dayjs().unix() >= dayjs(define.settings.endDateTime).unix()
    }
    return false
  }, [define?.settings?.endDateTime])

  React.useLayoutEffect(() => {
    if (define?.settings?.name) {
      setTimeout(() => {
        document.title = define?.settings?.name
      }, 200)
    }
  }, [define?.settings?.name])
  const historyDataMap = React.useMemo(
    () =>
      historyData?.data
        ? historyData?.data.reduce(
            (p: any, c: any) => ({
              ...p,
              [c.defKey]: c
            }),
            {}
          )
        : {},
    [historyData?.data]
  )
  const hideSubButton = define?.settings?.type == 'NOTICE'
  return !isReady ? (
    <Box className='h-screen flex items-center justify-center'>
      <DotLoading />
    </Box>
  ) : (
    <React.Suspense fallback={<PageLoading />}>
      <Box className='h-screen'>
        <PageWrapper isRunning={true}>
          {(isPC: boolean) => (
            <Box>
              {components && define.page.componentTree ? (
                <StyledForm
                  isPC={isPC}
                  disabledShowNormal={joinState === ActivityState.NORMAL}
                  form={form}
                  onFinish={handleFinish}
                  initialValues={formData.data}
                >
                  <PageRender
                    isPC={isPC}
                    baseData={BaseData}
                    componentTree={define.page.componentTree || []}
                    components={components}
                  />
                </StyledForm>
              ) : null}
              <Box sx={{ p: 3 }}>
                <Button
                  style={{
                    '--background-color': define?.settings?.pageSettings?.themeColor,
                    '--border-color': define?.settings?.pageSettings?.themeColor
                  }}
                  disabled={saveLoading}
                  onClick={handleSubmit}
                  block
                  shape='rounded'
                  color='primary'
                >
                  {define?.settings?.pageSettings?.submitText || '提交'}
                </Button>
              </Box>
            </Box>
            // <StandardTemplate
            //   isEnd={isEnd}
            //   workflow={define.workflow}
            //   submitting={saveLoading}
            //   ref={templateRef}
            //   onSubmit={handleSubmit}
            //   btnText={btnText}
            //   isApplied={joinState === ActivityState.PARTICIPATED}
            //   desc={define?.settings?.desc}
            //   imgs={define?.settings?.imgs}
            //   title={define?.settings?.name}
            //   settings={define?.settings}
            //   pageSettings={define?.settings?.pageSettings}
            //   imageUrlPrefix={(window as any).APP_CONFIG.server.fileUrlPrefix}
            //   processHistory={historyDataMap}
            //   formData={formData?.data}
            //   isPC={isPC}
            // >
            //   {components && define.page.componentTree ? (
            //     <StyledForm
            //       isPC={isPC}
            //       disabledShowNormal={joinState === ActivityState.NORMAL}
            //       form={form}
            //       onFinish={handleFinish}
            //       initialValues={formData.data}
            //     >
            //       <PageRender
            //         isPC={isPC}
            //         baseData={BaseData}
            //         componentTree={define.page.componentTree || []}
            //         components={components}
            //       />
            //     </StyledForm>
            //   ) : null}
            // </StandardTemplate>
          )}
        </PageWrapper>
      </Box>
    </React.Suspense>
  )
}
