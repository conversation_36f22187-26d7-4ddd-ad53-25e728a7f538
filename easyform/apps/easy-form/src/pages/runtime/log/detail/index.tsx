import React from 'react'
import { useLocation } from 'react-router-dom'
import { PullToRefresh } from 'antd-mobile'
import { Box } from '@mui/system'
import { getUrlParam } from '@/utils'
import PageWrapper from '@/@template/PageWrapper'
import LogDetail from './LogDetail'

export default function () {
  const { state }: any = useLocation()
  const [[activityId, activityInstanceId, formId, groupId]] = React.useState([
    getUrlParam('activityId') || state?.activityId,
    getUrlParam('instanceId') || state?.activityInstanceId,
    getUrlParam('formId') || state?.instFormId,
    getUrlParam('groupId') || state?.groupId
  ])
  return (
    <PullToRefresh>
      <Box className='h-screen'>
        <PageWrapper isRunning>
          <LogDetail
            groupId={groupId}
            activityId={activityId}
            instFormId={formId}
            activityInstanceId={activityInstanceId}
          />
        </PageWrapper>
      </Box>
    </PullToRefresh>
  )
}
