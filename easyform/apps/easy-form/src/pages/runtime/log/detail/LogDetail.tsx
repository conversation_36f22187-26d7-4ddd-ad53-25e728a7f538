/* eslint-disable no-prototype-builtins */
import React from 'react'
import { Box, styled } from '@mui/system'
import { useNavigate } from 'react-router-dom'
import { PullToRefresh, Image, SpinLoading, Form, Input, Swiper, Button } from 'antd-mobile'
import { EyeOutline as IconRead } from 'antd-mobile-icons'
import { Typography, LoadingWrapper, Avatar } from '@/components'
import { Submited, Approve, Pass, NoPass, TicketUsed, IconSuccess, Return } from '@/assets/icon'
import { useTicketRequest } from '@/hooks'
import { getTicketQrCode, getTicketDetail } from '@/api/ticket'
import { useEasyFormRequest } from '@/hooks'
import { getUrlParam, formatDate, getAvatarByUserId } from '@/utils'
import { getActivityDefine, getMyFormData, getProcessHistory, loadGroupAppFormData, readState } from '@/api/easyform'
import { useCom<PERSON>, PageRender } from '@/helper'
import { History } from '@/components'
import { transformToList } from '@/components/PersonnelPicker2/util'

export const State = {
  SUBMITED: 'SUBMITED', //已提交
  APPROVALING: 'APPROVALING', // 审批中
  PASS: 'PASS', //已通过
  NO_PASS: 'NO_PASS', //未通过
  RETURN: 'RETURN',
  TICKET_USED: 'TICKET_USED' //已完成核票
}
const StateIcon: Record<string, any> = {
  TICKET_USED: TicketUsed, //已完成核票
  APPROVALING: Approve, // 审批中
  PASS: Pass, //已通过
  NO_PASS: NoPass, // 未通过
  SUBMITED: Submited,
  RETURN: Return
}

const StateWrapper = styled(Box)({
  position: 'absolute',
  right: 12,
  top: '50%',
  transform: 'translateY(-50%)'
})

const StyledForm = styled(Form, { shouldForwardProp: (prop) => prop !== 'disabledShowNormal' })<any>(
  ({ disabledShowNormal }) => ({
    '& .adm-list-item-disabled.adm-list-item-disabled > .adm-list-item-content > *': {
      opacity: disabledShowNormal ? 1 : 0.6
    }
  })
)
const AvatarGroup = styled(Box)({
  '&>*:not(:first-of-type)': {
    marginLeft: '-6px'
  }
})
const processAction: any = {
  同意: 1,
  不同意: 0,
  退回: 2
}
export type ParticipateDetailProps = {
  activityId?: string
  activityInstanceId?: string
  instFormId?: string
  groupId?: string
  showFormDetail?: boolean
  onReady?: (params: any) => void
  scanSuccessText?: string
  allowBack?: boolean
}

export default React.forwardRef<any, ParticipateDetailProps>(function (
  {
    activityId,
    activityInstanceId,
    instFormId,
    showFormDetail,
    groupId,
    onReady,
    scanSuccessText = '您的入场二维码已确认',
    allowBack = true
  },
  ref
) {
  const navigate = useNavigate()
  const [define, setDefine] = React.useState<any>({})

  const [formData, setFormData] = React.useState<Record<string, any>>()
  const { data: activityData } = useEasyFormRequest(() => getActivityDefine(activityId || ''), {
    ready: Boolean(activityId)
  })

  const { loadComplete, components } = useComponents(define.page?.componentTree, define.page?.remoteDependencies)

  const isReady = React.useMemo(() => {
    return Boolean(components) && Boolean(define?.page?.componentTree)
  }, [components, define?.page?.componentTree])

  useEasyFormRequest(() => loadGroupAppFormData(groupId || '', activityId || '', instFormId || ''), {
    ready: Boolean(instFormId),
    onSuccess: (res: any) => {
      setFormData(res.data)
    }
  })
  // useEasyFormRequest(() => loadFormDataByInstFormId(instFormId || ''), {
  //   ready: Boolean(instFormId),
  //   onSuccess: (res: any) => {
  //     setFormData(res.data)
  //   }
  // })
  const isProcessForm = React.useMemo(() => (formData ? formData.hasOwnProperty('reason') : false), [formData])
  const { data: _historyData } = useEasyFormRequest(() => getProcessHistory(formData?.instFormId), {
    ready: isProcessForm
  })
  const historyDataMap = React.useMemo(
    () =>
      _historyData?.data
        ? _historyData?.data.reduce(
            (p: any, c: any) => ({
              ...p,
              [c.defKey]: c
            }),
            {}
          )
        : {},
    [_historyData?.data]
  )
  const historyData: any = React.useMemo(() => {
    return define?.workflow && Array.isArray(_historyData?.data)
      ? Object.keys(define.workflow || {}).length === 0
        ? void 0
        : Object.keys(define.workflow)
            .filter((k) => !['start', 'agree-end', 'refuse-end'].includes(k))
            .map((k) => ({
              title: define.workflow[k].name,
              name: define.workflow[k]?.participantDetail
                ? transformToList(define.workflow[k]?.participantDetail).map((d) => d.name)
                : '',
              // name: define.workflow[k]?.participantDetail
              //   ? Object.entries(define.workflow[k]?.participantDetail)
              //       .map(([k, v]: any) => v.map((n: any) => n.name))
              //       .flat()
              //       .join(',')
              //   : '',
              // name: workflow[k]?.participantDetail?.user.map((d: any) => d.name).join(',') || '',
              status: _historyData.data ? processAction[historyDataMap[k]?.action] : void 0,
              date: historyDataMap ? historyDataMap[k]?.updateAt : void 0
            }))
      : void 0
  }, [_historyData?.data, define?.workflow, historyDataMap])
  const finalHistoryData: any = React.useMemo(
    () =>
      historyData && formData
        ? [{ title: '申请', name: formData.creatorName, status: 9, date: formData?.createAt }, ...historyData]
        : void 0,
    [formData, historyData]
  )

  const { data: readData } = useEasyFormRequest(() =>
    readState(groupId || '', activityId || '', instFormId || '', { isRead: true })
  )

  React.useEffect(() => {
    console.log('===:', finalHistoryData, historyDataMap)
  }, [finalHistoryData, historyDataMap])

  const { data: ticketDetail } = useTicketRequest(() => getTicketDetail(formData?.ticketId), {
    ready: Boolean(formData?.ticketId)
  })

  const { data: qrcode, run: getQrCode } = useTicketRequest(getTicketQrCode, {
    manual: true,
    loadingDelay: 200
  })

  const handleRefreshQrCode = () => {
    getQrCode(formData?.ticketId)
  }

  React.useEffect(() => {
    if (activityData) {
      setDefine(activityData.data)
    }
  }, [activityData])
  React.useEffect(() => {
    if (formData?.ticketId && !showFormDetail) {
      getQrCode(formData?.ticketId)
    }
  }, [formData?.ticketId, getQrCode, showFormDetail])

  React.useImperativeHandle(ref, () => ({
    refresh: async () => {}
  }))
  const swiperRef = React.useRef<any>()

  const loadingPage = React.useMemo(() => !formData || !components || !define, [components, define, formData])
  const status = React.useMemo(() => {
    if (ticketDetail?.status === 1) {
      return State.TICKET_USED
    }
    if (isProcessForm) {
      if (formData?.reason === 'agree') {
        return State.PASS
      }
      if (formData?.reason === 'refuse') {
        return State.NO_PASS
      }
      if (formData?.curTask?.startsWith('start')) {
        return State.RETURN
      }
      return State.APPROVALING
    }

    return State.SUBMITED
  }, [formData?.curTask, formData?.reason, isProcessForm, ticketDetail?.status])
  const allowShowQrCode = React.useMemo(
    () => formData?.ticketId && !showFormDetail && [State.SUBMITED, State.PASS].includes(status),
    [formData?.ticketId, showFormDetail, status]
  )
  const defaultSwiperIndex = React.useMemo(() => {
    if (status === State.TICKET_USED) {
      return 2
    }
    if (showFormDetail) {
      return 3
    }
    if (allowShowQrCode) {
      return 1
    }
    if ([State.APPROVALING, State.NO_PASS, State.PASS, State.RETURN].includes(status)) {
      return 3
    }

    return 0
  }, [allowShowQrCode, showFormDetail, status])
  React.useImperativeHandle(ref, () => ({
    swipeTo: (index: number) => {
      /**
       * 0 已提交
       * 1 二维码
       * 2 票据已确认
       * 3 表单详情
       *
       */
      swiperRef.current.swipeTo(index)
    }
  }))
  React.useEffect(() => {
    if (!loadingPage) {
      if (typeof onReady === 'function') {
        onReady(status)
      }
    }
  }, [loadingPage, onReady, status])
  return (
    <Box className='h-full flex flex-col' sx={{ bgcolor: '#FFF' }}>
      <LoadingWrapper loading={loadingPage}>
        <Box
          sx={{
            // position: 'sticky',
            top: 0,
            position: 'relative',
            zIndex: 100,
            p: 1.5,
            bgcolor: '#FFF',
            borderBottom: '1px solid',
            borderColor: 'divider'
          }}
        >
          <Typography variant='h6'>{define?.settings?.name}</Typography>
          <Box sx={{ mt: 1 }} className='flex items-center'>
            <Typography variant='subtitle2'>提交人：{formData?.creatorName}</Typography>
            <Typography sx={{ ml: 2 }} variant='subtitle2'>
              提交时间：{formatDate(formData?.createAt, 'YYYY-MM-DD HH:mm:ss')}
            </Typography>
          </Box>
          <StateWrapper>
            {React.createElement(StateIcon[status] || StateIcon.Submited, { width: 56, height: 56 })}
          </StateWrapper>
        </Box>
        <Box sx={{ bgcolor: 'background.default' }} className='flex-1 overflow-hidden'>
          <Swiper
            ref={swiperRef}
            defaultIndex={defaultSwiperIndex}
            indicator={() => null}
            allowTouchMove={false}
            style={{ '--height': '100%' }}
          >
            <Swiper.Item>
              <Box className='h-full overflow-auto'>
                <StyledForm initialValues={formData} disabled mode='card'>
                  {components && define.page.componentTree ? (
                    <PageRender componentTree={define.page.componentTree || []} components={components} />
                  ) : null}
                </StyledForm>
                {readData && (
                  <Box className='flex items-center' sx={{ px: 2 }}>
                    <Typography variant='subtitle2'>
                      <IconRead style={{ marginRight: 4 }} />
                      {readData.total}人已读
                    </Typography>
                    <Box sx={{ ml: 0.5, pt: '4px' }}>
                      <AvatarGroup>
                        {readData.data?.slice(0, 12)?.map((u: any) => (
                          <Avatar size={18} key={u.id} src={getAvatarByUserId(u.userId)} round />
                        ))}
                      </AvatarGroup>
                    </Box>
                  </Box>
                )}
              </Box>
            </Swiper.Item>
          </Swiper>
        </Box>
      </LoadingWrapper>
    </Box>
  )
})
