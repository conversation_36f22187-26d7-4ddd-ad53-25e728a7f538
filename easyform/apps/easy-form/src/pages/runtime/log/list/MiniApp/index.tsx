import React from 'react'
import { Box, styled } from '@mui/system'
import { InfiniteScroll, PullToRefresh, Popup } from 'antd-mobile'
import { useMemoizedFn } from 'ahooks'
import { AddButton, Typography } from '@/components'
import { useEasyFormRequest } from '@/hooks'
import { getUrlParam } from '@/utils'
import { getMyAllInstFormList, delGroupForm } from '@/api/easyform'
import { useNavigate } from 'react-router-dom'
import Item from '../Item'
import produce from 'immer'
import { formatDate } from '@/utils'
import { PopAction } from '../../components'
import { useAppContext } from '@/context/AppContext'
import { Toast } from 'antd-mobile'

const chineseWeekday: any = {
  Sunday: '周日',
  Monday: '周一',
  Tuesday: '周二',
  Wednesday: '周三',
  Thursday: '周四',
  Friday: '周五',
  Saturday: '周六'
}

export type MiniAppProps = {
  activityId?: string
  title?: string
}
export default function ({ activityId, title = '日志' }: MiniAppProps) {
  const [{ user }] = useAppContext()
  const navigate = useNavigate()
  const [groupByDateList, setGroupByDateList] = React.useState<{ [k: string]: any[] }>({})
  const [[groupId]] = React.useState([getUrlParam('groupId')])
  // const [list, setList] = React.useState<any>([])
  const [hasMore, setHasMore] = React.useState(true)
  const [pagination, setPagination] = React.useState({ page: 1, pageSize: 10 })
  const { runAsync: load } = useEasyFormRequest((params?: any) => getMyAllInstFormList(activityId, params, groupId), {
    manual: true
  })
  const { runAsync: del } = useEasyFormRequest(delGroupForm, {
    manual: true
  })
  const loadMore = async () => {
    const data = await load({
      pageSize: pagination.pageSize,
      pageNo: pagination.page
    })
    setGroupByDateList(
      produce(groupByDateList, (draft) => {
        data.data?.forEach((d: any) => {
          const day = formatDate(d.createAt)
          if (draft[day]) {
            draft[day].push(d)
          } else {
            draft[day] = [d]
          }
        })
      })
    )
    // setList(list.concat(data.data || []))
    if (pagination.page * pagination.pageSize >= data.total) {
      setHasMore(false)
    } else {
      setPagination({
        ...pagination,
        page: pagination.page + 1
      })
    }
  }
  const refresh = useMemoizedFn(async (name?: string) => {
    const data = await load({
      pageSize: pagination.pageSize,
      pageNo: 1,
      name: name
    })
    setGroupByDateList(
      produce({} as any, (draft) => {
        data.data?.forEach((d: any) => {
          const day = formatDate(d.createAt)
          if (draft[day]) {
            draft[day].push(d)
          } else {
            draft[day] = [d]
          }
        })
      })
    )
    // setList(data.data || [])
    if (1 * pagination.pageSize >= data.total) {
      setHasMore(false)
    } else {
      setHasMore(true)
      setPagination({
        ...pagination,
        page: 2
      })
    }
  })

  // useEasyFormRequest(() => getMyAllInstFormList((window as any).APP_CONFIG.blog?.day, {}, groupId))
  const handleAdd = (instanceId = '') => {
    navigate(`/submitlog?activityId=${activityId}&groupId=${groupId}&instanceId=${instanceId}`)
  }
  const handleDetail = (instanceId: string, formId: string) => {
    navigate(`/logdetail?activityId=${activityId}&formId=${formId}&instanceId=${instanceId}&groupId=${groupId}`)
  }
  const [currentItemData, setCurrentItemData] = React.useState<any>(null)
  const handleOpenAction = (d: any) => {
    setCurrentItemData(d)
  }
  const handleAction = (action: string) => {
    switch (action) {
      case 'edit': {
        handleAdd(currentItemData.instId)
        break
      }
      case 'delete': {
        del(activityId, currentItemData.instId, currentItemData.id, groupId).then(() => {
          setCurrentItemData(null)
          Toast.show({ content: '删除成功' })
          refresh()
        })
        break
      }
    }
  }

  return (
    <Box sx={{ p: 2 }} className='h-full relative'>
      <PullToRefresh onRefresh={refresh}>
        {Object.entries(groupByDateList).map(([k, v]) => (
          <Box key={k}>
            <Typography sx={{ mb: 0.5 }} variant='subtitle2'>
              {formatDate(k, 'MM月DD日') + ' ' + chineseWeekday[formatDate(k || '', 'dddd')]}
            </Typography>
            {v.map((d: any) => (
              <Item
                onMore={
                  user.id === d.creatorId
                    ? () => {
                        handleOpenAction(d)
                      }
                    : void 0
                }
                onClick={() => handleDetail(d.instId, d.id)}
                key={d.id}
                creatorName={d.creatorName}
                date={d.createAt}
                title={title}
              />
            ))}
          </Box>
        ))}

        <InfiniteScroll hasMore={hasMore} loadMore={loadMore} />
      </PullToRefresh>
      <AddButton onClick={() => handleAdd()} sx={{ position: 'absolute', bottom: '24px', right: '24px' }} />
      <PopAction
        onClose={() => setCurrentItemData(null)}
        open={Boolean(currentItemData)}
        onAction={handleAction}
        actions={[
          { key: 'edit', title: '修改' },
          { key: 'delete', title: '删除' }
        ]}
      />
    </Box>
  )
}
