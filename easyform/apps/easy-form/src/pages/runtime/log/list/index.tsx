import React from 'react'
import { Box, styled } from '@mui/system'
import { InfiniteScroll, PullToRefresh, <PERSON><PERSON>, Swiper, SwiperRef } from 'antd-mobile'
import { AddButton, TabPanel } from '@/components'
import { useEasyFormRequest } from '@/hooks'
import { getUrlParam } from '@/utils'
import Item from './Item'
import MiniApp from './MiniApp'

const logApp = (window as any).APP_CONFIG.logApp || []

export default function () {
  const [[groupId]] = React.useState([getUrlParam('groupId')])
  const swiperRef = React.useRef<SwiperRef>(null)
  const [activeIndex, setActiveIndex] = React.useState(0)
  return (
    <Box sx={{ bgcolor: 'background.default' }} className='h-screen relative flex flex-col'>
      <Box className='shrink-0'>
        <Tabs
          stretch={false}
          activeKey={activeIndex + ''}
          onChange={(key) => {
            // const index = tabItems.findIndex((item) => item.key === key)
            setActiveIndex(Number(key))
            swiperRef.current?.swipeTo(Number(key))
          }}
        >
          {logApp.map((item: any, i: number) => (
            <Tabs.Tab title={item.title} key={i + ''} />
          ))}
        </Tabs>
      </Box>
      <Box className='flex-1'>
        <Swiper
          style={{ '--height': '100%' }}
          direction='horizontal'
          indicator={() => null}
          ref={swiperRef}
          defaultIndex={activeIndex}
          onIndexChange={(index) => {
            setActiveIndex(index)
          }}
        >
          {logApp.map((t: any, i: number) => (
            <Swiper.Item key={t.key}>
              <TabPanel showValue={activeIndex} value={i}>
                <MiniApp activityId={t.id} />
              </TabPanel>
            </Swiper.Item>
          ))}
        </Swiper>
      </Box>
    </Box>
  )
}
