import React from 'react'
import { Box, styled } from '@mui/system'
import { Typography } from '@/components'
import { Avatar } from 'antd-mobile'
import { MoreOutline as IconMore } from 'antd-mobile-icons'
import { getAvatarByUserId, formatDate } from '@/utils'

export type ItemProps = {
  date?: string | number
  title?: string
  content?: string
  creatorName?: string
  onClick?: () => void
  onMore?: () => void
}

const Container = styled(Box)({
  borderRadius: 12,
  backgroundColor: '#FFF',
  padding: 16
})

export default function ({ date = '', title = '日志', content, creatorName, onClick, onMore }: ItemProps) {
  const handleMoreClick = (e: any) => {
    e.stopPropagation()
    if (typeof onMore === 'function') {
      onMore()
    }
  }

  return (
    <Box sx={{ mb: 2 }} onClick={onClick}>
      <Container>
        <Box className='flex items-center'>
          <Avatar src={getAvatarByUserId('15715167105')} />
          <Box sx={{ ml: 1.5 }} className='flex flex-col flex-grow'>
            <Typography sx={{ mb: 0.5 }} variant='h6'>
              {`${creatorName}的${title}`}
            </Typography>
            <Typography variant='subtitle2'>{formatDate(date, 'MM-DD HH:mm')}</Typography>
          </Box>
          {Boolean(onMore) && (
            <Box className='flex justify-end' onClick={handleMoreClick} sx={{ width: '32px', cursor: 'pointer' }}>
              <IconMore style={{ color: '#999' }} />
            </Box>
          )}
        </Box>
      </Container>
    </Box>
  )
}
