import React from 'react'
import { Popup as AmPopup } from 'antd-mobile'
import { Box, styled } from '@mui/system'

export type PopupActionProps = {
  actions: { key: string; title: string }[]
  open?: boolean
  onClose?: () => void
  onAction?: (key: string) => void
}

const Popup = styled(AmPopup)({
  '--adm-color-background': 'transparent'
})
const Wrapper = styled(Box)({
  background: 'rgba(232,232,232,0.9)',
  marginBottom: 24,
  borderRadius: 12,
  '&>*': {
    borderBottom: '1px solid #ededed'
  },
  '& > :last-child': {
    borderBottom: 'none'
  }
  //   filter: 'blur(10px)'
})

const StyledButton = styled(Box)(({ theme }) => ({
  padding: '12px 0',
  color: theme.palette.primary.main,
  display: 'flex',
  justifyContent: 'center'
}))

export default function ({ actions, open, onClose, onAction }: PopupActionProps) {
  const handleAction = (k: string) => {
    if (typeof onAction === 'function') {
      onAction(k)
    }
  }
  return (
    <Popup onClose={onClose} visible={open}>
      <Box sx={{ p: 2 }}>
        <Wrapper>
          {actions.map((a) => (
            <StyledButton onClick={() => handleAction(a.key)} key={a.key}>
              {a.title}
            </StyledButton>
          ))}
        </Wrapper>
        <Wrapper sx={{ background: '#FFF' }}>
          <StyledButton onClick={onClose} sx={{ fontWeight: 'bold' }}>
            取消
          </StyledButton>
        </Wrapper>
      </Box>
    </Popup>
  )
}
