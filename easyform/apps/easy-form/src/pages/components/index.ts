import { styled, Box } from '@mui/system'
import { Typography } from '@/components'
import { Tabs } from 'antd-mobile'

export const Header = styled('div')<any>(({ theme }) => ({
  position: 'relative',
  backgroundImage: 'url(/easyform/assets/header-bg.png)',
  backgroundSize: 'cover',
  backgroundPosition: 'top',
  padding: '0 16px',
  height: 80
}))
export const HeaderContent = styled('div')({
  height: 48,
  display: 'flex',
  alignItems: 'center',
  justifyContent: 'space-between',
  font: '13px/24px " "'
})

export const ContentWrapper = styled('div')(({ theme }) => ({
  transform: 'translateY(-32px)',
  backgroundColor: theme.palette.background.default,
  borderTopLeftRadius: 24,
  borderTopRightRadius: 24,
  //height: 'calc(100vh - 48px)',
  padding: '24px 16px',
  '& .adm-pull-to-refresh,& .adm-pull-to-refresh-content': {
    height: '100%'
  }
}))
// export const StyledTab = styled(Typography)<any>(({ theme, selected }) => ({
//   textAlign: 'center',
//   borderRadius: selected ? 2 : 0,
//   fontWeight: selected ? 'bold' : 'transparent',
//   color: '#FFF',
//   borderBottom: selected ? '2px solid #FFF' : 'none',
//   margin: '0 8px',
//   '&:last-child': {
//     marginRight: 0
//   }
// }))
export const StyledTabs = styled(Tabs)({
  '& .adm-tabs-header': {
    borderBottom: 'none'
  },
  '& .adm-tabs-tab': {
    padding: '0 0 4px 0'
  },
  '--active-title-color': '#FFF',
  '--active-line-color': '#FFF',
  '--title-font-size': 14,
  color: '#eee'
})

export const StyledTab = styled(Tabs.Tab)({
  padding: '0 0 4px 0'
})
