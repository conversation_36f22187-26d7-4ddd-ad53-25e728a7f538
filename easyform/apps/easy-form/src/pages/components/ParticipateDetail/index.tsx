/* eslint-disable no-prototype-builtins */
import React from 'react'
import { Box, styled } from '@mui/system'
import { useNavigate } from 'react-router-dom'
import { PullToRefresh, Image, SpinLoading, Form, Input, Swiper, Button } from 'antd-mobile'
import { Typography, LoadingWrapper } from '@/components'
import { Submited, Approve, Pass, NoPass, TicketUsed, IconSuccess, Return } from '@/assets/icon'
import { useTicketRequest } from '@/hooks'
import { getTicketQrCode, getTicketDetail } from '@/api/ticket'
import { useEasyFormRequest } from '@/hooks'
import { getUrlParam, formatDate } from '@/utils'
import { getActivityDefine, getMyFormData, getProcessHistory, loadFormDataByInstFormId } from '@/api/easyform'
import { useComponents, PageRender, useLocalComponents } from '@/helper'
import { History } from '@/components'
import { transformToList } from '@/components/PersonnelPicker2/util'

export const State = {
  SUBMITED: 'SUBMITED', //已提交
  APPROVALING: 'APPROVALING', // 审批中
  PASS: 'PASS', //已通过
  NO_PASS: 'NO_PASS', //未通过
  RETURN: 'RETURN',
  TICKET_USED: 'TICKET_USED' //已完成核票
}
const StateIcon: Record<string, any> = {
  TICKET_USED: TicketUsed, //已完成核票
  APPROVALING: Approve, // 审批中
  PASS: Pass, //已通过
  NO_PASS: NoPass, // 未通过
  SUBMITED: Submited,
  RETURN: Return
}

const StateWrapper = styled(Box)({
  position: 'absolute',
  right: 12,
  top: '50%',
  transform: 'translateY(-50%)'
})

const StyledForm = styled(Form, { shouldForwardProp: (prop) => prop !== 'disabledShowNormal' })<any>(
  ({ disabledShowNormal }) => ({
    '& .adm-list-item-disabled.adm-list-item-disabled > .adm-list-item-content > *': {
      opacity: disabledShowNormal ? 1 : 0.6
    }
  })
)

const processAction: any = {
  同意: 1,
  不同意: 0,
  退回: 2
}
export type ParticipateDetailProps = {
  activityId?: string
  activityInstanceId?: string
  instFormId?: string
  showFormDetail?: boolean
  onReady?: (params: any) => void
  scanSuccessText?: string
  allowBack?: boolean
}

export default React.forwardRef<any, ParticipateDetailProps>(function (
  {
    activityId,
    activityInstanceId,
    instFormId,
    showFormDetail,
    onReady,
    scanSuccessText = '您的入场二维码已确认',
    allowBack = true
  },
  ref
) {
  const navigate = useNavigate()
  const [define, setDefine] = React.useState<any>({})

  const [formData, setFormData] = React.useState<Record<string, any>>()
  const { data: activityData } = useEasyFormRequest(() => getActivityDefine(activityId || ''), {
    ready: Boolean(activityId)
  })

  const { loadComplete, components } = useLocalComponents(define.page?.componentTree, define.page?.remoteDependencies)

  const isReady = React.useMemo(() => {
    return Boolean(components) && Boolean(define?.page?.componentTree)
  }, [components, define?.page?.componentTree])

  useEasyFormRequest(() => getMyFormData(activityId, activityInstanceId), {
    ready: (Boolean(activityId) || Boolean(activityInstanceId)) && !instFormId,
    onSuccess: (res: any) => {
      setFormData(res.data)
    }
  })
  useEasyFormRequest(() => loadFormDataByInstFormId(instFormId || ''), {
    ready: Boolean(instFormId),
    onSuccess: (res: any) => {
      setFormData(res.data)
    }
  })
  const isProcessForm = React.useMemo(() => (formData ? formData.hasOwnProperty('reason') : false), [formData])
  const { data: _historyData } = useEasyFormRequest(() => getProcessHistory(formData?.instFormId), {
    ready: isProcessForm
  })
  const historyDataMap = React.useMemo(
    () =>
      _historyData?.data
        ? _historyData?.data.reduce(
            (p: any, c: any) => ({
              ...p,
              [c.defKey]: c
            }),
            {}
          )
        : {},
    [_historyData?.data]
  )
  const historyData: any = React.useMemo(() => {
    return define?.workflow && Array.isArray(_historyData?.data)
      ? Object.keys(define.workflow || {}).length === 0
        ? void 0
        : Object.keys(define.workflow)
            .filter((k) => !['start', 'agree-end', 'refuse-end'].includes(k))
            .map((k) => ({
              title: define.workflow[k].name,
              name: define.workflow[k]?.participantDetail
                ? transformToList(define.workflow[k]?.participantDetail).map((d) => d.name)
                : '',
              // name: define.workflow[k]?.participantDetail
              //   ? Object.entries(define.workflow[k]?.participantDetail)
              //       .map(([k, v]: any) => v.map((n: any) => n.name))
              //       .flat()
              //       .join(',')
              //   : '',
              // name: workflow[k]?.participantDetail?.user.map((d: any) => d.name).join(',') || '',
              status: _historyData.data ? processAction[historyDataMap[k]?.action] : void 0,
              date: historyDataMap ? historyDataMap[k]?.updateAt : void 0
            }))
      : void 0
  }, [_historyData?.data, define?.workflow, historyDataMap])
  const finalHistoryData: any = React.useMemo(
    () =>
      historyData && formData
        ? [{ title: '申请', name: formData.creatorName, status: 9, date: formData?.createAt }, ...historyData]
        : void 0,
    [formData, historyData]
  )

  React.useEffect(() => {
    console.log('===:', finalHistoryData, historyDataMap)
  }, [finalHistoryData, historyDataMap])

  const { data: ticketDetail } = useTicketRequest(() => getTicketDetail(formData?.ticketId), {
    ready: Boolean(formData?.ticketId)
  })

  const { data: qrcode, run: getQrCode } = useTicketRequest(getTicketQrCode, {
    manual: true,
    loadingDelay: 200
  })

  const handleRefreshQrCode = () => {
    getQrCode(formData?.ticketId)
  }

  React.useEffect(() => {
    if (activityData) {
      setDefine(activityData.data)
    }
  }, [activityData])
  React.useEffect(() => {
    if (formData?.ticketId && !showFormDetail) {
      getQrCode(formData?.ticketId)
    }
  }, [formData?.ticketId, getQrCode, showFormDetail])

  React.useImperativeHandle(ref, () => ({
    refresh: async () => {}
  }))
  const swiperRef = React.useRef<any>()

  const loadingPage = React.useMemo(
    () => !define || !formData || (formData?.ticketId ? Boolean(!ticketDetail) : false),
    [define, formData, ticketDetail]
  )
  const status = React.useMemo(() => {
    if (ticketDetail?.status === 1) {
      return State.TICKET_USED
    }
    if (isProcessForm) {
      if (formData?.reason === 'agree') {
        return State.PASS
      }
      if (formData?.reason === 'refuse') {
        return State.NO_PASS
      }
      if (formData?.curTask?.startsWith('start')) {
        return State.RETURN
      }
      return State.APPROVALING
    }

    return State.SUBMITED
  }, [formData?.curTask, formData?.reason, isProcessForm, ticketDetail?.status])
  const allowShowQrCode = React.useMemo(
    () => formData?.ticketId && !showFormDetail && [State.SUBMITED, State.PASS].includes(status),
    [formData?.ticketId, showFormDetail, status]
  )
  const defaultSwiperIndex = React.useMemo(() => {
    if (status === State.TICKET_USED) {
      return 2
    }
    if (showFormDetail) {
      return 3
    }
    if (allowShowQrCode) {
      return 1
    }
    if ([State.APPROVALING, State.NO_PASS, State.PASS, State.RETURN].includes(status)) {
      return 3
    }

    return 0
  }, [allowShowQrCode, showFormDetail, status])
  React.useImperativeHandle(ref, () => ({
    swipeTo: (index: number) => {
      /**
       * 0 已提交
       * 1 二维码
       * 2 票据已确认
       * 3 表单详情
       *
       */
      swiperRef.current.swipeTo(index)
    }
  }))
  React.useEffect(() => {
    if (!loadingPage) {
      if (typeof onReady === 'function') {
        onReady(status)
      }
    }
  }, [loadingPage, onReady, status])
  console.log('define====',define)
  return (
    <Box className='h-full flex flex-col' sx={{ bgcolor: '#FFF' }}>
      <LoadingWrapper loading={loadingPage}>
        <Box
          sx={{
            // position: 'sticky',
            top: 0,
            position: 'relative',
            zIndex: 100,
            p: 1.5,
            bgcolor: '#FFF',
            borderBottom: '1px solid',
            borderColor: 'divider'
          }}
        >
          <Typography variant='h6'>{define?.settings?.name}</Typography>
          <Box sx={{ mt: 1 }} className='flex items-center'>
            <Typography variant='subtitle2'>提交人：{formData?.creatorName}</Typography>
            <Typography sx={{ ml: 2 }} variant='subtitle2'>
              提交时间：{formatDate(formData?.createAt, 'YYYY-MM-DD HH:mm:ss')}
            </Typography>
          </Box>
          <StateWrapper>
            {React.createElement(StateIcon[status] || StateIcon.Submited, { width: 56, height: 56 })}
          </StateWrapper>
        </Box>
        <Box sx={{ bgcolor: 'background.default' }} className='flex-1 overflow-hidden'>
          <Swiper
            ref={swiperRef}
            defaultIndex={defaultSwiperIndex}
            indicator={() => null}
            allowTouchMove={false}
            style={{ '--height': '100%' }}
          >
            <Swiper.Item>
              <Box
                sx={{
                  px: 6,
                  bgcolor: '#FFF',
                  '.adm-image-img': {
                    objectFit: 'contain!important'
                  }
                }}
                className='h-full flex items-center flex-col'
              >
                <Image width='80%' src='/easyform/assets/applied.jpg' />
                <Box sx={{ mt: 1 }} className='flex items-center flex-col'>
                  <Box className='flex items-center'>
                    <IconSuccess width='24px' height='24px' />
                    <Typography variant='h6' sx={{ ml: 1 }}>
                      已提交
                    </Typography>
                  </Box>
                  {define?.settings?.templateSettings?.successTip && (
                    <Typography sx={{ mt: 2, lineHeight: 1.8 }} indent variant='subtitle1'>
                      {define?.settings?.templateSettings?.successTip}
                    </Typography>
                  )}
                  <Typography
                    onClick={() => {
                      swiperRef.current.swipeTo(3)
                    }}
                    variant='subtitle2'
                    sx={{ mt: 4, color: 'primary.main' }}
                  >
                    查看详情
                  </Typography>
                  {/* <Box className='flex justify-center' sx={{ mt: 1 }}>
                <Typography sx={{ textIndent: '2em', fontSize: 16, letterSpacing: '1px' }} variant='subtitle1'>
                  {defaultText}
                </Typography>
              </Box> */}
                </Box>
              </Box>
            </Swiper.Item>
            <Swiper.Item>
              <Box className='h-full flex flex-col items-center' sx={{ bgcolor: '#FFF', pt: 6 }}>
                <Box sx={{ width: '60vw', height: '60vw' }} className='flex items-center justify-center'>
                  {qrcode ? (
                    <Image
                      style={{ maxWidth: 400, maxHeight: 400 }}
                      onClick={handleRefreshQrCode}
                      width='100%'
                      height='100%'
                      src={qrcode}
                    />
                  ) : (
                    <SpinLoading />
                  )}
                </Box>
                <Typography variant='subtitle2' sx={{ mt: 0 }}>
                  点击入场二维码刷新
                </Typography>
                <Typography variant='body2' sx={{ mt: 2 }}>
                  请凭上方二维码入场
                </Typography>
                {define?.settings?.templateSettings?.successTip && (
                  <Typography indent variant='body2' sx={{ mt: 2, mx: 2, color: 'text.hint' }}>
                    {define?.settings?.templateSettings?.successTip}
                  </Typography>
                )}
                <Typography
                  onClick={() => {
                    swiperRef.current.swipeTo(3)
                  }}
                  variant='subtitle2'
                  sx={{ mt: 4, color: 'primary.main' }}
                >
                  查看详情
                </Typography>
              </Box>
            </Swiper.Item>
            <Swiper.Item>
              <Box sx={{ px: 6, paddingTop: '25%', bgcolor: '#FFF' }} className='h-full flex items-center flex-col'>
                <Image width='80%' src='/easyform/assets/applied.jpg' />
                <Box sx={{ mt: 1 }} className='flex items-center flex-col'>
                  <Box className='flex items-center'>
                    <IconSuccess width='24px' height='24px' />
                    <Typography variant='h6' sx={{ ml: 1 }}>
                      {scanSuccessText}
                    </Typography>
                  </Box>
                  <Typography
                    onClick={() => {
                      swiperRef.current.swipeTo(3)
                    }}
                    variant='subtitle2'
                    sx={{ mt: 4, color: 'primary.main' }}
                  >
                    查看详情
                  </Typography>
                </Box>
              </Box>
            </Swiper.Item>
            <Swiper.Item>
              <Box className='h-full overflow-auto'>
                <StyledForm initialValues={formData} disabled mode='card'>
                  <Form.Header>基本信息</Form.Header>
                  <Form.Item name='creatorName' label='申请人'>
                    <Input />
                  </Form.Item>
                  <Form.Item name='creatorId' label='学工号'>
                    <Input />
                  </Form.Item>
                  <Form.Item name='creatorMobile' label='联系电话'>
                    <Input />
                  </Form.Item>
                  <Form.Item name='creatorOrgName' label='部门/院系'>
                    <Input />
                  </Form.Item>
                  <Form.Header>表单信息</Form.Header>
                  {components && define?.page?.componentTree ? (
                    <PageRender componentTree={define?.page?.componentTree || []} components={components} disabled={true} />
                  ) : null}
                  {finalHistoryData && (
                    <>
                      <Form.Header>审批历史</Form.Header>
                      <Box sx={{ p: 1.5 }}>{<History data={finalHistoryData} />}</Box>
                    </>
                  )}
                </StyledForm>

                {allowShowQrCode ? (
                  <Typography
                    align='center'
                    onClick={() => {
                      swiperRef.current.swipeTo(1)
                    }}
                    variant='subtitle2'
                    sx={{ my: 4, color: 'primary.main' }}
                  >
                    查看入场二维码
                  </Typography>
                ) : status === State.RETURN ? (
                  <Box sx={{ position: 'sticky', bottom: 0, pt: 1.5, pb: 3, px: 2, bgcolor: '#FFF' }}>
                    <Button
                      shape='rounded'
                      color='primary'
                      block
                      onClick={() => {
                        navigate(
                          `/submit?activityId=${activityId}` +
                            (activityInstanceId ? `&instanceId=${activityInstanceId}` : ''),
                          {
                            state: { submitFormId: formData?.id }
                          }
                        )
                      }}
                    >
                      重新填写
                    </Button>
                  </Box>
                ) : allowBack ? (
                  <Typography
                    align='center'
                    onClick={() => {
                      swiperRef.current.swipeTo(0)
                    }}
                    variant='subtitle2'
                    sx={{ my: 4, color: 'primary.main' }}
                  >
                    返回
                  </Typography>
                ) : null}
              </Box>
            </Swiper.Item>
          </Swiper>
        </Box>
      </LoadingWrapper>
    </Box>
  )
})
