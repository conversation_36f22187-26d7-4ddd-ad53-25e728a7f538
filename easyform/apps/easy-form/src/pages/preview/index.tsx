import React from 'react'
import { Form, Button, Toast } from 'antd-mobile'
import { useComponents, PageRender } from '@/helper'
import { PageLoading } from '@/components/Loading'
import DefaultTemeplate from '@/@template/default'
import StandardTemeplate from '@/@template/FormTemplate'
import { useAppContext } from '@/context/AppContext'

export default function Preview() {
  const [
    {
      user: { name, id, extra: userData = {} }
    }
  ] = useAppContext()
  const [form] = Form.useForm()
  const templateRef = React.useRef<any>(null)
  const [state] = React.useState<any>(JSON.parse(localStorage.getItem('_yb_preivew') || '{}'))
  const { loadComplete, components } = useComponents(state?.componentTree, state?.remoteDependencies)
  const handleFinish = React.useCallback(
    (values) => {
      console.log(values, state?.componentTree)
      Toast.show({
        content: '预览状态提交无效'
      })
    },
    [state?.componentTree]
  )
  const BaseData = React.useMemo(() => {
    return {
      userName: userData.name || '方楠',
      phone: userData.mobile || '18856856868',
      org: Array.isArray(userData.orgs) ? userData.orgs[0]?.name : void 0,
      speciality: userData.speciality?.name,
      class: userData.class?.name,
      code: userData.code
    }
  }, [userData.class, userData.code, userData.mobile, userData.name, userData.orgs, userData.speciality])
  const handleSubmit = React.useCallback(() => {
    form.submit()
    // templateRef.current?.swipeTo()
  }, [form])
  React.useLayoutEffect(() => {
    if (state?.settings?.name) {
      setTimeout(() => {
        document.title = state?.settings?.name
      }, 500)
    }
  }, [state?.settings?.name])
  const hideSubButton = state?.settings?.type == 'NOTICE'

  return (
    <StandardTemeplate
      isPC={false}
      imageUrlPrefix={(window as any).APP_CONFIG.server.fileUrlPrefix}
      ref={templateRef}
      onSubmit={handleSubmit}
      btnText={state?.settings.templateSettings?.btnText}
      settings={state?.settings}
      pageSettings={state?.pageSettings}
    >
      <React.Suspense fallback={<PageLoading />}>
        {components ? (
          <Form
            form={form}
            onFinish={handleFinish}
            // footer={
            //   <Button block type='submit' color='primary' size='large'>
            //     {state?.settings.templateSettings?.btnText || '提交'}
            //   </Button>
            // }
          >
            <PageRender baseData={BaseData} componentTree={state.componentTree || []} components={components} />
          </Form>
        ) : null}
      </React.Suspense>
    </StandardTemeplate>
  )
}
