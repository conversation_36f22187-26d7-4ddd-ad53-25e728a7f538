import React, { Suspense } from 'react'
import { UpdaterProvider } from '@/pages/pc/Designer/context/UpdateContext'
import FormstatementAlone from '@/pages/pc/Designer/FormstatementAlone'
import { Spin, Alert } from 'antd'
import { useSearchParams } from 'react-router-dom'
import ErrorBoundary from './ErrorBoundary'

/**
 * 独立的表单统计页面
 * 提供必要的上下文包装，使 FormstatementAlone 组件可以独立访问
 *
 * 使用方式:
 * /form-statistics?id=活动ID&insId=实例ID
 *
 * 参数说明:
 * - id: 活动ID (必需)
 * - insId: 实例ID (可选)
 * - pageTo: 页面跳转参数 (可选)
 */
export default function FormStatisticsStandalone() {
  const [searchParams] = useSearchParams()
  const id = searchParams.get('id')
  const insId = searchParams.get('insId')
  const pageTo = searchParams.get('pageTo')

  // 检查必需参数
  if (!id) {
    return (
      <div style={{ padding: 24 }}>
        <Alert
          message="参数错误"
          description="缺少必需的参数 'id'。请在URL中添加 ?id=活动ID"
          type="error"
          showIcon
        />
      </div>
    )
  }

  return (
    <ErrorBoundary>
      <div style={{ height: '100vh', overflow: 'auto', backgroundColor: '#f5f5f5' }}>
        <Suspense
          fallback={
            <div style={{
              display: 'flex',
              justifyContent: 'center',
              alignItems: 'center',
              height: '100vh'
            }}>
              <Spin size="large" tip="加载中..." />
            </div>
          }
        >
          <UpdaterProvider>
            {(isTemplateEdit: boolean) => (
              <div style={{ height: '100%' }}>
                <FormstatementAlone />
              </div>
            )}
          </UpdaterProvider>
        </Suspense>
      </div>
    </ErrorBoundary>
  )
}
