# 表单统计独立页面

这是一个独立的表单统计页面，可以通过路由直接访问，无需嵌入到其他页面中。

## 访问路径

```
/form-statistics
```

## URL 参数

| 参数名 | 类型 | 必需 | 说明 |
|--------|------|------|------|
| id | string | 是 | 活动ID |
| insId | string | 否 | 实例ID |
| pageTo | string | 否 | 页面跳转参数 |

## 使用示例

### 基本用法
```
http://localhost:3000/form-statistics?id=12345
```

### 带实例ID
```
http://localhost:3000/form-statistics?id=12345&insId=67890
```

### 完整参数
```
http://localhost:3000/form-statistics?id=12345&insId=67890&pageTo=1
```

## 功能特性

- ✅ 独立路由访问
- ✅ 参数验证
- ✅ 错误处理
- ✅ 加载状态
- ✅ 响应式布局
- ✅ 上下文提供者包装

## 技术实现

1. **上下文包装**: 使用 `UpdaterProvider` 提供必要的上下文
2. **懒加载**: 使用 `React.lazy` 进行代码分割
3. **错误边界**: 提供友好的错误提示
4. **参数处理**: 自动解析 URL 参数并传递给组件

## 注意事项

- 确保传入的 `id` 参数是有效的活动ID
- 页面需要相应的权限才能访问统计数据
- 建议在生产环境中添加身份验证
