import React, { Component, ReactNode } from 'react'
import { Al<PERSON>, Button } from 'antd'

interface Props {
  children: ReactNode
}

interface State {
  hasError: boolean
  error?: Error
}

/**
 * 错误边界组件
 * 用于捕获表单统计页面中的错误
 */
export default class ErrorBoundary extends Component<Props, State> {
  constructor(props: Props) {
    super(props)
    this.state = { hasError: false }
  }

  static getDerivedStateFromError(error: Error): State {
    return { hasError: true, error }
  }

  componentDidCatch(error: Error, errorInfo: React.ErrorInfo) {
    console.error('表单统计页面错误:', error, errorInfo)
  }

  handleReload = () => {
    window.location.reload()
  }

  render() {
    if (this.state.hasError) {
      return (
        <div style={{ padding: 24 }}>
          <Alert
            message="页面加载失败"
            description={
              <div>
                <p>表单统计页面遇到了一个错误，请尝试刷新页面。</p>
                {this.state.error && (
                  <details style={{ marginTop: 8 }}>
                    <summary>错误详情</summary>
                    <pre style={{ 
                      marginTop: 8, 
                      padding: 8, 
                      backgroundColor: '#f5f5f5',
                      fontSize: 12,
                      overflow: 'auto'
                    }}>
                      {this.state.error.message}
                      {this.state.error.stack}
                    </pre>
                  </details>
                )}
                <Button 
                  type="primary" 
                  onClick={this.handleReload}
                  style={{ marginTop: 16 }}
                >
                  刷新页面
                </Button>
              </div>
            }
            type="error"
            showIcon
          />
        </div>
      )
    }

    return this.props.children
  }
}
