import React from 'react'
import { useTheme, styled, Box } from '@mui/system'
import Typography from '@/components/Typography'
import { Image } from 'antd-mobile'
import { AddOutline } from 'antd-mobile-icons'
// const svg = require('/easyform/assets/icon/createApp.svg')

const StyledDiv = styled('div')({
  backgroundColor: '#fff',
  padding: '25px 12px 13px',
  borderRadius: 6
})
const StyledTitle = styled('div')({})
const StyledDivImg = styled('div')<any>(({ theme }) => ({
  height: 40,
  width: 40,
  margin: '0 auto',
  overflow: 'hidden',
  img: {
    height: 40,
    width: 40,
    filter: `drop-shadow(${theme?.palette?.primary?.light} 40px 0)`,
    transform: 'translateX(-40px)'
  }
}))
const StyledTypography = styled(Typography)<any>(({ theme, isNull }) => ({
  fontSize: isNull ? '17px' : '15px',
  textAlign: isNull ? 'center' : 'left',
  color: isNull ? theme?.palette?.primary?.light : null,
  fontWeight: isNull ? 'bold' : 'normal',
  minHeight: '42px',
  display: '-webkit-box',
  WebkitBoxOrient: 'vertical',
  WebkitLineClamp: '2' /* 这里是超出几行省略 */,
  overflow: 'hidden'
}))

const Root = styled(Box)(({ theme }) => ({
  backgroundColor: '#FFF',
  borderRadius: 8,
  display: 'flex',
  flexDirection: 'column',
  padding: '32px 12px 16px 12px'
}))

const CatImages: any = {
  INFO: '/easyform/assets/icon/tp1.svg',
  PSQ: '/easyform/assets/icon/tp2.svg',
  VOTE: '/easyform/assets/icon/tp3.svg',
  APPLY: '/easyform/assets/icon/tp4.svg'
}

export default function (props: any) {
  const { data, jumpDesign } = props
  return (
    <Root className='flex flex-col' onClick={() => jumpDesign(data)}>
      <Box className='flex justify-center'>
        {data.id ? (
          <Image height={32} src={CatImages[data.category] || '/easyform/assets/icon/tp4.svg'} />
        ) : (
          <Image height={32} src='/easyform/assets/icon/createAppNew.svg' />
        )}
      </Box>
      <Typography
        align={data.id ? void 0 : 'center'}
        sx={{ mt: 2.5, fontWeight: 500, fontSize: 14, color: data.id ? void 0 : '#537fe9' }}
      >
        {data.name}
      </Typography>
    </Root>
  )
  // return (
  //   <StyledDiv
  //     onClick={() => {
  //       jumpDesign(data)
  //     }}
  //   >
  //     <StyledTitle>
  //       <StyledDivImg>
  //         {!data?.id ? (
  //           <Image src='/easyform/assets/icon/createAppNew.svg' />
  //         ) : (
  //           <Image src='/easyform/assets/icon/tp1.svg' />
  //         )}
  //       </StyledDivImg>
  //     </StyledTitle>
  //     <StyledTypography
  //       isNull={data?.isNull}
  //       sx={{
  //         marginTop: '15px'
  //       }}
  //     >
  //       {data?.name}
  //     </StyledTypography>
  //   </StyledDiv>
  // )
}
