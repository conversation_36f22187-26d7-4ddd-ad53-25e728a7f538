import React, { useEffect, useState } from 'react'
import { Box, styled } from '@mui/system'
import { useLocation, useNavigate } from 'react-router-dom'
import { SearchBar, Tabs, InfiniteScroll } from 'antd-mobile'
import { ActivityTypes } from '@/@define/Activity'
import TemplateCard from './components/TemplateCard'
import { getUrlParam } from '@/utils'
import { useEasyFormRequest } from '@/hooks'
import { getTemplateDefine, templateList } from '@/api/easyform'

const Header = styled('div')({
  position: 'relative',
  // height: 80,
  backgroundImage: 'url(/easyform/assets/header-bg.png)',
  backgroundSize: 'cover',
  backgroundPosition: 'top',
  padding: '16px 8px 16px 8px'
})
const Content = styled('div')({
  display: 'grid',
  gridTemplateColumns: '1fr 1fr',
  gridColumnGap: 10,
  gridRowGap: 10
})
const ContentDiv = styled('div')({
  padding: '16px 10px',
  background: 'rgb(242,242,242)',
  flexGrow: 1,
  overflow: 'auto'
})
const StyledTabs = styled(Tabs)(({ theme }) => ({
  color: '#FFF',
  '--active-line-color': '#FFF',
  '--adm-color-border': 'transparent',
  '& .adm-tabs-header-mask-left,& .adm-tabs-header-mask-right': {
    background: 'transparent'
  },
  '.adm-tabs-tab': {
    color: '#eee',
    fontSize: 14
  },
  '.adm-tabs-tab.adm-tabs-tab-active': {
    color: '#FFF',
    fontWeight: 500
  }
}))
const activityTypes = [
  {
    id: '',
    title: '全部'
  }
].concat(ActivityTypes)
const defaultPagination = { page: 1, pageSize: 10 }
export default function () {
  const [activityId, usedBy] = [getUrlParam('activityId'), getUrlParam('usedBy')]
  const [{ page, pageSize }, setPagination] = React.useState(defaultPagination)
  const location: any = useLocation()
  const [hasMore, setHasMore] = React.useState(false)
  const [type, setType] = useState<string>('APPLY')
  const [templates, setTemplates] = React.useState<any[]>([])
  const [searchKey, setSearchKey] = useState('')

  const navigate = useNavigate()
  const { runAsync: fetchTemplate } = useEasyFormRequest(templateList, {
    manual: true,
    loadingDelay: 200
  })

  const handleRefresh = React.useCallback(async () => {
    const res = await fetchTemplate({
      pageNo: defaultPagination.page,
      pageSize: defaultPagination.pageSize,
      category: type,
      keywords: searchKey
    })
    const _hasMore = res.total > defaultPagination.page * defaultPagination.pageSize
    setPagination({ page: defaultPagination.page + 1, pageSize: defaultPagination.pageSize })
    setHasMore(_hasMore)
    setTemplates(res.data)
  }, [fetchTemplate, type, searchKey])
  const loadMoreTemplate = async () => {
    const res = await fetchTemplate({ pageNo: page, pageSize: pageSize, category: type })
    const _hasMore = res.total > page * pageSize
    setHasMore(_hasMore)
    if (_hasMore) {
      setPagination(({ page, pageSize }) => ({ page: page + 1, pageSize: pageSize }))
    }
    setTemplates((prev) => [...prev, ...res.data])
  }

  useEffect(() => {
    location?.state?.type && setType(location?.state?.type)
  }, [location?.state])
  const jumpDesign = (templateData: any) => {
    if (!templateData.id) {
      navigate('/designer', { state: { usedBy: usedBy, type: location.state?.type || 'APPLY' } })
    } else {
      navigate('/previewtd', {
        state: {
          templateId: templateData.id,
          usedBy: usedBy,
          type: location.state?.type || 'APPLY'
        }
      })
    }
  }
  // React.useEffect(() => {
  //   fetch('/easyform/templates.json').then((res) =>
  //     res.json().then((data) => {
  //       setTemplates(data)
  //     })
  //   )
  // }, [])
  // const list = React.useMemo(() => templates.filter((t) => !type || t.category === type), [templates, type])
  React.useEffect(() => {
    if (activityId) {
      navigate('/designer', {
        state: { id: activityId, usedBy: usedBy },
        replace: true
      })
    }
  }, [activityId, navigate, usedBy])
  React.useEffect(() => {
    handleRefresh()
  }, [handleRefresh, type])
  React.useEffect(() => {
    if (window.self !== window.top) {
      window.parent.postMessage('HIDE_BACK', '*')
    }
  }, [])
  return (
    <Box className='h-full flex flex-col'>
      <Header>
        <SearchBar
          onSearch={(value) => {
            setSearchKey(value)
          }}
          placeholder='搜索模版，拿来即用'
        />
      </Header>
      <ContentDiv>
        <Content>
          <TemplateCard data={{ name: '从空白创建' }} jumpDesign={jumpDesign}></TemplateCard>
          {templates.map((item: any, i: string | number) => (
            <TemplateCard key={i} data={item} jumpDesign={jumpDesign}></TemplateCard>
          ))}
        </Content>
        <InfiniteScroll hasMore={hasMore} loadMore={loadMoreTemplate}>
          {null}
        </InfiniteScroll>
      </ContentDiv>
    </Box>
  )
}
