import React from 'react'
import { Form, Button, Toast } from 'antd-mobile'
import { useNavigate, useLocation } from 'react-router-dom'
import { styled, Box } from '@mui/system'
import { useComponents, PageRender, useLocalComponents } from '@/helper'
import { PageLoading } from '@/components/Loading'
import DefaultTemeplate from '@/@template/standard'
import { getUrlParam } from '@/utils'
import { getTemplateDefine } from '@/api/easyform'
import { useEasyFormRequest } from '@/hooks'

const Footer = styled(Box)({
  position: 'sticky',
  zIndex: 9999,
  padding: '0 16px',
  bottom: 0,
  height: 72,
  left: 0,
  right: 0,
  backgroundColor: '#FFF',
  display: 'flex',
  alignItems: 'center',
  justifyContent: 'center',
  boxShadow: '0 -10px 15px -3px rgb(0 0 0 / 0.1), 0 -4px 6px -4px rgb(0 0 0 / 0.1)'
})

export default function () {
  const navigate = useNavigate()
  const location: any = useLocation()
  const [templateId] = React.useState(location.state?.templateId || getUrlParam('templateId'))
  const [templates, setTemplates] = React.useState<any[]>([])
  const [state, setState] = React.useState<any>()
  const { loadComplete, components } = useLocalComponents(state?.page?.componentTree, state?.page?.remoteDependencies)
  const { run: fetchDefine } = useEasyFormRequest(() => getTemplateDefine(templateId), {
    ready: Boolean(templateId),
    onSuccess: (res: any) => {
      setState(res.data)
    }
  })
  const handleFinish = React.useCallback(
    (values) => {
      console.log(values, state?.componentTree)
      Toast.show({
        content: '预览状态提交无效'
      })
    },
    [state?.componentTree]
  )
  // React.useEffect(() => {
  //   fetch('/easyform/templates.json').then((res) =>
  //     res.json().then((data) => {
  //       setTemplates(data)
  //     })
  //   )
  // }, [])
  // React.useEffect(() => {
  //   if (templateId && templates.length > 0) {
  //     console.log('t:', templateId === '1')
  //     const _template = templates.find((t) => t.id === templateId)
  //     if (_template && _template.data) {
  //       setState(_template.data)
  //     }
  //   }
  // }, [templateId, templates])

  const handleUseTemplate = React.useCallback(() => {
    if (state) {
      navigate('/designer', { state: { template: state, usedBy: location.state?.usedBy }, replace: true })
    }
  }, [location.state?.usedBy, navigate, state])

  React.useEffect(() => {
    if ((window as any).EASY_FORM && state) {
      ;(window as any).EASY_FORM.submitActivity = (callbackFn: any) => {
        if (typeof callbackFn === 'function') {
          callbackFn(state)
        }
      }
    }

    return () => {
      if ((window as any).EASY_FORM?.submitActivity) {
        ;(window as any).EASY_FORM.submitActivity = void 0
      }
    }
  }, [state])
  React.useEffect(() => {
    if (window.self !== window.top) {
      window.parent.postMessage('SHOW_BACK', '*')
    }
  }, [])
  return (
    <>
      <DefaultTemeplate
        imageUrlPrefix={(window as any).APP_CONFIG.server.fileUrlPrefix}
        desc={state?.settings?.desc}
        imgs={state?.settings?.imgs}
        title={state?.settings?.name}
      >
        <React.Suspense fallback={<PageLoading />}>
          {components ? (
            <Form onFinish={handleFinish}>
              <PageRender componentTree={state.page.componentTree || []} components={components} />
            </Form>
          ) : null}
        </React.Suspense>
      </DefaultTemeplate>
      <Footer>
        <Button onClick={handleUseTemplate} block shape='rounded' color='success'>
          在此基础上编辑模板
        </Button>
      </Footer>
    </>
  )
}
