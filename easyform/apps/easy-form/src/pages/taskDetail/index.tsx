import React, { useMemo } from 'react'
import { useNavigate, useLocation } from 'react-router-dom'
import { Box, styled } from '@mui/system'
import { InfiniteScroll, PullToRefresh, Tabs } from 'antd-mobile'
import { AddOutline as IconAdd } from 'antd-mobile-icons'
import { Spin, Result } from 'antd'
import { useEasyFormRequest, useBaseRequest } from '@/hooks'

import { TabPanel, Typography } from '@/components'
import { Header, HeaderContent, ContentWrapper } from '@/pages/components'

import { useAppContext } from '@/context/AppContext'
import { RemoteComponent } from '@/helper'
import { getActivity, getActivityList } from '@/api/easyform'

export default function () {
  const navigate = useNavigate()
  const location = useLocation()
  const {
    state: { taskId, id }
  } = location as any

  const [userContext, dispach] = useAppContext()

  const userInfo = useMemo(() => {
    const { user, ybUser } = userContext
    return {
      id: user?.id,
      displayName: user?.name,
      manageOrgs: ybUser?.manageOrgs,
      classLeaderAcademy: ybUser?.classLeaderAcademy,
      manageclasses: ybUser?.manageclasses,
      orgs: user.orgs,
      currentIdentity: ybUser?.currentIdentity
    }
  }, [userContext])
  const { loading, data: activityData } = useEasyFormRequest(() => getActivity(id), {
    ready: Boolean(id)
  })
  const ErrMsg = useMemo(() => {
    if (
      activityData &&
      activityData.data?.settings?.info?.taskProcess &&
      activityData.data?.settings?.info?.taskProcess?.length
    ) {
      return {
        title: '当前表单未发布',
        subTitle: '委派将在表单发布后生效，请先发布'
      }
    }
    return {
      title: '无委派任务',
      subTitle: '当前表单未启用任务委派'
    }
  }, [activityData])
  return (
    <Box sx={{ bgcolor: 'background.default', height: '100%' }}>
      <Header>
        <HeaderContent></HeaderContent>
      </Header>
      <ContentWrapper sx={{ px: 0, backgroundColor: 'white' }}>
        {taskId ? (
          <Box sx={{ minHeight: '100%', background: 'white' }}>
            {React.createElement(RemoteComponent, {
              _remoteInfo: {
                componentName: 'yiban-frontend-admin.EasyformTaskDetail',
                version: '0.1.0'
              },
              taskId,
              delegate: true,
              user: userInfo,
              title: <Typography sx={{ fontWeight: 'bold' }}>进度跟踪</Typography>,
              fallback: (
                <Spin tip={'正在加载任务详情'}>
                  <Box sx={{ height: '90vh' }}></Box>
                </Spin>
              )
            })}
          </Box>
        ) : (
          <Result status='404' title={ErrMsg.title} subTitle={ErrMsg.subTitle} />
        )}
      </ContentWrapper>
    </Box>
  )
}
