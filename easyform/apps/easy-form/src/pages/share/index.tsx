import { Box, styled } from '@mui/system'
import React, { useEffect, useRef, useState } from 'react'
import { Button, Card, Space, Toast, Popup } from 'antd-mobile'
import { Typography } from '@/components'
import { Input, Form } from 'antd-mobile'
import { useQRCode } from 'next-qrcode'
import { useLocation, useNavigate } from 'react-router-dom'
import { useLongPress } from 'ahooks'
import { DownloadOutlined } from '@ant-design/icons'
const CopyIcon = () => (
  <svg viewBox='0 0 1024 1024' version='1.1' xmlns='http://www.w3.org/2000/svg' p-id='19977' width='32' height='32'>
    <path d='M589.3 260.9v30H371.4v-30H268.9v513h117.2v-304l109.7-99.1h202.1V260.9z' fill='#E1F0FF' p-id='19978'></path>
    <path d='M516.1 371.1l-122.9 99.8v346.8h370.4V371.1z' fill='#E1F0FF' p-id='19979'></path>
    <path d='M752.7 370.8h21.8v435.8h-21.8z' fill='#446EB1' p-id='19980'></path>
    <path d='M495.8 370.8h277.3v21.8H495.8z' fill='#446EB1' p-id='19981'></path>
    <path d='M495.8 370.8h21.8v124.3h-21.8z' fill='#446EB1' p-id='19982'></path>
    <path d='M397.7 488.7l-15.4-15.4 113.5-102.5 15.4 15.4z' fill='#446EB1' p-id='19983'></path>
    <path d='M382.3 473.3h135.3v21.8H382.3z' fill='#446EB1' p-id='19984'></path>
    <path d='M382.3 479.7h21.8v348.6h-21.8zM404.1 806.6h370.4v21.8H404.1z' fill='#446EB1' p-id='19985'></path>
    <path
      d='M447.7 545.1h261.5v21.8H447.7zM447.7 610.5h261.5v21.8H447.7zM447.7 675.8h261.5v21.8H447.7z'
      fill='#6D9EE8'
      p-id='19986'
    ></path>
    <path d='M251.6 763h130.7v21.8H251.6z' fill='#446EB1' p-id='19987'></path>
    <path
      d='M251.6 240.1h21.8v544.7h-21.8zM687.3 240.1h21.8v130.7h-21.8zM273.4 240.1h108.9v21.8H273.4z'
      fill='#446EB1'
      p-id='19988'
    ></path>
    <path
      d='M578.4 240.1h130.7v21.8H578.4zM360.5 196.5h21.8v108.9h-21.8zM382.3 283.7h196.1v21.8H382.3zM534.8 196.5h65.4v21.8h-65.4z'
      fill='#446EB1'
      p-id='19989'
    ></path>
    <path
      d='M360.5 196.5h65.4v21.8h-65.4zM404.1 174.7h152.5v21.8H404.1zM578.4 196.5h21.8v108.9h-21.8z'
      fill='#446EB1'
      p-id='19990'
    ></path>
  </svg>
)
const StyledButton = styled(Button)(({ theme }) => ({
  backgroundColor: '#eeeeee'
}))
export default () => {
  const location = useLocation()
  const { Canvas } = useQRCode()
  const navigator = useNavigate()
  const [joinUrl, setJoinUrl] = React.useState<string>()
  const spanRef = React.useRef<any>(null)
  const [PopOpen, setPopOpen] = useState(false)
  const qrcodeRef = useRef(null)
  useLongPress(
    () => {
      setPopOpen(true)
    },
    qrcodeRef,
    { delay: 500 }
  )
  console.log(location?.state, 2120)
  useEffect(() => {
    if ((location.state as any)?.id) {
      const id = (location.state as any)?.id
      setJoinUrl(`${window.location.origin}/easyform/submit?activityId=${id}`)
    }
  }, [location])
  const handleCopy = () => {
    spanRef.current?.select()
    document.execCommand('copy')
    // navigator.clipboard.writeText(text).then(() => {
    //   message.open({
    //     type: 'success',
    //     content: '已成功复制到剪切板'
    //   })
    // })
    Toast.show({
      icon: 'success',
      content: '复制成功！'
    })
  }
  const downloadQRCode = () => {
    const canvas = document.getElementById('mobile-form-qrcode')?.querySelector<HTMLCanvasElement>('canvas')
    if (canvas) {
      const url = canvas.toDataURL()
      const a = document.createElement('a')
      a.download = '表单分享.png'
      a.href = url
      document.body.appendChild(a)
      a.click()
      document.body.removeChild(a)
      Toast.show({
        icon: 'success',
        content: '保存成功！'
      })
      return
    }
    Toast.show({
      icon: 'fail',
      content: '保存失败！'
    })
  }
  const handleDownloadQr = () => {
    downloadQRCode()
    setPopOpen(false)
  }
  return (
    <Box className={'h-full'} sx={{ backgroundColor: '#f3f4f5' }}>
      <Card style={{ border: 'none', marginBottom: '16px', borderRadius: 0 }} title={<Typography>问卷链接</Typography>}>
        <Box sx={{ display: 'flex', alignItems: 'center' }}>
          <Typography>
            <CopyIcon />
          </Typography>
          <input
            ref={spanRef}
            value={joinUrl}
            readOnly
            style={{
              wordBreak: 'break-all',
              display: 'inline-block',
              overflow: 'hidden',
              whiteSpace: 'nowrap',
              background: 'none',
              textOverflow: 'ellipsis',
              border: 'none',
              outline: 'none',
              flexGrow: 1
            }}
          ></input>
        </Box>
        <Space wrap>
          <StyledButton
            onClick={() => {
              handleCopy()
            }}
          >
            复制
          </StyledButton>
          <StyledButton
            onClick={() => {
              if (joinUrl) {
                window.open(joinUrl)
              }
            }}
          >
            打开
          </StyledButton>
        </Space>
      </Card>
      <Card style={{ border: 'none', marginBottom: '16px', borderRadius: 0 }} title={<Typography>二维码</Typography>}>
        <Typography sx={{ color: '#949494', fontSize: 15 }}>长按二维码，保存到手机</Typography>
        <Box
          onClick={(e) => {
            e.preventDefault()
          }}
          onTouchEnd={(e) => {
            e.preventDefault() //阻止移动端长按弹出浏览器菜单
          }}
          id={'mobile-form-qrcode'}
          sx={{ width: 'fit-content' }}
          ref={qrcodeRef}
        >
          {joinUrl ? <Canvas text={joinUrl || ''}></Canvas> : <></>}
        </Box>
      </Card>
      <Popup
        visible={PopOpen}
        bodyStyle={{ height: '10vh' }}
        onMaskClick={() => {
          setPopOpen(false)
        }}
      >
        <Box sx={{ display: 'flex', justifyContent: 'center' }}>
          <Button
            onClick={() => {
              handleDownloadQr()
            }}
            style={{ width: '100%' }}
            fill='none'
          >
            <DownloadOutlined />
            <Typography>保存到手机</Typography>
          </Button>
        </Box>
      </Popup>
    </Box>
  )
}
