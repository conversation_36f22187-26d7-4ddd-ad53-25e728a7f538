/* eslint-disable @typescript-eslint/no-non-null-assertion */
import React from 'react'
import { Form, Button, Input, Toast, DotLoading, Image } from 'antd-mobile'
import { Box, styled } from '@mui/system'
import { useLocation } from 'react-router-dom'
import { useCom<PERSON>, PageRender } from '@/helper'
import { PageLoading } from '@/components/Loading'
import DefaultTemeplate from '@/@template/default'
import StandardTemplate from '@/@template/standard'
import Approve, { TBaseData } from '@/@template/approve'
import { Typography, LoadingWrapper } from '@/components'

import { useEasyFormRequest } from '@/hooks'
import {
  getActivityDefine,
  saveActivityInstance,
  read,
  loadFormData,
  checkAllowJoin,
  getProcessContext,
  approveProcess
} from '@/api/easyform'
import { getUrlParam, getAcitvityDate } from '@/utils'
import { useNavigate } from 'react-router-dom'
import { useAppContext } from '@/context/AppContext'
import PageWrapper from '@/@template/PageWrapper'
import { IconSuccess } from '@/@template/icon'

const StyledForm = styled(Form, { shouldForwardProp: (prop) => prop !== 'disabledShowNormal' })<any>(
  ({ disabledShowNormal }) => ({
    '& .adm-list-item-disabled.adm-list-item-disabled > .adm-list-item-content > *': {
      opacity: disabledShowNormal ? 1 : 0.6
    }
  })
)

export default function () {
  const navigate = useNavigate()
  const [
    {
      user: { name, id, extra: userData = {} }
    }
  ] = useAppContext()
  const [define, setDefine] = React.useState<any>({})
  const [[formId, instFormId, assignmentId]] = React.useState([
    getUrlParam('formId'),
    getUrlParam('instFormId'),
    getUrlParam('assignmentId')
  ])
  const [activityId, setActivityId] = React.useState('')
  const [hasFinished, setHasFinished] = React.useState(false)

  const { run: approve } = useEasyFormRequest(approveProcess, {
    manual: true,
    onSuccess: () => {
      Toast.show({
        icon: 'success',
        content: '提交成功'
      })
      if (window.opener) {
        window.opener.postMessage({ todoId: assignmentId })
      }
      setTimeout(() => {
        setHasFinished(true)
        // window.close()
      }, 1000)
    }
  })
  const [approveBaseData, setApproveBaseData] = React.useState<TBaseData>()

  const { data: activityData } = useEasyFormRequest(() => getActivityDefine(activityId), {
    ready: Boolean(activityId)
  })

  const { data: context } = useEasyFormRequest(() => getProcessContext({ formId, instFormId }), {
    onSuccess: ({ data }: any) => {
      console.log('==process context:', data)
      /**
       * 退回到提交人开始节点时，跳转到提交页面
       */
      if (data.current?.key === 'start') {
        navigate(`/submit?activityId=${data.activityId}&instanceId=${data.activityInstId}`, { replace: true })
        return
      }
      setActivityId(data.activityId)
    }
  })

  const { loading: saveLoading, run: save } = useEasyFormRequest(saveActivityInstance, {
    loadingDelay: 200,
    manual: true,
    onSuccess: () => {
      Toast.show({
        icon: 'success',
        content: '提交成功'
      })
    }
  })
  React.useEffect(() => {
    if (activityData) {
      setDefine(activityData.data)
    }
  }, [activityData])
  const { loadComplete, components } = useComponents(define.page?.componentTree, define.page?.remoteDependencies)

  const BaseData = React.useMemo(() => {
    return {
      userName: userData.name,
      phone: userData.mobile,
      org: Array.isArray(userData.orgs) ? userData.orgs[0]?.name : void 0,
      speciality: userData.speciality?.name,
      class: userData.class?.name,
      code: userData.code
    }
  }, [userData.class, userData.code, userData.mobile, userData.name, userData.orgs, userData.speciality])
  const handleApprove = React.useCallback(
    (values) => {
      approve(context?.data?.activityInstFormId, { ...values, assignmentId })
    },
    [approve, assignmentId, context?.data?.activityInstFormId]
  )

  const isReady = React.useMemo(() => {
    return Boolean(components) && Boolean(define?.page?.componentTree)
  }, [components, define?.page?.componentTree])

  React.useEffect(() => {
    if (activityData && context) {
      const _activitySettings = activityData.data.settings
      const _contextData = context.data
      setApproveBaseData({
        activity: {
          name: _activitySettings.name,
          date: getAcitvityDate(_activitySettings.startDateTime, _activitySettings.endDateTime)
        },
        applicat: {
          name: _contextData.applicantInfo.name,
          code: _contextData.applicantInfo.code,
          org: _contextData.applicantInfo.orgs[0]?.name,
          phone: _contextData.formData.creatorMobile || ''
        },
        applicatFormData: _contextData.formData,
        nexts: Array.isArray(_contextData.nexts) ? _contextData.nexts : []
      })
    }
  }, [activityData, context])

  return !isReady || !approveBaseData ? (
    <Box className='h-screen flex items-center justify-center'>
      <DotLoading />
    </Box>
  ) : (
    <React.Suspense fallback={<PageLoading />}>
      <Box className='h-screen' sx={{ bgcolor: '#F8f8f8' }}>
        <Box className='h-full' sx={{ maxWidth: 800, margin: '0 auto', bgcolor: '#FFF' }}>
          <PageWrapper>
            {hasFinished ? (
              <Box
                sx={{
                  px: 6,
                  bgcolor: '#FFF',
                  '.adm-image-img': {
                    objectFit: 'contain!important'
                  }
                }}
                className='h-full flex items-center flex-col'
              >
                <Image width='70%' src='/easyform/assets/access.png' />
                <Box sx={{ mt: 1 }} className='flex items-center flex-col'>
                  <Box className='flex items-center'>
                    <IconSuccess width='24px' height='24px' />
                    <Typography variant='h6' sx={{ ml: 1 }}>
                      已处理
                    </Typography>
                  </Box>
                  <Box sx={{ mt: 2 }}>
                    <Button
                      onClick={() => {
                        window.close()
                      }}
                      size='small'
                      shape='rounded'
                    >
                      返回
                    </Button>
                  </Box>
                </Box>
              </Box>
            ) : (
              <Approve onSubmit={handleApprove} baseData={approveBaseData!}>
                {components && define.page.componentTree ? (
                  <PageRender
                    baseData={BaseData}
                    componentTree={define.page.componentTree || []}
                    components={components}
                  />
                ) : null}
              </Approve>
            )}
          </PageWrapper>
        </Box>
      </Box>
    </React.Suspense>
  )
}
