import React, { useEffect } from 'react'
import { Box, styled } from '@mui/system'
import { Button, Input, Form, SpinLoading } from 'antd-mobile'
import { useBoolean } from 'ahooks'
import { useEditorContext, updateTemplateSettings, updateEditorSettings } from '@/pages/designer/context'
import { getDependencies } from '@/helper'
import { StyledForm, StyledFormItem } from '../settings'
import { TemplateSelect, Typography } from '@/components'
import { USED_BY_PLATEFORM } from '@/@define'

const templates = [
  {
    id: 1,
    url: '/easyform/assets/template/template2.png',
    componentName: 'Default',
    packageName: '@yiban/page-template',
    version: '0.1.0'
  },
  {
    id: 2,
    url: '/easyform/assets/template/template3.png',
    componentName: 'Default',
    packageName: '@yiban/page-template',
    version: '0.1.0'
  },
  {
    id: 3,
    url: '/easyform/assets/template/template1.png',
    componentName: 'Default',
    packageName: '@yiban/page-template',
    version: '0.1.0'
  }
]
const ButtonWrapper = styled(Box)({
  margin: '12px'
})

interface IPreview {
  onSave: (data: any) => void
  usedBy?: string
  saveLoading?: boolean
  editMode?: boolean
}
export default React.forwardRef<any, IPreview>(({ onSave, usedBy, saveLoading, editMode }, ref) => {
  const [form] = Form.useForm()
  const [templatesData, setTemplatesData] = React.useState(templates)
  const btnText = Form.useWatch('btnText', form)
  const [{ componentTree, settings, components, pageSettings, formType, formStatus }, editorDispatch] =
    useEditorContext()
  const handlePreview = () => {
    localStorage.setItem(
      '_yb_preivew',
      JSON.stringify({
        componentTree: componentTree,
        remoteDependencies: getDependencies(componentTree, components),
        settings: settings,
        pageSettings
      })
    )
    window.open(`${(window as any).PUBLIC_URL}/preview`)
  }
  useEffect(() => {
    updateEditorSettings(editorDispatch, {
      submitText: btnText
    })
  }, [btnText, editorDispatch])
  const handleSave = React.useCallback(() => {
    if (typeof onSave === 'function') {
      onSave(componentTree)
    }
  }, [componentTree, onSave])
  const handleFormChange = React.useCallback(
    (changeValues: any, allValues?: any) => {
      updateTemplateSettings(editorDispatch, changeValues)
    },
    [editorDispatch]
  )

  React.useImperativeHandle(ref, () => ({
    submit: async () => {
      return new Promise((resolve, reject) => {
        form
          .validateFields()
          .then((res) => {
            resolve({ result: 1, data: res })
          })
          .catch(() => {
            resolve({ result: 0 })
          })
      })
    },
    setValues: (values: any) => {
      form.setFieldsValue({ ...values })
    }
  }))
  return (
    <Box sx={{ px: 1 }} ref={ref}>
      <Box>
        <StyledForm
          onValuesChange={handleFormChange}
          form={form}
          mode='card'
          initialValues={
            settings.templateSettings || {
              template: templatesData[0],
              btnText: '提交'
            }
          }
        >
          {/* <StyledFormItem
            noBorder
            name='template'
            label={
              <div className='flex justify-between'>
                <span>页面风格</span>
                {templatesData?.length > 3 ? <span className='text-sm underline text-stone-400'>更多</span> : null}
              </div>
            }
          >
            <TemplateSelect dataset={templatesData} />
          </StyledFormItem> */}
          {formType !== 'NOTICE' ? (
            <>
              <StyledFormItem noBorder label='按钮文本' name='btnText'>
                <Input placeholder='例如：提交' />
              </StyledFormItem>
              <StyledFormItem noBorder label='报名成功提示语' name='successTip'>
                <Input placeholder='' />
              </StyledFormItem>
            </>
          ) : (
            ''
          )}
        </StyledForm>
      </Box>
      <ButtonWrapper>
        <Button style={{ marginBottom: 16 }} onClick={handlePreview} fill='outline' color='primary' block>
          预览
        </Button>
        {usedBy !== USED_BY_PLATEFORM.TASK && (
          <Button disabled={saveLoading} onClick={handleSave} color='primary' block>
            {saveLoading ? (
              <Box className='flex justify-center'>
                <SpinLoading color='#FFF' />
              </Box>
            ) : editMode ? (
              '保存'
            ) : (
              '保存'
            )}
          </Button>
        )}
      </ButtonWrapper>
    </Box>
  )
})
