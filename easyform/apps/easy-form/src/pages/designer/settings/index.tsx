import React, { useCallback, useEffect, useMemo, useState } from 'react'
import { styled, Box } from '@mui/system'
import { useLocation } from 'react-router-dom'
import {
  EditFilled,
  EyeFilled,
  QuestionOutlined,
  CloseCircleOutlined,
  LoadingOutlined,
  PlusOutlined,
  FundFilled,
  ExclamationCircleFilled,
  SwapOutlined,
  A<PERSON>toreOutlined,
  TeamOutlined,
  QuestionCircleOutlined
} from '@ant-design/icons'
import { Form, Input, TextArea, Radio, Popup, Space, Switch } from 'antd-mobile'
import { DateRangePicker, Typography, PersonnelPicker, PersonnelPicker2, ImageUploader } from '@/components'
import { useEditorContext, updateSettings, updateEditorSettings } from '@/pages/designer/context'
import { ParticipantCat } from '@/@define'
import { useAppContext } from '@/context/AppContext'
import { PARTYPES } from '@/pages/pc/Designer/FormSetting'
import {
  Breadcrumb,
  Tag,
  Segmented,
  Upload,
  Select,
  DatePicker,
  List,
  Row,
  Popover,
  Col,
  Card,
  Modal,
  InputNumber,
  Switch as PcSwitch,
  Button,
  Spin,
  Image,
  message
} from 'antd'
import { USED_BY_PLATEFORM, ADMIN_TYPE, FORM_CONFIG } from '@/@define'
import RemoteComponent from '@yiban/system/lib/helper/RemoteComponent'
import { isDepartAdminUser, isFdyUser, isSupperUser, isXgAdminUser } from '@/pages/pc/utils'
import { isLocalSource } from '@/utils'

interface ISettings {
  onSubmit?: (values: any) => void
  // isUsedByTask?: boolean
  usedBy?: string
  settings?: any
}

export const StyledForm = styled(Form)({
  '.adm-tag': {
    marginRight: '10px'
  },
  '& .adm-list.adm-list': {
    '--padding-left': 0,
    '--padding-right': 0
  },
  '& .adm-list-card': {
    margin: '0 12px'
  },
  '& .horizontalName': {
    fontSize: 'var(--adm-font-size-7);'
  }
})

export const StyledFormItem = styled(Form.Item)<any>(({ noBorder }) => ({
  '& .adm-list-item-content': {
    borderStyle: noBorder ? 'none' : void 0
  },
  '& .adm-form-item-label': {
    marginBottom: '8px !important;'
  }
}))
const StyledPopup = styled(Popup)<any>(() => ({
  '& .adm-popup-body': {
    width: '100vw !important'
  }
}))

async function upload(file: File) {
  const res = await new Promise<any>((resolve, reject) => {
    setTimeout(() => {
      resolve({ url: URL.createObjectURL(file) })
    }, 3000)
  })
  return res
}

export default React.forwardRef<any, ISettings>(function Settings({ onSubmit, usedBy }, ref) {
  const [{ settings, taskInfo }, editorDispatch] = useEditorContext()
  const [
    {
      user: { adminType, orgs }
    }
  ] = useAppContext()
  const [enableTask, setEnableTask] = useState(false)
  const [form] = Form.useForm()
  const allowTicket = Form.useWatch('isTicket', form)
  const cover = Form.useWatch('imgs', form)
  const title = Form.useWatch('name', form)
  const desc = Form.useWatch('desc', form)
  const location: any = useLocation()
  const [formType, setFormType] = useState(location.state?.type || 'APPLY')
  const allowAUDIT = Form.useWatch('type', form)
  const clazzExtra = React.useMemo(() => {
    return orgs
      ? orgs?.map((o) => ({
          code: `os:${o.code}`,
          name: `${o.name}所有学生`
        }))
      : []
  }, [orgs])

  //处理任务数据
  const handleTaskData = async (data?: any) => {
    if (data) {
      return
    } else {
      try {
        const taskData = taskRef && taskRef.current && (await (taskRef.current as any).getTaskValue(true))
        if (taskData) {
          return taskData
        } else {
          throw new Error('未选择人员')
        }
      } catch (e) {
        throw new Error('任务信息校验出错')
      }
    }
  }
  // useEffect(() => {
  //   updateEditorSettings(
  //     editorDispatch,
  //     isLocalSource(cover?.[0])
  //       ? {
  //           cover: { value: cover }
  //         }
  //       : {
  //           cover: { src: cover }
  //         }
  //   )
  // }, [cover, editorDispatch])
  useEffect(() => {
    updateSettings(editorDispatch, {
      name: title,
      desc: desc
    })
  }, [title, desc, editorDispatch])
  React.useImperativeHandle(ref, () => ({
    submit: async () => {
      return new Promise((resolve, reject) => {
        form
          .validateFields()
          .then((res) => {
            console.log('表单校验结果:', res)
            if (enableTask) {
              handleTaskData().then(({ process }: any) => {
                console.log('任务信息校验结果:', { ...res, taskProcess: process })
                resolve({ result: 1, data: { ...res, taskProcess: process } })
              })
            } else {
              resolve({ result: 1, data: res })
            }
          })
          .catch(() => {
            resolve({ result: 0 })
          })
      })
    },
    setValues: (values: any) => {
      console.log('===values:', values)
      //回填前展开表单数据
      const type = getFormType(values) // 'apply' | 'info' | 'psq'
      setFormType(type)
      console.log('====setvalues', values)
      const _values = { ...values[type.toLowerCase()] }
      delete values[type]
      form.setFieldsValue({ ...values, ..._values })
    },
    updateFormValue: (name: any, value: any) => {
      form.setFieldValue(name, value)
    }
  }))
  const [userContext, dispach] = useAppContext()
  const userInfo = React.useMemo(() => {
    const { user, ybUser } = userContext
    return {
      id: user?.id,
      displayName: user?.name,
      manageOrgs: ybUser?.manageOrgs,
      classLeaderAcademy: ybUser?.classLeaderAcademy,
      manageclasses: ybUser?.manageclasses,
      fdy: isFdyUser(ybUser?.currentIdentity),
      departAdmin: isDepartAdminUser(ybUser?.currentIdentity),
      supper: isSupperUser(ybUser?.currentIdentity),
      xgAdmin: isXgAdminUser(ybUser?.currentIdentity),
      orgs: user.orgs
    }
  }, [userContext])
  const getFormType = useCallback(
    (values = undefined) => {
      if (values) {
        return (
          Object.keys(values)
            .filter((key) => ['apply', 'info', 'psq', 'notice'].indexOf(key) > -1)[0]
            .toLocaleUpperCase() || 'APPLY'
        )
      } else {
        if (location.state?.type) {
          //从路由获取

          return location.state?.type
        }
        if (location.state.template) {
          //从模板上获取
          const { settings = { apply: {} } } = location.state.template
          return (
            Object.keys(settings)
              .filter((key) => ['apply', 'info', 'psq', 'notice'].indexOf(key) > -1)[0]
              .toLocaleUpperCase() || 'APPLY'
          )
        }
      }

      //打开已有表单，从define上获取

      return 'APPLY'
    },
    [location.state]
  )

  const handleFormChange = React.useCallback(
    (changeValues: any, allValues?: any) => {
      console.log('setting change:', changeValues)
      updateSettings(editorDispatch, [formType, changeValues])
    },
    [editorDispatch, formType]
  )
  React.useEffect(() => {
    if (taskInfo) setEnableTask(true)
  }, [taskInfo, settings])
  const taskRef = React.useRef(null)
  type FORMTYPE = 'APPLY' | 'PSQ' | 'INFO' | 'NOTICE'
  const formConfig = useMemo(() => (formType ? FORM_CONFIG[formType as FORMTYPE] : FORM_CONFIG.APPLY), [formType])
  return (
    <Box sx={{ px: 1 }} ref={ref}>
      <StyledForm
        onValuesChange={handleFormChange}
        initialValues={{
          template: 1,
          participantDetail: {},
          daterange: [],
          imgs: [],
          nametype: 'SM',
          isTicket: false,
          type: 'APPLY'
        }}
        form={form}
        mode='card'
      >
        <StyledFormItem required rules={[{ required: true }]} label={formConfig.name.label} name='name'>
          <Input placeholder={formConfig.name.placeholder} />
        </StyledFormItem>
        <StyledFormItem required rules={[{ required: false }]} label={formConfig.desc.label} name='desc'>
          <TextArea autoSize placeholder={formConfig.desc.placeholder} />
        </StyledFormItem>
        {formType !== 'NOTICE' && (
          <StyledFormItem hidden className='border' name='imgs' label={formConfig.imgs.label}>
            <ImageUploader
              imageUrlPrefix={isLocalSource(form.getFieldValue('imgs')?.[0]) ? '' : undefined}
              noFormItem
              maxCount={1}
            />
          </StyledFormItem>
        )}

        {usedBy !== USED_BY_PLATEFORM.TASK && (
          <StyledFormItem rules={[{ required: false }]} noBorder name='daterange' label={formConfig.daterange.label}>
            <DateRangePicker precision='second' />
          </StyledFormItem>
        )}
        {usedBy !== USED_BY_PLATEFORM.TASK && formType == 'APPLY' && (
          <StyledFormItem noBorder name='activitylocation' label={formConfig.activitylocation.label}>
            <TextArea autoSize placeholder={formConfig.activitylocation.placeholder} />
          </StyledFormItem>
        )}
        {usedBy !== USED_BY_PLATEFORM.TASK && (
          <StyledFormItem
            noBorder
            required
            name='participantDetail'
            rules={[
              {
                validator: (_: any, value: any) => {
                  console.log(_, value)
                  if ((value && Object.keys(value)?.length) || enableTask) {
                    return Promise.resolve()
                  }
                  return Promise.reject(new Error('请设置发布范围!'))
                }
              }
            ]}
            label={
              formType == 'INFO' ? (
                <Box sx={{ display: 'flex', alignItems: 'center' }}>
                  <Typography>{formConfig.participantDetail.label}</Typography>
                  <Box sx={{ display: 'flex', alignItems: 'center' }}>
                    <Popover
                      placement='top'
                      content={
                        '设置下派过程，手动或自动往下一层级委派。每一层级可以下钻查看下级层级的完成情况。此设置一经开启不可取消或更改。'
                      }
                    >
                      <Button type='link'>
                        委派模式
                        <QuestionCircleOutlined />
                      </Button>
                    </Popover>
                  </Box>
                  <PcSwitch
                    unCheckedChildren={'关闭'}
                    disabled={taskInfo}
                    checkedChildren={'开启'}
                    checked={enableTask}
                    onChange={(value: boolean) => {
                      setEnableTask(value)
                    }}
                  ></PcSwitch>
                </Box>
              ) : (
                <Box>{formConfig.participantDetail.label}</Box>
              )
            }
          >
            {!enableTask ? (
              <PersonnelPicker2
                orgCode={adminType === ADMIN_TYPE.SUPER ? void 0 : orgs ? orgs[0]?.code : void 0}
                categories={
                  adminType === ADMIN_TYPE.SUPER
                    ? void 0
                    : [
                        { key: 'student', label: '学生' },
                        { key: 'jzg', label: '教职工' }
                      ]
                }
              />
            ) : (
              <StyledFormItem sx={{ pl: 0.5 }}>
                {React.createElement(RemoteComponent, {
                  _remoteInfo: {
                    componentName: 'yiban-frontend-admin.EasyformTask',
                    version: '0.1.0'
                  },
                  task: taskInfo,
                  user: userInfo,
                  ref: taskRef,
                  hiddenCreateButton: true,
                  onCreateTask: (value: any) => {
                    // form1.validateFields().then(() => {
                    //   handleTask(value, form1.getFieldsValue())
                    // })
                    return false
                  }
                })}
              </StyledFormItem>
            )}
          </StyledFormItem>
        )}
        {usedBy !== USED_BY_PLATEFORM.TASK && formType == 'APPLY' && (
          <StyledFormItem label={formConfig.maxParticipantNum.label} name='maxParticipantNum'>
            <Input clearable type='number' min={1} placeholder='' />
          </StyledFormItem>
        )}
        {formType == 'APPLY' && (
          <StyledFormItem
            noBorder
            name='isTicket'
            label={formConfig.isTicket.label}
            layout='horizontal'
            valuePropName='checked'
            childElementPosition='right'
            className='horizontalName'
          >
            <Switch />
          </StyledFormItem>
        )}

        {allowTicket && (
          <StyledFormItem
            rules={[{ required: true }]}
            noBorder
            name='ticketWriteOffUserScopeDetail'
            label={formConfig.ticketWriteOffUserScopeDetail.label}
          >
            <PersonnelPicker2
              categories={[
                { key: 'student', label: '学生' },
                { key: 'jzg', label: '教职工' },
                { key: 'group', label: '组' }
              ]}
              text='请添加验票人员'
              // categories={ParticipantCat}
            />
          </StyledFormItem>
        )}
        {formType == 'PSQ' && (
          <StyledFormItem label={formConfig.nametype.label} name={'nametype'}>
            <Radio.Group>
              <Space>
                {/* <Space direction='vertical'> */}
                <Radio value='SM'>实名</Radio>
                <Radio value='NM'>匿名</Radio>
              </Space>
            </Radio.Group>
          </StyledFormItem>
        )}
        {formType == 'APPLY' && (
          <StyledFormItem label={formConfig.type.label} name={'type'}>
            <Radio.Group>
              <Space>
                {/* <Space direction='vertical'> */}
                <Radio value='APPLY'>报名制</Radio>
                <Radio value='AUDIT'>审核制</Radio>
              </Space>
            </Radio.Group>
          </StyledFormItem>
        )}
        {formType !== 'NOTICE' ? (
          <StyledFormItem label='提交按钮文本' name='btnText'>
            <Input></Input>
          </StyledFormItem>
        ) : (
          ''
        )}
        {formType !== 'NOTICE' ? (
          <StyledFormItem label='提交成功文本' name='successTip'>
            <Input></Input>
          </StyledFormItem>
        ) : (
          ''
        )}
        {/* {formType == 'INFO' ? (
          <StyledFormItem label={'委派模式'}>
            <Switch
              uncheckedText={'关闭'}
              disabled={taskInfo}
              checkedText={'开启'}
              checked={enableTask}
              onChange={(value: boolean) => {
                setEnableTask(value)
              }}
            ></Switch>
          </StyledFormItem>
        ) : (
          ''
        )} */}

        {/* {allowAUDIT == 'AUDIT' && (
          <StyledFormItem rules={[{ required: true }]} noBorder name='auditUserScopeDetail' label='审核人员'>
            <PersonnelPicker2 text='请添加审核人员' />
          </StyledFormItem>
        )} */}
      </StyledForm>
    </Box>
  )
})
