import React, { useRef } from 'react'
import { Box, styled } from '@mui/system'
import { Form } from '@yiban/common-mobile'
import { Button, List, Popup, Toast, Input } from 'antd-mobile'
import { CloseOutline as IconClose } from 'antd-mobile-icons'
import { getComponent } from './helper'
import { useBoolean } from 'ahooks'
import { RenderSetting } from './advanceSetting'
import { useEditorContext } from '@/pages/designer/context'
import { getAllCheckItem } from '@/pages/designer/context/editorUtils'
import { DefaultInitValue } from '@/@define'

interface IProps {
  def?: any
  onClose?: () => void
  values?: any
  settings?: any
  onFinish?: (values: any) => void
  currentIndex?: number
}
const StyledForm = styled(Form)<any>(({ theme }) => ({}))

export default React.forwardRef<HTMLDivElement, IProps>(
  ({ def, onClose, values, settings, onFinish, currentIndex }, ref) => {
    const [{ componentTree }] = useEditorContext()
    const [checkSubject, setCheckSubject] = React.useState<any[]>([])
    const [form] = Form.useForm()
    const [showAdvanceSetting, { toggle: toggleAdvanceSetting }] = useBoolean(false)
    const formRef = useRef(null)
    const [renderSettings, setRenderSettings] = React.useState<any>(void 0)
    const getDefComponentProps = (props: any = {}) => {
      const { value, ...other } = props
      return other
    }
    const properties = React.useMemo(() => {
      return {
        ...def?.properties
      }
    }, [def?.properties])
    const handleSubmit = React.useCallback(() => {
      form.submit()
    }, [form])
    const handleFinish = React.useCallback(
      (values) => {
        // console.log(formRef?.current)
        console.log('==config:', values)
        if (typeof onFinish === 'function') {
          console.log('===finish:', { props: values, settings: { render: renderSettings } })
          onFinish({ props: values, settings: { render: renderSettings } })
        }
      },
      [onFinish, renderSettings]
    )
    const initValue = React.useMemo(() => {
      return {
        ...Object.keys(properties).reduce(
          (p: any, c: string) => (properties[c]?.props?.value ? { ...p, [c]: properties[c]?.props?.value } : { ...p }),
          {}
        ),
        ...values
      }
    }, [properties, values])

    const handleSetRender = React.useCallback(() => {
      const checkItems = getAllCheckItem(componentTree, currentIndex || 0)
      if (checkItems.length) {
        console.log('====check subject:', checkItems)
        setCheckSubject(checkItems)
        toggleAdvanceSetting()
      } else {
        Toast.show({
          content: '此题目之前没有选择题，不能进行显示设置'
        })
      }
    }, [componentTree, currentIndex, toggleAdvanceSetting])
    const handleRenderSettingOk = (v: any) => {
      setRenderSettings(v)
      toggleAdvanceSetting()
    }
    React.useEffect(() => {
      setRenderSettings(settings?.render)
    }, [settings?.render])

    return (
      <>
        <Box className='h-full relative p-1 overflow-auto' sx={{ bgcolor: 'background.default' }} ref={ref}>
          <StyledForm initialValues={initValue} onFinish={handleFinish} form={form} mode='card' ref={formRef}>
            <Form.Header>基础设置</Form.Header>
            {Object.keys(properties).map(
              (key) =>
                React.createElement(getComponent(properties[key]?.component), {
                  key: key,
                  name: key,
                  label: properties[key].label,
                  ...getDefComponentProps(properties[key].props)
                })
              // <Form.Item name={key} key={key} label={properties[key].label} rules={properties[key].rules}>
              //   {React.createElement(getComponent(properties[key]?.component), {
              //     ...getDefComponentProps(properties[key].props),
              //     noFormItem: true
              //   })}
              // </Form.Item>
            )}
          </StyledForm>
          <Box>
            <List header='高级设置' mode='card'>
              <List.Item onClick={handleSetRender} extra={renderSettings ? '已设置' : '未设置'} clickable>
                显示设置
              </List.Item>
            </List>
          </Box>
          <Box className='p-3' sx={{ '&>*': { mb: 2 } }}>
            <Button onClick={handleSubmit} block shape='rounded' color='primary'>
              确认
            </Button>
            <Button block shape='rounded' onClick={onClose}>
              取消
            </Button>
          </Box>
        </Box>
        <Popup destroyOnClose position='right' visible={showAdvanceSetting} bodyStyle={{ width: '100vw' }}>
          <Box className='h-full' sx={{ bgcolor: 'background.default' }}>
            <RenderSetting
              title={values?.label}
              dataset={renderSettings}
              onOk={handleRenderSettingOk}
              checkSubject={checkSubject}
              onClose={toggleAdvanceSetting}
            />
          </Box>
        </Popup>
      </>
    )
  }
)
