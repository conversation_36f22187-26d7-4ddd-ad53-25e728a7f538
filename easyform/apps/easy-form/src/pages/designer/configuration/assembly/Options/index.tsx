import React from 'react'
import { FormItemProps, FormItemWrapper } from '@yiban/common-mobile'
import { Input, Button, Checkbox } from 'antd-mobile'
import { MinusOutline as IconDel, PictureOutline as IconPic, AddCircleOutline as IconAdd } from 'antd-mobile-icons'
import { Box, styled } from '@mui/system'
import { useBoolean } from 'ahooks'
import { Typography } from '@/components'
import ImageUploader from '../ImageUploader'
import produce from 'immer'
import BatchAdd from './BatchAdd'
interface Option {
  label: string
  value: string
  image?: string
  inputAble?: boolean
}

type OptionsProps = {
  value?: Option[]
  onChange?: (value: Option[]) => void
}

const StyledDel = styled('span')({
  color: '#FFF',
  display: 'inline-flex',
  width: 18,
  height: 18,
  flexShrink: 0,
  alignItems: 'center',
  justifyContent: 'center',
  fontSize: 14,
  backgroundColor: 'var(--adm-color-danger)',
  borderRadius: '50%',
  marginRight: 8
})

type ItemProps = {
  onDelete?: () => void
  onChange?: (v: Option) => void
  autoFocus?: boolean
  inputAble?: boolean
} & Option

const Item = ({ label, value, image, inputAble, autoFocus, onDelete, onChange }: ItemProps) => {
  const inputRef = React.useRef<any>()
  const handleChange = (data: Partial<Record<keyof Option, string | boolean>>) => {
    if (typeof onChange === 'function') {
      onChange(Object.assign({ label, value, image, inputAble }, data))
    }
  }
  React.useEffect(() => {
    if (autoFocus) {
      console.log(inputRef)
      inputRef.current.focus()
    }
  }, [autoFocus])
  return (
    <Box
      className='flex items-center'
      sx={{
        mb: 2,
        '&:last-child': {
          mb: 0
        }
      }}
    >
      <StyledDel onClick={onDelete}>
        <IconDel />
      </StyledDel>
      <Input
        ref={inputRef}
        clearable
        onChange={(v) => handleChange({ label: v, value: v })}
        value={label}
        placeholder='输入选项'
      />
      <Checkbox onChange={(v) => handleChange({ inputAble: v })} checked={inputAble} />
      <ImageUploader
        onChange={(v: any) => handleChange({ image: v[0] })}
        value={image ? [image] : []}
        maxCount={1}
        noFormItem
        style={{ '--cell-size': '32px', marginLeft: 8 }}
      >
        <Box
          sx={{
            width: 32,
            height: 32,
            borderRadius: '50%',
            bgcolor: '#f5f5f5',
            display: 'flex',
            justifyContent: 'center',
            alignItems: 'center',
            color: '#999'
          }}
        >
          <IconPic />
        </Box>
      </ImageUploader>
    </Box>
  )
}

const Options = ({ value, onChange }: OptionsProps) => {
  const [openBatch, { toggle: toggleBatch }] = useBoolean(false)
  const handleChange = (newValue: Option[]) => {
    if (typeof onChange === 'function') {
      onChange(newValue)
    }
  }
  const handleAdd = () => {
    handleChange([...(value || []), { label: '', value: '' }])
  }
  const handleDelete = (index: number) => {
    handleChange(
      value
        ? produce(value, (draft) => {
            draft.splice(index, 1)
          })
        : []
    )
  }
  const handleItemChange = (index: number, itemData: Option) => {
    handleChange(
      value
        ? produce(value, (draft) => {
            draft[index] = itemData
          })
        : []
    )
  }
  const handleBatchSubmit = (value: string) => {
    handleChange(
      value.split('\n').map((line) => ({
        label: line,
        value: line
      }))
    )
    toggleBatch()
  }
  return (
    <Box>
      <Typography variant='subtitle2' sx={{ mb: 1.5 }}>
        提示：勾选表示选中该选项时支持自定义输入
      </Typography>
      {value?.map(({ label, value: itemValue, image, inputAble }, i) => (
        <Item
          autoFocus={i === value?.length - 1 && !itemValue}
          onDelete={() => handleDelete(i)}
          onChange={(d) => {
            handleItemChange(i, d)
          }}
          label={label}
          key={i}
          value={itemValue}
          image={image}
          inputAble={inputAble}
        />
      ))}
      <Box className='flex items-center justify-between'>
        <Box onClick={handleAdd} className='flex items-center'>
          <IconAdd style={{ color: 'var(--adm-color-primary)' }} />
          <Typography sx={{ color: 'primary.main', ml: 0.5 }} variant='subtitle2'>
            添加选项
          </Typography>
        </Box>
        <Button
          onClick={toggleBatch}
          style={{ fontSize: '12px', padding: '1px 8px' }}
          size='mini'
          fill='outline'
          color='primary'
          shape='rounded'
        >
          批量添加
        </Button>
      </Box>
      <BatchAdd onSubmit={handleBatchSubmit} onClose={toggleBatch} open={openBatch} />
    </Box>
  )
}

export default function ({ rules, ...other }: FormItemProps) {
  const checkOptions = (rule: any, value: Option[] | undefined) => {
    if (value && value.filter((v) => !v.label).length > 0) {
      console.log('==rule:', rule)
      return Promise.reject(new Error('选项不能为空'))
    }
    return Promise.resolve()
  }
  const _rules = React.useMemo(() => [...(rules || []), { validator: checkOptions }], [rules])
  return (
    <FormItemWrapper validateTrigger='onChange' rules={_rules} {...other}>
      <Options />
    </FormItemWrapper>
  )
}
