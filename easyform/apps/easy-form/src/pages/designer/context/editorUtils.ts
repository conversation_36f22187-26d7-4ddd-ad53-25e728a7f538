const findTargetNode = (tree: any[], targetId: string) => {
  const index = tree.findIndex((n) => n._id === targetId)
  return index === -1 ? [null, -1] : [tree[index], index]
}
const getAllCheckItem = (tree: any[], endIndex: number) => {
  const result = []
  for (let i = 0; i < endIndex; i++) {
    if (/(.Checkbox|.Radio)$/.test(tree[i].componentName)) {
      result.push({ data: tree[i], index: i })
    }
  }
  return result
}
export { findTargetNode, getAllCheckItem }
