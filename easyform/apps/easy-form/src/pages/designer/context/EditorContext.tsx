import React from 'react'
import produce from 'immer'
import { Action_Editor } from './ActionType'
import { getUniqueId } from '@/utils'
import { findTargetNode } from './editorUtils'
import { getComponentName } from '@/helper'
import { cloneNode } from '@/editor-kit/context/editorUtils'
import { PageSettings } from '@/editor-kit/context/EditorContext'
import { THEMESETTINGS } from '@/@define'

interface IState {
  schemaVersion?: string
  componentTree?: any
  currentComponent?: any
  components?: any
  settings?: any
  componentList?: any
  defaultWorkflow?: any
  formType?: any
  taskInfo?: any
  formStatus?: boolean //表单是否发布
  pageSettings?: PageSettings
}

interface IAction {
  type: Action_Editor
  payload?: any
  noAssign?: boolean
}
interface IProvider {
  children?: React.ReactNode
}

const initState: IState = {
  schemaVersion: '1.0.0',
  currentComponent: null,
  componentTree: [],
  components: null,
  taskInfo: null,
  formType: 'APPLY',
  componentList: {
    quick: [],
    list: []
  },
  settings: { pageSettings: THEMESETTINGS.DEFAULT, imgs: [] },
  formStatus: false,
  pageSettings: THEMESETTINGS.DEFAULT
}
type TContext = [IState, React.Dispatch<IAction>]

const reducer = (state: IState, action: IAction) => {
  const { type, payload } = action
  return produce(state, (draft) => {
    switch (type) {
      case Action_Editor.INIT: {
        const { settings, componentTree, defaultWorkflow } = payload
        draft.settings = settings
        draft.componentTree = componentTree
        draft.defaultWorkflow = defaultWorkflow
        break
      }
      case Action_Editor.ADD: {
        const { componentName, props } = payload
        const _id = getUniqueId()
        draft.componentTree.push({ _id: _id, componentName, props: { ...props, name: _id } })
        break
      }
      case Action_Editor.UPDATE_EDITOR_SETTING: {
        draft.pageSettings = action?.noAssign ? { ...payload } : { ...draft.pageSettings, ...payload }
        break
      }
      case Action_Editor.SET_COMPONENTS: {
        draft.componentList = payload
        draft.components = payload.list
          .map((l: any) => l.widgets)
          .flat()
          .reduce((p: any, c: any) => ({ ...p, [getComponentName(c.packageName, c.componentName)]: { ...c } }), {})
        break
      }
      case Action_Editor.UPDATE: {
        const { props, settings } = payload
        if (draft.currentComponent) {
          const [node] = findTargetNode(draft.componentTree, draft.currentComponent._id)
          node.props = { ...node.props, ...props }
          const _settings = { ...node.settings, ...settings }
          node.settings = Object.keys(_settings).length > 0 ? _settings : void 0
          /**
           * 修改当前选中节点
           */
          draft.currentComponent = { ...draft.currentComponent, ...node }
        }

        break
      }
      case Action_Editor.UPDATE_TASK:
        draft.taskInfo = payload
        break
      case Action_Editor.UPDATE_FORM_STATUS:
        draft.formStatus = payload
        break
      case Action_Editor.UPDATE_FORM_TYPE:
        draft.formType = payload
        break
      case Action_Editor.SELECT: {
        const [node, i] = findTargetNode(draft.componentTree, payload)
        if (node?._id === draft.currentComponent?._id) {
          draft.currentComponent = null
        } else {
          draft.currentComponent = { ...node, index: i }
        }
        break
      }
      case Action_Editor.COMMAND: {
        const { cmd, targetId } = payload
        const [node, index] = findTargetNode(draft.componentTree, targetId)
        if (!node) {
          return
        }
        switch (cmd) {
          case 'UP': {
            const targetIndex = index - 1
            if (targetIndex < 0) {
              return
            }
            draft.componentTree.splice(index, 1)
            draft.componentTree.splice(targetIndex, 0, node)
            draft.currentComponent.index = targetIndex
            break
          }
          case 'DOWN': {
            const targetIndex = index + 1
            if (targetIndex > draft.componentTree.length - 1) {
              return
            }
            draft.componentTree.splice(index, 1)
            draft.componentTree.splice(targetIndex, 0, node)
            draft.currentComponent.index = targetIndex
            break
          }
          case 'COPY': {
            draft.componentTree.splice(index, 0, cloneNode(node))
            break
          }
          case 'DELETE': {
            draft.componentTree.splice(index, 1)
            if (draft.currentComponent?._id === targetId) {
              draft.currentComponent = void 0
              return
            }
            break
          }
          default:
        }
        break
      }
      case Action_Editor.UPDATE_SETTINGS: {
        draft.settings = { ...draft.settings, ...payload }
        break
      }
      case Action_Editor.UPDATE_TEMPLATE_SETTING: {
        draft.settings.templateSettings = { ...draft.settings.templateSettings, ...payload }
        break
      }
      default:
    }
  })
}

const Context = React.createContext<TContext>([initState, (action) => action])

const EditorProvider: React.FC<IProvider> = ({ children }) => {
  const _reducer = React.useReducer(reducer, initState)
  return <Context.Provider value={_reducer}>{children}</Context.Provider>
}

const useEditorContext = () => {
  const _context = React.useContext(Context)
  if (_context === undefined) {
    throw new Error('must be used within a EditorProvider')
  }
  return _context
}
const add = (dispatch: React.Dispatch<IAction>, componentName: string, props: any) => {
  dispatch({
    type: Action_Editor.ADD,
    payload: { componentName, props }
  })
}
const update = (dispatch: React.Dispatch<IAction>, data: any) => {
  dispatch({
    type: Action_Editor.UPDATE,
    payload: data
  })
}
const updateSettings = (dispatch: React.Dispatch<IAction>, data: any) => {
  dispatch({
    type: Action_Editor.UPDATE_SETTINGS,
    payload: data
  })
}
const updateTemplateSettings = (dispatch: React.Dispatch<IAction>, data: any) => {
  dispatch({
    type: Action_Editor.UPDATE_TEMPLATE_SETTING,
    payload: data
  })
}
const selectComponent = (dispatch: React.Dispatch<IAction>, _id: string) => {
  dispatch({
    type: Action_Editor.SELECT,
    payload: _id
  })
}
const command = (dispatch: React.Dispatch<IAction>, _id: string, cmd: string) => {
  dispatch({
    type: Action_Editor.COMMAND,
    payload: { targetId: _id, cmd }
  })
}
const updateEditorSettings = (dispatch: React.Dispatch<IAction>, data: any, noAssign?: boolean) => {
  dispatch({
    type: Action_Editor.UPDATE_EDITOR_SETTING,
    payload: data,
    noAssign
  })
}
const setComponents = (dispatch: React.Dispatch<IAction>, componentData: any) => {
  dispatch({
    type: Action_Editor.SET_COMPONENTS,
    payload: componentData
  })
}
const init = (dispatch: React.Dispatch<IAction>, data: any) => {
  dispatch({
    type: Action_Editor.INIT,
    payload: data
  })
}
const updateFormType = (dispatch: React.Dispatch<IAction>, data: any) => {
  dispatch({
    type: Action_Editor.UPDATE_FORM_TYPE,
    payload: data
  })
}
const updateTask = (dispatch: React.Dispatch<IAction>, data: any) => {
  dispatch({
    type: Action_Editor.UPDATE_TASK,
    payload: data
  })
}
const updatestatus = (dispatch: React.Dispatch<IAction>, data: any) => {
  dispatch({
    type: Action_Editor.UPDATE_FORM_STATUS,
    payload: data
  })
}
export {
  useEditorContext,
  EditorProvider,
  selectComponent,
  add,
  command,
  update,
  setComponents,
  updateSettings,
  updateTemplateSettings,
  updateFormType,
  updateTask,
  updatestatus,
  updateEditorSettings,
  init
}
