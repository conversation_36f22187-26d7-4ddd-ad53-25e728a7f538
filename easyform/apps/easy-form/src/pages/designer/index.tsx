import React, { useEffect, useCallback, useState } from 'react'
import { useTheme, styled, Box } from '@mui/system'
import { Image, Swiper, NoticeBar, Button, Toast, Dialog, Popup, Tabs, TabBar, TabBarItemProps } from 'antd-mobile'
import { SwiperRef } from 'antd-mobile/es/components/swiper'
import {
  EditorProvider,
  useEditorContext,
  updateSettings,
  init,
  updateTask,
  setComponents,
  updatestatus,
  updateFormType,
  updateEditorSettings
} from '@/pages/designer/context'
import { Typography, Back, Stepper, TabPanel } from '@/components'
import Theme from './theme'
import Editor from './editor'
import Settings from './settings'
import Preview from './preview'
import Workflow from './workflow'
import { useEasyFormRequest, useBaseRequest } from '@/hooks'
import { saveActivity, getActivity, getTemplateDefine, toggleActivityEnable, getActivityList } from '@/api/easyform'
import { saveActivity as saveYbActivity } from '@/api'
import { Button as FloatButton } from 'antd'

import { useLocation, useNavigate } from 'react-router-dom'
import { getDependencies } from '@/helper'
import { transformParticipant } from './helper'
import { getUrlParam } from '@/utils'
import { THEME, THEMESETTINGS, USED_BY_PLATEFORM, themetype } from '@/@define'
import { getTaskInfo } from '@/api/task'
import { EditOutlined, SaveOutlined, SettingOutlined, SkinFilled, SkinOutlined, TeamOutlined } from '@ant-design/icons'
import { MobileTemList } from '@/editor-kit/pc/components/Tools/Panels/PanelSetting'
import PageSettings from './editor/components/PageSettings'
import { EditSOutline, FileOutline, SetOutline, TeamOutline } from 'antd-mobile-icons'

const Header = styled('div')({
  position: 'relative',
  // height: 80,
  backgroundImage: 'url(/easyform/assets/header-bg.png)',
  backgroundSize: 'cover',
  backgroundPosition: 'top'
})
const ContentWrapper = styled('div')({
  transform: 'translateY(-32px)',
  height: 'calc(100vh - 153px)'
})
const RoundContainer = styled('div')({
  borderTopLeftRadius: 24,
  borderTopRightRadius: 24,
  paddingTop: 8,
  overflowX: 'hidden'
})
type TStep = {
  id: string | number
  title: string
  component: any
  hide?: boolean
  [key: string]: any
}

const allSteps: TabBarItemProps & TStep[] = [
  {
    id: 'SETTING',
    title: '设置',
    component: Settings,
    icon: <SettingOutlined />
  },
  {
    id: 'THEME',
    title: '主题',
    component: Theme,
    icon: <SkinOutlined />
  },
  {
    id: 'DESIGN',
    title: '设计',
    component: Editor,
    icon: <EditOutlined />
  },
  {
    id: 'WORKFLOW',
    title: '流程',
    component: Workflow,
    icon: <TeamOutlined />
  },
  {
    id: 'SAVE',
    title: '保存',
    component: Preview,
    icon: <SaveOutlined />
  }
]

const Designer = React.forwardRef(function Designer(props, ref) {
  const { state }: any = useLocation()
  const navigate = useNavigate()
  const [
    { componentTree: contextComponentTree, components, settings, formStatus, pageSettings, formType },
    editorDispatch
  ] = useEditorContext()
  const [instances] = React.useState<any>({
    SETTING: React.createRef(),
    PREVIEW: React.createRef(),
    WORKFLOW: React.createRef()
  })
  const swiperRef = React.useRef<SwiperRef>(null)
  const [currentStep, setCurrentStep] = React.useState(state?.pageTo || 0)
  const [activityId] = React.useState(getUrlParam('activityId') || state?.id)
  const [templateId] = React.useState(getUrlParam('templateId') || state?.templateId)
  /**
   * 通过参数判断是否来自任务
   */
  const [usedBy] = React.useState(state?.usedBy || getUrlParam('usedBy'))
  const { runAsync: publishRun } = useEasyFormRequest(toggleActivityEnable, {
    manual: true
  })
  // const isUsedByTask = React.useMemo(() => usedBy === 'task', [usedBy])

  // const isUsedByTask = React.useMemo(() => {
  //   try {
  //     return Boolean((window.parent as any).YB)
  //   } catch (error) {
  //     return false
  //   }
  // }, [])

  // const { loading: saveLoading, run: runSave } = useEasyFormRequest(saveActivity, {
  //   manual: true,
  //   onSuccess: (res: any) => {
  //     Toast.show({
  //       icon: 'success',
  //       content: '保存成功'
  //     })
  //     setTimeout(() => {
  //       navigate('/', { state: { tab: 'CREATED' } })
  //     }, 1000)
  //   }
  // })
  const { runAsync: toggleActivity2, loading: toggleLoading2 } = useEasyFormRequest(
    () => toggleActivityEnable(activityId),
    {
      manual: true,
      ready: Boolean(activityId),
      onSuccess: (data: any) => {},
      onError: () => {}
    }
  )
  const { loading: saveLoading, run: runSave } = useEasyFormRequest(saveActivity, {
    manual: true,
    onSuccess: async (res: any, params: any) => {
      console.log(res)
      //保存后，如果当前表单已经发布则重新发布
      // if (formStatus) {
      //   await publishRun(activityId)
      //   await publishRun(activityId)
      //   console.log('重新发布')
      // }
      // if (!params[0].enable && typeof res.data !== 'boolean') {
      //   Dialog.confirm({
      //     content: '是否立即发布?',
      //     onConfirm: async () => {
      //       await publishRun(res.data)
      //       Toast.show({
      //         icon: 'success',
      //         content: '发布成功'
      //       })
      //       setTimeout(() => {
      //         if (window.self !== window.top) {
      //           window.parent.postMessage('CLOSE', '*')
      //         } else {
      //           navigate('/', { state: { tab: 'CREATED' } })
      //         }
      //       }, 1000)
      //     }
      //   })
      // } else {
      //   Toast.show({
      //     icon: 'success',
      //     content: '保存成功'
      //   })
      //   setTimeout(() => {
      //     if (window.self !== window.top) {
      //       window.parent.postMessage('CLOSE', '*')
      //     } else {
      //       navigate('/', { state: { tab: 'CREATED' } })
      //     }
      //   }, 1000)
      // }
      if (!formStatus) {
        Dialog.confirm({
          content: '保存成功，是否立即发布？',
          title: '提示',
          confirmText: '立即发布',
          cancelText: '继续编辑',
          onConfirm: async () => {
            await toggleActivity2()
            Toast.show({
              icon: 'success',
              content: '发布成功！'
            })
          }
        })
      } else {
        Toast.show({
          icon: 'success',
          content: '保存成功！'
        })
      }
    }
  })

  const { loading } = useEasyFormRequest(() => getActivity(activityId), {
    ready: Boolean(activityId),
    onSuccess: (res: any) => {
      const {
        page: { componentTree },
        workflow,
        settings
      } = res.data
      initData(componentTree, settings, workflow)
    }
  })
  const {
    runAsync: getActivityStatus,
    data: activityStatus,
    loading: activityLoading,
    refreshAsync: refreshActivity
  } = useEasyFormRequest(() => getActivityList({ id: activityId }), {
    //manual: true,
    ready: Boolean(activityId),
    refreshDeps: [],
    onSuccess: (data: any) => {
      //dispach({ type: 'updateStatus', payload: { status: data.data[0] } })
      //该表单已发布
      // if (data.data[0]?.enable) {
      //   Dialog.confirm({
      //     content: (
      //       <Box>
      //         <Typography sx={{ textAlign: 'center', fontWeight: 'bold', py: 1 }}>提示</Typography>
      //         <Box>修改问卷时，问卷会终止发布，修改完后再次发布问卷可继续收集答卷，问卷链接不会改变。</Box>
      //       </Box>
      //     ),
      //     onConfirm: () => {
      //       publishRun(activityId)
      //     },
      //     onCancel: () => {
      //       window.history.back()
      //     }
      //   })
      // }
      if (data.data[0]?.taskId) {
        const tskId = data.data[0]?.taskId
        getTaskData(tskId)
      }
      updatestatus(editorDispatch, data?.data[0]?.enable == 1 ? true : false)
    }
  })
  const { runAsync: getTaskData } = useBaseRequest((id: any) => getTaskInfo(id), {
    manual: true,
    onSuccess: (res: any) => {
      updateTask(editorDispatch, res)
    }
  })
  const { loading: templateLoading } = useEasyFormRequest(() => getTemplateDefine(templateId), {
    ready: Boolean(templateId),
    onSuccess: (res: any) => {
      const {
        page: { componentTree },
        workflow,
        settings
      } = res.data
      initData(componentTree, settings, workflow)
    }
  })

  useEffect(() => {
    //如果配置了委派且任务未发布则回填settings中的数据
    if (settings && settings.info && settings.info.taskProcess && settings.info.taskProcess.length) {
      const task = settings.info.taskProcess
      if (activityStatus && activityStatus.data[0] && !activityStatus.data[0].taskId) {
        updateTask(editorDispatch, { process: task })
      }
    }
  }, [activityStatus, settings, editorDispatch])
  const steps = React.useMemo(() => {
    if (formType == 'NOTICE') {
      //通知公告表单不需要配置流程
      const noticeSteps = allSteps.filter((a, b) => b != 3)
      noticeSteps[0].title = '编辑公告'
      return noticeSteps
    }
    return allSteps
  }, [formType])
  //设计器页面设置主题后，需要将主题的cover地址同步到settings字段
  useEffect(() => {
    instances?.SETTING?.current?.updateFormValue('imgs', settings?.imgs)
  }, [editorDispatch, instances?.SETTING, settings?.imgs])
  const handleStepChange = React.useCallback((s) => {
    setCurrentStep(s)
    swiperRef.current?.swipeTo(s)
  }, [])

  const nextStep = React.useMemo(() => steps[currentStep + 1], [currentStep, steps])
  const prevStep = React.useMemo(() => steps[currentStep - 1], [currentStep, steps])
  const getFormType = useCallback(() => {
    if (state?.type) {
      //从路由获取类型
      return state?.type
    }
    if (state?.template) {
      //从模板上获取
      const { settings = { apply: {} } } = state.template
      return (
        Object.keys(settings)
          .filter((key) => ['apply', 'info', 'psq', 'notice'].indexOf(key) > -1)[0]
          ?.toLocaleUpperCase() || 'APPLY'
      )
    }
    //已有表单从settings上获取
    if (settings && Object.keys(settings).length) {
      return (
        Object.keys(settings)
          .filter((key) => ['apply', 'info', 'psq', 'notice'].indexOf(key) > -1)[0]
          ?.toLocaleUpperCase() || 'APPLY'
      )
    }
    return 'APPLY'
  }, [state, settings])
  const handleSave = React.useCallback(
    async (pageDefine: any, callbackFn?: any) => {
      const formSettings = await instances.SETTING.current.submit()
      if (formSettings.result === 0) {
        handleStepChange(0)
        Toast.show({
          content: '请检查活动设置',
          icon: 'fail'
        })
        return
      }
      // const templateSettings = await instances.PREVIEW.current?.submit()
      const templateSettings = {
        data: { btnText: formSettings?.data?.btnText, successTip: formSettings?.data?.successTip }
      }
      const workflowSettings = await instances.WORKFLOW.current?.submit()
      if (workflowSettings && workflowSettings.result === 0) {
        handleStepChange(2)
        Toast.show({
          content: '请检查流程配置',
          icon: 'fail'
        })
        return
      }
      const taskProcess = formSettings.data?.taskProcess
      delete formSettings.data?.taskProcess
      const participant = transformParticipant(formSettings.data.participantDetail)
      //开启了签到
      const ticketWriteOffUserScope = formSettings.data?.isTicket
        ? transformParticipant(formSettings.data?.ticketWriteOffUserScopeDetail)
        : void 0
      //审核制
      const auditUserScope =
        formSettings.data?.type == 'AUDIT' ? transformParticipant(formSettings.data?.auditUserScopeDetail) : void 0
      let define: any = {
        settings: {
          name: formSettings.data.name,
          desc: formSettings.data.desc,
          imgs: formSettings.data.imgs,
          daterange: formSettings.data.daterange,
          type: getFormType(),
          participant: participant,
          participantDetail: formSettings.data.participantDetail,
          apply: {
            ticketWriteOffUserScope: ticketWriteOffUserScope,
            ticketWriteOffUserScopeDetail: formSettings.data.ticketWriteOffUserScopeDetail,
            isTicket: formSettings.data.isTicket,
            type: formSettings.data?.type || 'APPLY',
            auditUserScope: auditUserScope,
            applylimit: formSettings.data.maxParticipantNum > 0,
            auditUserScopeDetail: formSettings.data.auditUserScopeDetail,
            maxParticipantNum: formSettings.data.maxParticipantNum
              ? Number(formSettings.data.maxParticipantNum)
              : void 0
          },
          pageSettings,
          startDateTime: Array.isArray(formSettings.data?.daterange) ? formSettings.data.daterange[0] : '',
          endDateTime: Array.isArray(formSettings.data?.daterange) ? formSettings.data.daterange[1] : '',
          templateSettings: templateSettings?.data || {}
        },
        page: {
          componentTree: pageDefine,
          remoteDependencies: getDependencies(pageDefine, components)
        },
        workflow: workflowSettings?.data || {}
      }
      switch (getFormType()) {
        case 'APPLY':
          define = {
            settings: {
              name: formSettings.data.name,
              desc: formSettings.data.desc,
              imgs: formSettings.data.imgs,
              daterange: formSettings.data.daterange,
              type: getFormType(),
              participant: participant,
              participantDetail: formSettings.data.participantDetail,
              pageSettings,
              apply: {
                ticketWriteOffUserScope: ticketWriteOffUserScope,
                ticketWriteOffUserScopeDetail: formSettings.data.ticketWriteOffUserScopeDetail,
                isTicket: formSettings.data.isTicket,
                type: formSettings.data?.type || 'APPLY',
                auditUserScope: auditUserScope,
                applylimit: formSettings.data.maxParticipantNum > 0,
                auditUserScopeDetail: formSettings.data.auditUserScopeDetail,
                maxParticipantNum: formSettings.data.maxParticipantNum
                  ? Number(formSettings.data.maxParticipantNum)
                  : void 0
              },
              startDateTime: Array.isArray(formSettings.data?.daterange) ? formSettings.data.daterange[0] : '',
              endDateTime: Array.isArray(formSettings.data?.daterange) ? formSettings.data.daterange[1] : '',
              templateSettings: templateSettings?.data || {}
            },
            page: {
              componentTree: pageDefine,
              remoteDependencies: getDependencies(pageDefine, components)
            },
            workflow: workflowSettings?.data || {}
          }
          break
        case 'INFO':
          define = {
            settings: {
              name: formSettings.data.name,
              desc: formSettings.data.desc,
              imgs: formSettings.data.imgs,
              daterange: formSettings.data.daterange,
              type: getFormType(),
              participant: participant,
              participantDetail: formSettings.data.participantDetail,
              pageSettings,
              info: {
                //nametype: formSettings.data.nametype
                taskProcess
              },
              startDateTime: Array.isArray(formSettings.data?.daterange) ? formSettings.data.daterange[0] : '',
              endDateTime: Array.isArray(formSettings.data?.daterange) ? formSettings.data.daterange[1] : '',
              templateSettings: templateSettings?.data || {}
            },
            page: {
              componentTree: pageDefine,
              remoteDependencies: getDependencies(pageDefine, components)
            },
            workflow: workflowSettings?.data || {}
          }
          break
        case 'PSQ':
          define = {
            settings: {
              name: formSettings.data.name,
              desc: formSettings.data.desc,
              imgs: formSettings.data.imgs,
              daterange: formSettings.data.daterange,
              type: getFormType(),
              participant: participant,
              participantDetail: formSettings.data.participantDetail,
              pageSettings,
              psq: {
                nametype: formSettings.data.nametype
              },
              startDateTime: Array.isArray(formSettings.data?.daterange) ? formSettings.data.daterange[0] : '',
              endDateTime: Array.isArray(formSettings.data?.daterange) ? formSettings.data.daterange[1] : '',
              templateSettings: templateSettings?.data || {}
            },
            page: {
              componentTree: pageDefine,
              remoteDependencies: getDependencies(pageDefine, components)
            },
            workflow: workflowSettings?.data || {}
          }
          break
        case 'NOTICE':
          define = {
            settings: {
              name: formSettings.data.name,
              desc: formSettings.data.desc,
              imgs: formSettings.data.imgs || [],
              daterange: formSettings.data.daterange,
              type: getFormType(),
              participant: participant,
              notice: {},
              participantDetail: formSettings.data.participantDetail,
              pageSettings,
              startDateTime: Array.isArray(formSettings.data?.daterange) ? formSettings.data.daterange[0] : '',
              endDateTime: Array.isArray(formSettings.data?.daterange) ? formSettings.data.daterange[1] : '',
              templateSettings: templateSettings?.data || {}
            },
            page: {
              componentTree: pageDefine,
              remoteDependencies: getDependencies(pageDefine, components)
            },
            workflow: workflowSettings?.data || {}
          }
          break
      }
      if (state?.templateId) {
        define.settings.templateId = state?.templateId
      }
      if (callbackFn) {
        if (typeof callbackFn === 'function') {
          callbackFn(define)
        }
        return
      }
      //
      runSave(
        {
          enable: false, //任务派发的表单在创建后发布
          define: define
        },
        activityId
      )

      // runSave(
      //   {
      //     payload: JSON.stringify(define)
      //   },
      //   activityId
      // )
    },
    [
      instances.SETTING,
      instances.WORKFLOW,
      getFormType,
      pageSettings,
      components,
      state?.templateId,
      runSave,
      activityId,
      handleStepChange
    ]
  )
  const initData = React.useCallback(
    (_componentTree, _settings, _workflow) => {
      const { templateSettings, pageSettings, ...baseSettings } = _settings
      instances.SETTING.current.setValues({ ...baseSettings, ...templateSettings })
      init(editorDispatch, { settings: _settings, componentTree: _componentTree, defaultWorkflow: _workflow })
      updateEditorSettings(editorDispatch, _settings?.pageSettings)
    },
    [editorDispatch, instances.SETTING]
  )
  React.useEffect(() => {
    updateFormType(editorDispatch, getFormType())
  })
  React.useEffect(() => {
    if (state?.template) {
      const {
        page: { componentTree },
        settings,
        workflow
      } = state.template
      initData(componentTree, settings, workflow)
    }
  }, [initData, state?.template])
  React.useEffect(() => {
    fetch('/easyform/componentList.json').then((res) =>
      res.json().then((comps: any) => {
        setComponents(editorDispatch, comps)
      })
    )
  }, [editorDispatch])

  React.useImperativeHandle(ref, () => ({
    submitActivity: async (callback: any) => {
      handleSave(contextComponentTree, callback)
    }
  }))
  const [openThemePop, setOpenThemePop] = useState(false)
  const handleTemChange = (value?: themetype) => {
    updateEditorSettings(
      editorDispatch,
      value && value.length
        ? {
            theme: value,
            ...THEMESETTINGS[value]
          }
        : undefined,
      true
    )
    updateSettings(editorDispatch, value && value.length && { imgs: THEMESETTINGS[value]?.cover?.value })
  }
  return (
    <Box className='h-full' sx={{ bgcolor: '#FFF', overflow: 'auto' }}>
      {/* {formStatus ? <NoticeBar content='当前表单已发布，若要修改请先取消发布' color='alert' closeable /> : ''} */}
      <Header sx={{ px: 2, pt: 2, pb: 6, height: 24, boxSizing: 'content-box' }}>
        {/* <Stepper onChange={handleStepChange} activeStep={currentStep} steps={steps} /> */}
        <Typography align={'center'} sx={{ fontWeight: 'bold', color: 'white' }}>
          设计表单
        </Typography>
      </Header>
      <ContentWrapper>
        <RoundContainer sx={{ background: '#FFF', height: '100%' }}>
          <Swiper
            allowTouchMove={false}
            direction='horizontal'
            indicator={() => null}
            ref={swiperRef}
            defaultIndex={currentStep}
            onIndexChange={(index) => {
              setCurrentStep(index)
            }}
          >
            {steps.map((s, i) => (
              <Swiper.Item key={i}>
                <TabPanel value={i} isPreRender={true} showValue={currentStep}>
                  {s.component &&
                    React.createElement(s.component, {
                      ref: instances[s.id],
                      onSave: handleSave,
                      usedBy,
                      saveLoading: saveLoading,
                      editMode: Boolean(activityId)
                    })}
                </TabPanel>
              </Swiper.Item>
            ))}
          </Swiper>
        </RoundContainer>
        {/* <Box className='flex justify-between' sx={{ p: 2, mt: 3 }}>
          {prevStep && nextStep && (
            <Button
              style={{ maxWidth: nextStep ? '45%' : void 0 }}
              onClick={() => handleStepChange(currentStep - 1)}
              className='flex-grow shrink-0'
              color='primary'
              fill='outline'
            >
              上一步 {prevStep.title}
            </Button>
          )}
          {nextStep && (
            <Button
              style={{ maxWidth: prevStep ? '45%' : void 0 }}
              onClick={() => handleStepChange(currentStep + 1)}
              className='flex-grow shrink-0'
              color='primary'
            >
              下一步 {nextStep.title}
            </Button>
          )}
        </Box> */}
      </ContentWrapper>
      <Box
        sx={{
          position: 'absolute',
          width: '100%',
          boxShadow: '0 0 8px #eee',
          '.adm-tab-bar-wrap': {
            minHeight: 65
          },
          '.adm-tab-bar-item-title': {
            fontSize: 12
          }
        }}
      >
        <TabBar
          activeKey={currentStep + ''}
          onChange={(tabValue: string) => {
            if (tabValue == '4') {
              handleSave(contextComponentTree)
              return
            }
            handleStepChange(parseInt(tabValue))
          }}
        >
          {allSteps.map((item, index) => (
            <TabBar.Item key={index} icon={item.icon} title={item.title} />
          ))}
        </TabBar>
      </Box>
      {/* {currentStep == 1 ? (
        <FloatButton
          type='text'
          onClick={() => {
            setOpenThemePop(true)
          }}
          style={{
            position: 'absolute',
            right: 0,
            top: '50vh',
            borderRadius: 0,
            zIndex: 9,
            borderTopLeftRadius: 18,
            borderBottomLeftRadius: 18,
            width: 45,
            background: 'white',
            height: 35,
            boxShadow: '-2px 0 5px rgba(196,197,204,.5)'
          }}
          icon={<SkinFilled style={{ color: '#1ea0fa' }} />}
        />
      ) : (
        ''
      )} */}
      {/* <Popup
        visible={openThemePop}
        onMaskClick={() => {
          setOpenThemePop(false)
        }}
        onClose={() => {
          setOpenThemePop(false)
        }}
        position='right'
        bodyStyle={{ width: 221 }}
      >
        <Tabs>
          <Tabs.Tab title='主题' key='themes'>
            <MobileTemList
              onChange={handleTemChange}
              value={pageSettings?.theme || 'DEFAULT'}
              options={THEME}
            ></MobileTemList>
          </Tabs.Tab>
          <Tabs.Tab title='外观' key='page'>
            <PageSettings></PageSettings>
          </Tabs.Tab>
        </Tabs>
      </Popup> */}
    </Box>
  )
})

export default function () {
  const ref = React.useRef<any>()
  React.useEffect(() => {
    if ((window as any).EASY_FORM) {
      ;(window as any).EASY_FORM.submitActivity = (callbackFn: any) => {
        ref.current?.submitActivity(callbackFn)
      }
    }

    return () => {
      if ((window as any).EASY_FORM?.submitActivity) {
        ;(window as any).EASY_FORM.submitActivity = void 0
      }
    }
  }, [])
  React.useEffect(() => {
    if (window.self !== window.top) {
      window.parent.postMessage('SHOW_BACK', '*')
    }
  }, [])
  return (
    <EditorProvider>
      <Designer ref={ref} />
    </EditorProvider>
  )
}
