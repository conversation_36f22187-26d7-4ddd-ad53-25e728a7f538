import React, { useCallback, useState } from 'react'
import { But<PERSON>, <PERSON>up, DotLoading, Form, Image, Tabs } from 'antd-mobile'
import { styled, Box } from '@mui/system'
import EditNode from './helper/EditNode'
import { useBoolean, useMemoizedFn } from 'ahooks'
import ComponentList from './components/ComponentList'
import {
  useEditorContext,
  add,
  selectComponent,
  command,
  update,
  setComponents,
  updateEditorSettings
} from '@/pages/designer/context'
import { ConfigPanel } from '@/pages/designer/configuration'
import {
  FillinOutline as IconInput,
  CheckCircleOutline as IconCheckCircle,
  AddOutline as IconAdd,
  MoreOutline as IconMore,
  DownCircleOutline as IconSelect,
  PictureOutline as IconImage,
  StarOutline as IconStar
} from 'antd-mobile-icons'
import { Typography } from '@/components'
import { RemoteComponent, getComponentName, useLoadComponentDef } from '@/helper'
import { DefaultInitValueMap, THEME, THEMESETTINGS, themetype } from '@/@define'
import { useLocation } from 'react-router-dom'
import { But<PERSON> as FloatButton } from 'antd'
import { SkinFilled } from '@ant-design/icons'
import FormTemplate from '@/@template/FormTemplate'
import { MobileTemList, TemList } from '@/editor-kit/pc/components/Tools/Panels/PanelSetting'

const AddButton = styled(Button)({
  width: '100%'
})
const _INDEX = 1

const Header = styled('div')({
  height: '15vh',
  background: 'linear-gradient(to right, rgb(134, 239, 172), rgb(59, 130, 246), rgb(147, 51, 234))'
})

const StyledForm = styled(Form)({
  '--border-inner': 'none',
  '--border-top': 'none',
  '--border-bottom': 'none',
  '& .adm-list.adm-list': {
    '--padding-left': 0,
    '--padding-right': 0
  }
})

const ContentWrapper = styled('div')({
  // padding: 16,
  paddingTop: 8,
  display: 'flex',
  position: 'relative',
  flexDirection: 'column',
  '&>*': {
    // marginBottom: 8
  }
})
const ComponentWrapper = styled(Box)(({ theme }) => ({
  marginTop: 24,
  display: 'flex',
  alignItems: 'center',
  padding: '8px 8px',
  border: `4px dashed ${theme.palette.divider}`
  // borderRadius: 8
}))
const ComponentItem = styled('div')(({ theme }) => ({
  display: 'flex',
  fontSize: 24,
  color: theme.palette.text.secondary,
  alignItems: 'center',
  flexDirection: 'column',
  justifyContent: 'center'
  // marginRight: 16
}))

const getValue = (v: any, baseData?: any) => {
  const reg = /{{(.*?)}}/
  if (reg.test(v)) {
    const key = v.match(reg)[1]
    return baseData ? baseData[key] : void 0
  }
  return v
}

const getProps = (props: any, baseData?: any) => {
  const _props = Object.entries(props).reduce((p, [k, v]) => {
    return { ...p, [k]: getValue(v, baseData) }
  }, {})
  console.log('===props:', _props, baseData)
  return _props
}

export default function Editor() {
  const [{ componentTree, currentComponent, components, settings, pageSettings, componentList }, editorDispatch] =
    useEditorContext()
  const [openSelectComponent, { toggle: toggleSelectComponent }] = useBoolean(false)
  const [openSetting, { toggle: toggleOpenSetting }] = useBoolean(false)
  const location = useLocation()
  const currentComponentInfo = React.useMemo(
    () =>
      components && currentComponent
        ? [
            components[currentComponent.componentName].packageName,
            components[currentComponent.componentName].componentName,
            components[currentComponent.componentName].version
          ]
        : [],
    [components, currentComponent]
  )
  const [def, { loading: defLoading, loadedError: defLoadedError }] = useLoadComponentDef(...currentComponentInfo)

  React.useEffect(() => {
    console.log('===当前组件定义:', def)
  }, [def])

  const [willAdd, setWillAdd] = React.useState<any>(null)

  const handleFinishSetting = useMemoizedFn((propsAndSettings) => {
    console.log('==finish settings:', propsAndSettings)
    if (willAdd) {
      add(editorDispatch, willAdd.componentName, propsAndSettings)
    } else {
      update(editorDispatch, propsAndSettings)
      // edit componentProps
    }
    toggleOpenSetting()
  })

  const handleSelect = React.useCallback(
    (_id: string) => {
      selectComponent(editorDispatch, _id)
    },
    [editorDispatch]
  )
  const handleCommand = React.useCallback(
    (_id: string, cmd: string) => {
      console.log('==command:', cmd, _id)
      switch (cmd) {
        case 'EDIT': {
          toggleOpenSetting()
          break
        }
        default: {
          command(editorDispatch, _id, cmd)
        }
      }
    },
    [editorDispatch, toggleOpenSetting]
  )

  const handleAddItem = React.useCallback(
    (componentData: any) => {
      console.log('==componentData:', componentData)
      add(editorDispatch, getComponentName(componentData.packageName, componentData.componentName), {
        ...componentData?.meta?.defaultProps,
        label: '标题'
      })
    },
    [editorDispatch]
  )
  const handleAddComponent = React.useCallback(
    (d: any) => {
      handleAddItem(d)
      toggleSelectComponent()
    },
    [handleAddItem, toggleSelectComponent]
  )

  const initSettingValues = React.useMemo(
    () => (willAdd ? willAdd.defaultProps : currentComponent?.props),
    [currentComponent, willAdd]
  )
  const lastIndex = React.useMemo(() => componentTree.length - 1, [componentTree.length])
  const handleClick = () => {
    toggleSelectComponent()
  }
  const { state }: any = useLocation()

  const getFormType = useCallback(() => {
    if (state?.type) {
      //从路由获取类型
      return state?.type
    }
    if (state?.template) {
      //从模板上获取
      const { settings = { apply: {} } } = state.template
      return (
        Object.keys(settings)
          .filter((key) => ['apply', 'info', 'psq', 'notice'].indexOf(key) > -1)[0]
          ?.toLocaleUpperCase() || 'APPLY'
      )
    }
    //已有表单从settings上获取
    if (settings) {
      return (
        Object.keys(settings)
          .filter((key) => ['apply', 'info', 'psq', 'notice'].indexOf(key) > -1)[0]
          ?.toLocaleUpperCase() || 'APPLY'
      )
    }
    return 'APPLY'
  }, [state, settings])

  console.log('====components:', components)
  return (
    <>
      <Box sx={{ px: 2, position: 'relative', height: 'calc(100vh - 161px)' }}>
        <FormTemplate
          isPC={false}
          height={'calc(100vh - 252px)'}
          imageUrlPrefix={(window as any).APP_CONFIG.server.fileUrlPrefix}
          btnText={settings.templateSettings?.btnText}
          settings={settings}
          hideSubmit={(location?.state as any)?.type == 'NOTICE'}
          pageSettings={pageSettings || settings?.pageSettings}
        >
          <StyledForm>
            <ContentWrapper>
              {components ? (
                <>
                  {componentTree.length === 0 ? (
                    <Typography sx={{ color: 'text.hint', mt: 1 }} align='center'>
                      请添加表单项创建表单
                    </Typography>
                  ) : (
                    componentTree.map(({ componentName, _id, props }: any, i: number) => (
                      <EditNode
                        componentName={components[componentName].name}
                        allowUp={i !== 0}
                        allowDown={i !== lastIndex}
                        onCommand={(cmd: string) => handleCommand(_id, cmd)}
                        selected={currentComponent?._id === _id}
                        key={_id}
                        _index={i}
                        // component={React.createElement(Assembly.Radio, { ...props })}
                        component={React.createElement(RemoteComponent, {
                          _remoteInfo: {
                            version: components[componentName]?.version,
                            componentName: componentName
                          },
                          ...getProps(props, DefaultInitValueMap)
                        })}
                        // component={(Assembly as any)[componentName]}
                        onClick={() => handleSelect(_id)}
                      />
                    ))
                  )}
                </>
              ) : (
                <div>
                  <DotLoading />
                </div>
              )}
            </ContentWrapper>
          </StyledForm>
        </FormTemplate>
        {getFormType() !== 'NOTICE' ? (
          <ComponentWrapper>
            <Typography sx={{ color: 'primary.main', fontWeight: 'bold', mr: 2, fontSize: 14 }}>添加表单项</Typography>
            <Box className='flex items-center flex-grow justify-between'>
              {componentList.quick?.map((c: any) => (
                <ComponentItem
                  onClick={() => {
                    handleAddItem(c)
                  }}
                  key={c.name}
                >
                  <Image width={24} height={24} src={c.icon} />

                  <Typography sx={{ fontSize: 12 }}>{c.name}</Typography>
                </ComponentItem>
              ))}
            </Box>
            <Box sx={{ color: 'text.hint', fontSize: 24, ml: 1 }} onClick={handleClick}>
              <IconMore />
            </Box>
          </ComponentWrapper>
        ) : (
          <ComponentWrapper>
            <Typography sx={{ color: 'primary.main', fontWeight: 'bold', mr: 2, fontSize: 14 }}>添加组件项</Typography>
            <Box className='flex items-center flex-grow flex-start'>
              {componentList?.list[1]?.widgets?.map((c: any) => (
                <ComponentItem
                  sx={{ mr: 2 }}
                  onClick={() => {
                    handleAddItem(c)
                  }}
                  key={c.name}
                >
                  <Image width={24} height={24} src={c.icon} />

                  <Typography sx={{ fontSize: 12 }}>{c.name}</Typography>
                </ComponentItem>
              ))}
            </Box>
          </ComponentWrapper>
        )}
      </Box>
      <Popup
        visible={openSelectComponent}
        onMaskClick={toggleSelectComponent}
        bodyStyle={{
          borderTopLeftRadius: '8px',
          borderTopRightRadius: '8px',
          height: '90vh'
        }}
      >
        <div className='p-4 h-full flex flex-col pb-12'>
          <p className='text-center font-medium mb-4'>选择要填写的信息</p>
          <div className='flex-grow'>
            <ComponentList componentList={componentList.list} onClick={handleAddComponent} />
          </div>
          <Button shape='rounded' onClick={toggleSelectComponent}>
            返回
          </Button>
        </div>
      </Popup>
      <Popup
        destroyOnClose
        position='right'
        visible={openSetting}
        onMaskClick={toggleOpenSetting}
        bodyStyle={{ width: '100vw', height: '100vh', overflow: 'auto' }}
      >
        <ConfigPanel
          currentIndex={currentComponent?.index}
          def={def}
          values={initSettingValues}
          settings={currentComponent?.settings}
          onFinish={handleFinishSetting}
          onClose={toggleOpenSetting}
        />
      </Popup>
    </>
  )
}
