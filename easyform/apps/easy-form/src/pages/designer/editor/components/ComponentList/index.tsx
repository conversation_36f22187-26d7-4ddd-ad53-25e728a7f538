import React from 'react'
import { styled } from '@mui/system'
import {
  AaOutline as IconInput,
  SmileOutline as IconRadio,
  CheckShieldOutline as IconCheckShield,
  PictureOutline as IconPicture,
  StarOutline as IconStar
} from 'antd-mobile-icons'
import { Button, Image } from 'antd-mobile'

const GridWrapper = styled('div')({
  display: 'grid',
  gridTemplateColumns: 'repeat(4,minmax(80px,60px))',
  gap: '12px'
})
const Item = styled('div')({
  display: 'flex',
  flexDirection: 'column',
  gap: 8,
  alignItems: 'center',
  justifyContent: 'center',
  height: 80,
  cursor: 'pointer',
  transition: 'background-color .35s ease-in-out',

  '&:hover': {
    backgroundColor: '#eee'
  },
  '& >svg': {
    width: '20px',
    height: '20px'
  },
  '& >span': {
    fontSize: '14px'
  }
})

interface IProps {
  onClick?: (d: any) => void
  componentList?: any[]
}

export default function ComponentList({ onClick, componentList }: IProps) {
  const handleClick = (d: any) => {
    if (typeof onClick === 'function') {
      onClick(d)
    }
  }
  const list = React.useMemo(() => componentList?.map((c: any) => c.widgets).flat(), [componentList])
  return (
    <div className='flex flex-col'>
      <GridWrapper className='flex-grow'>
        {list?.map((l, i) => (
          <Item key={i} onClick={() => handleClick(l)}>
            <Image width={24} height={24} src={l.icon} />
            <span>{l.name}</span>
          </Item>
        ))}
      </GridWrapper>
    </div>
  )
}
