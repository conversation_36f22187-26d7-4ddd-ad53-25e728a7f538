import { THEMECOLORS } from '@/@define'
import BkgList from '@/editor-kit/pc/components/FormConfiguration/components/BkgList'
import { updateEditorSettings, useEditorContext } from '@/pages/designer/context'
import { Box } from '@mui/system'
import { Checkbox } from 'antd'
import { CheckList, Radio, Space } from 'antd-mobile'
import React, { useState } from 'react'
export const ThemeColorPicker = ({ value, onChange }: any) => {
  return (
    <Box>
      <Radio.Group
        value={value}
        onChange={(value: any) => {
          onChange && onChange(value)
        }}
      >
        <Box sx={{ display: 'flex', flexWrap: 'wrap' }}>
          {THEMECOLORS.map((color, key) => (
            <Box
              key={key}
              sx={{
                p: 2,
                '.adm-radio-icon': {
                  backgroundColor: `${color}!important`,
                  borderColor: `${color}!important`
                }
              }}
            >
              <Radio
                value={color}
                style={{
                  '--icon-size': '28px',
                  '--font-size': '20px',
                  '--gap': '10px'
                }}
              ></Radio>
            </Box>
          ))}
        </Box>
      </Radio.Group>
    </Box>
  )
}
export default () => {
  const [
    { componentTree: contextComponentTree, components, settings, formStatus, pageSettings, formType },
    editorDispatch
  ] = useEditorContext()
  const handleChangeBKG = (value?: any, isUpload = false, isColor = false) => {
    if (isColor) {
      updateEditorSettings(editorDispatch, {
        background: { color: [value] }
      })
    } else {
      updateEditorSettings(editorDispatch, {
        background: isUpload ? { src: value.length ? value : [] } : { value }
      })
    }
  }
  const handleChangeThemeColor = (color: any) => {
    updateEditorSettings(editorDispatch, {
      themeColor: color,
      titleColor: color,
      descriptionColor: color
    })
  }
  return (
    <>
      <Box
        sx={{
          overflow: 'auto',
          height: 'calc(100vh - 53px)',
          '::-webkit-scrollbar': {
            width: 0
          }
        }}
      >
        <Space direction='vertical' style={{ width: '100%', padding: '8px 0' }}>
          <Box sx={{ fontSize: 16, fontWeight: 600, textAlign: 'center' }}>设置背景</Box>
          <Box sx={{ transform: 'translateX(7px)' }}>
            <BkgList
              width={'19.5vw'}
              height={'27vw'}
              onChange={(record: any, isU: any, isC: any) => {
                handleChangeBKG(record?.value || record?.color || undefined, isU, isC)
              }}
              value={pageSettings?.background?.value?.[0] || pageSettings?.background?.color?.[0]}
            ></BkgList>
          </Box>
          <Box sx={{ pl: 1 }}>
            <Box>
              <Checkbox
                value={!pageSettings?.hideMobileBKG}
                onChange={(e: any) => {
                  const checked = e?.target.checked
                  updateEditorSettings(editorDispatch, { hideMobileBKG: !checked })
                }}
              >
                对移动端应用背景图
              </Checkbox>
            </Box>
          </Box>
        </Space>
        <Space direction='vertical' style={{ width: '100%', padding: '8px 0' }}>
          <Box sx={{ fontSize: 16, fontWeight: 600, textAlign: 'center' }}>设置主题色</Box>
          <Box>
            <ThemeColorPicker
              value={pageSettings?.themeColor || undefined}
              onChange={handleChangeThemeColor}
            ></ThemeColorPicker>
          </Box>
        </Space>
      </Box>
    </>
  )
}
