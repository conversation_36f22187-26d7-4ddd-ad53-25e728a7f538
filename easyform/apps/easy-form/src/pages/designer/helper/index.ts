const map: any = {
  org: 'o',
  posset: 'ps',
  clazz: 'c',
  user: 'u',
  pos: 'p',
  // cat: '自动筛选',
  group: 'g',
  jzg: 'ot',
  student: 'os'
  // expr: 'expr'
}

export function transformParticipant(detail: any) {
  const res: any[] = []
  Object.entries(detail || {}).forEach(([k, v]: any) => {
    // posset:[] 这类格式
    if (Array.isArray(v)) {
      if (v.length > 0) {
        res.push(...v.map((d) => `${map[k] || k}:${d.code}`))
      }
    } else {
      /**
       * student:{org:[],pos:[],posset:[]}
       */
      const { org, ...other } = v
      Object.entries(other).forEach(([ck, cv]: any) => {
        if (Array.isArray(cv) && cv.length > 0) {
          res.push(...cv.map((d) => `${map[ck]}:${d.code}`))
        }
      })
      /**
       * org下xxxx
       */
      if (Array.isArray(org) && org.length > 0) {
        res.push(...org.map((d) => `${map[k] || k}:${d.code}`))
      }
    }
  })
  return res
  // return Object.entries(detail || {}).reduce((p: any, [k, v]: any) => {
  //   return [...p, ...v.map(({ code }: any) => (k === 'cat' || k === 'extra' ? code : `${[map[k] || k]}:${code}`))]
  // }, [])
}
//pc万能表单人员选择器用的转换方法
// export function transformParticipant2(detail: any) {
//   if (detail) {
//     let _detail: any = {}
//     Object.keys(detail).forEach((item) => {
//       if (item == 'student' || item == 'jzg') {
//         _detail = { ..._detail, ...detail[item] }
//       } else {
//         _detail[item] = detail[item]
//       }
//     })
//     return Object.entries(_detail || {}).reduce((p: any, [k, v]: any) => {
//       return [...p, ...v.map(({ code }: any) => (k === 'cat' || k === 'extra' ? code : `${[map[k] || k]}:${code}`))]
//     }, [])
//   }
// }
