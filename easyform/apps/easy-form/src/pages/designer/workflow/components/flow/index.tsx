import React from 'react'
import { Box, styled } from '@mui/system'
import {
  FillinOutline as IconNode,
  UserOutline as IconUser,
  CloseOutline as IconClose,
  AddOutline as IconAdd,
  AddCircleOutline as IconAddOutline,
  DeleteOutline as IconDelete,
  SetOutline as IconSetting
} from 'antd-mobile-icons'
import { Input } from 'antd-mobile'
import { Typography } from '@/components'
import { transformToList, Categories } from '@/components/PersonnelPicker2/util'
import { Dropdown } from 'antd'

const Root = styled(Box)({
  display: 'grid',
  gridTemplateColumns: '1fr 6fr',
  columnGap: 8
})

const Circle = styled(Box)(({ theme }) => ({
  width: 36,
  height: 36,
  backgroundColor: theme.palette.primary.main,
  color: '#FFF',
  display: 'flex',
  alignItems: 'center',
  justifyContent: 'center',
  borderRadius: '50%'
}))
const Line = styled('div')<any>(({ theme }) => ({
  width: 1,
  backgroundColor: theme.palette.primary.main
}))

const NodeContainer = styled(Box)(({ theme }) => ({
  borderRadius: '8px',
  backgroundColor: '#F8F8F8',
  padding: 12,
  flex: '1 1'
}))

const StyledInput = styled(Input)({
  '--font-size': '14px'
})

interface IUserProps {
  id: string
  name: string
  type?: string
  onDelete?: (id: string) => void
}
const UserTag = ({ id, name, onDelete }: IUserProps) => {
  const handleDelete = () => {
    if (typeof onDelete === 'function') {
      onDelete(id)
    }
  }
  return (
    <Box
      className='flex items-center'
      sx={{
        color: 'secondary.main',
        border: 1,
        borderColor: 'secondary.main',
        borderRadius: 1,
        px: 1,
        py: 0.25,
        mx: 0.5,
        my: 0.5
      }}
    >
      <Typography sx={{ fontSize: 12, color: 'inherit', mr: 0.5 }}>{name}</Typography>
      <IconClose onClick={handleDelete} />
    </Box>
  )
}
interface IBtnAddProps {
  onAdd?: () => void
}
const BtnAdd = React.forwardRef(({ onAdd, ...other }: IBtnAddProps, ref) => {
  return (
    <Box
      ref={ref}
      onClick={onAdd}
      className='flex items-center'
      sx={{
        color: 'secondary.main',
        border: 1,
        borderStyle: 'dotted',
        borderColor: 'secondary.main',
        borderRadius: 1,
        px: 1,
        py: 0.25,
        mx: 0.5
      }}
      {...other}
    >
      <IconAdd />
    </Box>
  )
})

interface INodeDisplayProps {
  hideTopLine?: boolean
  hideBottomLine?: boolean
}

interface ITaskNodeProps {
  name?: string
  id?: string
  participants?: Record<string, any>
  onDelete?: () => void
  onDeleteParticipant?: (d: any) => void
  onAddParticipant?: (category?: any) => void
  onNameChange?: (name: string) => void
}

const Node = ({
  hideTopLine,
  hideBottomLine,
  name,
  id,
  participants,
  onDelete,
  onDeleteParticipant,
  onAddParticipant,
  onNameChange
}: ITaskNodeProps & INodeDisplayProps) => {
  const participantArr = React.useMemo<any[]>(() => {
    return participants ? transformToList(participants) : []
  }, [participants])
  const handleDeleteParticipant = (d: any) => {
    if (typeof onDeleteParticipant === 'function') {
      onDeleteParticipant(d)
    }
  }
  return (
    <Root>
      <Box className='flex flex-col items-center justify-center'>
        <Line className='flex-1' sx={{ opacity: hideTopLine ? 0 : 1 }} />
        <Circle>
          <IconNode />
        </Circle>
        <Line className='flex-1' sx={{ opacity: hideBottomLine ? 0 : 1 }} />
      </Box>
      <Box className='flex items-center'>
        <NodeContainer>
          <Box className='flex items-center justify-between'>
            <StyledInput placeholder='请输入' onChange={onNameChange} sx={{ mb: 1 }} value={name} />
            {/* <Typography sx={{ mb: 1, fontWeight: 'bold', fontSize: 14 }}>{name}</Typography> */}
            <Typography onClick={onDelete} className='shrink-0' color='secondary'>
              {/* <IconSetting /> */}
              <IconDelete />
            </Typography>
          </Box>
          <Box sx={{ color: 'secondary.main' }} className='flex items-center flex-wrap'>
            <IconUser style={{ marginRight: 4 }} />
            {participantArr?.map((item) => (
              <UserTag key={item.code} id={item.code} name={item.name} onDelete={() => handleDeleteParticipant(item)} />
            ))}
            <Dropdown
              menu={{ items: [...Categories, { key: 'expr', label: '自动筛选' }], onClick: onAddParticipant }}
              trigger={['click']}
            >
              <BtnAdd />
            </Dropdown>
          </Box>
        </NodeContainer>
      </Box>
    </Root>
  )
}

interface IAddNodeProps {
  onAddNode?: () => void
}
const AddNode = ({ hideTopLine, hideBottomLine, onAddNode }: INodeDisplayProps & IAddNodeProps) => {
  return (
    <Root>
      <Box sx={{ color: 'primary.main' }} className='flex flex-col items-center justify-center'>
        <Line className='flex-1' sx={{ opacity: hideTopLine ? 0 : 1 }} />
        <IconAddOutline onClick={onAddNode} />
        <Line className='flex-1' sx={{ opacity: hideBottomLine ? 0 : 1 }} />
      </Box>
      <Box sx={{ color: 'secondary.main', py: 2 }}>
        <Typography onClick={onAddNode} component='span' sx={{ fontSize: 12 }}>
          增加审批环节
        </Typography>
      </Box>
    </Root>
  )
}

const demoData = [
  {
    id: 'Task_1',
    name: '辅导员审批',
    participants: [
      {
        id: '1',
        name: '张三'
      },
      {
        id: '2',
        name: '张四'
      }
    ]
  },
  {
    id: 'Task_2',
    name: '院系领导审批',
    participants: [
      {
        id: '1',
        name: '李三'
      },
      {
        id: '2',
        name: '赵四'
      }
    ]
  }
]

interface ITaskNode {
  id: string
  name: string
  [key: string]: any
}

interface IFlowProps {
  dataset: ITaskNode[]
  onAddNode?: (targetIndex: number) => void
  onDeleteNode?: (index: number) => void
  onAddParticipant?: (nodeIndex: number, nodeId?: string) => void
  onDeleteParticipant?: (nodeIndex: number, itemData?: any) => void
  onNodeNameChange?: (nodeIndex: number, name: string) => void
}
export default React.forwardRef<any, IFlowProps>(
  ({ dataset, onAddNode, onDeleteNode, onAddParticipant, onDeleteParticipant, onNodeNameChange }, ref) => {
    const lastTaskNodeIndex = React.useMemo(() => dataset.length - 1, [dataset])
    const handleAddNode = (index: number) => {
      if (typeof onAddNode === 'function') {
        onAddNode(index)
      }
    }
    const handleDeleteNode = (index: number) => {
      if (typeof onDeleteNode === 'function') {
        onDeleteNode(index)
      }
    }
    const handleNodeNameChange = (index: number, name: string) => {
      if (typeof onNodeNameChange === 'function') {
        onNodeNameChange(index, name)
      }
    }
    const handleAddParticipant = (index: number, category?: any) => {
      if (typeof onAddParticipant === 'function') {
        onAddParticipant(index, category)
      }
    }
    const handleDeleteParticipant = (index: number, itemData: any) => {
      if (typeof onDeleteParticipant === 'function') {
        onDeleteParticipant(index, itemData)
      }
    }
    return (
      <Box>
        {dataset.map(({ id, name, participantDetail }, i) => (
          <React.Fragment key={id}>
            <Node
              hideTopLine={i == 0}
              onDelete={() => handleDeleteNode(i)}
              id={id}
              name={name}
              participants={participantDetail}
              onNameChange={(newName) => handleNodeNameChange(i, newName)}
              onAddParticipant={(category) => handleAddParticipant(i, category)}
              onDeleteParticipant={(d) => handleDeleteParticipant(i, d)}
            />
            {i !== lastTaskNodeIndex && <AddNode onAddNode={() => handleAddNode(i + 1)} />}
          </React.Fragment>
        ))}
        <AddNode hideTopLine={!dataset.length} hideBottomLine onAddNode={() => handleAddNode(lastTaskNodeIndex + 1)} />
      </Box>
    )
  }
)
