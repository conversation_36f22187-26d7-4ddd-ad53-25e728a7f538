import React from 'react'
import { Box, styled } from '@mui/system'
import { useBoolean } from 'ahooks'
import { Switch } from 'antd-mobile'
import produce from 'immer'

import { Typography, PersonnelPicker2 } from '@/components'

import Flow from './components/flow'
import { useEditorContext } from '@/pages/designer/context'
import { getUniqueId } from '@/utils'
import { transformParticipant } from '../helper'
import { CandidateCat, ADMIN_TYPE } from '@/@define'
import { useAppContext } from '@/context/AppContext'
import { Categories } from '@/components/PersonnelPicker2/util'

function transformWorkflowData(_workflow?: any) {
  const _data = []
  if (_workflow) {
    for (const [k, v] of Object.entries<any>(_workflow)) {
      if (!['start', 'end', 'refuse-end', 'agree-end'].includes(k)) {
        _data.push({ id: k, ...v })
      }
    }
  }
  return _data
}

export default React.forwardRef((props, ref) => {
  const [
    {
      user: { adminType }
    }
  ] = useAppContext()
  const [{ defaultWorkflow }] = useEditorContext()
  const [surpportProcess, { toggle: toggleSupportProcess }] = useBoolean(
    defaultWorkflow && Object.keys(defaultWorkflow).length > 0
  )
  const [workflowData, setWorkflowData] = React.useState<any>(transformWorkflowData(defaultWorkflow))
  const [genNameIndex, setGenNameIndex] = React.useState(1)
  const [currentPersonnelValue, setCurrentPersonnelValue] = React.useState<any>()
  const [currentNodeIndex, setCurrentNodeIndex] = React.useState<number>(0)
  const personnelRef = React.useRef<any>()

  const handleAddNode = (targetIndex: number) => {
    setWorkflowData(
      produce(workflowData, (draft: any) => {
        draft.splice(targetIndex, 0, {
          id: getUniqueId('task_'),
          name: `审批${genNameIndex}`
        })
      })
    )
    setGenNameIndex((prev) => prev + 1)
  }
  const handleDeleteNode = (targetIndex: number) => {
    setWorkflowData(
      produce(workflowData, (draft: any) => {
        draft.splice(targetIndex, 1)
      })
    )
  }
  const handleNodeNameChange = (targetIndex: number, name: string) => {
    setWorkflowData(
      produce(workflowData, (draft: any) => {
        draft[targetIndex].name = name
      })
    )
  }
  const handlePersonnelChange = React.useCallback(
    (v: any, desc?: string) => {
      setWorkflowData(
        produce(workflowData, (draft: any) => {
          draft[currentNodeIndex].participantDetail = v
        })
      )
    },
    [currentNodeIndex, workflowData]
  )
  const handleDeleteParticipant = React.useCallback(
    (targetIndex: number, item?: any) => {
      console.log('===:', item)
      const { type, ...itemData } = item
      setWorkflowData(
        produce(workflowData, (draft: any) => {
          const _target = draft[targetIndex].participantDetail
          if (['jzg', 'student'].includes(type)) {
            const _childType = itemData.nodeType || 'org'
            if (!_target[type]) {
              return
            }
            const _existIndex = _target[type][_childType].findIndex((n: any) => n.code === itemData.code)
            if (_existIndex !== -1) {
              _target[type][_childType].splice(_existIndex, 1)
            }
          } else {
            if (!_target[type]) {
              return
            } else {
              const _existIndex = _target[type].findIndex((n: any) => n.code === itemData.code)
              if (_existIndex !== -1) {
                _target[type].splice(_existIndex, 1)
              }
            }
          }
        })
      )
      // setWorkflowData(
      //   produce(workflowData, (draft: any) => {
      //     const _index = draft[targetIndex].participantDetail[type]?.findIndex((i: any) => i.code === participantId)
      //     if (_index > -1) {
      //       draft[targetIndex].participantDetail[type].splice(_index, 1)
      //     }
      //   })
      // )
    },
    [workflowData]
  )

  const handleAddParticipant = React.useCallback(
    (targetIndex: number, category?: any) => {
      setCurrentNodeIndex(targetIndex)
      setCurrentPersonnelValue(workflowData[targetIndex].participantDetail)
      personnelRef.current.toggle(category)
    },
    [workflowData]
  )

  React.useImperativeHandle(ref, () => ({
    submit: async () => {
      return new Promise((resolve, reject) => {
        if (surpportProcess) {
          if (workflowData.length === 0) {
            resolve({ result: 0, msg: '请配置流程' })
          }
          const _workFlowMap: any = { start: { name: '开始', next: workflowData[0].id } }
          workflowData.forEach(({ id, ...other }: any, i: number) => {
            _workFlowMap[id] = {
              ...other,
              candidates: transformParticipant(other.participantDetail),
              actions: [
                { name: '同意', next: workflowData[i + 1]?.id ?? 'agree-end' },
                { name: '不同意', next: 'refuse-end' },
                { name: '退回', next: 'start' }
              ]
            }
          })
          _workFlowMap['refuse-end'] = { name: '结束' }
          _workFlowMap['agree-end'] = { name: '结束' }
          resolve({ data: _workFlowMap })
        }
        resolve({ data: {}, result: 1 })
      })
    }
  }))
  React.useEffect(() => {
    console.log('===workflow:', workflowData)
  }, [workflowData])

  return (
    <>
      <Box sx={{ px: 2, py: 1 }}>
        <Box sx={{ mb: 2 }} className='flex items-center'>
          <Typography variant='body2' sx={{ mr: 1 }}>
            该活动需要审批
          </Typography>
          <Switch onChange={toggleSupportProcess} checked={surpportProcess} />
        </Box>
        {surpportProcess && (
          <Flow
            dataset={workflowData}
            onNodeNameChange={handleNodeNameChange}
            onDeleteNode={handleDeleteNode}
            onAddNode={handleAddNode}
            onAddParticipant={handleAddParticipant}
            onDeleteParticipant={handleDeleteParticipant}
          />
        )}
      </Box>
      <PersonnelPicker2
        // advanced={adminType === ADMIN_TYPE.SUPER}
        text='审批人选择'
        ref={personnelRef}
        value={currentPersonnelValue}
        onChange={handlePersonnelChange}
        renderValue={() => null}
        extend={{
          categories: [{ key: 'expr', label: '自动筛选' }],
          data: {
            expr: [{ code: 'postset:poss_xgbzr', name: '部门学工办主任' }]
          }
        }}
        // categories={CandidateCat}
      />
    </>
  )
})
