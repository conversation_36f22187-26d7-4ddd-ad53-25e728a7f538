import { Box } from '@mui/system'
import React from 'react'
import Preview from './preview'
import { updateEditorSettings, updateSettings, useEditorContext } from '../context/EditorContext'
import { Tabs } from 'antd-mobile'
import { MobileTemList } from '@/editor-kit/pc/components/Tools/Panels/PanelSetting'
import { THEME, THEMESETTINGS, themetype } from '@/@define'
import PageSettings from '../editor/components/PageSettings'
export default () => {
  const [{ settings, pageSettings, taskInfo }, editorDispatch] = useEditorContext()
  const handleTemChange = (value?: themetype) => {
    updateEditorSettings(
      editorDispatch,
      value && value.length
        ? {
            theme: value,
            ...THEMESETTINGS[value]
          }
        : undefined,
      true
    )
    updateSettings(editorDispatch, value && value.length && { imgs: THEMESETTINGS[value]?.cover?.value })
  }
  return (
    <Box>
      <Tabs>
        <Tabs.Tab title='主题' key='themes'>
          <MobileTemList
            onChange={handleTemChange}
            value={pageSettings?.theme || 'DEFAULT'}
            options={THEME}
          ></MobileTemList>
        </Tabs.Tab>
        <Tabs.Tab title='外观' key='page'>
          <PageSettings></PageSettings>
        </Tabs.Tab>
      </Tabs>
    </Box>
  )
}
