import React from 'react'
import { styled } from '@mui/system'
import { Button, Input, Stepper, Switch, TextArea, Checkbox, DatePicker, Rate, Slider, Radio } from 'antd-mobile'

const Wrapper = styled('div')({
  backgroundColor: '#FFF',
  border: '1px solid #eee',
  borderRadius: '8px',
  padding: 24
})

const Block = ({ title, children }: any) => {
  return (
    <Wrapper>
      <p className='mb-4 font-medium'>{title}</p>
      <div className='flex gap-6'>{children}</div>
    </Wrapper>
  )
}

export default function PlayGround() {
  const [visible, setVisible] = React.useState(false)

  return (
    <div className='p-4 h-full' style={{ backgroundColor: '#f8f8f8' }}>
      <div className='flex flex-col gap-4'>
        <Block title='按钮'>
          <Button color='primary' fill='solid'>
            Solid
          </Button>
          <Button color='primary' fill='outline'>
            Outline
          </Button>
          <Button color='primary' fill='none'>
            None
          </Button>
        </Block>
        <Block title='时间'>
          <Button
            onClick={() => {
              setVisible(true)
            }}
          >
            选择日期
          </Button>
          <DatePicker
            title='时间选择'
            visible={visible}
            onClose={() => {
              setVisible(false)
            }}
          />
        </Block>
        <Block title='输入框'>
          <Input placeholder='请输入内容' clearable />
        </Block>
        <Block title='文本域'>
          <TextArea />
        </Block>
        <Block title='Stepper'>
          <Stepper defaultValue={1} />
        </Block>
        <Block title='开关'>
          <Switch />
        </Block>
        <Block title='单选'>
          <Radio.Group defaultValue='1'>
            <Radio value='1'>第一项</Radio>
            <Radio value='2'>第二项</Radio>
            <Radio value='3'>第三项</Radio>
          </Radio.Group>
        </Block>
        <Block title='复选框'>
          <Checkbox>苹果</Checkbox>
          <Checkbox>西瓜</Checkbox>
        </Block>
        <Block title='评分'>
          <Rate allowHalf defaultValue={2.5} />
        </Block>
        <Block title='滑动输入条'>
          <Slider className='flex-grow' step={20} defaultValue={60} popover />
        </Block>
      </div>
    </div>
  )
}
