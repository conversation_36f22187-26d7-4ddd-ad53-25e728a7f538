import React from 'react'
import { Box, styled } from '@mui/system'
import ReactEcharts from 'echarts-for-react'

const option = {
  tooltip: {
    trigger: 'item'
  },
  //   legend: {
  //     orient: 'vertical',
  //     left: 'left'
  //   },
  series: [
    {
      name: 'Access From',
      type: 'pie',
      radius: '50%',
      data: [
        { value: 1048, name: 'Search Engine' },
        { value: 735, name: 'Direct' },
        { value: 580, name: 'Email' },
        { value: 484, name: 'Union Ads' },
        { value: 300, name: 'Video Ads' }
      ],
      emphasis: {
        itemStyle: {
          shadowBlur: 10,
          shadowOffsetX: 0,
          shadowColor: 'rgba(0, 0, 0, 0.5)'
        }
      }
    }
  ]
}

const getOption = (name: string, data: any[], type = 'pie') => {
  return {
    tooltip: {
      trigger: 'item'
    },
    series: [
      {
        // name: name,
        type: type,
        radius: '50%',
        data: data,
        emphasis: {
          itemStyle: {
            shadowBlur: 10,
            shadowOffsetX: 0,
            shadowColor: 'rgba(0, 0, 0, 0.5)'
          }
        }
      }
    ]
  }
}

interface IProps {
  name: string
  data: any[]
}

export default function ({ name, data }: IProps) {
  const option = React.useMemo(() => getOption(name, data), [data, name])
  return <ReactEcharts option={option} />
}
