import React, { useCallback, useEffect, useMemo, useRef, useState } from 'react'
import { Box, fontSize, styled } from '@mui/system'
import { Button, Space } from 'antd-mobile'
import { FilterOutline as IconFilter } from 'antd-mobile-icons'
import { IViewBaseProps } from '../types'
import { getStatisticParticipate } from '@/api/easyform'
import { useEasyFormRequest } from '@/hooks'
import { ActionMenu, Typography } from '@/components'
import { COUNT_TYPE } from './define'
import { BarChart, Chart as PieChart } from '../graph/components'
import { Col, Row, Radio, Select } from 'antd'
import { useOverlayScrollbars } from '@yiban/system'

type CardInfo = {
  title?: string | React.ReactNode
  type?: 'pie' | 'bar' //表格类型
  headerExtra?: React.ReactElement<{ source: any; setSource: (newSource: any) => void }> | 'sorter' //自定义的头部扩展组件,可以接受source为数据源，setSource修改数据源(例如实现前端过滤).也可传递字符串使用预定义的过滤器，排序
}

export interface OverviewCardProsps {
  activityId: any
  instanceId?: any
  cardInfo?: CardInfo
  params: any
  canSorter?: boolean
}

const OverviewCard = React.forwardRef((props: OverviewCardProsps, ref) => {
  const { activityId, instanceId, canSorter = true, params, cardInfo = { title: '卡片标题', type: 'bar' } } = props
  const { title, headerExtra, type } = cardInfo
  const bodyRef = useRef()
  const [initialize, instance] = useOverlayScrollbars({ options: { scrollbars: { autoHide: 'scroll' } } })
  const [dataset, setDataset] = React.useState({
    dimensions: ['dimensionName', 'count'],
    source: []
  })
  const [sorterValue, setSorterValue] = useState(1)
  const [filterValue, setFilterValue] = useState<any>('[0]')

  const onChange = (e: any) => {
    console.log('radio checked', e.target.value)
    setSorterValue(e.target.value)
  }
  useEffect(() => {
    bodyRef.current && initialize(bodyRef.current)
  }, [initialize])
  const { loading, run } = useEasyFormRequest((params) => getStatisticParticipate(activityId, instanceId, params), {
    manual: true,
    onSuccess: (res: any) => {
      if (Array.isArray(res.data)) {
        const _data = res.data.sort((a: any, b: any) => b.count - a.count)
        setDataset({
          dimensions: ['dimensionName', 'count'],
          source: _data
        })
      }
    }
  })
  React.useEffect(() => {
    run(params)
  }, [params, run])
  const chartDataSet = useMemo(() => {
    let _data: any
    if (type == 'bar') {
      if (sorterValue == 1) {
        _data = dataset.source.sort((a: any, b: any) => b.count - a.count)
      } else {
        _data = dataset.source.sort((a: any, b: any) => a.count - b.count)
      }
      return {
        dimensions: ['dimensionName', 'count'],
        source: _data?.slice(JSON.parse(filterValue))
      }
    }
    if (type == 'pie') {
      const _data = dataset.source.map((item: any) => ({ name: item.dimensionName, value: item.count }))
      // if (sorterValue == 1) {
      //   _data = pieData.sort((a: any, b: any) => b.count - a.count)
      // } else {
      //   _data = pieData.sort((a: any, b: any) => a.count - b.count)
      // }
      return _data
    }
  }, [type, sorterValue, filterValue, dataset.source])
  const isEmpty = useMemo(() => {
    if (type == 'pie') {
      if (!(chartDataSet as any)?.length) {
        return true
      }
    }
    if (type == 'bar') {
      if (!(chartDataSet as any)?.source?.length) {
        return true
      }
    }
    return false
  }, [chartDataSet, type])
  return (
    <Box
      sx={{
        border: '1px solid #f0f0f0',
        height: '100%',
        pt: 2,
        borderRadius: '10px',
        position: 'relative',
        transition: 'box-shadow 0.2s,border-color 0.2s',
        '.ezf-radio-button-wrapper': {
          fontSize: 12
        }
        // ':hover': {
        //   boxShadow:
        //     '0 1px 2px -2px rgba(0, 0, 0, 0.16), 0 3px 6px 0 rgba(0, 0, 0, 0.12), 0 5px 12px 4px rgba(0, 0, 0, 0.09)'
        // }
      }}
    >
      <Box
        sx={{
          px: 1,
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'space-between',
          borderLeft: (theme) => `3px solid ${theme.palette.primary.main}`
        }}
      >
        <Typography sx={{ fontSize: 'h2.fontSize', p: 1 }}>{title}</Typography>
        {headerExtra}
        {canSorter ? (
          <Box
            sx={{
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'center',
              '.ezf-select-selection-item': { fontSize: 12 }
            }}
          >
            <Select
              size='small'
              style={{ marginRight: 16, width: 75 }}
              value={filterValue}
              onChange={(value: any) => {
                setFilterValue(value)
              }}
              options={[
                { label: '全部', value: '[0]' },
                { label: '前五条', value: '[0,5]' },
                { label: '后五条', value: '[-5]' },
                { label: '前十条', value: '[0,10]' },
                { label: '后十条', value: '[-10]' }
              ]}
            ></Select>
            <Radio.Group
              size={'small'}
              optionType='button'
              options={[
                { label: '倒序', value: 1 },
                { label: '正序', value: 2 }
              ]}
              buttonStyle='solid'
              onChange={onChange}
              value={sorterValue}
            ></Radio.Group>
          </Box>
        ) : (
          ''
        )}
      </Box>
      <Box sx={{ p: 1, height: 300 }} ref={bodyRef}>
        {type == 'bar' ? <BarChart unit='人' data={chartDataSet} /> : <PieChart unit='人' data={chartDataSet} />}
      </Box>
    </Box>
  )
})

export default React.forwardRef(function ({ activityId, instanceId, pageDefine, fieldsInfo }: IViewBaseProps, ref) {
  return (
    <Box ref={ref}>
      <Box className='flex justify-between items-center'></Box>
      <Row gutter={[16, 16]}>
        {COUNT_TYPE.map((item, key) => (
          <Col sm={24} md={12} xxl={6} key={key}>
            <OverviewCard
              activityId={activityId}
              instanceId={instanceId}
              canSorter={item.chartType == 'bar'}
              cardInfo={{ title: item.text, type: item.chartType as any }}
              params={{
                type: item.key,
                finishReason: void 0, //流程状态,
                ticketStatus: void 0
              }}
            ></OverviewCard>
          </Col>
        ))}
      </Row>
    </Box>
  )
})
