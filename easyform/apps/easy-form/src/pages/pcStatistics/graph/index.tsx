import React, { useEffect, useMemo, useRef } from 'react'
import { Box, styled } from '@mui/system'
import { SpinLoading } from 'antd-mobile'
import { IViewBaseProps } from '../types'
import { getStatistic } from '@/api/easyform'
import { useEasyFormRequest } from '@/hooks'
import { Typography, Tag } from '@/components'
import { DataLoading } from '@/components/Loading'
import * as Charts from './components'
import { Row, Col } from 'antd'
import { useOverlayScrollbars } from '@yiban/system'
const VIEW_TYPES = {
  Chart: 'Chart',
  Word: 'Word',
  BarChart: 'BarChart'
}

const getViewType = (componentType: string) => {
  switch (componentType) {
    case 'Radio':
    case 'Rate': {
      return VIEW_TYPES.Chart
    }
    case 'Checkbox': {
      return VIEW_TYPES.BarChart
    }
    default: {
      return VIEW_TYPES.Word
    }
  }
}
const filterOtherOption = (options: any[], data: any[]) => {
  const otherLabels: any = {}
  options.forEach((o) => {
    if (o.inputAble) {
      otherLabels[o.label] = 0
    }
  })
  if (Object.keys(otherLabels).length === 0) {
    return data
  }
  const res: any[] = []
  const reg = new RegExp(
    `^(${Object.keys(otherLabels)
      .map((k) => `${k}`)
      .join('|')})`
  )
  data.forEach((d) => {
    if (reg.test(d.value)) {
      const _label = d.value.split(':')[0]
      otherLabels[_label] += d.count
    } else {
      res.push(d)
    }
  })
  return res.concat(Object.entries(otherLabels).map(([k, v]) => ({ value: k, count: v })))
}
const transformDataForView = (viewType: string, data: any[], fieldInfo?: any) => {
  // console.log('===info:', fieldInfo)
  switch (viewType) {
    case VIEW_TYPES.Chart: {
      const _data = filterOtherOption(fieldInfo.options, data)
      return _data.map((item: any) => ({
        name: `${item.value}`,
        value: item.count
      }))
    }
    case VIEW_TYPES.BarChart: {
      const _data = filterOtherOption(fieldInfo.options, data)
      return {
        dimensions: ['value', 'count'],
        source: _data
      }
    }
    case VIEW_TYPES.Word: {
      return data.map((item: any) => ({
        text: item[0],
        value: item[1] * 100
      }))
    }
  }
}

const formatData = (data: any, fieldsInfo: any = {}) => {
  /**
   * 图表or词云
   */
  return data.map((d: any) => {
    const viewType = getViewType(fieldsInfo[d.name].componentType)
    // const viewType = ['Radio', 'Checkbox', 'Rate'].includes(fieldsInfo[d.name].componentType) ? 'Chart' : 'Word'
    return {
      id: d.name,
      title: d.title,
      viewType: viewType,
      data: transformDataForView(viewType, d.data, fieldsInfo[d.name])
    }
  })
}
const ItemCard = ({
  title,
  viewType,
  fieldsInfo,
  i,
  id,
  data
}: {
  title: any
  viewType: any
  fieldsInfo: any
  i: any
  id: any
  data: any
}) => {
  const chartRef = useRef()
  const [initialize, instance] = useOverlayScrollbars({ options: { scrollbars: { autoHide: 'scroll' } } })
  useEffect(() => {
    chartRef.current && initialize(chartRef.current)
  }, [initialize])
  return (
    <Box sx={{ border: '1px solid #f0f0f0', borderRadius: '8px', p: 1 }}>
      <Box className='flex justify-between items-baseline' sx={{ pt: 1, pb: 1, borderBottom: '1px solid #f0f0f0' }}>
        <Typography sx={{ fontWeight: 'bold' }}>{`${i + 1}.${title}`}</Typography>
        <Tag>{fieldsInfo[id].componentZhName}</Tag>
      </Box>
      <Box ref={chartRef} sx={{ height: 300, bgcolor: '#FFF', mb: 2 }}>
        {React.createElement((Charts as any)[viewType], {
          name: title,
          data: data
        })}
      </Box>
    </Box>
  )
}
export default React.forwardRef(function ({ activityId, instanceId, fieldsInfo }: IViewBaseProps, ref) {
  const [data, setData] = React.useState([])

  const { loading, run } = useEasyFormRequest(() => getStatistic(activityId, instanceId), {
    onSuccess: (res: any) => {
      setData(formatData(res.data, fieldsInfo))
    }
  })
  React.useImperativeHandle(ref, () => ({
    refresh: () => {
      run()
    }
  }))

  return (
    <Box className='h-full relative'>
      {!loading && data.length === 0 ? (
        <Typography variant='subtitle2' align='center'>
          暂无数据
        </Typography>
      ) : (
        <Row gutter={[16, 16]}>
          {data.map(({ id, title, viewType, data }, i) => (
            <Col sm={24} md={12} xxl={6} key={id}>
              <ItemCard fieldsInfo={fieldsInfo} id={id} title={title} viewType={viewType} data={data} i={i}></ItemCard>
            </Col>
          ))}
        </Row>
      )}
      {loading && <DataLoading />}
    </Box>
  )
})
