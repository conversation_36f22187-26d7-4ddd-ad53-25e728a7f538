import React from 'react'
import { Box, styled } from '@mui/system'
import WordCloud from 'react-d3-cloud'
import { withAutoSize, useEchartsTheme } from '@yiban/system'
import Typography from '@/components/Typography'

interface dataItem {
  text: string
  value: number
}
interface IProps {
  name: string
  type?: string
  data: dataItem[]
  height?: any
  width?: any
}

const data2 = [
  { text: 'Hey', value: 1000 },
  { text: 'lol', value: 200 },
  { text: 'first impression', value: 800 },
  { text: 'very cool', value: 1000000 },
  { text: 'duck', value: 10000 }
]

export default withAutoSize(function ({ name, data, height, width }: IProps) {
  return (
    <Box sx={{ position: 'relative' }}>
      <WordCloud data={data} height={height} width={width} />
      {!data.length ? (
        <Typography
          sx={{
            position: 'absolute',
            transform: 'translate(-50%, -50%)',
            top: '50%',
            left: '50%',
            zIndex: 1
          }}
        >
          暂无数据
        </Typography>
      ) : (
        ''
      )}
    </Box>
  )
})
