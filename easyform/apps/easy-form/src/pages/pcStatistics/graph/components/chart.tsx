import React from 'react'
import { Box, styled } from '@mui/system'
import ReactEcharts from 'echarts-for-react'
import { withAutoSize, useEchartsTheme } from '@yiban/system'
import Typography from '@/components/Typography'

const option = {
  tooltip: {
    trigger: 'item'
  },
  //   legend: {
  //     orient: 'vertical',
  //     left: 'left'
  //   },
  series: [
    {
      name: 'Access From',
      type: 'pie',
      radius: '50%',
      data: [
        { value: 1048, name: 'Search Engine' },
        { value: 735, name: 'Direct' },
        { value: 580, name: 'Email' },
        { value: 484, name: 'Union Ads' },
        { value: 300, name: 'Video Ads' }
      ],
      emphasis: {
        itemStyle: {
          shadowBlur: 10,
          shadowOffsetX: 0,
          shadowColor: 'rgba(0, 0, 0, 0.5)'
        }
      }
    }
  ]
}

const getOption = (name: string, data: any[], type = 'pie') => {
  return {
    tooltip: {
      trigger: 'item'
    },
    series: [
      {
        type: 'pie',
        radius: [20, 60],
        itemStyle: {
          borderColor: '#fff',
          borderWidth: 1
        },
        data: data,
        label: {
          formatter: function (params: any) {
            // console.log('===params:', params)
            return '{a|' + params.name + '}\n{b|' + params.percent + '%}'
          },
          rich: {
            a: {
              fontSize: 13,
              // fontWeight: 'bold',
              lineHeight: 22
            },
            b: {
              fontSize: 12,
              color: '#999'
            }
          }
        }
      }
    ]
  }
}

interface IProps {
  name: string
  data: any[]
  width?: any
  height?: any
}

export default withAutoSize(function ({ name, data, width, height }: IProps) {
  const theme = useEchartsTheme()
  const option = React.useMemo(() => getOption(name, data), [data, name])
  return (
    <Box>
      <ReactEcharts theme={theme} style={{ width: width, height: height }} option={option} />
      {!data.length ? (
        <Typography
          sx={{
            position: 'absolute',
            transform: 'translate(-50%, -50%)',
            top: '50%',
            left: '50%',
            zIndex: 1
          }}
        >
          暂无数据
        </Typography>
      ) : (
        ''
      )}
    </Box>
  )
})
