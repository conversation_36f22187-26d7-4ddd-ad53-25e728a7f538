import React from 'react'
import * as echarts from 'echarts'
import ReactEcharts from 'echarts-for-react'
import { withAutoSize, useEchartsTheme, Box } from '@yiban/system'
import { getOption } from './helper'
import Typography from '@/components/Typography'

const defaultDataset = {
  dimensions: [],
  source: []
}
export type ChartProps = any & {
  unit?: string
}
const ProgressBar = React.forwardRef<any, ChartProps>(
  ({ data = defaultDataset, unit = '人', style, width, height }: ChartProps, ref) => {
    console.log('===graph:', data)
    const echartsTheme = useEchartsTheme()
    const _height = React.useMemo(() => {
      return Math.max(290, (data?.source?.length || 0) * 64)
    }, [data?.source?.length])

    const option = React.useMemo(() => getOption(data, unit, echartsTheme), [data, echartsTheme, unit])
    return (
      <Box>
        <ReactEcharts
          theme={echartsTheme}
          echarts={echarts}
          option={option}
          ref={ref}
          style={{ width: width, height: _height, ...style }}
        />
        {!data?.source?.length ? (
          <Typography
            sx={{
              position: 'absolute',
              transform: 'translate(-50%, -50%)',
              top: '50%',
              left: '50%',
              zIndex: 1
            }}
          >
            暂无数据
          </Typography>
        ) : (
          ''
        )}
      </Box>
    )
  }
)
//export default ProgressBar
export default withAutoSize(ProgressBar)
