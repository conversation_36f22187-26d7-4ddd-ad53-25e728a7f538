import * as echarts from 'echarts'
import { setAlpha } from '@/utils'

export function getOption(data: any, unit = '', theme?: any): any {
  return {
    tooltip: {
      formatter: (params: any, o: any) => {
        return params.name + ' : ' + params.data[params.seriesName] + unit
      },
      textStyle: {
        align: 'left'
      }
    },
    dataset: data,
    grid: {
      top: '10',
      left: '0',
      right: '0',
      bottom: '0',
      containLabel: true
    },
    xAxis: {
      show: false,
      type: 'value',
      splitLine: {
        show: false
      }
    },
    yAxis: {
      axisLine: {
        show: false
      },
      type: 'category',
      axisTick: {
        show: false
      },
      inverse: true, //排序
      // data: data?.map((data: any) => data.name),
      axisLabel: {
        show: false,
        inside: true,
        verticalAlign: 'bottom', // 调整左侧文字的3个属性，缺一不可
        align: 'left', //调整文字上右下左
        padding: [10, 0, 16, -8]
      }
    },
    series: [
      {
        type: 'bar',
        // data: data?.map((data: any) => data.value),
        barWidth: 14,
        itemStyle: {
          // 柱子样式
          normal: {
            //normal 图形在默认状态下的样式;//emphasis图形在高亮状态下的样式
            barBorderRadius: 12, //柱条圆角半径,单位px.分开设置[10,10,10,10]顺时针左上、右上、右下、左下
            color: function (params: any) {
              if (!Array.isArray(theme?.color)) {
                return params.color
              }
              const colorList = theme.color
              const colorIndex = params.dataIndex % colorList.length
              return new echarts.graphic.LinearGradient(0, 0, 1, 0, [
                {
                  offset: 0,
                  color: colorList[colorIndex]
                },
                {
                  offset: 1,
                  color: setAlpha(colorList[colorIndex], 0.3)
                }
              ])
            },
            label: {
              show: true, // 显示文本
              // distance: 8,
              position: [0, -20],
              // position: 'top', // 数据值位置
              formatter: `{b}  {@[1]}${unit}`,
              rich: {
                a: {
                  fontWeight: 900
                },
                z: {
                  fontWeight: 900
                }
              }
            }
          }
        },
        zlevel: 1 //柱状图所有图形的 zlevel 值,zlevel 大的 Canvas 会放在 zlevel 小的 Canvas 的上面
      }
      // {
      //   // name: '进度条背景',
      //   type: 'bar',
      //   barGap: '-100%', //不同系列的柱间距离，为百分比。
      //   // 在同一坐标系上，此属性会被多个 'bar' 系列共享。
      //   // 此属性应设置于此坐标系中最后一个 'bar' 系列上才会生效，
      //   //并且是对此坐标系中所有 'bar' 系列生效。
      //   barWidth: 14,
      //   // data: data.source.map(() => 100),
      //   color: 'rgb(243,243,243)', //柱条颜色
      //   itemStyle: {
      //     normal: {
      //       barBorderRadius: 12
      //     }
      //   },
      //   tooltip: {
      //     show: false // 设置为false，鼠标移入时不展示信息提示框
      //   }
      //   // label: {
      //   //   show: true, // 显示文本
      //   //   // distance: -16,
      //   //   padding: [-28, 0, 0, 0],
      //   //   position: 'insideTopRight', // 数据值位置
      //   //   formatter: '{c}' + unit, //显示百分比,
      //   //   fontWeight: 900,
      //   //   align: 'right'
      //   // }
      // }
    ]
  }
}
