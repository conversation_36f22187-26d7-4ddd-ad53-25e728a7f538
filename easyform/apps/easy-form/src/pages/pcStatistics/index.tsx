import { <PERSON><PERSON>, StyledTabs, StyledTab, Header<PERSON>ontent, ContentWrapper } from '@/pages/components'
import { styled, Box } from '@mui/system'
import { useState, useEffect, useMemo, createRef } from 'react'
import { useLocation } from 'react-router-dom'
import { TabPanel, Typography } from '@/components'
import { PullToRefresh, DotLoading, SpinLoading } from 'antd-mobile'
import { EchartsThemeProvider } from '@yiban/system'

import Overview from './overview'
import Graph from './graph'
import List from './list'

import { useEasyFormRequest } from '@/hooks'
import { getActivityDefine } from '@/api/easyform'
import { getUrlParam } from '@/utils'

const tabs = ['表单统计', '表单项统计', '详细数据']
// const tabs = ['表单项统计', '详细数据']

const ComponentNameMap: any = {
  Input: '文本输入',
  Checkbox: '多选',
  Radio: '单选',
  Rate: '评分'
}

export default function () {
  const { state }: any = useLocation()
  const [activeTab, setActiveTab] = useState(tabs[0])
  const [activityId] = useState(getUrlParam('activityId') || state?.activityId)
  const [instanceId] = useState(getUrlParam('instanceId') || state?.instanceId)
  const [pageDefine, setPageDefine] = useState<any>()
  const { loading } = useEasyFormRequest(() => getActivityDefine(activityId), {
    ready: Boolean(activityId),
    loadingDelay: 200,
    onSuccess: (res: any) => {
      setPageDefine(res?.data?.page?.componentTree || [])
    }
  })
  const fieldsInfo = useMemo(() => {
    return (pageDefine || []).reduce((p: any, c: any) => {
      const componentType = c.componentName.split('.').pop()
      return {
        ...p,
        [c._id]: {
          componentName: c.componentName,
          componentType: componentType,
          options: c?.props?.options || [],
          componentZhName: ComponentNameMap[componentType] || '未知'
        }
      }
    }, {})
  }, [pageDefine])

  const [refs] = useState<Record<string, any>>({
    [tabs[0]]: createRef(),
    [tabs[1]]: createRef(),
    [tabs[2]]: createRef()
  })
  const handleRefresh = async () => {
    await refs[activeTab]?.current?.refresh()
  }
  return (
    <EchartsThemeProvider>
      <Box sx={{ bgcolor: 'background.default', height: '100vh' }}>
        <Header>
          <HeaderContent>
            <Typography></Typography>
            <Box className='flex'>
              <StyledTabs activeKey={activeTab} onChange={(t) => setActiveTab(t)}>
                {tabs.map((t, i) => (
                  <StyledTab key={t} title={t} />
                ))}
              </StyledTabs>
            </Box>
          </HeaderContent>
        </Header>
        <ContentWrapper className='overflow-auto'>
          <PullToRefresh onRefresh={handleRefresh}>
            {pageDefine ? (
              <>
                <TabPanel value={tabs[0]} showValue={activeTab}>
                  <Overview
                    ref={refs[tabs[0]]}
                    activityId={activityId}
                    instanceId={instanceId}
                    pageDefine={pageDefine}
                    fieldsInfo={fieldsInfo}
                  />
                </TabPanel>
                <TabPanel value={tabs[1]} showValue={activeTab}>
                  <Graph
                    ref={refs[tabs[1]]}
                    activityId={activityId}
                    instanceId={instanceId}
                    pageDefine={pageDefine}
                    fieldsInfo={fieldsInfo}
                  />
                </TabPanel>
                <TabPanel value={tabs[2]} showValue={activeTab}>
                  <List
                    ref={refs[tabs[2]]}
                    activityId={activityId}
                    instanceId={instanceId}
                    pageDefine={pageDefine}
                    fieldsInfo={fieldsInfo}
                  />
                </TabPanel>
              </>
            ) : (
              <Box className='h-full flex justify-center items-center'>
                <SpinLoading color='primary' />
              </Box>
            )}
          </PullToRefresh>
        </ContentWrapper>
      </Box>
    </EchartsThemeProvider>
  )
}
