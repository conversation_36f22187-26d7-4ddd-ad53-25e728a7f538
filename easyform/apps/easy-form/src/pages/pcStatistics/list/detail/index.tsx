import React, { useCallback, useEffect } from 'react'
import { Box, styled } from '@mui/system'
import { Typography } from '@/components'
import ImageList from '@/components/ImageList'
import UploadList from '@/components/UploadList'

import { Select } from '@/pages/designer/configuration/assembly'
interface DProps {
  detailList?: any
  values: any
}
const getValue = (v: any, componentName: string) => {
  if (componentName.endsWith('ImageUploader')) {
    return <ImageList imgs={typeof v === 'string' ? v.split(',') : v} singleStyle={{ maxWidth: '40%' }} />
  }
  if(componentName.endsWith('Upload')){
    return <UploadList value={v} />
  }
  return <Typography variant='body2'>{v}</Typography>
}
export default function ({ detailList, values }: DProps) {
  return (
    <Box>
      {detailList.map((item: any, i: number) => (
        <Box
          key={item._id}
          sx={{
            py: 1,
            borderBottom: '1px dashed',
            borderColor: 'divider',
            display: 'flex',
            flexDirection: 'column',
            justifyItems: 'flex-end'
          }}
        >
          <Typography sx={{ mb: 1, color: 'text.primary' }} variant='body2'>
            {item?.props?.label}
          </Typography>
          {getValue(values[item._id], item.componentName)}
        </Box>
      ))}
    </Box>
  )
}
