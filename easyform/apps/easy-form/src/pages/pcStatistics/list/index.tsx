import React, { useState } from 'react'
import { Box, fontSize, styled } from '@mui/system'
import { IViewBaseProps } from '../types'
import { Typography } from '@/components'
import { Collapse, Avatar, InfiniteScroll } from 'antd-mobile'
import { DownOutline } from 'antd-mobile-icons'
import Detail from './detail'
import { getList } from '@/api/easyform'
import { useEasyFormRequest } from '@/hooks'
import { formatDate } from '@/utils'
const Root = styled('div')({})
const StyledCollapse = styled(Collapse)(({ theme }) => ({
  '& .adm-list': {
    '--border-top': 'none',
    '--border-bottom': 'none',
    borderRadius: '8px',
    overflow: 'hidden'
  },
  '& a.adm-list-item:active:not(.adm-list-item-disabled)': {
    backgroundColor: 'transparent'
  }
}))
const CircleAvatar = styled(Avatar)({
  '--border-radius': '100%',
  '--size': '48px',
  border: '1px solid #FFF'
})
const StyledPanel = styled(Collapse.Panel)(({ theme }) => ({}))
const Content = styled('div')({
  display: 'flex'
})

export default React.forwardRef(function ({ activityId, instanceId, fieldsInfo, pageDefine }: IViewBaseProps, ref) {
  const [open, setOpen] = useState(false)
  const [hasMore, setHasMore] = useState(true)
  const handleClick = () => {
    setOpen(!open)
  }
  const [list, setList] = useState<any>([])
  const {
    // data: list,
    loading: listLoading,
    runAsync: loadList
  } = useEasyFormRequest(getList, {
    manual: true
  })
  const [pagination, setPagination] = useState({ pageNo: 1, pageSize: 20 })
  const loadMore = async () => {
    const res = await loadList(instanceId, activityId, pagination)
    const _hasMore = res.total > pagination.pageNo * pagination.pageSize
    setHasMore(_hasMore)
    setList((prev: any[]) => [...prev, ...(res.data || [])])
    if (_hasMore) {
      setPagination((prev) => ({
        ...prev,
        pageNo: prev.pageNo + 1
      }))
    }
  }
  React.useImperativeHandle(ref, () => ({
    refresh: async () => {
      const res = await loadList(instanceId, activityId, { pageNo: 1, pageSize: 20 })
      setHasMore(res.total > pagination.pageNo * pagination.pageSize)
      setList(res.data)
      setPagination({ pageNo: 2, pageSize: 20 })
    }
  }))

  return (
    <Root>
      {!listLoading && list.length === 0 ? (
        <Typography variant='subtitle2' align='center' sx={{ py: 2 }}>
          暂无可查看的数据明细
        </Typography>
      ) : (
        <StyledCollapse>
          {list.map(({ id, creatorName, createAt, ...rest }: any, index: number) => (
            <StyledPanel
              key={id}
              title={
                <Box className='flex items-center'>
                  <CircleAvatar src='' />
                  <Box sx={{ ml: 1.5 }}>
                    <Typography sx={{ mb: 0.5 }} variant='body2'>
                      {creatorName}
                    </Typography>
                    <Typography variant='subtitle1'>{formatDate(createAt, 'YYYY-MM-DD HH:mm') || ''}</Typography>
                  </Box>
                </Box>
              }
              arrow={<DownOutline />}
              onClick={handleClick}
            >
              <Box>
                <Detail detailList={pageDefine} values={rest} />
              </Box>
            </StyledPanel>
          ))}
        </StyledCollapse>
      )}
      <InfiniteScroll loadMore={loadMore} hasMore={hasMore} />
    </Root>
  )
})
