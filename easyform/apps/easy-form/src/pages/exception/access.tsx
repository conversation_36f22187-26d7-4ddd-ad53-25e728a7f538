import React from 'react'
import { Image } from 'antd-mobile'
import { useLocation } from 'react-router-dom'
import { Box } from '@mui/system'
import { Typography } from '@/components'
import { ActivityStateText } from '@/@define/Activity'

function Icon({ width = 24, height = 24 }: any) {
  return (
    <svg
      viewBox='0 0 1024 1024'
      version='1.1'
      xmlns='http://www.w3.org/2000/svg'
      p-id='4365'
      width={height}
      height={height}
    >
      <path
        d='M512 128a384 384 0 1 0 0 768 384 384 0 0 0 0-768zM42.666667 512C42.666667 252.8 252.8 42.666667 512 42.666667s469.333333 210.133333 469.333333 469.333333-210.133333 469.333333-469.333333 469.333333S42.666667 771.2 42.666667 512z'
        fill='#f44336'
        p-id='4366'
      ></path>
      <path
        d='M561.621333 394.752a42.666667 42.666667 0 0 1 14.08 37.205333l-34.986666 279.552 24.917333-6.229333a42.666667 42.666667 0 1 1 20.736 82.773333l-85.333333 21.333334a42.666667 42.666667 0 0 1-52.693334-46.677334l34.944-279.552-24.917333 6.229334a42.666667 42.666667 0 0 1-20.736-82.773334l85.333333-21.333333a42.666667 42.666667 0 0 1 38.656 9.472zM448 277.333333a42.666667 42.666667 0 0 1 42.666667-42.666666h42.666666a42.666667 42.666667 0 0 1 0 85.333333h-42.666666a42.666667 42.666667 0 0 1-42.666667-42.666667z'
        fill='#f44336'
        p-id='4367'
      ></path>
    </svg>
  )
}

export default function NotFound() {
  const { state }: any = useLocation()
  // const reasonText = React.useMemo(
  //   () => (ActivityStateText as any)[state?.reason] || ActivityStateText.NOT_ACCESS,
  //   [state?.reason]
  // )
  return (
    <Box className='h-screen flex items-center flex-col' sx={{ pt: '10vh' }}>
      <Image style={{ maxWidth: 480, margin: '0 auto' }} width='80%' src='/easyform/assets/access.png' />
      <Box sx={{ mt: 2 }} className='flex items-center'>
        <Icon width='16px' height='16px' />
        <Box sx={{ ml: 0.5 }} className='flex justify-center flex-col'>
          <Typography sx={{ fontSize: 14 }} color='textSecondary'>
            {state?.reason || ''}
          </Typography>
        </Box>
      </Box>
    </Box>
  )
}
