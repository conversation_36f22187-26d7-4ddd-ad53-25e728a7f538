import React, { useEffect, useState } from 'react'
import { useLocation, useNavigate } from 'react-router-dom'
import { Box, styled } from '@mui/system'
import { SearchBar, Tabs, InfiniteScroll } from 'antd-mobile'
import { ActivityTypes } from '@/@define/Activity'
import { getUrlParam } from '@/utils'
import TemplateCard, { classItem, CatImages } from './ClassificationCard'
import { useEasyFormRequest } from '@/hooks'
import { getTemplateDefine, templateList } from '@/api/easyform'
import { Typography } from '@/components'

const Header = styled('div')({
  position: 'relative',
  // height: 80,
  backgroundImage: 'url(/easyform/assets/header-bg.png)',
  backgroundSize: 'cover',
  backgroundPosition: 'top',
  padding: '16px 8px 16px 8px'
})
const Content = styled('div')({
  display: 'grid',
  gridTemplateColumns: '1fr 1fr',
  //gridTemplateRows: '180px',
  gridColumnGap: 15,
  gridRowGap: 10
})
const ContentDiv = styled('div')({
  padding: '16px 10px',
  background: 'rgb(242,242,242)',
  flexGrow: 1,
  overflow: 'auto'
})
//模板分类页面
export default () => {
  const location = useLocation()
  const navigate = useNavigate()
  const [templateList, setTemplate] = useState<Array<classItem>>([
    {
      id: 1,
      image: '',
      category: 'APPLY',
      desc: ['人员登记，多种报名', '扫码签到，流程审批'],
      name: '活动报名'
    },
    {
      id: 2,
      image: '',
      category: 'INFO',
      desc: ['预制选项，信息收集', '进度跟踪，同步更新'],
      name: '信息收集'
    },
    { id: 3, image: '', category: 'PSQ', desc: ['多类题型，支持匿名', '进度跟踪，海量题库'], name: '问卷调查' },
    { id: 4, image: '', category: 'NOTICE', desc: ['操作友好，安全可靠', '自定内容，快速发布'], name: '通知公告' }
  ])
  return (
    <>
      <Box className='h-full flex flex-col'>
        <Header>
          {/* <SearchBar placeholder='搜索模版，拿来即用' /> */}
          <Typography align={'center'} sx={{ fontWeight: 'bold', color: 'white' }}>
            选择创建类型
          </Typography>
        </Header>
        <ContentDiv>
          <Content>
            {templateList.map((item, key) => (
              <Box
                onClick={() => {
                  navigate('/template', {
                    state: {
                      type: item.category || 'APPLY'
                    }
                  })
                }}
                key={key}
              >
                <TemplateCard sx={{ height: 200 }} value={item}></TemplateCard>
              </Box>
            ))}
          </Content>
        </ContentDiv>
      </Box>
    </>
  )
}
