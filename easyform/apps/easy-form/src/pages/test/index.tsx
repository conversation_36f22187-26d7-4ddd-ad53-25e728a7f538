import { Button, Form } from 'antd-mobile'
import { useNavigate } from 'react-router-dom'
import { ActivityState } from '@/@define/Activity'
import Approve, { TBaseData } from '@/@template/approve'
import PageWrapper from '@/@template/PageWrapper'
import { Box } from '@mui/system'
import { Typography } from '@/components'
import { ImageUploader } from '@/editor-kit/pc/components/Configuration/assembly'

export default function () {
  const navigate = useNavigate()
  return (
    <PageWrapper>
      <Form>
        <ImageUploader isPC={true} label='123' />
      </Form>
    </PageWrapper>
  )
}
