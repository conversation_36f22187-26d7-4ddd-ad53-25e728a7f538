import React, { useEffect, useState, useRef, forwardRef, useContext, useMemo } from 'react'
import { Box, styled } from '@mui/system'
import { RemoteComponent } from '@/helper'
import { NoticeBar, Switch, Dialog } from 'antd-mobile'
import { useAppContext } from '@/context/AppContext'
import { isXgAdminUser, isDepartAdminUser, isSupperUser, isFdyUser,isAdminUser, isOrgUser, isCounselorUser } from '@/pages/pc/utils'
// import ParticipatorPicker from '@yiban/participator-picker'
import {RoleSelect,EasyformTask} from '@/components'
import {PersonnelPicker2} from '@/components'

import { UpdateContext } from './context/UpdateContext'
import dayjs from 'dayjs'
import WorkFlow from './workflow'
import {
  Breadcrumb,
  Form,
  Tag,
  Segmented,
  Upload,
  Select,
  DatePicker,
  List,
  Row,
  Popover,
  Col,
  Card,
  Input,
  Modal,
  InputNumber,
  Switch as PcSwitch,
  Button,
  Radio,
  Spin,
  Image,
  message,
  Checkbox
} from 'antd'
import { useLocation, useNavigate } from 'react-router-dom'
import { transformParticipant } from '@/pages/designer/helper'
import { ImageUploader, Typography } from '@/components'
import { TabPanel } from '@/components'
import { InfiniteScroll } from 'antd-mobile'
import { useOverlayScrollbars } from '@yiban/system'
import { createFormTask, getTaskInfo, updateFormTask } from '@/api/task'
import { saveActivity, exportExcelForInstanceForm, toggleActivityEnable, getActivityList } from '@/api/easyform'
import useBaseRequest, { useEasyFormRequest } from '@/hooks/useBaseRequest'
import {
  EditFilled,
  EyeFilled,
  QuestionOutlined,
  CloseCircleOutlined,
  LoadingOutlined,
  PlusOutlined,
  FundFilled,
  ExclamationCircleFilled,
  SwapOutlined,
  AppstoreOutlined,
  TeamOutlined,
  QuestionCircleOutlined
} from '@ant-design/icons'
import { ADMIN_TYPE } from '@/@define'
import { isLocalSource } from '@/utils'

const { TextArea } = Input
const RangePicker: any = DatePicker.RangePicker //写上any会是正确的
export enum APPLYTYPES {
  'APPLY' = '报名制',
  'AUDIT' = '审核制',
  'RANDOM' = '随机制'
}
export enum PARTYPES {
  'SM' = '实名',
  'NM' = '匿名'
}
export type selectorItems = '学生' | '教职工' | '组' | '岗位' | '职称'
export type selectorsMenus = Array<{
  label: selectorItems
  key: selectorItems
}>
const { confirm } = Modal
//活动类型的表单
export const HDForm = forwardRef((props: any, ref) => {
  const { PickerController, disabled = false } = props
  const groupContentRef = useRef(null)
  const workflowRef = useRef(null)
  const [form1] = Form.useForm()
  const location: any = useLocation()
  const navigate = useNavigate()
  const template = location.state && location.state.template
  const [form2] = Form.useForm()
  const [saveLoading, setSaveLoading] = useState(0)

  //通过context获取到表单settings，未使用模板创建表单时settings为undeifined，所以给个默认值

  const Updater = useContext<any>(UpdateContext)

  const [{ define, id, status }, updater] = Updater
  const {
    settings = {
      name: (location.state && location.state.name) || undefined, //获取从路由中传递的表单名称
      //alertMsg: '',
      desc: '',
      //isCancel: false,
      imgs: [],
      apply: { type: 'APPLY', applylimit: false, isTicket: false },
      templateSettings: {}
    },
    page,
    workflow
  } = define

  const [cancelOpen, setCancelOpen] = useState(settings && settings.isCancel)
  const [workflowData, setWorkflowData] = useState()
  //所有类型表单都有的基本字段(在保存表单特有字段时会与基本字段合并)
  const [formbase, setFormbase] = useState(
    settings //数据流重新设计后保证了settings一定会有值，所以不会走从template中取值的逻辑了
      ? {
          name: settings && settings.name,
          desc: settings && settings.desc,
          participantDetail: (settings && settings.participantDetail) || {},
          daterange: settings && settings.daterange,
          startDateTime: settings && settings.startDateTime,
          imgs: settings && settings.imgs,
          endDateTime: settings && settings.endDateTime,
          activitylocation: settings && settings.activitylocation,
          successTip: settings && settings.templateSettings?.successTip,
          btnText: settings && settings.templateSettings?.btnText
          //isCancel: (settings && settings.isCancel) || false,
          //canCancelHour: (settings && settings.canCancelHour) || undefined
          //isTicket: (settings && settings.isTicket) || '',
          //maxParticipantNum: (settings && settings.maxParticipantNum) || ''
        }
      : {
          name: (template && template.settings.name) || (location.state && location.state.name) || '',
          desc: (template && template.settings.desc) || '',
          //isCancel: (template && template.settings.isCancel) || false,
          participantDetail: (template && template.settings.participantDetail) || {},
          imgs: (template && template.settings.imgs) || [],
          templateSettings: {}
          //isTicket: template.settings.isTicket || '',
          //maxParticipantNum: template.settings.maxParticipantNum || ''
        }
  )
  const limitOpen = Form.useWatch('applylimit', form2)
  const ticketOpen = Form.useWatch('isTicket', form2)
  const [tagtipopen, setTagtipopen] = useState(false)
  const applyAdminOpen = Form.useWatch('type', form2)
  const {
    runAsync: fetchData,
    data: activityStatus,
    loading: activityLoading,
    refreshAsync: refreshActivity
  } = useEasyFormRequest(() => getActivityList({ id: id }), {
    //manual: true,
    ready: Boolean(id),
    refreshDeps: [],
    onSuccess: (data: any) => {
      updater({ type: 'updateStatus', payload: { status: data.data[0] } })
    }
  })
  const { runAsync: toggleActivity, loading: toggleLoading } = useEasyFormRequest(() => toggleActivityEnable(id), {
    manual: true,
    ready: Boolean(id),
    onSuccess: (data: any) => {
      refreshActivity()
    },
    onError: () => {}
  })
  const { runAsync: toggleActivity2, loading: toggleLoading2 } = useEasyFormRequest(() => toggleActivityEnable(id), {
    manual: true,
    ready: Boolean(id),
    onSuccess: (data: any) => {},
    onError: () => {}
  })
  const { loading: setLoading, runAsync: saveSetting } = useEasyFormRequest((params: any) => saveActivity(params, id), {
    manual: true,
    onSuccess: (res: any) => {
      setSaveLoading(0)
      message.success('保存成功！')
      if (!status?.enable) {
        Dialog.confirm({
          title: '提示',
          content: '保存成功，是否立即发布',
          onConfirm: async () => {
            try {
              await toggleActivity2()
              message.success('发布成功')
              updater({ type: 'updateStatus', payload: { status: 1 } })
            } catch (e) {
              message.error('发布失败')
            }
          }
        })
      }

      //新建表单需要更新id值，更新表单需要更新顶层组件define
      if (!id) {
        updater({ type: 'build', payload: { id: res.data } })
        navigate('/pc/designForm', {
          state: {
            ...(location.state ? location.state : {}),
            id: res.data,
            type: location.state?.type || 'APPLY'
          },
          replace: true
        })
        //updater({ type: 'build', payload: { id: res.data } })
        console.log(res.data)
      }
    },
    onError: () => {
      setSaveLoading(0)
    }
    //ready: Boolean(location.state && location.state.id)
  })

  /**
   * @description 重新发布当前表单(就是调用两次接口)
   */
  const rePublish = async () => {
    await toggleActivity2()
    await toggleActivity()
  }
  const [initialize, instance] = useOverlayScrollbars({ options: { scrollbars: { autoHide: 'scroll' } } })
  useEffect(() => {
    groupContentRef.current && initialize(groupContentRef.current)
  }, [initialize])
  const onTagTipClose = () => {
    setTagtipopen(false)
  }
  const UploadButton = (
    <Box
      ref={ref}
      sx={{
        height: '100px',
        width: '100px',
        background: '#f8f8f8',
        cursor: 'pointer',
        p: 2,
        justifyContent: 'center',
        display: 'flex',
        flexDirection: 'column',
        alignItems: 'center',
        fontSize: 24,
        fontWeight: 'bold'
      }}
    >
      {tagtipopen ? <LoadingOutlined /> : <PlusOutlined />}
    </Box>
  )
  const handleReset = (form: any, resetWorkflow = false) => {
    form.resetFields()
    if (resetWorkflow) {
      workflowRef.current && (workflowRef.current as any).reset()
    }
  }

  //将form表单数据转化为settings
  const ToJson = (value: any, mergebase = false): any => {
    const data = {
      define: {
        settings: settings || { apply: {} },
        page: page || { componentTree: [] },
        workflow: workflow || {}
      }
    }
    //格式化日期
    if (!mergebase) {
      value.startDateTime =
        (value.activitytimerange &&
          value.activitytimerange[0] &&
          dayjs(value.activitytimerange[0].$d).format('YYYY-MM-DD HH:mm:ss')) ||
        undefined
      value.endDateTime =
        (value.activitytimerange &&
          value.activitytimerange[1] &&
          dayjs(value.activitytimerange[1].$d).format('YYYY-MM-DD HH:mm:ss')) ||
        undefined
      value.daterange = [value.startDateTime || undefined, value.endDateTime || undefined]
      delete value.activitytimerange
    }

    //转换人员选择器数据
    if (value?.ticketWriteOffUserScopeDetail) {
      value.ticketWriteOffUserScope = transformParticipant(value.ticketWriteOffUserScopeDetail)
    } else {
      delete value?.ticketWriteOffUserScope
      delete value?.ticketWriteOffUserScopeDetail
    }
    if (value.participantDetail) {
      value.participant = transformParticipant(value.participantDetail)
    } else {
      delete value.participant
      delete value.participantDetail
    }
    if (value?.auditUserScopeDetail) {
      value.auditUserScope = transformParticipant(value.auditUserScopeDetail)
    } else {
      delete value?.auditUserScope
      delete value?.auditUserScopeDetail
    }
    if (mergebase) {
      data.define.settings = { ...data.define.settings, ...formbase }
      data.define.settings.apply = { ...data.define.settings.apply, ...value }
    } else {
      data.define.settings = { ...data.define.settings, ...value }
    }
    //未开启人员限制则删除该属性
    if (!limitOpen) {
      delete data.define.settings.apply?.maxParticipantNum
    }
    if (!ticketOpen) {
      delete data.define.settings.apply?.ticketWriteOffUserScopeDetail
      delete data.define.settings.apply?.ticketWriteOffUserScope
    }
    if (applyAdminOpen == 'APPLY') {
      delete data.define.settings.apply?.auditUserScopeDetail
      delete data.define.settings.apply?.auditUserScope
    }
    if (!cancelOpen) {
      delete data.define.settings?.canCancelHour
    }
    //提示消息设置到templateSettings下
    if (data.define.settings.templateSettings) {
      data.define.settings.templateSettings = {
        ...data.define.settings.templateSettings,
        successTip: data.define.settings.successTip || undefined,
        btnText: data.define.settings.btnText || undefined
      }
    } else {
      data.define.settings.templateSettings = {
        successTip: data.define.settings.successTip,
        btnText: data.define.settings.btnText
      }
    }

    delete data.define.settings?.successTip
    delete data.define.settings?.btnText
    //使用了模板
    if (location.state?.templateId) {
      ;(data.define.settings as any).templateId = location.state.templateId
    }
    //设置表单类型
    if (location.state?.type) {
      ;(data.define.settings as any).type = location.state.type
    }
    //保存基础属性时同时保存流程
    if (!mergebase) {
      data.define.workflow = workflowData
    }
    return data
  }
  const handleSubmit = (value: any, form: any) => {
    setSaveLoading(form)
    //保存的是基本信息
    if (form == 1) {
      const params = ToJson(value)
      saveSetting(params)
      //缓存最新的基础信息
      setFormbase({ ...formbase, ...value })
      updater({ type: 'updateSettings', payload: params })
      updater({ type: 'updateWorkflow', payload: params })
      console.log('数据合并', { ...formbase, ...params.define.settings })
    }
    //其他表单提交需要附带上表单的必填信息
    if (form != 1) {
      const params = ToJson(value, true)
      saveSetting(params)
      updater({ type: 'updateSettings', payload: params })
    }
  }
  useEffect(() => {
    form1 &&
      form1.setFieldsValue({
        ...formbase,
        imgs: settings?.imgs,
        name: settings?.name,
        desc: settings?.desc,
        participantDetail: settings?.participantDetail,
        successTip: settings?.templateSettings?.successTip,
        btnText: settings?.templateSettings?.btnText,
        activitytimerange: settings?.daterange?.length && [
          (settings.daterange[0] && dayjs(settings.daterange[0])) || undefined,
          (settings.daterange[1] && dayjs(settings.daterange[1])) || undefined
        ]
      })
  }, [
    formbase,
    form1,
    settings?.imgs,
    settings?.name,
    settings?.desc,
    settings?.participantDetail,
    settings?.templateSettings?.btnText,
    settings.daterange,
    settings?.templateSettings?.successTip
  ])
  useEffect(() => {
    form2 &&
      form2.setFieldsValue({
        type: settings.apply?.type || (template && template.settings?.apply?.type) || 'APPLY',
        applylimit: settings.apply?.applylimit || (template && template.settings?.apply?.applylimit) || false,
        maxParticipantNum:
          settings.apply?.maxParticipantNum || (template && template.settings?.apply?.maxParticipantNum) || undefined,
        isTicket: settings.apply?.isTicket || (template && template.settings?.apply?.isTicket) || false,
        auditUserScopeDetail:
          settings.apply?.auditUserScopeDetail ||
          (template && template.settings?.apply?.auditUserScopeDetail) ||
          undefined,
        auditUserScope:
          settings.apply?.auditUserScope || (template && template.settings?.apply?.auditUserScope) || undefined,
        ticketWriteOffUserScope:
          settings.apply?.ticketWriteOffUserScope ||
          (template && template.settings?.apply?.ticketWriteOffUserScope) ||
          undefined,

        //successTip: settings && settings.templateSettings.successTip,
        //canCancelHour:
        //settings.apply?.canCancelHour || (template && template.settings?.apply?.canCancelHour) || undefined,
        //isCancel: settings.apply?.isCancel || (template && template.settings?.apply?.isCancel) || false,
        ticketWriteOffUserScopeDetail:
          settings.apply?.ticketWriteOffUserScopeDetail ||
          (template && template.settings?.apply?.ticketWriteOffUserScopeDetail) ||
          undefined
      })
  }, [
    form2,
    settings.apply?.applylimit,
    settings.apply?.auditUserScope,
    settings.apply?.auditUserScopeDetail,
    settings.apply?.isTicket,
    settings.apply?.maxParticipantNum,
    settings.apply?.ticketWriteOffUserScope,
    settings.apply?.ticketWriteOffUserScopeDetail,
    settings.apply?.type,
    template
  ])

  return (
    <>
      {/* {disabled ? <NoticeBar content='当前表单已发布，若要修改请先取消发布' color='alert' closeable /> : ''} */}
      <Box sx={{ width: '100%' }}>
        <Card
          bordered={false}
          style={{ boxShadow: 'none' }}
          title={
            <Box>
              <Typography
                onClick={() => {
                  console.log(settings, location)
                }}
                sx={{ fontWeight: 600, fontSize: 'h3.fontSize' }}
              >
                基本配置
              </Typography>
            </Box>
          }
        >
          <Form
            form={form1}
            initialValues={{ imgs: [], participantDetail: {} }}
            labelCol={{ span: 4 }}
            wrapperCol={{ span: 14 }}
            layout='vertical'
            disabled={disabled}
            style={{ maxWidth: '100%' }}
          >
            <Form.Item required label='活动标题' rules={[{ required: true, message: '请输入活动标题' }]} name='name'>
              <Input placeholder='请输入标题'></Input>
            </Form.Item>
            <Form.Item label='活动说明' rules={[{ required: false, message: '请输入活动描述' }]} name='desc'>
              <TextArea placeholder='请输入活动说明' autoSize={{ minRows: 2, maxRows: 6 }}></TextArea>
            </Form.Item>
            <Form.Item label='活动封面' hidden rules={[{ required: false, message: '请指定活动封面' }]} name={'imgs'}>
              <ImageUploader
                imageUrlPrefix={isLocalSource(form1.getFieldValue('imgs')?.[0]) ? '' : undefined}
                disabled={disabled}
                maxCount={1}
              />
              {/* <Upload accept='.jpg,.png,.ragif,.jpge'>
                {tagtipopen ? (
                  <Image
                    src={''}
                    preview={false}
                    width={150}
                    alt='avatar'
                    fallback='data:image/png;base64,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'
                  />
                ) : (
                  UploadButton
                )}
              </Upload> */}
            </Form.Item>
            <Form.Item
              label='活动时间'
              rules={[{ required: false, message: '请设置活动时间' }]}
              name='activitytimerange'
            >
              <RangePicker allowEmpty={[true, true]} showTime separator={'~'}></RangePicker>
            </Form.Item>
            <Form.Item label='活动地点' name='activitylocation'>
              <Input></Input>
            </Form.Item>
            <Form.Item name={'participant'} hidden>
              <Input></Input>
            </Form.Item>
            <Form.Item
              label='发布范围'
              name='participantDetail'
              rules={[{ required: true, message: '请指定发布范围' }]}
            >
              <RoleSelect
                orgCode={PickerController.orgCode}
                placeholder={'请选择人员'}
                isModal={true}
                disabled={disabled}
                modalWidth={800}
                MenuItem={PickerController.MenuItem}
              ></RoleSelect>
            </Form.Item>
            <Form.Item label='流程审批'>
              <WorkFlow
                disabled={disabled}
                ref={workflowRef}
                defaultWorkflow={workflow}
                onChange={(value: any) => {
                  setWorkflowData(value)  
                  //handleWorkFlowChange({ define: { workflow: value } })
                }}
              ></WorkFlow>
            </Form.Item>
            <Form.Item label='按钮文本' name='btnText'>
              <Input placeholder='请输入提交按钮显示的文本'></Input>
            </Form.Item>
            <Form.Item label='提交成功提示' name='successTip'>
              <Input placeholder='请输入报名成功后显示的提示文字'></Input>
            </Form.Item>
            {/* <Form.Item label={'取消报名'} name={'isCancel'}>
              <Radio.Group
                onChange={(e: any) => {
                  setCancelOpen(e.target.value)
                }}
              >
                <Radio value={false}>关闭</Radio>
                <Radio value={true}>开启</Radio>
              </Radio.Group>
            </Form.Item>
            {cancelOpen ? (
              <Form.Item label='取消时间'>
                <Typography
                  sx={{
                    fontSize: 'body2.fontSize',
                    display: 'flex',
                    alignItems: 'center',
                    color: 'primary.main',
                    fontWeight: 'bold',
                    '.canceltime': {
                      margin: 0
                    }
                  }}
                >
                  允许在距离活动开始{' '}
                  <Form.Item
                    required
                    rules={[{ required: true, message: '请指定可取消时间' }]}
                    name='canCancelHour'
                    className='canceltime'
                  >
                    <InputNumber min={0} style={{ width: 150, margin: '0 8px' }} placeholder='请输入小时'></InputNumber>
                  </Form.Item>
                  小时前，学生自行取消报名。
                </Typography>
              </Form.Item>
            ) : (
              ''
            )} */}
            <Form.Item label={' '} colon={false}>
              <Box sx={{ display: 'flex' }}>
                <Button
                  style={{ marginRight: 24 }}
                  onClick={() => {
                    handleReset(form1, true)
                  }}
                  type='default'
                  disabled={setLoading || disabled}
                  htmlType='submit'
                >
                  重置
                </Button>
                <Button
                  type='primary'
                  loading={saveLoading == 1 && setLoading}
                  onClick={() => {
                    form1.validateFields().then(() => {
                      // if (id && status?.enable == 1) {
                      //   confirm({
                      //     title: (
                      //       <Typography sx={{ fontWeight: 'bold', fontSize: 'h1.fontSize' }}>当前表单已发布</Typography>
                      //     ),
                      //     icon: <ExclamationCircleFilled />,
                      //     maskClosable: true,
                      //     content: (
                      //       <Box>
                      //         <Typography sx={{}}>本次修改需要重新发布表单才会生效，是否确定修改？</Typography>
                      //       </Box>
                      //     ),
                      //     onOk() {
                      //       toggleActivity()
                      //       handleSubmit(form1.getFieldsValue(), 1)
                      //     },
                      //     onCancel() {
                      //       console.log('Cancel')
                      //     }
                      //   })
                      // } else {
                      //   handleSubmit(form1.getFieldsValue(), 1)
                      // }
                      handleSubmit(form1.getFieldsValue(), 1)
                      if (id && status.enable == 1) {
                        // rePublish()
                        // console.log('rePublish=====')
                      }
                    })
                  }}
                  htmlType='submit'
                >
                  保存
                </Button>
              </Box>
            </Form.Item>
          </Form>
        </Card>
        <Card
          style={{ marginTop: '24px', boxShadow: 'none' }}
          bordered={false}
          title={
            <Box>
              <Typography sx={{ fontWeight: 600, fontSize: 'h3.fontSize' }}>高级设置</Typography>
            </Box>
          }
        >
          <Form
            disabled={disabled}
            form={form2}
            labelCol={{ span: 4 }}
            wrapperCol={{ span: 14 }}
            layout='vertical'
            //disabled={componentDisabled}
            style={{ maxWidth: '100%' }}
          >
            {/* <Form.Item label='报名时间' name='applytime'>
              <RangePicker showTime separator={'~'}></RangePicker>
            </Form.Item> */}

            <Form.Item
              label={
                <Typography>
                  {'报名方式'}
                  <Button
                    style={{ transform: 'scale(0.75)' }}
                    shape='circle'
                    onClick={() => {
                      setTagtipopen(true)
                    }}
                    size='small'
                    icon={<QuestionOutlined />}
                  />
                </Typography>
              }
              initialValue={'APPLY'}
              name='type'
            >
              <Radio.Group
                style={{ display: 'flex', alignItems: 'center' }}
                onChange={(e: any) => {
                  const value = e.target.value
                  //setApplyAdminOpen(value)
                }}
              >
                <Radio value={'APPLY'}>{APPLYTYPES.APPLY}</Radio>
                <Radio value={'AUDIT'}>{APPLYTYPES.AUDIT}</Radio>
                {/* <Radio value={'sjz'}>{APPLYTYPES.sjz}</Radio> */}
                {/* <Box
                  sx={{
                    cursor: 'pointer',
                    '.tagtip': {
                      backgroundColor: 'primary.light',
                      color: 'white',
                      p: 0.5,
                      px: 1,
                      fontSize: 'body2.fontSize'
                    }
                  }}
                >
                  <Tag
                    className='tagtip'
                    onClick={() => {
                      setTagtipopen(true)
                    }}
                  >
                    查看方式说明
                    <Button
                      style={{ transform: 'scale(0.7)' }}
                      shape='circle'
                      size='small'
                      icon={<QuestionOutlined />}
                    />
                  </Tag>
                </Box> */}
              </Radio.Group>
            </Form.Item>
            <Form.Item hidden name={'auditUserScope'}>
              <Input></Input>
            </Form.Item>
            {/* {applyAdminOpen == 'AUDIT' ? (
              <Form.Item
                label='添加审核人员'
                name={'auditUserScopeDetail'}
                rules={[{ required: false, message: '请指定审核人员' }]}
              >
                <ParticipatorPicker
                  isModal={true}
                  modalWidth={800}
                  orgCode={PickerController.orgCode}
                  placeholder={'添加负责报名审核的人员'}
                  MenuItem={
                    [
                      {
                        label: '学生',
                        key: '学生'
                      },
                      {
                        label: '教职工',
                        key: '教职工'
                      }
                    ] as selectorsMenus
                  }
                  // onChange={(value: any) => {
                  //   if (value) {
                  //     const { selectValue, participantDetail } = value
                  //     const participant = transformParticipant(participantDetail)
                  //     console.log('转化后的参与者', participant, selectValue)
                  //     switch (whoUsingSelecter) {
                  //       case 'ticketWriteOffUserScopeDetail':
                  //         form2.setFieldValue('ticketWriteOffUserScopeDetail', selectValue)
                  //         form2.setFieldValue('ticketWriteOffUserScope', participant)
                  //         break
                  //       case 'participantDetail':
                  //         form1.setFieldValue('participantDetail', selectValue)
                  //         form1.setFieldValue('participant', participant)
                  //         break
                  //       case 'auditUserScopeDetail':
                  //         form2.setFieldValue('auditUserScopeDetail', selectValue)
                  //         form2.setFieldValue('auditUserScope', participant)
                  //         break
                  //     }

                  //     setShowSelecter(false)
                  //   } else {
                  //     console.log('人员选择器错误，选择人员不存在')
                  //   }
                  // }}
                ></ParticipatorPicker>
              </Form.Item>
            ) : (
              ''
            )} */}
            <Form.Item label='报名人数' initialValue={false} name='applylimit'>
              <Radio.Group
                onChange={(e: any) => {
                  //setLimitOpen(e.target.value)
                }}
                style={{ display: 'flex', alignItems: 'center' }}
              >
                <Radio value={false}>不限</Radio>
                <Radio value={true}>限制</Radio>
                {limitOpen ? (
                  <Form.Item
                    style={{ margin: 0 }}
                    rules={[
                      { required: true, message: '请指定限制的人数' },
                      { pattern: /^[0-9]+$/, message: '请输入整数数字' }
                    ]}
                    name={'maxParticipantNum'}
                  >
                    <InputNumber
                      style={{ width: 200, margin: '0 8px' }}
                      addonAfter='人'
                      min={0}
                      placeholder='请输入限制人数'
                    ></InputNumber>
                  </Form.Item>
                ) : (
                  ''
                )}
              </Radio.Group>
            </Form.Item>
            <Form.Item hidden name={'ticketWriteOffUserScope'}>
              <Input></Input>
            </Form.Item>
            <Form.Item label='扫码签到' initialValue={false} name='isTicket'>
              <Radio.Group
                onChange={(e: any) => {
                  //setTicketOpen(e.target.value)
                }}
              >
                <Radio value={false}>关闭</Radio>
                <Radio value={true}>开启</Radio>
              </Radio.Group>
            </Form.Item>
            {ticketOpen ? (
              <Form.Item
                label='签到人员'
                rules={[{ required: true, message: '请指定扫码签到的人员' }]}
                name='ticketWriteOffUserScopeDetail'
              >
                <RoleSelect
                  isModal={true}
                  orgCode={PickerController.orgCode}
                  disabled={disabled}
                  placeholder={'请选择负责现场扫码签到的人员'}
                  modalWidth={800}
                  MenuItem={
                    [
                      {
                        label: '学生',
                        key: '学生'
                      },
                      {
                        label: '教职工',
                        key: '教职工'
                      }
                    ] as selectorsMenus
                  }
                  // onChange={(value: any) => {
                  //   if (value) {
                  //     const { selectValue, participantDetail } = value
                  //     const participant = transformParticipant(participantDetail)
                  //     console.log('转化后的参与者', participant, selectValue)
                  //     switch (whoUsingSelecter) {
                  //       case 'ticketWriteOffUserScopeDetail':
                  //         form2.setFieldValue('ticketWriteOffUserScopeDetail', selectValue)
                  //         form2.setFieldValue('ticketWriteOffUserScope', participant)
                  //         break
                  //       case 'participantDetail':
                  //         form1.setFieldValue('participantDetail', selectValue)
                  //         form1.setFieldValue('participant', participant)
                  //         break
                  //       case 'auditUserScopeDetail':
                  //         form2.setFieldValue('auditUserScopeDetail', selectValue)
                  //         form2.setFieldValue('auditUserScope', participant)
                  //         break
                  //     }

                  //     setShowSelecter(false)
                  //   } else {
                  //     console.log('人员选择器错误，选择人员不存在')
                  //   }
                  // }}
                ></RoleSelect>
              </Form.Item>
            ) : (
              ''
            )}

            <Form.Item label={' '} colon={false}>
              <Box sx={{ display: 'flex' }}>
                <Button
                  style={{ marginRight: 24 }}
                  onClick={() => {
                    handleReset(form2)
                    //setLimitOpen(false)
                    //setApplyAdminOpen(false)
                    //setTicketOpen(false)
                  }}
                  type='default'
                  htmlType='submit'
                  disabled={setLoading || disabled}
                >
                  重置
                </Button>
                <Button
                  type='primary'
                  loading={saveLoading == 2 && setLoading}
                  onClick={() => {
                    form1.validateFields().then(() => {
                      form2.validateFields().then(() => {
                        // if (id && status?.enable == 1) {
                        //   confirm({
                        //     title: (
                        //       <Typography sx={{ fontWeight: 'bold', fontSize: 'h1.fontSize' }}>
                        //         当前表单已发布
                        //       </Typography>
                        //     ),
                        //     icon: <ExclamationCircleFilled />,
                        //     maskClosable: true,
                        //     content: (
                        //       <Box>
                        //         <Typography sx={{}}>已发布的表单修改后需要重新发布才会生效，是否确定修改？</Typography>
                        //       </Box>
                        //     ),
                        //     onOk() {
                        //       toggleActivity()
                        //       handleSubmit(form2.getFieldsValue(), 2)
                        //     },
                        //     onCancel() {
                        //       console.log('Cancel')
                        //     }
                        //   })
                        // } else {
                        //   handleSubmit(form2.getFieldsValue(), 2)
                        // }
                        handleSubmit(form2.getFieldsValue(), 2)
                        if (id && status.enable == 1) {
                          // rePublish()
                          // console.log('rePublish=====')
                        }
                      })
                    })
                  }}
                  htmlType='submit'
                >
                  保存
                </Button>
              </Box>
            </Form.Item>
          </Form>
        </Card>
      </Box>
      <Modal
        closeIcon={<CloseCircleOutlined />}
        open={tagtipopen}
        width={600}
        destroyOnClose
        onCancel={onTagTipClose}
        onOk={onTagTipClose}
        footer={null}
        title={
          <Box>
            <Typography sx={{ fontWeight: 'bold', fontSize: 'h4.fontSize' }}>各类报名方式说明</Typography>
          </Box>
        }
      >
        <Box sx={{ pb: 1 }}>
          <Box sx={{ display: 'flex', alignItems: 'center', py: 1 }}>
            <Typography
              sx={{
                color: 'white',
                backgroundColor: 'primary.main',
                height: '24px',
                width: '24px',
                fontSize: 'h4.fontSize',
                fontWeight: 'bold',
                borderRadius: '50%',
                display: 'flex',
                alignItems: 'center',
                justifyContent: 'center'
              }}
            >
              1
            </Typography>
            <Typography sx={{ fontSize: 'h4.fontSize', fontWeight: 'bold', color: 'primary.main', ml: 1 }}>
              报名制
            </Typography>
          </Box>
          <Typography
            sx={{
              fontSize: 'body2.fontSize',
              color: 'text.secondary',
              pl: 4
            }}
          >
            学生提交报名，报名后直接获得参与活动资格。
          </Typography>
        </Box>
        <Box sx={{ pb: 1 }}>
          <Box sx={{ display: 'flex', alignItems: 'center', py: 1 }}>
            <Typography
              sx={{
                color: 'white',
                backgroundColor: 'primary.main',
                height: '24px',
                width: '24px',
                fontSize: 'h4.fontSize',
                fontWeight: 'bold',
                borderRadius: '50%',
                display: 'flex',
                alignItems: 'center',
                justifyContent: 'center'
              }}
            >
              2
            </Typography>
            <Typography sx={{ fontSize: 'h4.fontSize', fontWeight: 'bold', color: 'primary.main', ml: 1 }}>
              审核制
            </Typography>
          </Box>
          <Typography
            sx={{
              fontSize: 'body2.fontSize',
              color: 'text.secondary',
              pl: 4
            }}
          >
            活动创建者拥有审核权限。
          </Typography>
          <Typography
            sx={{
              fontSize: 'body2.fontSize',
              color: 'text.secondary',
              pl: 4
            }}
          >
            学生提交报名，经报名审核人审核后，仅被批准通过的学生获得参与活动资格。
          </Typography>
        </Box>
        {/* <Box sx={{ pb: 1 }}>
          <Box sx={{ display: 'flex', alignItems: 'center', py: 1 }}>
            <Typography
              sx={{
                color: 'white',
                backgroundColor: 'primary.main',
                height: '24px',
                width: '24px',
                fontSize: 'h4.fontSize',
                fontWeight: 'bold',
                borderRadius: '50%',
                display: 'flex',
                alignItems: 'center',
                justifyContent: 'center'
              }}
            >
              3
            </Typography>
            <Typography sx={{ fontSize: 'h4.fontSize', fontWeight: 'bold', color: 'primary.main', ml: 1 }}>
              随机制
            </Typography>
          </Box>
          <Typography
            sx={{
              fontSize: 'body2.fontSize',
              color: 'text.secondary',
              pl: 4
            }}
          >
            学生提交报名，经管理员点击随机审核按钮后，自动随机审核学生，仅被批准的学生获得参与活动资格。
          </Typography>
        </Box> */}
      </Modal>
    </>
  )
})

export const WJForm = forwardRef((props: any, ref) => {
  const groupContentRef = useRef(null)
  const [form1] = Form.useForm()
  const { PickerController, disabled } = props

  const location: any = useLocation()
  const navigate = useNavigate()
  const template = location.state && location.state.template
  const [form2] = Form.useForm()
  const [saveLoading, setSaveLoading] = useState(0)
  const workflowRef = useRef(null)
  const Updater = useContext<any>(UpdateContext)

  const [{ define, id, status }, updater] = Updater
  const {
    settings = {
      name: (location.state && location.state.name) || undefined,
      //alertMsg: '',
      templateSettings: {},
      desc: '',
      //isCancel: false,
      imgs: [],
      psq: { nametype: 'SM' }
    },
    page,
    workflow
  } = define
  const [cancelOpen, setCancelOpen] = useState(settings && settings.isCancel)
  const [workflowData, setWorkflowData] = useState()
  const [formbase, setFormbase] = useState(
    settings
      ? {
          name: (settings && settings.name) || undefined,
          desc: (settings && settings.desc) || undefined,
          participantDetail: (settings && settings.participantDetail) || undefined,
          daterange: (settings && settings.daterange) || undefined,
          startDateTime: (settings && settings.startDateTime) || undefined,
          imgs: (settings && settings.imgs) || undefined,
          endDateTime: (settings && settings.endDateTime) || undefined,
          successTip: settings && settings.templateSettings?.successTip,
          btnText: settings && settings.templateSettings?.btnText
          //isCancel: (settings && settings.isCancel) || false,
          //canCancelHour: (settings && settings.canCancelHour) || undefined
          //isTicket: (settings && settings.isTicket) || '',
          //maxParticipantNum: (settings && settings.maxParticipantNum) || ''
        }
      : {
          name: (template && template.settings.name) || (location.state && location.state.name) || undefined,
          desc: (template && template.settings.desc) || undefined,
          participantDetail: (template && template.settings.participantDetail) || undefined,
          imgs: (template && template.settings.imgs) || undefined
          //isTicket: template.settings.isTicket || '',
          //maxParticipantNum: template.settings.maxParticipantNum || ''
        }
  )
  const [limitOpen, setLimitOpen] = useState((settings && settings.applylimit) || false)
  const [tagtipopen, setTagtipopen] = useState(false)
  const [applyAdminOpen, setApplyAdminOpen] = useState(settings && settings.type)
  console.log('表单数据', settings, location.state)
  const { loading: setLoading, runAsync: saveSetting } = useEasyFormRequest((params: any) => saveActivity(params, id), {
    manual: true,
    onSuccess: (res: any) => {
      setSaveLoading(0)
      message.success('修改成功！')
      if (!status?.enable) {
        Dialog.confirm({
          title: '提示',
          content: '保存成功，是否立即发布',
          onConfirm: async () => {
            try {
              await toggleActivity2()
              updater({ type: 'updateStatus', payload: { status: 1 } })
              message.success('发布成功')
            } catch (e) {
              message.error('发布失败')
            }
          }
        })
      }
      //新建表单需要更新id值，更新表单需要更新顶层组件define
      if (!id) {
        updater({ type: 'build', payload: { id: res.data } })
        navigate('/pc/designForm', {
          state: {
            ...(location.state ? location.state : {}),
            id: res.data,
            type: location.state?.type || 'APPLY'
          },
          replace: true
        })
        //updater({ type: 'build', payload: { id: res.data } })
        console.log(res.data)
      }
    },
    onError: () => {
      setSaveLoading(0)
    }
    //ready: Boolean(location.state && location.state.id)
  })

  const {
    runAsync: fetchData,
    data: activityStatus,
    loading: activityLoading,
    refreshAsync: refreshActivity
  } = useEasyFormRequest(() => getActivityList({ id: id }), {
    //manual: true,
    ready: Boolean(id),
    refreshDeps: [],
    onSuccess: (data: any) => {
      updater({ type: 'updateStatus', payload: { status: data.data[0] } })
    }
  })
  const { runAsync: toggleActivity, loading: toggleLoading } = useEasyFormRequest(() => toggleActivityEnable(id), {
    manual: true,
    ready: Boolean(id),
    onSuccess: (data: any) => {
      refreshActivity()
    },
    onError: () => {}
  })
  const { runAsync: toggleActivity2, loading: toggleLoading2 } = useEasyFormRequest(() => toggleActivityEnable(id), {
    manual: true,
    ready: Boolean(id),
    onSuccess: (data: any) => {},
    onError: () => {}
  })

  /**
   * @description 重新发布当前表单(就是调用两次接口)
   */
  const rePublish = async () => {
    await toggleActivity2()
    await toggleActivity()
  }
  const [initialize, instance] = useOverlayScrollbars({ options: { scrollbars: { autoHide: 'scroll' } } })
  useEffect(() => {
    groupContentRef.current && initialize(groupContentRef.current)
  }, [initialize])
  const onTagTipClose = () => {
    setTagtipopen(false)
  }
  const UploadButton = (
    <Box
      ref={ref}
      sx={{
        height: '100px',
        width: '100px',
        background: '#f8f8f8',
        cursor: 'pointer',
        p: 2,
        justifyContent: 'center',
        display: 'flex',
        flexDirection: 'column',
        alignItems: 'center',
        fontSize: 24,
        fontWeight: 'bold'
      }}
    >
      {tagtipopen ? <LoadingOutlined /> : <PlusOutlined />}
    </Box>
  )
  const handleReset = (form: any, resetWorkflow = false) => {
    form.resetFields()
    if (resetWorkflow) {
      workflowRef.current && (workflowRef.current as any).reset()
    }
  }

  const ToJson = (value: any, mergebase = false): any => {
    const data = {
      define: {
        settings: settings || { apply: {} },
        page: page || { componentTree: [] },
        workflow: workflow || {}
      }
    }
    //格式化日期
    if (!mergebase) {
      value.startDateTime =
        (value.activitytimerange &&
          value.activitytimerange[0] &&
          dayjs(value.activitytimerange[0].$d).format('YYYY-MM-DD HH:mm:ss')) ||
        undefined
      value.endDateTime =
        (value.activitytimerange &&
          value.activitytimerange[1] &&
          dayjs(value.activitytimerange[1].$d).format('YYYY-MM-DD HH:mm:ss')) ||
        undefined
      value.daterange = [value.startDateTime || undefined, value.endDateTime || undefined]
      delete value.activitytimerange
    }
    if (value.participantDetail) {
      value.participant = transformParticipant(value.participantDetail)
    } else {
      delete value.participant
      delete value.participantDetail
    }
    if (mergebase) {
      data.define.settings = { ...data.define.settings, ...formbase }
      data.define.settings.psq = { ...data.define.settings.psq, ...value }
    } else {
      data.define.settings = { ...data.define.settings, ...value }
    }
    //未开启人员限制则删除该属性
    if (!limitOpen) {
      delete data.define.settings?.psq?.maxParticipantNum
    }
    if (!cancelOpen) {
      delete data.define.settings?.canCancelHour
    }
    //提示消息设置到templateSettings下
    if (data.define.settings.templateSettings) {
      data.define.settings.templateSettings = {
        ...data.define.settings.templateSettings,
        successTip: data.define.settings.successTip || undefined,
        btnText: data.define.settings.btnText || undefined
      }
    } else {
      data.define.settings.templateSettings = {
        successTip: data.define.settings.successTip,
        btnText: data.define.settings.btnText
      }
    }

    delete data.define.settings?.successTip
    delete data.define.settings?.btnText
    //使用了模板
    if (location.state?.templateId) {
      ;(data.define.settings as any).templateId = location.state.templateId
    }
    //设置表单类型
    if (location.state?.type) {
      ;(data.define.settings as any).type = location.state.type
    }
    //保存基础属性时同时保存流程
    if (!mergebase) {
      data.define.workflow = workflowData
    }
    return data
  }
  const handleSubmit = (value: any, form: any) => {
    setSaveLoading(form)
    if (form == 1) {
      const params = ToJson(value)

      saveSetting(params)
      //缓存最新的基础信息
      setFormbase({ ...formbase, ...value })
      updater({ type: 'updateSettings', payload: params })
      updater({ type: 'updateWorkflow', payload: params })
      console.log('数据合并', { ...formbase, ...params.define.settings })
    }
    //其他表单提交需要附带上表单的必填信息
    if (form != 1) {
      const params = ToJson(value, true)
      saveSetting(params)
      updater({ type: 'updateSettings', payload: params })
    }
  }
  useEffect(() => {
    form1 &&
      form1.setFieldsValue({
        ...formbase,
        name: settings?.name,
        desc: settings?.desc,
        participantDetail: settings?.participantDetail,
        successTip: settings?.templateSettings?.successTip,
        imgs: settings?.imgs,
        btnText: settings?.templateSettings?.btnText,
        activitytimerange: settings?.daterange?.length && [
          (settings.daterange[0] && dayjs(settings.daterange[0])) || undefined,
          (settings.daterange[1] && dayjs(settings.daterange[1])) || undefined
        ]
      })
  }, [
    formbase,
    form1,
    settings?.name,
    settings?.desc,
    settings?.participantDetail,
    settings?.templateSettings?.successTip,
    settings?.templateSettings?.btnText,
    settings?.imgs,
    settings.daterange
  ])
  useEffect(() => {
    form2 &&
      form2.setFieldsValue({
        // alertMsg: settings.psq?.alertMsg || (template && template.settings?.psq?.alertMsg) || undefined,
        // isCancel: settings.psq?.isCancel || (template && template.settings?.psq?.isCancel) || false
        nametype: settings.psq?.nametype || (template && template.settings?.psq?.nametype) || 'SM'
      })
  }, [form2, settings.psq?.nametype, template])
  return (
    <>
      {/* {disabled ? <NoticeBar content='当前表单已发布，若要修改请先取消发布' color='alert' closeable /> : ''} */}
      <Box sx={{ width: '100%' }}>
        <Card
          bordered={false}
          style={{ boxShadow: 'none' }}
          title={
            <Box>
              <Typography
                onClick={() => {
                  console.log(settings, location)
                }}
                sx={{ fontWeight: 600, fontSize: 'h3.fontSize' }}
              >
                基本配置
              </Typography>
            </Box>
          }
        >
          <Form
            disabled={disabled}
            form={form1}
            initialValues={{ imgs: [], participantDetail: {} }}
            labelCol={{ span: 4 }}
            wrapperCol={{ span: 14 }}
            layout='vertical'
            //disabled={componentDisabled}
            style={{ maxWidth: '100%' }}
          >
            <Form.Item required label='问卷标题' rules={[{ required: true, message: '请输入问卷标题' }]} name='name'>
              <Input placeholder='请输入标题'></Input>
            </Form.Item>
            <Form.Item label='问卷说明' rules={[{ required: false, message: '请输入问卷描述' }]} name='desc'>
              <TextArea placeholder='请输入问卷说明' autoSize={{ minRows: 2, maxRows: 6 }}></TextArea>
            </Form.Item>
            <Form.Item hidden label='问卷封面' rules={[{ required: false, message: '请指定问卷封面' }]} name={'imgs'}>
              <ImageUploader
                imageUrlPrefix={isLocalSource(form1.getFieldValue('imgs')?.[0]) ? '' : undefined}
                disabled={disabled}
                maxCount={1}
              />
              {/* <Upload accept='.jpg,.png,.ragif,.jpge'>
                {tagtipopen ? (
                  <Image
                    src={''}
                    preview={false}
                    width={150}
                    alt='avatar'
                    fallback='data:image/png;base64,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'
                  />
                ) : (
                  UploadButton
                )}
              </Upload> */}
            </Form.Item>
            <Form.Item
              label='问卷时间'
              rules={[{ required: false, message: '请设置问卷时间' }]}
              name='activitytimerange'
            >
              <RangePicker allowEmpty={[true, true]} showTime separator={'~'}></RangePicker>
            </Form.Item>
            <Form.Item hidden name={'participant'}>
              <Input></Input>
            </Form.Item>

            <Form.Item
              label='发布范围'
              name='participantDetail'
              rules={[{ required: true, message: '请指定发布范围' }]}
            >
              <RoleSelect
                disabled={disabled}
                orgCode={PickerController.orgCode}
                placeholder={'请选择人员'}
                isModal={true}
                MenuItem={PickerController.MenuItem}
                modalWidth={800}
              ></RoleSelect>
            </Form.Item>
            <Form.Item label='流程审批'>
              <WorkFlow
                ref={workflowRef}
                defaultWorkflow={workflow}
                disabled={disabled}
                onChange={(value: any) => {
                  setWorkflowData(value)
                  //handleWorkFlowChange({ define: { workflow: value } })
                }}
              ></WorkFlow>
            </Form.Item>
            <Form.Item label='按钮文本' name='btnText'>
              <Input placeholder='请输入提交按钮显示的文本'></Input>
            </Form.Item>
            <Form.Item label='提交成功提示' name='successTip'>
              <Input placeholder='请输入提交成功后显示的提示文字'></Input>
            </Form.Item>
            {/* <Form.Item label={'结束提醒'} name={'isCancel'}>
              <Radio.Group
                onChange={(e: any) => {
                  setCancelOpen(e.target.value)
                }}
              >
                <Radio value={false}>关闭</Radio>
                <Radio value={true}>开启</Radio>
              </Radio.Group>
            </Form.Item>
            {cancelOpen ? (
              <Form.Item label='提醒时间'>
                <Typography
                  sx={{
                    fontSize: 'body2.fontSize',
                    display: 'flex',
                    alignItems: 'center',
                    color: 'primary.main',
                    fontWeight: 'bold',
                    '.canceltime': {
                      margin: 0
                    }
                  }}
                >
                  在问卷调查结束
                  <Form.Item
                    required
                    rules={[{ required: true, message: '请指定可取消时间' }]}
                    name='canCancelHour'
                    className='canceltime'
                  >
                    <InputNumber min={0} style={{ width: 150, margin: '0 8px' }} placeholder='请输入小时'></InputNumber>
                  </Form.Item>
                  小时前，再次使用消息提醒未填写学生。
                </Typography>
              </Form.Item>
            ) : (
              ''
            )} */}
            <Form.Item label={' '} colon={false}>
              <Box sx={{ display: 'flex' }}>
                <Button
                  style={{ marginRight: 24 }}
                  onClick={() => {
                    handleReset(form1, true)
                  }}
                  type='default'
                  disabled={setLoading || disabled}
                  htmlType='submit'
                >
                  重置
                </Button>
                <Button
                  type='primary'
                  loading={saveLoading == 1 && setLoading}
                  onClick={() => {
                    form1.validateFields().then(() => {
                      // if (id && status?.enable == 1) {
                      //   confirm({
                      //     title: (
                      //       <Typography sx={{ fontWeight: 'bold', fontSize: 'h1.fontSize' }}>当前表单已发布</Typography>
                      //     ),
                      //     icon: <ExclamationCircleFilled />,
                      //     maskClosable: true,
                      //     content: (
                      //       <Box>
                      //         <Typography sx={{}}>已发布的表单修改后需要重新发布才会生效，是否确定修改？</Typography>
                      //       </Box>
                      //     ),
                      //     onOk() {
                      //       toggleActivity()
                      //       handleSubmit(form1.getFieldsValue(), 1)
                      //     },
                      //     onCancel() {
                      //       console.log('Cancel')
                      //     }
                      //   })
                      // } else {
                      //   handleSubmit(form1.getFieldsValue(), 1)
                      // }
                      handleSubmit(form1.getFieldsValue(), 1)
                      if (id && status.enable == 1) {
                        // rePublish()
                        // console.log('rePublish=====')
                      }
                    })
                  }}
                  htmlType='submit'
                >
                  保存
                </Button>
              </Box>
            </Form.Item>
          </Form>
        </Card>
        <Card
          style={{ marginTop: '24px', boxShadow: 'none' }}
          bordered={false}
          title={
            <Box>
              <Typography sx={{ fontWeight: 600, fontSize: 'h3.fontSize' }}>高级设置</Typography>
            </Box>
          }
        >
          <Form
            form={form2}
            disabled={disabled}
            labelCol={{ span: 4 }}
            wrapperCol={{ span: 14 }}
            layout='vertical'
            //disabled={componentDisabled}
            style={{ maxWidth: '100%' }}
          >
            {/* <Form.Item label='报名时间' name='applytime'>
              <RangePicker showTime separator={'~'}></RangePicker>
            </Form.Item> */}

            <Form.Item label='填写方式' initialValue={'SM'} name='nametype'>
              <Radio.Group
                style={{ display: 'flex', alignItems: 'center' }}
                onChange={(e: any) => {
                  const value = e.target.value
                  setApplyAdminOpen(value)
                }}
              >
                <Radio value={'SM'}>{PARTYPES.SM}</Radio>
                <Radio value={'NM'}>{PARTYPES.NM}</Radio>
                {/* <Radio value={'sjz'}>{APPLYTYPES.sjz}</Radio> */}
                <Box
                  sx={{
                    cursor: 'pointer',
                    '.tagtip': {
                      backgroundColor: 'primary.light',
                      color: 'primary.main',
                      p: 0.5,
                      px: 1,
                      fontSize: 'body2.fontSize'
                    }
                  }}
                >
                  <Popover content={'匿名方式对管理员不生效'}>
                    <Button shape='circle' size='small' icon={<QuestionOutlined />} />
                  </Popover>
                </Box>
              </Radio.Group>
            </Form.Item>

            <Form.Item label={' '} colon={false}>
              <Box sx={{ display: 'flex' }}>
                <Button
                  style={{ marginRight: 24 }}
                  onClick={() => {
                    handleReset(form2)
                  }}
                  type='default'
                  htmlType='submit'
                  disabled={setLoading || disabled}
                >
                  重置
                </Button>
                <Button
                  type='primary'
                  loading={saveLoading == 2 && setLoading}
                  onClick={() => {
                    form1.validateFields().then(() => {
                      form2.validateFields().then(() => {
                        // if (id && status?.enable == 1) {
                        //   confirm({
                        //     title: (
                        //       <Typography sx={{ fontWeight: 'bold', fontSize: 'h1.fontSize' }}>
                        //         当前表单已发布
                        //       </Typography>
                        //     ),
                        //     icon: <ExclamationCircleFilled />,
                        //     maskClosable: true,
                        //     content: (
                        //       <Box>
                        //         <Typography sx={{}}>已发布的表单修改后需要重新发布才会生效，是否确定修改？</Typography>
                        //       </Box>
                        //     ),
                        //     onOk() {
                        //       toggleActivity()
                        //       handleSubmit(form2.getFieldsValue(), 2)
                        //     },
                        //     onCancel() {
                        //       console.log('Cancel')
                        //     }
                        //   })
                        // } else {
                        //   handleSubmit(form2.getFieldsValue(), 2)
                        // }
                        handleSubmit(form2.getFieldsValue(), 2)
                        if (id && status.enable == 1) {
                          // rePublish()
                          // console.log('rePublish=====')
                        }
                      })
                    })
                  }}
                  htmlType='submit'
                >
                  保存
                </Button>
              </Box>
            </Form.Item>
          </Form>
        </Card>
      </Box>
    </>
  )
})
export const TZForm = forwardRef((props: any, ref) => {
  const groupContentRef = useRef(null)
  const [form1] = Form.useForm()
  const { PickerController, disabled } = props

  const location: any = useLocation()
  const navigate = useNavigate()
  const template = location.state && location.state.template
  //const [form2] = Form.useForm()
  const [saveLoading, setSaveLoading] = useState(0)
  const workflowRef = useRef(null)
  const Updater = useContext<any>(UpdateContext)

  const [{ define, id, status }, updater] = Updater
  const {
    settings = {
      name: (location.state && location.state.name) || undefined,
      //alertMsg: '',
      templateSettings: {},
      desc: '',
      //isCancel: false,
      imgs: []
    },
    page,
    workflow
  } = define
  const [cancelOpen, setCancelOpen] = useState(settings && settings.isCancel)
  const [workflowData, setWorkflowData] = useState()
  const [formbase, setFormbase] = useState(
    settings
      ? {
          name: (settings && settings.name) || undefined,
          desc: (settings && settings.desc) || undefined,
          participantDetail: (settings && settings.participantDetail) || undefined,
          daterange: (settings && settings.daterange) || undefined,
          startDateTime: (settings && settings.startDateTime) || undefined,
          imgs: (settings && settings.imgs) || undefined,
          endDateTime: (settings && settings.endDateTime) || undefined,
          successTip: settings && settings.templateSettings?.successTip,
          btnText: settings && settings.templateSettings?.btnText
          //isCancel: (settings && settings.isCancel) || false,
          //canCancelHour: (settings && settings.canCancelHour) || undefined
          //isTicket: (settings && settings.isTicket) || '',
          //maxParticipantNum: (settings && settings.maxParticipantNum) || ''
        }
      : {
          name: (template && template.settings.name) || (location.state && location.state.name) || undefined,
          desc: (template && template.settings.desc) || undefined,
          participantDetail: (template && template.settings.participantDetail) || undefined,
          imgs: (template && template.settings.imgs) || undefined
          //isTicket: template.settings.isTicket || '',
          //maxParticipantNum: template.settings.maxParticipantNum || ''
        }
  )
  const [limitOpen, setLimitOpen] = useState((settings && settings.applylimit) || false)
  const [tagtipopen, setTagtipopen] = useState(false)
  const [applyAdminOpen, setApplyAdminOpen] = useState(settings && settings.type)
  console.log('表单数据', settings, location.state)
  const { loading: setLoading, runAsync: saveSetting } = useEasyFormRequest((params: any) => saveActivity(params, id), {
    manual: true,
    onSuccess: (res: any) => {
      setSaveLoading(0)
      message.success('修改成功！')
      if (!status?.enable) {
        Dialog.confirm({
          title: '提示',
          content: '保存成功，是否立即发布',
          onConfirm: async () => {
            try {
              await toggleActivity2()
              message.success('发布成功')
              updater({ type: 'updateStatus', payload: { status: 1 } })
            } catch (e) {
              message.error('发布失败')
            }
          }
        })
      }
      //新建表单需要更新id值，更新表单需要更新顶层组件define
      if (!id) {
        updater({ type: 'build', payload: { id: res.data } })
        navigate('/pc/designForm', {
          state: {
            ...(location.state ? location.state : {}),
            id: res.data,
            type: location.state?.type || 'APPLY'
          },
          replace: true
        })
        //updater({ type: 'build', payload: { id: res.data } })
        console.log(res.data)
      }
    },
    onError: () => {
      setSaveLoading(0)
    }
    //ready: Boolean(location.state && location.state.id)
  })

  const {
    runAsync: fetchData,
    data: activityStatus,
    loading: activityLoading,
    refreshAsync: refreshActivity
  } = useEasyFormRequest(() => getActivityList({ id: id }), {
    //manual: true,
    ready: Boolean(id),
    refreshDeps: [],
    onSuccess: (data: any) => {
      updater({ type: 'updateStatus', payload: { status: data.data[0] } })
    }
  })
  const { runAsync: toggleActivity, loading: toggleLoading } = useEasyFormRequest(() => toggleActivityEnable(id), {
    manual: true,
    ready: Boolean(id),
    onSuccess: (data: any) => {
      refreshActivity()
    },
    onError: () => {}
  })
  const { runAsync: toggleActivity2, loading: toggleLoading2 } = useEasyFormRequest(() => toggleActivityEnable(id), {
    manual: true,
    ready: Boolean(id),
    onSuccess: (data: any) => {},
    onError: () => {}
  })

  /**
   * @description 重新发布当前表单(就是调用两次接口)
   */
  const rePublish = async () => {
    await toggleActivity2()
    await toggleActivity()
  }
  const [initialize, instance] = useOverlayScrollbars({ options: { scrollbars: { autoHide: 'scroll' } } })
  useEffect(() => {
    groupContentRef.current && initialize(groupContentRef.current)
  }, [initialize])
  const onTagTipClose = () => {
    setTagtipopen(false)
  }
  const UploadButton = (
    <Box
      ref={ref}
      sx={{
        height: '100px',
        width: '100px',
        background: '#f8f8f8',
        cursor: 'pointer',
        p: 2,
        justifyContent: 'center',
        display: 'flex',
        flexDirection: 'column',
        alignItems: 'center',
        fontSize: 24,
        fontWeight: 'bold'
      }}
    >
      {tagtipopen ? <LoadingOutlined /> : <PlusOutlined />}
    </Box>
  )
  const handleReset = (form: any, resetWorkflow = false) => {
    form.resetFields()
    if (resetWorkflow) {
      workflowRef.current && (workflowRef.current as any).reset()
    }
  }

  const ToJson = (value: any, mergebase = false): any => {
    const data = {
      define: {
        settings: settings || { apply: {} },
        page: page || { componentTree: [] },
        workflow: workflow || {}
      }
    }
    //格式化日期
    if (!mergebase) {
      value.startDateTime =
        (value.activitytimerange &&
          value.activitytimerange[0] &&
          dayjs(value.activitytimerange[0].$d).format('YYYY-MM-DD HH:mm:ss')) ||
        undefined
      value.endDateTime =
        (value.activitytimerange &&
          value.activitytimerange[1] &&
          dayjs(value.activitytimerange[1].$d).format('YYYY-MM-DD HH:mm:ss')) ||
        undefined
      value.daterange = [value.startDateTime || undefined, value.endDateTime || undefined]
      delete value.activitytimerange
    }
    if (value.participantDetail) {
      value.participant = transformParticipant(value.participantDetail)
    } else {
      delete value.participant
      delete value.participantDetail
    }
    if (mergebase) {
      data.define.settings = { ...data.define.settings, ...formbase }
      //data.define.settings.psq = { ...data.define.settings.psq, ...value }
    } else {
      data.define.settings = { ...data.define.settings, ...value }
    }
    //未开启人员限制则删除该属性
    // if (!limitOpen) {
    //   delete data.define.settings?.psq?.maxParticipantNum
    // }
    if (!cancelOpen) {
      delete data.define.settings?.canCancelHour
    }
    //提示消息设置到templateSettings下
    if (data.define.settings.templateSettings) {
      data.define.settings.templateSettings = {
        ...data.define.settings.templateSettings,
        successTip: data.define.settings.successTip || undefined,
        btnText: data.define.settings.btnText || undefined
      }
    } else {
      data.define.settings.templateSettings = {
        successTip: data.define.settings.successTip,
        btnText: data.define.settings.btnText
      }
    }

    delete data.define.settings?.successTip
    delete data.define.settings?.btnText
    //使用了模板
    if (location.state?.templateId) {
      ;(data.define.settings as any).templateId = location.state.templateId
    }
    //设置表单类型
    if (location.state?.type) {
      ;(data.define.settings as any).type = location.state.type
    }
    //保存基础属性时同时保存流程
    if (!mergebase) {
      data.define.workflow = workflowData
    }
    return data
  }
  const handleSubmit = (value: any, form: any) => {
    setSaveLoading(form)
    if (form == 1) {
      const params = ToJson(value)

      saveSetting(params)
      //缓存最新的基础信息
      setFormbase({ ...formbase, ...value })
      updater({ type: 'updateSettings', payload: params })
      updater({ type: 'updateWorkflow', payload: params })
      console.log('数据合并', { ...formbase, ...params.define.settings })
    }
    //其他表单提交需要附带上表单的必填信息
    if (form != 1) {
      const params = ToJson(value, true)
      saveSetting(params)
      updater({ type: 'updateSettings', payload: params })
    }
  }
  useEffect(() => {
    form1 &&
      form1.setFieldsValue({
        ...formbase,
        imgs: settings?.imgs,
        btnText: settings?.templateSettings?.btnText,
        activitytimerange: settings &&
          settings.daterange?.length && [
            (settings.daterange[0] && dayjs(settings.daterange[0])) || undefined,
            (settings.daterange[1] && dayjs(settings.daterange[1])) || undefined
          ]
      })
  }, [formbase, form1, settings])
  // useEffect(() => {
  //   form2 &&
  //     form2.setFieldsValue({
  //       nametype: settings.psq?.nametype || (template && template.settings?.psq?.nametype) || 'SM'
  //     })
  // }, [form2, settings, template])
  return (
    <>
      {disabled ? <NoticeBar content='当前公告已发布，若要修改请先取消发布' color='alert' closeable /> : ''}
      <Box sx={{ width: '100%', height: '100%' }}>
        <Card
          bordered={false}
          style={{ boxShadow: 'none', height: '100%' }}
          title={
            <Box>
              <Typography
                onClick={() => {
                  console.log(settings, location)
                }}
                sx={{ fontWeight: 600, fontSize: 'h3.fontSize' }}
              >
                基本配置
              </Typography>
            </Box>
          }
        >
          <Form
            form={form1}
            disabled={disabled}
            initialValues={{ imgs: [], participantDetail: {} }}
            labelCol={{ span: 4 }}
            wrapperCol={{ span: 14 }}
            layout='vertical'
            //disabled={componentDisabled}
            style={{ maxWidth: '100%' }}
          >
            <Form.Item required label='标题' rules={[{ required: true, message: '请输入标题' }]} name='name'>
              <Input placeholder='请输入标题'></Input>
            </Form.Item>
            <Form.Item label='说明' rules={[{ required: false, message: '请输入描述' }]} name='desc'>
              <TextArea placeholder='请输入说明' autoSize={{ minRows: 2, maxRows: 6 }}></TextArea>
            </Form.Item>
            {/* <Form.Item label='通告封面' rules={[{ required: false, message: '请指定封面' }]} name={'imgs'}>
              <ImageUploader maxCount={1} />
             
            </Form.Item> */}
            <Form.Item
              label='发布时间'
              rules={[{ required: false, message: '请设置发布时间' }]}
              name='activitytimerange'
            >
              <RangePicker allowEmpty={[true, true]} showTime separator={'~'}></RangePicker>
            </Form.Item>
            <Form.Item hidden name={'participant'}>
              <Input></Input>
            </Form.Item>

            <Form.Item
              label='发布范围'
              name='participantDetail'
              rules={[{ required: true, message: '请指定发布范围' }]}
            >
              <RoleSelect
                disabled={disabled}
                orgCode={PickerController.orgCode}
                placeholder={'请选择人员'}
                isModal={true}
                MenuItem={PickerController.MenuItem}
                modalWidth={800}
              ></RoleSelect>
            </Form.Item>
            <Form.Item hidden label='流程审批'>
              <WorkFlow
                disabled={disabled}
                ref={workflowRef}
                defaultWorkflow={workflow}
                onChange={(value: any) => {
                  setWorkflowData(value)
                  //handleWorkFlowChange({ define: { workflow: value } })
                }}
              ></WorkFlow>
            </Form.Item>
            {/* <Form.Item label='按钮文本' name='btnText'>
              <Input placeholder='请输入提交按钮显示的文本'></Input>
            </Form.Item>
            <Form.Item label='提交成功提示' name='successTip'>
              <Input placeholder='请输入提交成功后显示的提示文字'></Input>
            </Form.Item> */}
            {/* <Form.Item label={'结束提醒'} name={'isCancel'}>
              <Radio.Group
                onChange={(e: any) => {
                  setCancelOpen(e.target.value)
                }}
              >
                <Radio value={false}>关闭</Radio>
                <Radio value={true}>开启</Radio>
              </Radio.Group>
            </Form.Item>
            {cancelOpen ? (
              <Form.Item label='提醒时间'>
                <Typography
                  sx={{
                    fontSize: 'body2.fontSize',
                    display: 'flex',
                    alignItems: 'center',
                    color: 'primary.main',
                    fontWeight: 'bold',
                    '.canceltime': {
                      margin: 0
                    }
                  }}
                >
                  在问卷调查结束
                  <Form.Item
                    required
                    rules={[{ required: true, message: '请指定可取消时间' }]}
                    name='canCancelHour'
                    className='canceltime'
                  >
                    <InputNumber min={0} style={{ width: 150, margin: '0 8px' }} placeholder='请输入小时'></InputNumber>
                  </Form.Item>
                  小时前，再次使用消息提醒未填写学生。
                </Typography>
              </Form.Item>
            ) : (
              ''
            )} */}
            <Form.Item label={' '} colon={false}>
              <Box sx={{ display: 'flex' }}>
                <Button
                  style={{ marginRight: 24 }}
                  onClick={() => {
                    handleReset(form1, true)
                  }}
                  type='default'
                  disabled={setLoading || disabled}
                  htmlType='submit'
                >
                  重置
                </Button>
                <Button
                  type='primary'
                  loading={saveLoading == 1 && setLoading}
                  onClick={() => {
                    form1.validateFields().then(() => {
                      // if (id && status?.enable == 1) {
                      //   confirm({
                      //     title: (
                      //       <Typography sx={{ fontWeight: 'bold', fontSize: 'h1.fontSize' }}>当前表单已发布</Typography>
                      //     ),
                      //     icon: <ExclamationCircleFilled />,
                      //     maskClosable: true,
                      //     content: (
                      //       <Box>
                      //         <Typography sx={{}}>已发布的表单修改后需要重新发布才会生效，是否确定修改？</Typography>
                      //       </Box>
                      //     ),
                      //     onOk() {
                      //       toggleActivity()
                      //       handleSubmit(form1.getFieldsValue(), 1)
                      //     },
                      //     onCancel() {
                      //       console.log('Cancel')
                      //     }
                      //   })
                      // } else {
                      //   handleSubmit(form1.getFieldsValue(), 1)
                      // }
                      handleSubmit(form1.getFieldsValue(), 1)
                      if (id && status.enable == 1) {
                        // rePublish()
                        // console.log('rePublish=====')
                      }
                    })
                  }}
                  htmlType='submit'
                >
                  保存
                </Button>
              </Box>
            </Form.Item>
          </Form>
        </Card>
      </Box>
    </>
  )
})
export const XXForm = forwardRef((props: any, ref) => {
  const groupContentRef = useRef(null)
  const [form1] = Form.useForm()
  const location: any = useLocation()
  const { PickerController, disabled } = props
  const navigate = useNavigate()
  const template = location.state && location.state.template
  const [form2] = Form.useForm()
  const [saveLoading, setSaveLoading] = useState(0)
  const Updater = useContext<any>(UpdateContext)
  const workflowRef = useRef(null)
  const [{ define, id, status, taskInfo }, updater] = Updater
  console.log('taskInfo====',taskInfo)
  const {
    settings = {
      name: (location.state && location.state.name) || undefined,
      //alertMsg: '',
      templateSettings: {},
      desc: '',
      //isCancel: false,
      imgs: [],
      info: { nametype: 'SM' }
    },
    page,
    workflow
  } = define
  const [enableTask, setToggleTaskButton] = useState(false)
  useEffect(() => setToggleTaskButton(Boolean(taskInfo && taskInfo.detail)), [taskInfo])

  const [cancelOpen, setCancelOpen] = useState(settings && settings.isCancel)
  const [workflowData, setWorkflowData] = useState()
  const [formbase, setFormbase] = useState(
    settings
      ? {
          name: settings && settings.name,
          desc: settings && settings.desc,
          participantDetail: settings && settings.participantDetail,
          daterange: settings && settings.daterange,
          startDateTime: settings && settings.startDateTime,
          imgs: settings && settings.imgs,
          endDateTime: settings && settings.endDateTime,
          successTip: settings && settings.templateSettings?.successTip,
          btnText: settings && settings.templateSettings?.btnText
          //isCancel: (settings && settings.isCancel) || false,
          //canCancelHour: (settings && settings.canCancelHour) || undefined
          //isTicket: (settings && settings.isTicket) || '',
          //maxParticipantNum: (settings && settings.maxParticipantNum) || ''
        }
      : {
          name: (template && template.settings.name) || (location.state && location.state.name) || undefined,
          desc: (template && template.settings.desc) || undefined,
          participantDetail: (template && template.settings.participantDetail) || {},
          imgs: (template && template.settings.imgs) || undefined
          //isTicket: template.settings.isTicket || '',
          //maxParticipantNum: template.settings.maxParticipantNum || ''
        }
  )
  const [limitOpen, setLimitOpen] = useState((settings && settings.applylimit) || false)
  const [tagtipopen, setTagtipopen] = useState(false)
  const [taskCreating, setTaskCreating] = useState(false)
  const [applyAdminOpen, setApplyAdminOpen] = useState(settings && settings.type)
  console.log('表单数据', settings, location.state)
  const {
    runAsync: fetchData,
    data: activityStatus,
    loading: activityLoading,
    refreshAsync: refreshActivity
  } = useEasyFormRequest(() => getActivityList({ id: id }), {
    //manual: true,
    ready: Boolean(id),
    refreshDeps: [],
    onSuccess: (data: any) => {
      updater({ type: 'updateStatus', payload: { status: data.data[0] } })
    }
  })
  const { runAsync: toggleActivity, loading: toggleLoading } = useEasyFormRequest(() => toggleActivityEnable(id), {
    manual: true,
    ready: Boolean(id),
    onSuccess: (data: any) => {
      refreshActivity()
    },
    onError: () => {}
  })
  const { runAsync: toggleActivity2, loading: toggleLoading2 } = useEasyFormRequest(() => toggleActivityEnable(id), {
    manual: true,
    ready: Boolean(id),
    onSuccess: (data: any) => {},
    onError: () => {}
  })
  const { loading: setLoading, runAsync: saveSetting } = useEasyFormRequest((params: any) => saveActivity(params, id), {
    manual: true,
    onSuccess: (res: any) => {
      setSaveLoading(0)
      message.success('操作成功！')
      if (!status?.enable) {
        Dialog.confirm({
          title: '提示',
          content: '保存成功，是否立即发布',
          onConfirm: async () => {
            try {
              await toggleActivity2()
              message.success('发布成功')
              updater({ type: 'updateStatus', payload: { status: 1 } })
            } catch (e) {
              message.error('发布失败')
            }
          }
        })
      }
      //新建表单需要更新id值，更新表单需要更新顶层组件define
      if (!id) {
        updater({ type: 'build', payload: { id: res.data } })
        navigate('/pc/designForm', {
          state: {
            ...(location.state ? location.state : {}),
            id: res.data,
            type: location.state?.type || 'APPLY'
          },
          replace: true
        })

        console.log(res.data)
      }
      if (taskInfo && taskInfo.detail) {
        refreshTask()
      }
    },
    onError: () => {
      setSaveLoading(0)
    }
    //ready: Boolean(location.state && location.state.id)
  })

  const [initialize, instance] = useOverlayScrollbars({ options: { scrollbars: { autoHide: 'scroll' } } })
  useEffect(() => {
    groupContentRef.current && initialize(groupContentRef.current)
  }, [initialize])
  const onTagTipClose = () => {
    setTagtipopen(false)
  }
  const taskRef = useRef(null)
  const [userContext, dispach] = useAppContext()
  const userInfo = useMemo(() => {
    const { user, ybUser } = userContext
    console.log('user===',user)
    console.log('ybUser===',ybUser)
    return {
      id: user?.id,
      displayName: user?.name,
      manageOrgs: ybUser?.manageOrgs,
      classLeaderAcademy: ybUser?.classLeaderAcademy,
      manageclasses: ybUser?.manageclasses,
      // fdy: isFdyUser(ybUser?.currentIdentity),
      fdy: isCounselorUser(user?.adminType),
      // departAdmin: isDepartAdminUser(ybUser?.currentIdentity),
      departAdmin: isOrgUser(user?.adminType),
      // supper: isSupperUser(ybUser?.currentIdentity),
      supper: isAdminUser(user?.adminType),
      xgAdmin: isXgAdminUser(ybUser?.currentIdentity),
      orgs: user.orgs
    }
  }, [userContext])
  const UploadButton = (
    <Box
      ref={ref}
      sx={{
        height: '100px',
        width: '100px',
        background: '#f8f8f8',
        cursor: 'pointer',
        p: 2,
        justifyContent: 'center',
        display: 'flex',
        flexDirection: 'column',
        alignItems: 'center',
        fontSize: 24,
        fontWeight: 'bold'
      }}
    >
      {tagtipopen ? <LoadingOutlined /> : <PlusOutlined />}
    </Box>
  )
  const handleReset = (form: any, resetWorkflow = false) => {
    form.resetFields()
    if (resetWorkflow) {
      workflowRef.current && (workflowRef.current as any).reset()
    }
  }

  const ToJson = (value: any, mergebase = false): any => {
    const data = {
      define: {
        settings: settings || { apply: {} },
        page: page || { componentTree: [] },
        workflow: workflow || {}
      }
    }
    //格式化日期
    if (!mergebase) {
      value.startDateTime =
        (value.activitytimerange &&
          value.activitytimerange[0] &&
          dayjs(value.activitytimerange[0].$d).format('YYYY-MM-DD HH:mm:ss')) ||
        undefined
      value.endDateTime =
        (value.activitytimerange &&
          value.activitytimerange[1] &&
          dayjs(value.activitytimerange[1].$d).format('YYYY-MM-DD HH:mm:ss')) ||
        undefined
      value.daterange = [value.startDateTime || undefined, value.endDateTime || undefined]
      delete value.activitytimerange
    }
    if (value.participantDetail) {
      value.participant = transformParticipant(value.participantDetail)
    } else {
      if (enableTask) {
        //如果是任务则清空人员范围
        value.participant = []
        value.participantDetail = {}
      } else {
        delete value.participant
        delete value.participantDetail
      }
    }
    if (mergebase) {
      data.define.settings = { ...data.define.settings, ...formbase }
      data.define.settings.info = { ...data.define.settings.info, ...value }
    } else {
      data.define.settings = { ...data.define.settings, ...value }
    }
    //未开启人员限制则删除该属性
    if (!limitOpen) {
      delete data.define.settings?.info?.maxParticipantNum
    }
    if (!cancelOpen) {
      delete data.define.settings?.canCancelHour
    }
    //提示消息设置到templateSettings下
    if (data.define.settings.templateSettings) {
      data.define.settings.templateSettings = {
        ...data.define.settings.templateSettings,
        successTip: data.define.settings.successTip || undefined,
        btnText: data.define.settings.btnText || undefined
      }
    } else {
      data.define.settings.templateSettings = {
        successTip: data.define.settings.successTip,
        btnText: data.define.settings.btnText
      }
    }

    delete data.define.settings?.successTip
    delete data.define.settings?.btnText
    //使用了模板
    if (location.state?.templateId) {
      ;(data.define.settings as any).templateId = location.state.templateId
    }
    //设置表单类型
    if (location.state?.type) {
      ;(data.define.settings as any).type = location.state.type
    }
    //保存基础属性时同时保存流程
    if (!mergebase) {
      data.define.workflow = workflowData
    }
    return data
  }
  const handleSubmit = (value: any, form: any) => {
    setSaveLoading(form)
    if (form == 1) {
      const params = ToJson(value)

      saveSetting(params)
      //缓存最新的基础信息
      setFormbase({ ...formbase, ...value })
      updater({ type: 'updateSettings', payload: params })
      updater({ type: 'updateWorkflow', payload: params })
      console.log('数据合并', { ...formbase, ...params.define.settings })
    }
    //其他表单提交需要附带上表单的必填信息
    if (form != 1) {
      const params = ToJson(value, true)
      saveSetting(params)
      updater({ type: 'updateSettings', payload: params })
    }
  }
  useEffect(() => {
    form1 &&
      form1.setFieldsValue({
        ...formbase,
        name: settings?.name,
        desc: settings?.desc,
        participantDetail: settings?.participantDetail,
        successTip: settings?.templateSettings?.successTip,
        imgs: settings?.imgs,
        btnText: settings?.templateSettings?.btnText,
        activitytimerange: settings?.daterange?.length && [
          (settings.daterange[0] && dayjs(settings.daterange[0])) || undefined,
          (settings.daterange[1] && dayjs(settings.daterange[1])) || undefined
        ]
      })
  }, [
    formbase,
    form1,
    settings?.name,
    settings?.desc,
    settings?.participantDetail,
    settings?.templateSettings?.successTip,
    settings?.templateSettings?.btnText,
    settings?.imgs,
    settings.daterange
  ])
  /**
   * @description 重新发布当前表单(就是调用两次接口)
   */
  const rePublish = async () => {
    await toggleActivity2()
    await toggleActivity()
  }

  const { runAsync: createTask } = useBaseRequest(createFormTask, {
    manual: true,
    onSuccess: (res: any) => {
      message.success('任务创建成功')
    }
  })
  const { runAsync: updateTask } = useBaseRequest(updateFormTask, {
    manual: true,
    onSuccess: (res: any) => {
      message.success('任务更新成功')
    }
  })
  const { runAsync: getTaskData } = useBaseRequest((taskId) => getTaskInfo(taskId), {
    manual: true,
    onSuccess: (res: any) => {
      console.log('获取任务信息成功', res)
      updater({ type: 'updateTask', payload: { id: res?.id, detail: res } })
    }
  })
  const refreshTask = async () => {
    const res = await fetchData()
    const taskId = res.data[0]?.taskId
    if (taskId) {
      getTaskData(taskId)
    }
  }
  const handleTask = async (value: any, formData: any) => {
    setTaskCreating(true)
    const { process } = value
    //合并基础表单数据
    const params = ToJson(formData)
    //添加任务
    params.define.settings.info = { taskProcess: process?.length ? process : taskInfo?.detail?.process }
    //缓存最新的基础信息
    setFormbase({ ...formbase, ...formData })
    await saveSetting(params)
    //发布该表单
    //await toggleActivity()
    //更新成功获取任务id
    //refreshTask()
    updater({ type: 'updateSettings', payload: params })
    updater({ type: 'updateWorkflow', payload: params })
    console.log('处理任务表单', value, formData)
    setTaskCreating(false)
  }
  useEffect(() => {
    form2 &&
      form2.setFieldsValue({
        // alertMsg: settings.psq?.alertMsg || (template && template.settings?.psq?.alertMsg) || undefined,
        // isCancel: settings.psq?.isCancel || (template && template.settings?.psq?.isCancel) || false
        nametype: settings.info?.nametype || (template && template.settings?.info?.nametype) || 'SM'
      })
  }, [form2, settings.info?.nametype, template])
  const supportGroup = Form.useWatch('supportGroup', form1)

  console.log('userInfo===',userInfo)
  console.log('taskInfo===',taskInfo)

  return (
    <>
      {/* {disabled ? <NoticeBar content='当前表单已发布，若要修改请先取消发布' color='alert' closeable /> : ''} */}
      <Box
        sx={{
          width: '100%',
          '.taskCreatingSping .ant-spin-show-text': {
            top: '40%'
          }
        }}
      >
        <Spin tip={'任务创建中'} spinning={false} wrapperClassName={'taskCreatingSping'}>
          <Card
            bordered={false}
            style={{ boxShadow: 'none' }}
            title={
              <Box>
                <Typography
                  onClick={() => {
                    console.log(settings, location)
                  }}
                  sx={{ fontWeight: 600, fontSize: 'h3.fontSize' }}
                >
                  基本配置
                </Typography>
              </Box>
            }
          >
            <Form
              form={form1}
              disabled={disabled}
              labelCol={{ span: 4 }}
              initialValues={{ imgs: [], participantDetail: {} }}
              wrapperCol={{ span: 14 }}
              layout='vertical'
              //disabled={componentDisabled}
              style={{ maxWidth: '100%' }}
            >
              <Form.Item required label='标题' rules={[{ required: true, message: '请输入表单标题' }]} name='name'>
                <Input placeholder='请输入标题'></Input>
              </Form.Item>
              <Form.Item label='说明' rules={[{ required: false, message: '请输入表单描述' }]} name='desc'>
                <TextArea placeholder='请输入说明' autoSize={{ minRows: 2, maxRows: 6 }}></TextArea>
              </Form.Item>
              <Form.Item label='封面' hidden rules={[{ required: false, message: '请指定表单封面' }]} name={'imgs'}>
                <ImageUploader
                  imageUrlPrefix={isLocalSource(form1.getFieldValue('imgs')?.[0]) ? '' : undefined}
                  disabled={disabled}
                  maxCount={1}
                />
                {/* <Upload accept='.jpg,.png,.ragif,.jpge'>
                {tagtipopen ? (
                  <Image
                    src={''}
                    preview={false}
                    width={150}
                    alt='avatar'
                    fallback='data:image/png;base64,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'
                  />
                ) : (
                  UploadButton
                )}
              </Upload> */}
              </Form.Item>
              <Form.Item
                label='发布时间'
                rules={
                  [
                    //{ required: enableTask, message: '请设置任务时间' },
                    // {
                    //   validator: (_, value) => {
                    //     if (enableTask) {
                    //       if (!value || (!value[0] && !value[1])) {
                    //         return Promise.reject(new Error('请设置任务时间'))
                    //       }
                    //     }
                    //     return Promise.resolve()
                    //   }
                    // }
                  ]
                }
                name='activitytimerange'
              >
                <RangePicker allowEmpty={[true, true]} showTime separator={'~'}></RangePicker>
              </Form.Item>
              <Form.Item
                style={enableTask ? { marginBottom: 0 } : {}}
                labelCol={{ span: 14 }}
                rules={[{ required: !enableTask, message: '请指定发布范围' }]}
                label={
                  <Box
                    sx={{
                      display: 'flex',
                      alignItems: 'center',
                      '.ant-switch-inner': {
                        padding: '0 !important'
                      }
                    }}
                  >
                    <Typography>发布范围</Typography>
                    <Box sx={{ display: 'flex', alignItems: 'center' }}>
                      <Popover
                        content={
                          '设置下派过程，手动或自动往下一层级委派。每一层级可以下钻查看下级层级的完成情况。此设置一经开启不可取消或更改。'
                        }
                      >
                        <Button type='link'>
                          委派模式
                          <QuestionCircleOutlined />
                        </Button>
                      </Popover>
                      {/* <Segmented
                      options={[
                        {
                          label: 'List',
                          value: 'List',
                          icon: <AppstoreOutlined />
                        },
                        {
                          label: 'Kanban',
                          value: 'Kanban',
                          icon: <AppstoreOutlined />
                        }
                      ]}
                    /> */}
                      <PcSwitch
                        checked={enableTask}
                        disabled={taskInfo?.detail}
                        unCheckedChildren={'未开启'}
                        checkedChildren={'已开启'}
                        //style={{ '--width': '16px', '--height': '20px' }}
                        onChange={(value: boolean) => {
                          setToggleTaskButton(value)
                        }}
                      ></PcSwitch>
                    </Box>
                  </Box>
                }
                name='participantDetail'
              >
                {enableTask ? (
                  <Box>
                    {React.createElement(RemoteComponent, {
                      _remoteInfo: {
                        componentName: 'yiban-frontend-admin.EasyformTask',
                        version: '0.1.0'
                      },
                      task: taskInfo?.detail,
                      user: userInfo,
                      hiddenCreateButton: true,
                      ref: taskRef,
                      onCreateTask: (value: any) => {
                        // form1.validateFields().then(() => {
                        //   handleTask(value, form1.getFieldsValue())
                        // })
                        return false
                      }
                    })}
                    {/* <EasyformTask
                      task={taskInfo?.detail}
                      user={userInfo}
                      hiddenCreateButton={true}
                      ref={taskRef}
                      onCreateTask= {(value: any) => {
                        return false
                      }}
                    /> */}
                  </Box>
                ) : (
                  <RoleSelect
                    orgCode={PickerController.orgCode}
                    placeholder={'请选择人员'}
                    isModal={true}
                    disabled={disabled}
                    modalWidth={800}
                    MenuItem={PickerController.MenuItem}
                  ></RoleSelect>
                )}
              </Form.Item>

              <Form.Item label='流程审批'>
                <WorkFlow
                  disabled={disabled}
                  ref={workflowRef}
                  defaultWorkflow={workflow}
                  onChange={(value: any) => {
                    setWorkflowData(value)
                    //handleWorkFlowChange({ define: { workflow: value } })
                  }}
                ></WorkFlow>
              </Form.Item>
              <Form.Item label='按钮文本' name='btnText'>
                <Input placeholder='请输入提交按钮显示的文本'></Input>
              </Form.Item>
              <Form.Item label='提交成功提示' name='successTip'>
                <Input placeholder='请输入提交成功后显示的提示文字'></Input>
              </Form.Item>
              {/* <Form.Item valuePropName='checked' name='supportGroup' label='群组应用'>
                <Checkbox>支持群组</Checkbox>
              </Form.Item>
              {supportGroup && (
                <Form.Item name='scheduleCron' label='周期'>
                  <Input />
                </Form.Item>
              )} */}
              {/* <Form.Item label={'结束提醒'} name={'isCancel'}>
              <Radio.Group
                onChange={(e: any) => {
                  setCancelOpen(e.target.value)
                }}
              >
                <Radio value={false}>关闭</Radio>
                <Radio value={true}>开启</Radio>
              </Radio.Group>
            </Form.Item>
            {cancelOpen ? (
              <Form.Item label='提醒时间'>
                <Typography
                  sx={{
                    fontSize: 'body2.fontSize',
                    display: 'flex',
                    alignItems: 'center',
                    color: 'primary.main',
                    fontWeight: 'bold',
                    '.canceltime': {
                      margin: 0
                    }
                  }}
                >
                  在距离信息收集结束
                  <Form.Item
                    required
                    rules={[{ required: true, message: '请指定可取消时间' }]}
                    name='canCancelHour'
                    className='canceltime'
                  >
                    <InputNumber min={0} style={{ width: 150, margin: '0 8px' }} placeholder='请输入小时'></InputNumber>
                  </Form.Item>
                  小时前，再次使用消息提醒未填写学生。
                </Typography>
              </Form.Item>
            ) : (
              ''
            )} */}

              <Form.Item label={' '} colon={false}>
                <Box sx={{ display: 'flex' }}>
                  <Button
                    style={{ marginRight: 24 }}
                    onClick={() => {
                      handleReset(form1, true)
                    }}
                    type='default'
                    disabled={setLoading || disabled}
                    htmlType='submit'
                  >
                    重置
                  </Button>
                  <Button
                    type='primary'
                    loading={saveLoading == 1 && setLoading}
                    onClick={() => {
                      form1.validateFields().then(async () => {
                        // if (id && status?.enable == 1) {
                        //   confirm({
                        //     title: (
                        //       <Typography sx={{ fontWeight: 'bold', fontSize: 'h1.fontSize' }}>当前表单已发布</Typography>
                        //     ),
                        //     icon: <ExclamationCircleFilled />,
                        //     maskClosable: true,
                        //     content: (
                        //       <Box>
                        //         <Typography sx={{}}>已发布的表单修改后需要重新发布才会生效，是否确定修改？</Typography>
                        //       </Box>
                        //     ),
                        //     onOk() {
                        //       toggleActivity()
                        //       handleSubmit(form1.getFieldsValue(), 1)
                        //     },
                        //     onCancel() {
                        //       console.log('Cancel')
                        //     }
                        //   })
                        // } else {
                        //   handleSubmit(form1.getFieldsValue(), 1)
                        // }
                        if (enableTask) {
                          //获取任务委派信息
                          try {
                            const taskData =
                              taskRef && taskRef.current && (await (taskRef.current as any).getTaskValue(true))
                            if (taskData) {
                              handleTask(taskData, form1.getFieldsValue())
                            } else {
                              throw new Error('未选择人员')
                            }
                          } catch (e) {
                            throw new Error('任务信息校验出错')
                          }
                        } else {
                          handleSubmit({ ...form1.getFieldsValue(), onlyGroup: supportGroup ? 1 : 0 }, 1)
                          if (id && status.enable == 1) {
                            // rePublish()
                            // console.log('rePublish=====')
                          }
                        }
                      })
                    }}
                    htmlType='submit'
                  >
                    保存
                  </Button>
                </Box>
              </Form.Item>
            </Form>
          </Card>
          <Card
            style={{ marginTop: '24px', boxShadow: 'none', display: 'none' }}
            bordered={false}
            title={
              <Box>
                <Typography sx={{ fontWeight: 600, fontSize: 'h3.fontSize' }}>任务派发</Typography>
              </Box>
            }
          >
            <Form
              form={form2}
              labelCol={{ span: 4 }}
              wrapperCol={{ span: 14 }}
              layout='vertical'
              disabled={disabled}
              //disabled={componentDisabled}
              style={{ maxWidth: '100%' }}
            >
              {/* <Form.Item label='报名时间' name='applytime'>
              <RangePicker showTime separator={'~'}></RangePicker>
            </Form.Item> */}

              {/* <Form.Item label='填写方式' initialValue={'SM'} name='nametype'>
              <Radio.Group
                style={{ display: 'flex', alignItems: 'center' }}
                onChange={(e: any) => {
                  const value = e.target.value
                  setApplyAdminOpen(value)
                }}
              >
                <Radio value={'SM'}>{PARTYPES.SM}</Radio>
                <Radio value={'NM'}>{PARTYPES.NM}</Radio>
                <Box
                  sx={{
                    cursor: 'pointer',
                    '.tagtip': {
                      backgroundColor: 'primary.light',
                      color: 'primary.main',
                      p: 0.5,
                      px: 1,
                      fontSize: 'body2.fontSize'
                    }
                  }}
                >
                  <Popover content={'匿名方式对管理员不生效'}>
                    <Button shape='circle' size='small' icon={<QuestionOutlined />} />
                  </Popover>
                </Box>
              </Radio.Group>
            </Form.Item> */}
              {/* <Form.Item>
                <Form.Item hidden={!enableTask}>
                  {React.createElement(RemoteComponent, {
                    _remoteInfo: {
                      componentName: 'yiban-frontend-admin.EasyformTask',
                      version: '0.1.0'
                    },
                    task: taskInfo?.detail,
                    user: userInfo,
                    onCreateTask: (value: any) => {
                      form1.validateFields().then(() => {
                        handleTask(value, form1.getFieldsValue())
                      })
                      return false
                    }
                  })}
                </Form.Item>
              </Form.Item> */}

              {/* <Form.Item label={' '} colon={false}>
              <Box sx={{ display: 'flex' }}>
                <Button
                  style={{ marginRight: 24 }}
                  onClick={() => {
                    handleReset(form2)
                  }}
                  type='default'
                  htmlType='submit'
                  disabled={setLoading}
                >
                  重置
                </Button>
                <Button
                  type='primary'
                  loading={saveLoading == 2 && setLoading}
                  onClick={() => {
                    form1.validateFields().then(() => {
                      form2.validateFields().then(() => {
                        // if (id && status?.enable == 1) {
                        //   confirm({
                        //     title: (
                        //       <Typography sx={{ fontWeight: 'bold', fontSize: 'h1.fontSize' }}>
                        //         当前表单已发布
                        //       </Typography>
                        //     ),
                        //     icon: <ExclamationCircleFilled />,
                        //     maskClosable: true,
                        //     content: (
                        //       <Box>
                        //         <Typography sx={{}}>已发布的表单修改后需要重新发布才会生效，是否确定修改？</Typography>
                        //       </Box>
                        //     ),
                        //     onOk() {
                        //       toggleActivity()
                        //       handleSubmit(form2.getFieldsValue(), 2)
                        //     },
                        //     onCancel() {
                        //       console.log('Cancel')
                        //     }
                        //   })
                        // } else {
                        //   handleSubmit(form2.getFieldsValue(), 2)
                        // }
                        handleSubmit(form2.getFieldsValue(), 2)
                        if (id && status.enable == 1) {
                          rePublish()
                          console.log('rePublish=====')
                        }
                      })
                    })
                  }}
                  htmlType='submit'
                >
                  保存
                </Button>
              </Box>
            </Form.Item> */}
            </Form>
          </Card>
        </Spin>
      </Box>
    </>
  )
})

export default forwardRef((props: any, ref: any) => {
  const location = useLocation()
  const Updater = useContext<any>(UpdateContext)

  const [{ define, id, status }, dispach] = Updater
  const { settings, page, workflow } = define
  const type = (settings && (settings as any).type) || (location.state && (location.state as any).type)
  const [userContext, userdispach] = useAppContext()
  const { user, ybUser } = userContext

  //根据人员身份控制选择器权限
  const PickerController = useMemo(() => {
    const orgCode = ybUser?.orgs[0].code
    return {
      orgCode: user.adminType === ADMIN_TYPE.SUPER ? void 0 : orgCode ? orgCode : void 0,
      MenuItem:
        user.adminType === ADMIN_TYPE.SUPER
          ? void 0
          : [
              {
                label: '学生',
                key: '学生'
              },
              {
                label: '教职工',
                key: '教职工'
              }
            ]
    }
  }, [ybUser, user])
  if (type == 'PSQ') {
    return <WJForm ref={ref} PickerController={PickerController} {...props}></WJForm>
  }
  if (type == 'INFO' || type == 'VOTE') {
    return <XXForm ref={ref} PickerController={PickerController} {...props}></XXForm>
  }
  if (type == 'NOTICE') {
    return <TZForm ref={ref} PickerController={PickerController} {...props}></TZForm>
  }
  return <HDForm ref={ref} PickerController={PickerController} {...props}></HDForm>
})
