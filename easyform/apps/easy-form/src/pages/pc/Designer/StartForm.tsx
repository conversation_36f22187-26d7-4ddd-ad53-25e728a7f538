import React, { useState, useEffect, useRef, forwardRef, useContext } from 'react'
import { Box, styled } from '@mui/system'
import FormInfo from '../components/FormInfo'
import { TabPanel, Typography } from '@/components'

import { UpdateContext } from './context/UpdateContext'
import { Button as MButton } from 'antd-mobile'
import { useLocation, useNavigate } from 'react-router-dom'
import { MobilePreview, PcPreview } from '../components/DesignerPreview'
import {
  Form,
  Card,
  Popover,
  Row,
  Switch,
  Button,
  Modal,
  message,
  Skeleton,
  Dropdown,
  Space,
  Statistic,
  Col,
  Segmented
} from 'antd'
import { ClosePreviewIcon, MobileIcon, PcIcon } from '../icons/icons'
import { useOverlayScrollbars } from '@yiban/system'
import { APPLYTYPES } from './FormSetting'
import {
  EditFilled,
  CloseSquareFilled,
  DownOutlined,
  FieldTimeOutlined,
  UserOutlined,
  ScanOutlined,
  <PERSON>offOutlined,
  <PERSON>yOutOutlined,
  <PERSON>Outlined,
  DesktopOutlined,
  SettingFilled,
  QuestionOutlined,
  ShareAltOutlined,
  CopyOutlined
} from '@ant-design/icons'
import { useEasyFormRequest } from '@/hooks'
import { getActivityList, getStatisticParticipate, toggleActivityEnable } from '@/api/easyform'
import { getAcitvityDate } from '@/utils'
import { Render } from '@/editor-kit/common'
import { useQRCode } from 'next-qrcode'

const TitleTypography = styled(Typography)(({ theme }) => ({
  color: theme.palette.text.secondary,
  padding: '8px 0'
}))

//开始页面
export default forwardRef((props: any, ref) => {
  const [previewType, setPreviewType] = useState(2)
  const { setActive } = props
  const location: any = useLocation()
  const navigate = useNavigate()
  const updater = useContext<any>(UpdateContext)
  const [{ define, id, status }, dispach] = updater
  console.log(21201314, define)
  const {
    settings = {
      name: location.state && location.state.name,
      alertMsg: '',
      desc: '',
      isCancel: false,
      imgs: []
    }
  } = define
  const groupContentRef2 = useRef(null)
  const [initialize2, instance2] = useOverlayScrollbars({ options: { scrollbars: { autoHide: 'scroll' } } })
  useEffect(() => {
    groupContentRef2.current && initialize2(groupContentRef2.current)
  }, [initialize2])
  console.log('定义', define)
  const [refs] = React.useState<Record<number, any>>({
    1: React.createRef(),
    2: React.createRef()
  })
  const {
    runAsync: fetchData,
    data: activityStatus,
    loading: activityLoading,
    refreshAsync: refreshActivity
  } = useEasyFormRequest(() => getActivityList({ id: id }), {
    //manual: true,
    ready: Boolean(id),
    refreshDeps: [],
    onSuccess: (data: any) => {
      dispach({ type: 'updateStatus', payload: { status: data.data[0] } })
    }
  })
  const { data: participantCount2, loading: participantCount2Loading } = useEasyFormRequest(
    () => getStatisticParticipate(id, '', { ticketStatus: 1 }),
    {
      ready: Boolean(id)
    }
  )
  const { data: participantCount3, loading: participantCount3Loading } = useEasyFormRequest(
    () => getStatisticParticipate(id, '', { status: 0 }),
    {
      ready: Boolean(id)
    }
  )
  const { data: participantCount, loading: participantCountLoading } = useEasyFormRequest(
    () => getStatisticParticipate(id, '', {}),
    {
      ready: Boolean(id)
    }
  )
  const { runAsync: toggleActivity, loading: toggleLoading } = useEasyFormRequest(() => toggleActivityEnable(id), {
    manual: true,
    ready: id,
    onSuccess: () => {
      //setFormState(formState ? 0 : 1)
      refreshActivity()
      // navigate('/pc/designForm', {
      //   state: {
      //     ...location.state,
      //     enable: formState ? 0 : 1
      //   },
      //   replace: true
      // })
    },
    onError: () => {}
  })
  const AutoModal = styled(Modal)(() => ({
    '.ant-modal-content': {
      width: 'inherit'
    }
  }))
  const { Canvas } = useQRCode()

  const spanRef = React.useRef<HTMLInputElement>(null)
  const handleCopy = (text: string) => {
    spanRef.current?.select()
    document.execCommand('copy')
    // navigator.clipboard.writeText(text).then(() => {
    //   message.open({
    //     type: 'success',
    //     content: '已成功复制到剪切板'
    //   })
    // })
    message.open({
      type: 'success',
      content: '已成功复制到剪切板'
    })
  }
  const [joinUrl, setJoinUrl] = React.useState<string>()
  const handleShare = (id: any) => {
    setJoinUrl(`${window.location.origin}/easyform/submit?activityId=${id}`)
  }
  return (
    <>
      <Box sx={{ display: 'flex', height: '100%', background: 'white' }} ref={ref}>
        <Box
          sx={{ borderRight: '1px solid rgb(238, 238, 238)', flexGrow: 1, display: 'flex', flexDirection: 'column' }}
        >
          <Box
            sx={{
              height: 50,
              p: 0.5,
              borderBottom: '1px solid rgb(238, 238, 238)',
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'space-between'
            }}
          >
            <Box sx={{ display: 'flex', alignItems: 'center', pl: 2 }}>
              <Typography sx={{ color: 'warning.main' }}>提示：</Typography>
              <Typography sx={{ color: 'text.secondary' }}>此为预览页面，不能参与作答</Typography>
            </Box>
            <Segmented
              onChange={(value) => {
                console.log(value)
                setPreviewType(value as any)
              }}
              value={previewType}
              options={[
                {
                  value: 2,
                  icon: <MobileOutlined />
                },
                {
                  value: 1,
                  icon: <DesktopOutlined />
                }
              ]}
            />
          </Box>
          <Box
            ref={groupContentRef2}
            sx={{
              flexGrow: 1,
              borderRadius: '10px',
              // mr: 3,
              p: 3,
              pt: 0,
              height: '100%'
            }}
          >
            <Box className='h-full' sx={{ bgcolor: '#FFF', width: previewType === 1 ? '100%' : 375, margin: '0 auto' }}>
              <Render isPC={previewType === 1} define={define} pageDefine={define.page} settings={define.settings} />
            </Box>
          </Box>
        </Box>

        <Box
          className='shrink-0'
          sx={{
            width: '350px',
            borderRadius: '10px',
            py: 3,
            px: 3,
            display: 'flex',
            flexDirection: 'column',
            justifyContent: 'space-between'
          }}
        >
          <Box>
            <Box>
              <Popover content={settings.name}>
                <Typography noWrap sx={{ color: 'primary.main', fontWeight: 'bold', fontSize: 'h1.fontSize' }}>
                  {settings.name || (location.state && location.state.name)}
                </Typography>
              </Popover>
            </Box>
            <Box sx={{ pt: 4 }}>
              <Row gutter={[8, 16]} style={{ width: '100%' }}>
                <Col span={24}>
                  <Box sx={{ display: 'flex', justifyContent: 'space-between' }}>
                    <TitleTypography>
                      <PoweroffOutlined style={{ paddingRight: 8 }} />
                      表单状态
                    </TitleTypography>
                    {status?.enable ? (
                      <Button
                        onClick={() => {
                          handleShare(id)
                        }}
                        type='link'
                        disabled={!status?.enable}
                        icon={<ShareAltOutlined style={{ paddingRight: 0 }} />}
                      >
                        分享表单
                      </Button>
                    ) : (
                      <Popover content={'请先发布表单'}>
                        <Button
                          type='link'
                          disabled={!status?.enable}
                          icon={<ShareAltOutlined style={{ paddingRight: 0 }} />}
                        >
                          分享表单
                        </Button>
                      </Popover>
                    )}
                  </Box>

                  <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>
                    <Box sx={{ display: 'flex', alignItems: 'center' }}>
                      <Box
                        sx={{
                          width: '6px',
                          height: '6px',
                          borderRadius: '50%',
                          backgroundColor: status?.enable ? 'success.light' : 'error.main',
                          marginRight: '5px'
                        }}
                      ></Box>
                      <Typography sx={{ cursor: 'pointer' }}>
                        <Space>
                          {status?.enable ? '已发布' : '未发布'}
                          {/* <DownOutlined /> */}
                        </Space>
                      </Typography>
                    </Box>
                  </Box>
                </Col>
                <Col span={24}>
                  <TitleTypography>
                    <FieldTimeOutlined style={{ paddingRight: 8 }} />
                    活动时间
                  </TitleTypography>
                  <Box sx={{ display: 'flex', alignItems: 'center' }}>
                    {getAcitvityDate(settings.startDateTime || 0, settings.endDateTime || 0, 'YYYY-MM-DD HH:mm')}
                  </Box>
                </Col>
                <Col span={24}>
                  <TitleTypography>
                    <UserOutlined style={{ paddingRight: 8 }} />
                    参与人员范围
                  </TitleTypography>
                  <Box sx={{ display: 'flex', alignItems: 'center' }}>
                    当前参与人数 <Typography sx={{ px: 1 }}> | </Typography>
                    {((participantCount && participantCount.data[0] && participantCount.data[0].count) || 0) + '人'}
                  </Box>
                </Col>
                <Col span={24} style={{ marginBottom: '-16px' }}>
                  <TitleTypography>
                    <CarryOutOutlined style={{ paddingRight: 8 }} />
                    活动参与情况
                  </TitleTypography>
                </Col>
                <Col span={8}>
                  <Card title={null} bodyStyle={{ padding: 8 }}>
                    <Statistic
                      loading={participantCountLoading}
                      title={settings.type == 'APPLY' ? '报名人数' : '提交人数'}
                      value={(participantCount && participantCount.data[0] && participantCount.data[0].count) || 0}
                      //precision={2}
                      valueStyle={{ color: '#3f8600' }}
                      //prefix={<ArrowUpOutlined />}
                      suffix='人'
                    />
                  </Card>
                </Col>
                {settings.isTicket ? (
                  <>
                    <Col span={8}>
                      <Card title={null} bodyStyle={{ padding: 8 }}>
                        <Statistic
                          title='签到人数'
                          loading={participantCount2Loading}
                          value={
                            settings.isTicket
                              ? (participantCount2 && participantCount2.data[0] && participantCount2.data[0].count) || 0
                              : '-'
                          }
                          //precision={2}
                          valueStyle={{ color: '#3f8600' }}
                          //prefix={<ArrowUpOutlined />}
                          suffix='人'
                        />
                      </Card>
                    </Col>
                    <Col span={8}>
                      <Card title={null} bodyStyle={{ padding: 8 }}>
                        <Statistic
                          loading={participantCount3Loading}
                          title={
                            <Box
                              sx={{
                                cursor: 'pointer',
                                '.tagtip': {
                                  backgroundColor: 'primary.light',
                                  color: 'primary.main',
                                  p: 0.5,
                                  px: 1,
                                  fontSize: 'body2.fontSize'
                                }
                              }}
                            >
                              <Typography>已审核</Typography>
                              <Popover content={'匿名方式对管理员不生效'}>
                                <Button shape='circle' size='small' icon={<QuestionOutlined />} />
                              </Popover>
                            </Box>
                          }
                          value={
                            (participantCount3 && participantCount3.data[0] && participantCount3.data[0].count) || 0
                          }
                          //precision={2}
                          valueStyle={{ color: '#3f8600' }}
                          //prefix={<ArrowUpOutlined />}
                          suffix='人'
                        />
                      </Card>
                    </Col>
                    {/* <Col span={8}>
                    <Card title={null} bodyStyle={{ padding: 8 }}>
                      <Statistic
                        title='签到率'
                        value={
                          settings.isTicket
                            ? participantCount2 &&
                              participantCount2.data[0] &&
                              participantCount &&
                              participantCount.data[0] &&
                              participantCount.data[0].count > 0
                              ? (participantCount2.data[0].count / participantCount.data[0].count) * 100
                              : 0
                            : '-'
                        }
                        //precision={2}
                        valueStyle={{ color: '#3f8600' }}
                        //prefix={<ArrowUpOutlined />}
                        suffix='%'
                      />
                    </Card>
                  </Col> */}
                  </>
                ) : (
                  <Col span={8}>
                    <Card title={null} bodyStyle={{ padding: 8 }}>
                      <Statistic
                        title={
                          <Box
                            sx={{
                              cursor: 'pointer',
                              display: 'flex',
                              alignItems: 'center',
                              '.tagtip': {
                                backgroundColor: 'primary.light',
                                color: 'primary.main',
                                p: 0.5,
                                px: 1,
                                fontSize: '12'
                              }
                            }}
                          >
                            <Typography>已审核</Typography>
                            <Popover content={'无需审核的表单提交后自动视为已通过'}>
                              <Button
                                shape='circle'
                                size='small'
                                style={{ transform: 'scale(0.7)' }}
                                icon={<QuestionOutlined />}
                              />
                            </Popover>
                          </Box>
                        }
                        loading={participantCount2Loading}
                        value={(participantCount3 && participantCount3.data[0] && participantCount3.data[0].count) || 0}
                        //precision={2}
                        valueStyle={{ color: '#3f8600' }}
                        //prefix={<ArrowUpOutlined />}
                        suffix='人'
                      />
                    </Card>
                  </Col>
                )}
                {settings.type == 'APPLY' ? (
                  <>
                    <Col span={24} style={{ display: 'flex', justifyContent: 'space-between' }}>
                      <TitleTypography>
                        <UserOutlined style={{ paddingRight: 8 }} />
                        报名方式
                      </TitleTypography>
                      <Box sx={{ display: 'flex', alignItems: 'center' }}>
                        <Typography>
                          {APPLYTYPES[settings.apply?.type as keyof typeof APPLYTYPES] || APPLYTYPES.APPLY}
                        </Typography>
                      </Box>
                    </Col>
                    <Col span={24} style={{ display: 'flex', alignItems: 'flex-start' }}>
                      <Typography sx={{ lineHeight: '36px' }}>
                        <ScanOutlined style={{ paddingRight: 8 }} />
                      </Typography>

                      <Box sx={{ width: '100%' }}>
                        <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>
                          <TitleTypography>扫码签到</TitleTypography>
                          <Box>
                            {/* <Switch
                        checkedChildren={'已开启'}
                        unCheckedChildren={'已关闭'}
                        defaultChecked={settings.isTicket}
                      /> */}
                            {settings.apply?.isTicket ? '已开启' : '已关闭'}
                          </Box>
                        </Box>
                        {settings.isTicket ? (
                          <Box style={{ display: 'flex', justifyContent: 'space-between' }}>
                            <TitleTypography>签到人员</TitleTypography>
                            <Box sx={{ display: 'flex', alignItems: 'center' }}>Align</Box>
                          </Box>
                        ) : (
                          ''
                        )}
                      </Box>
                    </Col>
                  </>
                ) : (
                  ''
                )}
              </Row>
            </Box>
          </Box>
          <Box sx={{ display: 'flex', justifyContent: 'space-between', flexDirection: 'column', alignItems: 'center' }}>
            <MButton
              style={{ width: '50%' }}
              //type={'primary'}
              onClick={() => {
                setActive(1)
              }}
              block
              color='primary'
              //loading={toggleLoading}
            >
              编辑表单
            </MButton>
            {id ? (
              <MButton
                //type={'default'}
                loading={toggleLoading || activityLoading}
                onClick={() => {
                  toggleActivity()
                }}
                block
                style={{ marginTop: '16px', width: '50%' }}
                color={status?.enable ? 'danger' : 'success'}
              >
                {status?.enable ? '取消发布' : '立即发布'}
              </MButton>
            ) : (
              <Box></Box>
            )}
          </Box>
        </Box>
      </Box>
      <AutoModal
        onCancel={() => setJoinUrl('')}
        open={Boolean(joinUrl)}
        footer={null}
        title={
          <div>
            <ShareAltOutlined style={{ marginRight: 4 }} />
            <span>分享</span>
          </div>
        }
      >
        <Box sx={{ display: 'flex', flexDirection: 'column', alignItems: 'center' }}>
          <Canvas
            text={joinUrl || ''}
            options={{
              //level: 'L',
              margin: 2,
              scale: 5,
              width: 250,
              color: {
                dark: '#000005',
                light: '#ffffff'
              }
            }}
          />
          <Box
            onClick={() => handleCopy(joinUrl || '')}
            sx={{
              cursor: 'pointer',
              minWidth: 250,
              py: 1,
              px: 2,
              width: '100%',
              bgcolor: '#f8f8f8',
              borderRadius: '8px',
              color: '#999',
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'space-between'
            }}
          >
            <input
              ref={spanRef}
              css={{
                '&:selection': {
                  background: 'transparent',
                  color: 'inherit'
                }
              }}
              style={{
                wordBreak: 'break-all',
                display: 'inline-block',
                overflow: 'hidden',
                whiteSpace: 'nowrap',
                background: 'none',
                textOverflow: 'ellipsis',
                border: 'none',
                outline: 'none',
                flexGrow: 1
              }}
              value={joinUrl}
              readOnly
            />
            {/* <span
              style={{
                wordBreak: 'break-all',
                display: 'inline-block',
                overflow: 'hidden',
                whiteSpace: 'nowrap',
                textOverflow: 'ellipsis'
              }}
            >
              {joinUrl}
            </span> */}
            <CopyOutlined />
          </Box>
        </Box>
      </AutoModal>
    </>
  )
})
