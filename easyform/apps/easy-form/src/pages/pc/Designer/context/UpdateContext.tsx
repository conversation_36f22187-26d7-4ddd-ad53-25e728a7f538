import produce from 'immer'
import React, { useContext, createContext } from 'react'
import { useLocation, useNavigate } from 'react-router-dom'
import _ from 'lodash'
import { any } from 'prop-types'
import { THEMESETTINGS } from '@/@define'
export interface FormDefine {
  page: any
  settings: { name: ''; [key: string]: any }
  workflow: any
}

const initState: {
  [key: string]: FormDefine
} = {
  APPLY: {
    page: { componentTree: [], remoteDependencies: {} },
    settings: {
      name: '',
      desc: '',
      templateSettings: {},
      imgs: [],
      startDateTime: '',
      endDateTime: '',
      type: 'APPLY',
      participant: [],
      apply: { type: 'APPLY', applylimit: false, isTicket: false },
      pageSettings: THEMESETTINGS.DEFAULT
    },
    workflow: {}
  },
  PSQ: {
    page: { componentTree: [], remoteDependencies: {} },
    workflow: {},
    settings: {
      name: '',
      participant: [],
      type: 'PSQ',
      desc: '',
      templateSettings: {},
      startDateTime: '',
      endDateTime: '',
      imgs: [],
      psq: { nametype: 'SM' },
      pageSettings: THEMESETTINGS.DEFAULT
    }
  },
  INFO: {
    page: { componentTree: [], remoteDependencies: {} },
    workflow: {},
    settings: {
      name: '',
      type: 'INFO',
      desc: '',
      participant: [],
      startDateTime: '',
      endDateTime: '',
      templateSettings: {},
      imgs: [],
      info: {},
      pageSettings: THEMESETTINGS.DEFAULT
    }
  },
  NOTICE: {
    page: { componentTree: [], remoteDependencies: {} },
    workflow: {},
    settings: {
      name: '',
      type: 'NOTICE',
      desc: '',
      participant: [],
      startDateTime: '',
      endDateTime: '',
      templateSettings: {},
      imgs: [],
      notice: {},
      pageSettings: THEMESETTINGS.DEFAULT
    }
  }
}

/**
 * @description 构建form表单定义
 * @param baseDefine 可选参数，是一个定义，函数会检查该定义并基于该定义构建表单定义
 * @param config **/
export const getInitState = (
  config: { type: 'APPLY' | 'PSQ' | 'INFO' | 'NOTICE'; name: any } = { type: 'APPLY', name: '新建表单' },
  baseDefine?: FormDefine
): FormDefine => {
  const { type, name } = config
  let define
  if (!baseDefine) {
    //从空构建一个表单定义
    define = _.cloneDeep(initState[type])
    define.settings = { ...define.settings, name }
  } else {
    define = baseDefine
  }
  return define
}
export const UpdateContext = createContext<any>([{ define: {} }, (action: Action) => any])

export interface Action {
  type:
    | 'updatePage'
    | 'toEdit'
    | 'build'
    | 'updateSettings'
    | 'updateStatus'
    | 'updateDefine'
    | 'updateWorkflow'
    | 'updateTemplateDetail'
    | 'updateTask'
  payload: { define: FormDefine; id?: string; status?: any; taskInfo?: { id?: string; detail?: any } }
}

const reducer = (state: any, action: Action): any => {
  return produce(state, (draft: any) => {
    switch (action.type) {
      case 'updateDefine':
        console.log('--------reducer:updateDefine', action.payload)
        draft.define.page = action.payload.define?.page
        draft.define.settings = action.payload.define?.settings
        draft.define.workflow = action.payload.define?.workflow
        break
      case 'toEdit':
        break
      case 'updateWorkflow':
        draft.define.workflow = action.payload.define?.workflow
        break
      case 'updateTemplateDetail':
        draft.templateDetail = action.payload
        break
      case 'updatePage':
        draft.define.page = action.payload.define?.page
        break
      case 'updateSettings':
        draft.define.settings = action.payload.define?.settings
        break
      case 'build':
        draft.id = action.payload.id
        break
      case 'updateStatus':
        draft.status = action.payload.status
        break
      case 'updateTask':
        draft.taskInfo = action.payload
        break
      default:
        throw new Error('未定义的action')
    }
  })
}

export const UpdaterProvider = (props: { children: any }) => {
  const { children } = props
  const location: any = useLocation()
  const navigate: any = useNavigate()
  // 如果不存在state尝试从url参数获取
  console.log('来自query', location)

  if (!location.state && location.search) {
    const params = new URLSearchParams(location.search)
    //模板编辑/新建
    if (params.get('templateEdit')) {
      const templateId = params.get('templateId')
      const isTemplateEdit = Boolean(params.get('templateEdit'))
      location.state = { isTemplateEdit, templateId }
    } else {
      const id = params.get('id')
      const insId = params.get('insId')
      const pageTo = params.get('pageTo')
      if (id && insId) {
        //history.replaceState({ usr: { id, insId, enable } }, '')
        //navigate('/', { state: { id, insId, enable } })
        //重新获取一次
        location.state = { id, insId, pageTo: pageTo ? JSON.parse(pageTo) : 0 }
      }
    }
  }
  const updater = React.useReducer(reducer, {
    define: getInitState({ type: location.state?.type || 'APPLY', name: location.state?.name || '新建表单' }),
    id: location.state?.id,
    templateId: location.state?.templateId,
    isTemplateEdit: location.state?.isTemplateEdit,
    pageTo: location.state?.pageTo || (location.state?.id ? 0 : 1)
  })
  return <UpdateContext.Provider value={updater}>{children(location?.state?.isTemplateEdit)}</UpdateContext.Provider>
}
