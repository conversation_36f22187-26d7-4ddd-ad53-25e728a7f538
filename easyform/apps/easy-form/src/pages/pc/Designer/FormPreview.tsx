import React, { useState, useEffect, useMemo, forwardRef, useContext } from 'react'
import { Box } from '@mui/system'
import FormInfo from '../components/FormInfo'
import { TabPanel, Typography } from '@/components'
import { UpdateContext } from './context/UpdateContext'
import { useLocation } from 'react-router-dom'
import { MobilePreview, PcPreview } from '../components/DesignerPreview'
import { Radio, Row, Col } from 'antd'
import { ClosePreviewIcon, MobileIcon, PcIcon } from '../icons/icons'
import { APPLYTYPES } from './FormSetting'
import { EditFilled, CloseSquareFilled, EyeFilled, FundFilled, SaveFilled, SettingFilled } from '@ant-design/icons'

//预览表单页面
export default forwardRef((props: any, ref) => {
  const [previewType, setPreviewType] = useState(1)
  const { onClose, activityDefine } = props
  const location: any = useLocation()
  console.log('定义', activityDefine)
  const [refs] = React.useState<Record<number, any>>({
    1: React.createRef(),
    2: React.createRef()
  })
  return (
    <>
      <Box ref={ref} sx={{ backgroundColor: 'white' }}>
        {/* <Box
          sx={{
            display: 'flex',
            borderBottom: '2px solid ',
            borderColor: 'background.default',
            justifyContent: 'space-between'
          }}
        >
          <FormInfo sx={{ flexGrow: '1' }}></FormInfo>
          <Box sx={{ width: 400, display: 'flex', justifyContent: 'flex-end', pb: 2 }}>
            <Box
              sx={{
                pr: 3,
                display: 'flex',
                height: '100%',
                justifyContent: 'end',
                flexDirection: 'column'
              }}
            >
              <Box sx={{ display: 'flex', py: 1 }}>
                <Typography sx={{ color: 'text.secondary' }}>报名方式：</Typography>
                <Typography>{APPLYTYPES[settings.applytype as keyof typeof APPLYTYPES] || APPLYTYPES.bmz}</Typography>
              </Box>
              <Box sx={{ display: 'flex', py: 1 }}>
                <Typography sx={{ color: 'text.secondary' }}>报名人数：</Typography>
                <Typography>不限</Typography>
              </Box>
            </Box>
            <Box sx={{ pr: 3, justifyContent: 'flex-end', flexDirection: 'column', height: '100%', display: 'flex' }}>
              <Box sx={{ display: 'flex', py: 1 }}>
                <Typography sx={{ color: 'text.secondary' }}>扫码签到：</Typography>
                <Typography>{settings.isTicket ? '开启' : '关闭'}</Typography>
              </Box>
              <Box sx={{ display: 'flex', py: 1 }}>
                <Typography sx={{ color: 'text.secondary' }}>扫码人员：</Typography>
                <Typography>Align</Typography>
              </Box>
            </Box>
          </Box>
        </Box> */}
        <Box sx={{}}>
          <Box
            sx={{
              boxShadow: '0px 1px 0px 0px rgba(232,232,232,1)',
              px: 3,
              '.ant-radio-button-wrapper': {
                borderRadius: '0'
              }
            }}
          >
            <Row>
              <Col span={8} style={{ display: 'flex', justifyContent: 'flex-end' }}>
                <Box sx={{ display: 'flex', alignItems: 'center' }}>
                  <Typography sx={{ color: 'warning.main' }}>提示：</Typography>
                  <Typography sx={{ color: 'text.secondary' }}>此为预览页面，不能参与作答</Typography>
                </Box>
              </Col>
              <Col span={8} style={{ display: 'flex', justifyContent: 'center', textAlign: 'center' }}>
                <Radio.Group
                  buttonStyle='solid'
                  onChange={(e) => {
                    const value = e.target.value
                    if (value == 3) {
                      onClose()
                    } else {
                      setPreviewType(value)
                    }
                  }}
                  defaultValue={previewType}
                >
                  <Radio.Button value={1} style={{ height: '70px', width: '88px' }}>
                    <Typography sx={{ pt: 1 }}>
                      <Typography>
                        <MobileIcon fill={(previewType == 1 && 'white') || ''}></MobileIcon>
                      </Typography>
                      <Typography sx={{ textAlign: 'center' }}>手机预览</Typography>
                    </Typography>
                  </Radio.Button>
                  <Radio.Button value={2} style={{ height: '70px', width: '88px' }}>
                    <Typography sx={{ pt: 1 }}>
                      <Typography>
                        <PcIcon fill={(previewType == 2 && 'white') || ''}></PcIcon>
                      </Typography>
                      <Typography sx={{ textAlign: 'center' }}>电脑预览</Typography>
                    </Typography>
                  </Radio.Button>
                  <Radio.Button value={3} style={{ height: '70px', width: '88px' }}>
                    <Typography sx={{ pt: 1 }}>
                      <Typography>
                        <ClosePreviewIcon fill={(previewType == 3 && 'white') || ''} />
                      </Typography>
                      <Typography sx={{ textAlign: 'center' }}>关闭预览</Typography>
                    </Typography>
                  </Radio.Button>
                </Radio.Group>
              </Col>
              <Col span={8} style={{ display: 'flex', justifyContent: 'flex-start' }}>
                {' '}
                <Box></Box>
              </Col>
            </Row>
          </Box>
          <TabPanel value={1} showValue={previewType}>
            <Box
              ref={refs[1]}
              sx={{
                display: 'flex',
                justifyContent: 'center',
                backgroundColor: 'background.default',
                pt: 4
              }}
            >
              <MobilePreview
                activityDefine={activityDefine}
                sx={{
                  width: '30vw',
                  //borderRadius: '20px',
                  // border: '5px solid',
                  overflow: 'hidden',
                  backgroundColor: 'white'
                  // borderColor: 'primary.main',
                  // boxShadow:
                  //   '0 1px 2px -2px rgba(0, 0, 0, 0.16), 0 3px 6px 0 rgba(0, 0, 0, 0.12), 0 1px 2px -2px rgba(0, 0, 0, 0.16), 0 3px 6px 0 rgba(0, 0, 0, 0.12), 0 5px 12px 4px rgba(0, 0, 0, 0.09)'
                }}
                activityId={location.state.id}
              ></MobilePreview>
            </Box>
          </TabPanel>
          <TabPanel value={2} showValue={previewType}>
            <Box
              ref={refs[1]}
              sx={{
                display: 'flex',
                justifyContent: 'center',
                pt: 4
              }}
            >
              <PcPreview
                activityDefine={activityDefine}
                sx={{ width: '50vw' }}
                activityId={location.state.id}
              ></PcPreview>
            </Box>
          </TabPanel>
        </Box>
      </Box>
    </>
  )
})
