import React, { useState, useEffect, useMemo, forwardRef, useContext, useCallback } from 'react'
import { Box } from '@mui/system'
import { Radio, Input, Button, Popconfirm, Space, Table, Steps, Modal, message, Drawer, Dropdown } from 'antd'
import FormInfo from '../components/FormInfo'
import FormItemSummary from './FormItemSummary'
import { UpdateContext } from './context/UpdateContext'
import { useLocation, useSearchParams } from 'react-router-dom'
import ActivitySummary from './ActivitySummary'
import ParticipantDetail from '../components/ParticipantDetail'
import { APPLYTYPES } from './FormSetting'
import { TabPanel, Typography } from '@/components'
import { CaretDownOutlined, CloseCircleOutlined, MoreOutlined } from '@ant-design/icons'
import { useEasyFormRequest } from '@/hooks'
import {
  getActivityDefine,
  getList,
  getStatisticParticipate,
  changeFormStatus,
  exportExcelForInstanceForm,
  deleteFormIns
} from '@/api/easyform'

const ComponentNameMap: any = {
  Input: '文本输入',
  Checkbox: '多选',
  Radio: '单选',
  Rate: '评分'
}
const { Search } = Input

//详情统计页面
export default forwardRef((props, ref) => {
  const [filterType, setFilterType] = useState(1)
  const [isModalOpen, setIsModalOpen] = useState(false)
  const [selectedRowKeys, setSelectedRowKeys] = useState<React.Key[]>([])
  const [selectButton, setSelectButton] = useState({
    pass: false
  })
  const location: any = useLocation()
  console.log('===locatin:', location)
  const [participantDetail, setParticipantDetail] = useState({})
  const [instanceId, setInstanceId] = useState('')
  const updater = useContext<any>(UpdateContext)
  const [{ define, id }, dispach] = updater
  const { settings, workflow } = define
  const [workstatus, setWorkstatus] = useState<string>('')
  const [ticketStatus, setTicketStatus] = useState<string>('')
  const [isDown, setIsDown] = useState<boolean>()
  const [tableFilter, setTableFilter] = useState('')
  const [pageDefine, setPageDefine] = useState<any>()
  const [pagination, setPagination] = useState<{ current?: number | undefined; pageSize?: number | undefined }>({
    current: 1,
    pageSize: 10
  })
  const [sorter, setSorter] = useState<any>()
  const { loading } = useEasyFormRequest(() => getActivityDefine(id), {
    ready: Boolean(id),
    loadingDelay: 200,
    onSuccess: (res: any) => {
      setPageDefine(res?.data?.page?.componentTree || [])
    }
  })
  const {
    data: List,
    loading: ListLoading,
    refreshAsync: refreshList
  } = useEasyFormRequest(
    () =>
      getList('', id, {
        pageNo: pagination.current,
        pageSize: pagination.pageSize,
        orderBy: sorter,
        workflowDoneReason: workstatus,
        isWorkflowDone: isDown,
        ticketStatus,
        query: JSON.stringify({
          creatorName: tableFilter
        })
      }),
    {
      ready: Boolean(id),
      loadingDelay: 200,
      refreshDeps: [pagination, tableFilter, workstatus, isDown, ticketStatus],
      onSuccess: (res: any) => {
        console.log('res======',res)
        //setSelectedRowKeys([])
        setSelectButton({
          pass: false
        })
      }
    }
  )
  const { loading: changeFormStatusLoading, runAsync: updateStatus } = useEasyFormRequest(
    () =>
      changeFormStatus(location.state.insId, {
        formIds: selectedRowKeys,
        status: 0
      }),
    {
      ready: Boolean(location.state.insId),
      manual: true,
      onSuccess: () => {
        refreshList()
      }
    }
  )
  const { loading: deleteFormLoading, runAsync: runDeleteForm } = useEasyFormRequest(
    (insId) => deleteFormIns(location.state.id, { formIds: insId }),
    {
      manual: true,
      //ready: Boolean(location.state.insId),
      onSuccess: () => {
        refreshList()
      }
    }
  )
  const fieldsInfo = useMemo(() => {
    return (pageDefine || []).reduce((p: any, c: any) => {
      const componentType = c.componentName.split('.').pop()
      return {
        ...p,
        [c._id]: {
          componentName: c.componentName,
          componentType: componentType,
          options: c?.props?.option || [],
          componentZhName: ComponentNameMap[componentType] || '未知'
        }
      }
    }, {})
  }, [pageDefine])
  const handleAllow = () => {
    if (settings?.type == 'APPLY' && selectedRowKeys.length && selectButton.pass) {
      updateStatus()
    } else if (!selectButton.pass && selectedRowKeys.length) {
      message.info('选择人员中存在已批准人员，请勿重复批准')
    } else if (!selectedRowKeys.length) {
      message.info('请选择人员')
    }
  }
  const handleMenuClick = (key: number) => {
    // handleExport(key === 'CONDITION')
    exportExcelForInstanceForm(
      id,
      key === 1
        ? {}
        : {
            pageNo: pagination.current,
            pageSize: pagination.pageSize,
            orderBy: sorter,
            workflowDoneReason: workstatus,
            ticketStatus,
            isWorkflowDone: isDown,
            query: JSON.stringify({
              creatorName: tableFilter
            })
          },
      settings?.name
    )
  }
  const { data: participantCount } = useEasyFormRequest(() => getStatisticParticipate(id, '', {}), {
    ready: Boolean(id)
  })
  const { data: participantCount2 } = useEasyFormRequest(() => getStatisticParticipate(id, '', { ticketStatus: 1 }), {
    ready: Boolean(id)
  })
  const { data: participantCount3 } = useEasyFormRequest(() => getStatisticParticipate(id, '', { status: 0 }), {
    ready: Boolean(id)
  })
  const handleCancel = () => {
    setIsModalOpen(false)
  }
  const handleOk = () => {
    setIsModalOpen(false)
  }

  const getColumn = useCallback(() => {
    let column
    if (settings?.type == 'APPLY') {
      //是活动表单，需要展示签到信息
      if (workflow && Object.keys(workflow).length) {
        //流程表单，需要展示流程信息
        column = [
          {
            title: '姓名',
            dataIndex: 'name',
            key: 'name',
            render: (text: any) => (
              <a
                onClick={() => {
                  setIsModalOpen(true)
                  setParticipantDetail(List && List.data.filter((item: any) => item.creatorName == text)[0])
                }}
              >
                {text}
              </a>
            )
          },
          {
            title: '学号/工号',
            dataIndex: 'creatorId',
            key: 'creatorId'
            //sorter: true
          },
          {
            title: '院系/机构',
            dataIndex: 'org',
            key: 'org'
          },
          {
            title: '手机号',
            key: 'creatorMobile',
            dataIndex: 'creatorMobile'
          },
          {
            title: '性别',
            key: 'sex',
            dataIndex: 'sex',
            render: (value: any) => {
              return <Typography sx={{ fontWeight: 'bold' }}>{value === '1' ? '男' : '女'}</Typography>
            }
          },
          {
            title: '签到',
            key: 'ticketStatus',
            dataIndex: 'ticketStatus',
            sorter: true,
            render: (text: any) => {
              if (settings?.apply?.isTicket) {
                if (text == '0') {
                  return <Typography sx={{ color: 'error.main', fontWeight: 'bold' }}>{'未签到'}</Typography>
                } else if (text == '1') {
                  return <Typography sx={{ color: 'success.main', fontWeight: 'bold' }}>{'已签到'}</Typography>
                } else {
                  return <Typography sx={{ fontWeight: 'bold' }}>{'未签到'}</Typography>
                }
              } else {
                return <Typography sx={{ color: 'success.main', fontWeight: 'bold' }}>{'无需签到'}</Typography>
              }
            }
          },
          {
            title: '流程状态',
            key: 'reason',
            dataIndex: 'reason',
            sorter: true,
            render: (text: any) => {
              if (text == 'agree') {
                return <Typography sx={{ color: 'success.main', fontWeight: 'bold' }}>{'已通过'}</Typography>
              } else if (text == 'start') {
                return <Typography sx={{ fontWeight: 'bold', color: 'text.secondary' }}>{'已取消'}</Typography>
              } else if (text == 'refuse') {
                return <Typography sx={{ fontWeight: 'bold', color: 'error.main' }}>{'未通过'}</Typography>
              } else {
                return <Typography sx={{}}>{'待审批'}</Typography>
              }
            }
          },
          {
            title: '状态',
            key: 'status',
            dataIndex: 'status',
            sorter: true,
            render: (text: any) => {
              if (text == '0') {
                return <Typography sx={{ color: 'success.main', fontWeight: 'bold' }}>{'已报名'}</Typography>
              } else if (text == '2') {
                return <Typography sx={{}}>{'待审核'}</Typography>
              } else if (text == '1') {
                return <Typography sx={{ fontWeight: 'bold', color: 'text.secondary' }}>{'已取消'}</Typography>
              }
            }
          },
          {
            title: '操作',
            key: 'action',
            sorter: false,
            render: (text: any, a: any, c: any) => {
              return (
                <Dropdown
                  menu={{
                    items: [
                      {
                        label: (
                          <Popconfirm
                            title='删除记录'
                            description='确定要删除该记录吗?'
                            onConfirm={() => {
                              runDeleteForm([text.key])
                            }}
                            //onCancel={cancel}
                            okText='确定'
                            cancelText='取消'
                          >
                            <Typography sx={{ color: 'error.main' }}>删除</Typography>
                          </Popconfirm>
                        ),
                        key: '0'
                      }
                    ]
                  }}
                >
                  <span onClick={(e) => e.preventDefault()}>
                    <Space>
                      <MoreOutlined />
                    </Space>
                  </span>
                </Dropdown>
              )
            }
          }
        ]
      } else {
        column = [
          {
            title: '姓名',
            dataIndex: 'name',
            key: 'name',
            render: (text: any) => (
              <a
                onClick={() => {
                  setIsModalOpen(true)
                  setParticipantDetail(List && List.data.filter((item: any) => item.creatorName == text)[0])
                }}
              >
                {text}
              </a>
            )
          },
          {
            title: '学号/工号',
            dataIndex: 'creatorId',
            key: 'creatorId'
            //sorter: true
          },
          {
            title: '院系/机构',
            dataIndex: 'org',
            key: 'org'
          },
          {
            title: '手机号',
            key: 'creatorMobile',
            dataIndex: 'creatorMobile'
          },
          {
            title: '性别',
            key: 'sex',
            dataIndex: 'sex',
            render: (value: any) => {
              return <Typography sx={{ fontWeight: 'bold' }}>{value === '1' ? '男' : '女'}</Typography>
            }
          },
          {
            title: '签到',
            key: 'ticketStatus',
            dataIndex: 'ticketStatus',
            sorter: true,
            render: (text: any) => {
              if (settings?.apply?.isTicket) {
                if (text == '0') {
                  return <Typography sx={{ color: 'error.main', fontWeight: 'bold' }}>{'未签到'}</Typography>
                } else if (text == '1') {
                  return <Typography sx={{ color: 'success.main', fontWeight: 'bold' }}>{'已签到'}</Typography>
                } else {
                  return <Typography sx={{ fontWeight: 'bold' }}>{'未签到'}</Typography>
                }
              } else {
                return <Typography sx={{ color: 'success.main', fontWeight: 'bold' }}>{'无需签到'}</Typography>
              }
            }
          },
          {
            title: '状态',
            key: 'status',
            dataIndex: 'status',
            sorter: true,
            render: (text: any) => {
              if (text == '0') {
                return <Typography sx={{ color: 'success.main', fontWeight: 'bold' }}>{'已报名'}</Typography>
              } else if (text == '2') {
                return <Typography sx={{}}>{'待审核'}</Typography>
              } else if (text == '1') {
                return <Typography sx={{ fontWeight: 'bold', color: 'text.secondary' }}>{'已取消'}</Typography>
              }
            }
          },
          {
            title: '操作',
            key: 'action',
            sorter: false,
            render: (text: any, a: any, c: any) => {
              return (
                <Dropdown
                  menu={{
                    items: [
                      {
                        label: (
                          <Popconfirm
                            title='删除记录'
                            description='确定要删除该记录吗?'
                            onConfirm={() => {
                              runDeleteForm([text.key])
                            }}
                            //onCancel={cancel}
                            okText='确定'
                            cancelText='取消'
                          >
                            <Typography sx={{ color: 'error.main' }}>删除</Typography>
                          </Popconfirm>
                        ),
                        key: '0'
                      }
                    ]
                  }}
                >
                  <span onClick={(e) => e.preventDefault()}>
                    <Space>
                      <MoreOutlined />
                    </Space>
                  </span>
                </Dropdown>
              )
            }
          }
        ]
      }
    } else {
      if (workflow && Object.keys(workflow).length) {
        column = [
          {
            title: '姓名',
            dataIndex: 'name',
            key: 'name',
            render: (text: any) => (
              <a
                onClick={() => {
                  setIsModalOpen(true)
                  setParticipantDetail(List && List.data.filter((item: any) => item.creatorName == text)[0])
                }}
              >
                {text}
              </a>
            )
          },
          {
            title: '学号/工号',
            dataIndex: 'creatorId',
            key: 'creatorId'
            //sorter: true
          },
          {
            title: '院系/机构',
            dataIndex: 'org',
            key: 'org'
          },
          {
            title: '手机号',
            key: 'creatorMobile',
            dataIndex: 'creatorMobile'
          },
          {
            title: '性别',
            key: 'sex',
            dataIndex: 'sex',
            render: (value: any) => {
              return <Typography sx={{ fontWeight: 'bold' }}>{value == 1 ? '男' : '女'}</Typography>
            }
          },
          {
            title: '状态',
            key: 'status',
            dataIndex: 'status',
            sorter: true,
            render: (text: any) => {
              if (text == '0') {
                return <Typography sx={{ color: 'success.main', fontWeight: 'bold' }}>{'已提交'}</Typography>
              } else if (text == '2') {
                return <Typography sx={{}}>{'待审核'}</Typography>
              } else if (text == '1') {
                return <Typography sx={{ fontWeight: 'bold', color: 'text.secondary' }}>{'已取消'}</Typography>
              }
            }
          },
          {
            title: '流程状态',
            key: 'reason',
            dataIndex: 'reason',
            sorter: true,
            render: (text: any) => {
              if (text == 'agree') {
                return <Typography sx={{ color: 'success.main', fontWeight: 'bold' }}>{'已通过'}</Typography>
              } else if (text == 'start') {
                return <Typography sx={{ fontWeight: 'bold', color: 'text.secondary' }}>{'已退回'}</Typography>
              } else if (text == 'refuse') {
                return <Typography sx={{ fontWeight: 'bold', color: 'error.main' }}>{'已拒绝'}</Typography>
              } else {
                return <Typography sx={{}}>{'待审批'}</Typography>
              }
            }
          },
          {
            title: '操作',
            key: 'action',
            sorter: false,
            render: (text: any, a: any, c: any) => {
              return (
                <Dropdown
                  menu={{
                    items: [
                      {
                        label: (
                          <Popconfirm
                            title='删除记录'
                            description='确定要删除该记录吗?'
                            onConfirm={() => {
                              runDeleteForm([text.key])
                            }}
                            //onCancel={cancel}
                            okText='确定'
                            cancelText='取消'
                          >
                            <Typography sx={{ color: 'error.main' }}>删除</Typography>
                          </Popconfirm>
                        ),
                        key: '0'
                      }
                    ]
                  }}
                >
                  <span onClick={(e) => e.preventDefault()}>
                    <Space>
                      <MoreOutlined />
                    </Space>
                  </span>
                </Dropdown>
              )
            }
          }
        ]
      } else {
        column = [
          {
            title: '姓名',
            dataIndex: 'name',
            key: 'name',
            render: (text: any) => (
              <a
                onClick={() => {
                  setIsModalOpen(true)
                  setParticipantDetail(List && List.data.filter((item: any) => item.creatorName == text)[0])
                }}
              >
                {text}
              </a>
            )
          },
          {
            title: '学号/工号',
            dataIndex: 'creatorId',
            key: 'creatorId'
            //sorter: true
          },
          {
            title: '院系/机构',
            dataIndex: 'org',
            key: 'org'
          },
          {
            title: '手机号',
            key: 'creatorMobile',
            dataIndex: 'creatorMobile'
          },
          {
            title: '性别',
            key: 'sex',
            dataIndex: 'sex',
            render: (value: any) => {
              return <Typography sx={{ fontWeight: 'bold' }}>{value == 1 ? '男' : '女'}</Typography>
            }
          },
          {
            title: '状态',
            key: 'status',
            dataIndex: 'status',
            sorter: true,
            render: (text: any) => {
              if (text == '0') {
                return <Typography sx={{ color: 'success.main', fontWeight: 'bold' }}>{'已提交'}</Typography>
              } else if (text == '2') {
                return <Typography sx={{}}>{'待审核'}</Typography>
              } else if (text == '1') {
                return <Typography sx={{ fontWeight: 'bold', color: 'text.secondary' }}>{'已取消'}</Typography>
              }
            }
          },
          {
            title: '操作',
            key: 'action',
            sorter: false,
            render: (text: any, a: any, c: any) => {
              return (
                <Dropdown
                  menu={{
                    items: [
                      {
                        label: (
                          <Popconfirm
                            title='删除记录'
                            description='确定要删除该记录吗?'
                            onConfirm={() => {
                              runDeleteForm([text.key])
                            }}
                            //onCancel={cancel}
                            okText='确定'
                            cancelText='取消'
                          >
                            <Typography sx={{ color: 'error.main' }}>删除</Typography>
                          </Popconfirm>
                        ),
                        key: '0'
                      }
                    ]
                  }}
                >
                  <span onClick={(e) => e.preventDefault()}>
                    <Space>
                      <MoreOutlined />
                    </Space>
                  </span>
                </Dropdown>
              )
            }
          }
        ]
      }
    }
    return column
  }, [settings, List, workflow, runDeleteForm])
  const getFilter = () => {
    if (isDown === false) {
      return '待审批'
    } else if (!workstatus) {
      return '全部'
    } else if (workstatus == 'agree') {
      return '已通过'
    } else if (workstatus == 'refuse') {
      return '未通过'
    } else {
      return '全部'
    }
  }
  return (
    <>
      <Box
        ref={ref}
        sx={{ p: 3, minHeight: '100%', backgroundColor: 'white', display: 'flex', flexDirection: 'column' }}
      >
        <Box
          sx={{
            display: 'flex',
            alignItems: 'center',
            '.createForm': {
              backgroundColor: 'primary.main',
              color: 'white'
            }
          }}
        >
          <Box
            sx={{
              //pt: 3,
              '.ezf-radio-button-wrapper.ezf-radio-button-wrapper-checked': {
                backgroundColor: 'primary.main'
              }
            }}
          >
            <Radio.Group
              onChange={(e) => {
                const value = e.target.value || ''
                setFilterType(value)
              }}
              value={filterType}
              optionType='button'
              buttonStyle='solid'
            >
              {[
                { id: 1, title: '参与明细' },
                { id: 2, title: '表单统计' },
                { id: 3, title: '表单项统计' }
                // { id: 4, title: '表单统计' }
              ].map((type) => (
                <Radio.Button key={type.id} style={{ padding: '0 32px' }} value={type.id}>
                  {type.title}
                </Radio.Button>
              ))}
            </Radio.Group>
          </Box>
        </Box>

        <TabPanel value={1} showValue={filterType}>
          <Box sx={{ display: 'flex', justifyContent: 'space-between', py: 3, alignItems: 'center' }}>
            <Box sx={{ display: 'flex', alignItems: 'center', color: 'grey' }}>
              <Box
                sx={{
                  '.ezf-input-search-button': {
                    backgroundColor: 'background.default',
                    borderRadius: '5px',
                    border: 'none'
                  },
                  '.searchInput': {
                    backgroundColor: 'background.default'
                  }
                }}
              >
                <Search
                  placeholder='关键字搜索'
                  className='searchInput'
                  style={{ borderRadius: '5px' }}
                  onSearch={(value) => {
                    setTableFilter(value)
                  }}
                  enterButton={false}
                  bordered={false}
                />
              </Box>
              {workflow && Object.keys(workflow).length ? (
                <Box sx={{ pl: 1 }}>
                  <Dropdown
                    menu={{
                      selectable: true,
                      defaultSelectedKeys: [''],
                      items: [
                        {
                          label: <span>全部</span>,
                          key: '',
                          onClick: () => {
                            setWorkstatus('')
                            setIsDown(undefined)
                          }
                        },
                        {
                          label: <span>已通过</span>,
                          key: 1,
                          onClick: () => {
                            setWorkstatus('agree')
                            setIsDown(undefined)
                          }
                        },
                        {
                          label: <span>未通过</span>,
                          key: 0,
                          onClick: () => {
                            setWorkstatus('refuse')
                            setIsDown(undefined)
                          }
                        },
                        {
                          label: <span>待审批</span>,
                          key: 2,
                          onClick: () => {
                            setIsDown(false)
                            setWorkstatus('')
                          }
                        }
                      ]
                    }}
                  >
                    <span onClick={(e) => e.preventDefault()}>
                      <Typography
                        sx={{
                          fontSize: 'body2.fontSize',
                          transitionDuration: 0.33,
                          color: 'grey',
                          cursor: 'pointer',
                          ':hover': {
                            color: 'primary.main'
                          }
                        }}
                      >
                        {getFilter()}
                        <CaretDownOutlined />
                      </Typography>
                    </span>
                  </Dropdown>
                </Box>
              ) : (
                ''
              )}
              {settings?.apply?.isTicket ? (
                <Box sx={{ pl: 1 }}>
                  <Dropdown
                    menu={{
                      selectable: true,
                      defaultSelectedKeys: [''],
                      items: [
                        {
                          label: <span>全部</span>,
                          key: '',
                          onClick: () => {
                            setTicketStatus('')
                          }
                        },
                        {
                          label: <span>已签到</span>,
                          key: 1,
                          onClick: () => {
                            setTicketStatus('1')
                          }
                        },
                        {
                          label: <span>未签到</span>,
                          key: 0,
                          onClick: () => {
                            setTicketStatus('0')
                          }
                        }
                      ]
                    }}
                  >
                    <span onClick={(e) => e.preventDefault()}>
                      <Typography
                        sx={{
                          fontSize: 'body2.fontSize',
                          transitionDuration: 0.33,
                          color: 'grey',
                          cursor: 'pointer',
                          ':hover': {
                            color: 'primary.main'
                          }
                        }}
                      >
                        {!ticketStatus
                          ? '签到情况：全部'
                          : ticketStatus == '1'
                          ? '签到情况：已签到'
                          : '签到情况：未签到'}
                        <CaretDownOutlined />
                      </Typography>
                    </span>
                  </Dropdown>
                </Box>
              ) : (
                ''
              )}
              {/* {settings?.apply && settings.apply.isTicket ? (
                <Typography
                  sx={{
                    color: 'text.secondary',
                    display: 'flex',
                    alignItems: 'center',
                    marginLeft: '16px',
                    fontSize: 'body2.fontSize'
                  }}
                >
                  当前签到/报名情况：
                  <Typography sx={{ fontSize: 'h2.fontSize', fontWeight: '600', color: 'primary.main' }}>
                    {`${(participantCount2 && participantCount2.data[0] && participantCount2.data[0].count) || 0}/${
                      (participantCount && participantCount.data[0] && participantCount.data[0].count) || 0
                    }`}
                  </Typography>
                </Typography>
              ) : (
                <Typography
                  sx={{
                    color: 'text.secondary',
                    display: 'flex',
                    alignItems: 'center',
                    marginLeft: '16px',
                    fontSize: 'body2.fontSize'
                  }}
                >
                  当前审批/报名情况：
                  <Typography sx={{ fontSize: 'h2.fontSize', fontWeight: '600', color: 'primary.main' }}>
                    {`${(participantCount3 && participantCount3.data[0] && participantCount3.data[0].count) || 0}/${
                      (participantCount && participantCount.data[0] && participantCount.data[0].count) || 0
                    }`}
                  </Typography>
                </Typography>
              )} */}
              <Typography
                sx={{
                  color: 'text.secondary',
                  display: 'flex',
                  alignItems: 'center',
                  marginLeft: '16px',
                  fontSize: 'body2.fontSize'
                }}
              >
                参与人数：
                <Typography sx={{ fontSize: 'h2.fontSize', fontWeight: '600', color: 'primary.main' }}>
                  {`${(participantCount && participantCount.data[0] && participantCount.data[0].count) || 0}人`}
                </Typography>
              </Typography>
            </Box>

            <Box sx={{ display: 'flex', alignItems: 'center' }}>
              {settings?.type == 'APPLY' ? (
                <Box sx={{ pr: 2, fontSize: 'body2.fontSize', '.passbutton': { backgroundColor: 'primary.main' } }}>
                  <Button loading={changeFormStatusLoading} onClick={handleAllow} className='passbutton' type='primary'>
                    批准报名
                  </Button>
                </Box>
              ) : (
                ''
              )}
              {selectedRowKeys.length ? (
                <Box sx={{ pr: 2, fontSize: 'body2.fontSize' }}>
                  <Popconfirm
                    title='批量删除记录'
                    description='确定要删除选中的记录吗?'
                    onConfirm={() => {
                      runDeleteForm(selectedRowKeys)
                    }}
                    //onCancel={cancel}
                    okText='确定'
                    cancelText='取消'
                  >
                    <Button loading={deleteFormLoading} danger type='primary'>
                      {`批量删除${selectedRowKeys.length ? '(' + selectedRowKeys.length + ')' : ''}`}
                    </Button>
                  </Popconfirm>
                </Box>
              ) : (
                ''
              )}
              <Dropdown
                menu={{
                  items: [
                    {
                      key: '1',
                      label: (
                        <Typography
                          //sx={{ color: 'primary.main' }}
                          onClick={() => {
                            handleMenuClick(1)
                          }}
                        >
                          全量导出
                        </Typography>
                      )
                    },
                    {
                      key: '2',
                      label: (
                        <Typography
                          //sx={{ color: 'primary.main' }}
                          onClick={() => {
                            handleMenuClick(2)
                          }}
                        >
                          条件导出
                        </Typography>
                      )
                    }
                  ]
                }}
                placement='bottomLeft'
              >
                <Button>导出</Button>
              </Dropdown>
            </Box>
          </Box>
          <Box
            sx={{
              height: '100%',
              'thead th.ezf-table-cell': {
                backgroundColor: 'background.default'
              }
            }}
          >
            <Table
              //bordered
              onChange={(pagination, filters, sorter) => {
                const { field, order } = sorter as any
                setPagination(pagination)

                sorter && field ? setSorter(`${field} ${order == 'ascend' ? 'asc' : 'desc'}`) : setSorter('')
              }}
              // rowSelection={
              //   (settings?.apply &&
              //     settings.apply.type == 'AUDIT' && {
              //       type: 'checkbox',
              //       preserveSelectedRowKeys: true,
              //       onChange: (selectedRowKeys: any, selectedRows: any) => {
              //         console.log(`selectedRowKeys: ${selectedRowKeys}`, 'selectedRows: ', selectedRows)
              //         setSelectedRowKeys(selectedRowKeys)
              //         setSelectButton({
              //           pass: selectedRows.every((item: any) => item.status == 2)
              //         })
              //       }
              //     }) ||
              //   false
              // }
              rowSelection={{
                type: 'checkbox',
                preserveSelectedRowKeys: true,
                onChange: (selectedRowKeys: any, selectedRows: any) => {
                  console.log(`selectedRowKeys: ${selectedRowKeys}`, 'selectedRows: ', selectedRows)
                  setSelectedRowKeys(selectedRowKeys)
                  setSelectButton({
                    pass: selectedRows.every((item: any) => item.status == 2)
                  })
                }
              }}
              loading={ListLoading}
              pagination={{
                ...pagination,
                total: (List && List.total) || 0
              }}
              columns={getColumn()}
              dataSource={
                List &&
                List.data.map((item: any) => ({
                  key: item.id,
                  name: item.creatorName,
                  creatorId: item.creatorId,
                  org: item.creatorOrgName || '-',
                  //spec: item.specialitity || '-',
                  sex: item.creatorGender,
                  creatorMobile: item.creatorMobile,
                  ticketStatus: item.ticketStatus,
                  status: item.status,
                  reason: item.reason
                }))
              }
            />
          </Box>
        </TabPanel>
        {/* <TabPanel value={4} showValue={filterType}>
          <Box
            sx={{
              height: '100%',
              '.ant-steps .ant-steps-item-process .ant-steps-item-icon': {
                backgroundColor: 'primary.main'
              },
              '.ant-steps .ant-steps-item-finish .ant-steps-item-icon': {
                backgroundColor: 'primary.main'
              },
              '.ant-steps .ant-steps-item-finish .ant-steps-item-icon >.ant-steps-icon': {
                color: 'white'
              },
              '.ant-steps .ant-steps-item-wait .ant-steps-item-icon': {}
            }}
          >
            <Steps
              style={{ display: 'flex', alignItems: 'center', height: '100%' }}
              current={1}
              labelPlacement='vertical'
              items={[
                {
                  title: <Typography sx={{ fontWeight: 'bold', fontSize: 'h2' }}>管理员</Typography>,
                  description: <Typography sx={{ color: 'text.secondary' }}>发布表单任务</Typography>
                },
                {
                  title: <Typography sx={{ fontWeight: 'bold', fontSize: 'h4' }}>辅导员</Typography>,
                  description: <Typography sx={{ color: 'text.secondary' }}>任务派发学生</Typography>
                },
                {
                  title: <Typography sx={{ fontWeight: 'bold', fontSize: 'h4' }}>学生</Typography>,
                  description: <Typography sx={{ color: 'text.secondary' }}>完成表单填写</Typography>
                },
                {
                  title: <Typography sx={{ fontWeight: 'bold', fontSize: 'h4' }}>辅导员</Typography>,
                  description: <Typography sx={{ color: 'text.secondary' }}>表单信息上报</Typography>
                },
                {
                  title: <Typography sx={{ fontWeight: 'bold', fontSize: 'h4' }}>学工部</Typography>,
                  description: <Typography sx={{ color: 'text.secondary' }}>上报信息核验</Typography>
                }
              ]}
            />
          </Box>
        </TabPanel> */}
        <TabPanel value={2} showValue={filterType}>
          <Box>
            <ActivitySummary activityId={id}></ActivitySummary>
          </Box>
        </TabPanel>
        <TabPanel value={3} showValue={filterType}>
          <Box>
            <FormItemSummary activityId={id}></FormItemSummary>
          </Box>
        </TabPanel>
        <Drawer
          closeIcon={<CloseCircleOutlined></CloseCircleOutlined>}
          //title='Basic Modal'
          open={isModalOpen}
          //onOk={handleOk}
          onClose={handleCancel}
          footer={null}
          width={500}
        >
          <ParticipantDetail
            pageDefine={pageDefine}
            fieldsInfo={fieldsInfo}
            instanceId={instanceId}
            participantDetail={participantDetail}
            activityId={id}
          ></ParticipantDetail>
        </Drawer>
      </Box>
    </>
  )
})
