import React from 'react'
import { Box, styled } from '@mui/system'
import {
  FillinOutline as IconNode,
  UserOutline as IconUser,
  CloseOutline as IconClose,
  AddOutline as IconAdd,
  AddCircleOutline as IconAddOutline,
  DeleteOutline as IconDelete,
  SetOutline as IconSetting
} from 'antd-mobile-icons'
import ParticipatorPicker from '@yiban/participator-picker'
import { Input } from 'antd-mobile'
import { Typography } from '@/components'
import { transformToList, Categories } from '@/components/PersonnelPicker2/util'
import { Dropdown, Button, Form, Input as PcInput } from 'antd'

const Root = styled(Box)({
  display: 'grid',
  //backgroundColor: '#F8F8F8',
  gridTemplateColumns: '1fr 15fr',
  columnGap: 8
})

const Circle = styled(Box)(({ theme }) => ({
  width: 36,
  height: 36,
  backgroundColor: theme.palette.primary.main,
  color: '#FFF',
  display: 'flex',
  alignItems: 'center',
  justifyContent: 'center',
  borderRadius: '50%'
}))
const Line = styled('div')<any>(({ theme }) => ({
  width: 1,
  backgroundColor: theme.palette.primary.main
}))

const NodeContainer = styled(Box)(({ theme }) => ({
  borderRadius: '8px',
  //backgroundColor: '#F8F8F8',
  padding: 12,
  flex: '1 1'
}))

const StyledInput = styled(Input)({
  '--font-size': '14px'
})

interface IUserProps {
  id: string
  name: string
  type?: string
  onDelete?: (id: string) => void
}
const UserTag = ({ id, name, onDelete }: IUserProps) => {
  const handleDelete = () => {
    if (typeof onDelete === 'function') {
      onDelete(id)
    }
  }
  return (
    <Box
      className='flex items-center'
      sx={{
        color: 'primary.main',
        border: 1,
        borderColor: 'primary.main',
        borderRadius: 1,
        px: 1,
        py: 0.25,
        mx: 0.5,
        my: 0.5
      }}
    >
      <Typography sx={{ fontSize: 12, color: 'inherit', mr: 0.5 }}>{name}</Typography>
      <IconClose onClick={handleDelete} />
    </Box>
  )
}
interface IBtnAddProps {
  onAdd?: () => void
}
const BtnAdd = React.forwardRef(({ onAdd, ...other }: IBtnAddProps, ref) => {
  return (
    <Box
      ref={ref}
      onClick={onAdd}
      className='flex items-center'
      sx={{
        color: 'primary.main',
        border: 1,
        borderStyle: 'dotted',
        borderColor: 'primary.main',
        borderRadius: 1,
        px: 1,
        py: 0.25,
        mx: 0.5
      }}
      {...other}
    >
      <IconAdd />
    </Box>
  )
})

interface INodeDisplayProps {
  hideTopLine?: boolean
  hideBottomLine?: boolean
}

interface ITaskNodeProps {
  name?: string
  id?: string
  participants?: Record<string, any>
  onDelete?: () => void
  index?: any
  onDeleteParticipant?: (d: any) => void
  onAddParticipant?: (category?: any) => void
  onChange?: (index?: any, value?: any) => void
  onNameChange?: (name: string) => void
}

const Node = ({
  hideTopLine,
  hideBottomLine,
  name,
  index,
  id,
  participants,
  onDelete,
  onChange,
  onDeleteParticipant,
  onAddParticipant,
  onNameChange
}: ITaskNodeProps & INodeDisplayProps) => {
  const participantArr = React.useMemo<any[]>(() => {
    return participants ? transformToList(participants) : []
  }, [participants])
  const handleDeleteParticipant = (d: any) => {
    if (typeof onDeleteParticipant === 'function') {
      onDeleteParticipant(d)
    }
  }
  return (
    <Root>
      <Box className='flex flex-col items-center justify-center'>
        <Line className='flex-1' sx={{ opacity: hideTopLine ? 0 : 1 }} />
        <Circle>
          {/* <IconNode /> */}
          {index + 1}
        </Circle>
        <Line className='flex-1' sx={{ opacity: hideBottomLine ? 0 : 1 }} />
      </Box>
      <Box className='flex items-center'>
        <NodeContainer
          sx={{
            '.ezf-form-item-row': {
              display: 'flex',
              flexDirection: 'row!important'
            }
          }}
        >
          <Form>
            <Form.Item label={'环节名称'} labelCol={{ span: 4 }} wrapperCol={{ span: 22 }}>
              <Box className='flex items-center justify-between'>
                <PcInput
                  autoFocus
                  placeholder='请输入'
                  onChange={(e) => {
                    const value = e.target.value
                    onNameChange && onNameChange(value || '')
                  }}
                  style={{ marginBottom: 8 }}
                  value={name}
                />
              </Box>
            </Form.Item>
            <Form.Item label={'审批人'} labelCol={{ span: 4 }} wrapperCol={{ span: 22 }}>
              <ParticipatorPicker
                // advanced={adminType === ADMIN_TYPE.SUPER}
                //ref={personnelRef}
                value={participants}
                extend={{
                  categories: [{ key: 'expr', label: '自动筛选' }],
                  data: {
                    expr: [{ code: 'postset:poss_xgbzr', name: '部门学工办主任' }]
                  }
                }}
                onChange={(value: any) => {
                  onChange && onChange(index, value)
                }}
                placeholder={'请选择审批人员'}
                isModal={true}
                modalWidth={800}
                // categories={CandidateCat}
              />
            </Form.Item>
          </Form>

          {/* <Box sx={{ color: 'primary.main' }} className='flex items-center flex-wrap'>
            <IconUser style={{ marginRight: 4 }} />
            {participantArr?.map((item) => (
              <UserTag key={item.code} id={item.code} name={item.name} onDelete={() => handleDeleteParticipant(item)} />
            ))}
            <Dropdown
              menu={{ items: [...Categories, { key: 'expr', label: '自动筛选' }], onClick: onAddParticipant }}
              trigger={['click']}
            >
              <BtnAdd />
            </Dropdown>
          </Box> */}
        </NodeContainer>
        <Button
          size='small'
          type='text'
          icon={
            <Typography onClick={onDelete} sx={{ color: 'primary.main' }} className='shrink-0'>
              <IconDelete />
            </Typography>
          }
        ></Button>
      </Box>
    </Root>
  )
}

interface IAddNodeProps {
  onAddNode?: () => void
}
const AddNode = ({ hideTopLine, hideBottomLine, onAddNode }: INodeDisplayProps & IAddNodeProps) => {
  return (
    <Root>
      <Box sx={{ color: 'primary.main' }} className='flex flex-col items-center justify-center'>
        <Line className='flex-1' sx={{ opacity: hideTopLine ? 0 : 1 }} />
        <Button
          size='small'
          type='text'
          icon={
            <Typography onClick={onAddNode} sx={{ color: 'primary.main' }} className='shrink-0'>
              <IconAddOutline />
            </Typography>
          }
        ></Button>

        <Line className='flex-1' sx={{ opacity: hideBottomLine ? 0 : 1 }} />
      </Box>
      <Box sx={{ color: 'primary.main', py: 2 }}>
        <Button type='primary' size='small'>
          <Typography onClick={onAddNode} component='span' sx={{ fontSize: 14 }}>
            增加审批环节
          </Typography>
        </Button>
      </Box>
    </Root>
  )
}

const demoData = [
  {
    id: 'Task_1',
    name: '辅导员审批',
    participants: [
      {
        id: '1',
        name: '张三'
      },
      {
        id: '2',
        name: '张四'
      }
    ]
  },
  {
    id: 'Task_2',
    name: '院系领导审批',
    participants: [
      {
        id: '1',
        name: '李三'
      },
      {
        id: '2',
        name: '赵四'
      }
    ]
  }
]

interface ITaskNode {
  id: string
  name: string
  [key: string]: any
}

interface IFlowProps {
  dataset: ITaskNode[]
  onAddNode?: (targetIndex: number) => void
  onDeleteNode?: (index: number) => void
  onAddParticipant?: (nodeIndex: number, nodeId?: string) => void
  onChange?: (nodeIndex: any, value?: any) => void
  onDeleteParticipant?: (nodeIndex: number, itemData?: any) => void
  onNodeNameChange?: (nodeIndex: number, name: string) => void
}
export default React.forwardRef<any, IFlowProps>(
  ({ dataset, onAddNode, onDeleteNode, onAddParticipant, onDeleteParticipant, onNodeNameChange, onChange }, ref) => {
    const lastTaskNodeIndex = React.useMemo(() => dataset.length - 1, [dataset])
    const handleAddNode = (index: number) => {
      if (typeof onAddNode === 'function') {
        onAddNode(index)
      }
    }
    const handleDeleteNode = (index: number) => {
      if (typeof onDeleteNode === 'function') {
        onDeleteNode(index)
      }
    }
    const handleNodeNameChange = (index: number, name: string) => {
      if (typeof onNodeNameChange === 'function') {
        onNodeNameChange(index, name)
      }
    }
    const handleAddParticipant = (index: number, category?: any) => {
      if (typeof onAddParticipant === 'function') {
        onAddParticipant(index, category)
      }
    }
    const handleDeleteParticipant = (index: number, itemData: any) => {
      if (typeof onDeleteParticipant === 'function') {
        onDeleteParticipant(index, itemData)
      }
    }
    return (
      <Box>
        {dataset.map(({ id, name, participantDetail }, i) => (
          <React.Fragment key={id}>
            <Node
              hideTopLine={i == 0}
              onDelete={() => handleDeleteNode(i)}
              id={id}
              index={i}
              name={name}
              onChange={onChange}
              participants={participantDetail}
              onNameChange={(newName) => handleNodeNameChange(i, newName)}
              onAddParticipant={(category) => handleAddParticipant(i, category)}
              onDeleteParticipant={(d) => handleDeleteParticipant(i, d)}
            />
            {i !== lastTaskNodeIndex && <AddNode onAddNode={() => handleAddNode(i + 1)} />}
          </React.Fragment>
        ))}
        <AddNode hideTopLine={!dataset.length} hideBottomLine onAddNode={() => handleAddNode(lastTaskNodeIndex + 1)} />
      </Box>
    )
  }
)
