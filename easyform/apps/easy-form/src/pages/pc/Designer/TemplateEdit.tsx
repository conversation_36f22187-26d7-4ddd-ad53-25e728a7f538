import { Box } from '@mui/system'
import React, { forwardRef, useContext, useState, useCallback } from 'react'
import { useLocation, useNavigate } from 'react-router-dom'
import {
  saveActivity,
  exportExcelForInstanceForm,
  toggleActivityEnable,
  getActivityList,
  updateTemp
} from '@/api/easyform'
import { useEasyFormRequest } from '@/hooks/useBaseRequest'
import { UpdateContext } from './context/UpdateContext'
import { message, Modal } from 'antd'
import { EditorForPC } from '@/editor-kit'
import { Typography } from '@/components'
import { ExclamationCircleFilled } from '@ant-design/icons'
import { RemoteComponent } from '@/helper'
import { NoticeBar } from 'antd-mobile'
const { confirm } = Modal
export default forwardRef((props: any, ref) => {
  const location: any = useLocation()
  const { disabled } = props
  const navigate = useNavigate()
  const [saveLoading, setSaveLoading] = useState(0)
  const [confirmOpen, setConfirmOpen] = useState(false)
  const getInitSetting = useCallback(() => {
    if (location.state?.type == 'APPLY') {
      return {
        name: (location.state && location.state.name) || undefined,
        desc: '',
        templateSettings: {},
        imgs: [],
        apply: { type: 'APPLY', applylimit: false, isTicket: false }
      }
    }
    if (location.state?.type == 'PSQ') {
      return {
        name: (location.state && location.state.name) || undefined,
        desc: '',
        templateSettings: {},
        imgs: [],
        psq: { nametype: 'SM' }
      }
    }
    if (location.state?.type == 'INFO') {
      return {
        name: (location.state && location.state.name) || undefined,
        desc: '',
        templateSettings: {},
        imgs: [],
        info: { nametype: 'SM' }
      }
    }
    return {
      name: (location.state && location.state.name) || undefined,
      desc: '',
      templateSettings: {},
      imgs: [],
      apply: { type: 'APPLY', applylimit: false, isTicket: false }
    }
  }, [location.state])
  const Updater = useContext<any>(UpdateContext)

  const [{ define, templateDetail, id, templateId, status }, updater] = Updater
  const { settings = getInitSetting(), page = { componentTree: [] }, workflow = {} } = define
  const { loading: updateTempLoading, runAsync: udateTemplate } = useEasyFormRequest(
    (payload: any) => updateTemp(templateId || '', payload),
    {
      manual: true,
      onSuccess: () => {
        message.success('保存成功')
      }
    }
  )
  const onSave = (pageDefine: any, pageSeting: any) => {
    if (disabled) {
      message.warning('请先取消发布')
      return
    }
    //todo 或许在保存前需要处理下define
    const define = {
      page: pageDefine,
      settings: { ...settings, ...pageSeting },
      workflow
    }
    //使用了模板
    if (location.state?.templateId) {
      ;(define.settings as any).templateId = location.state.templateId
    }
    //设置表单类型
    if (location.state?.type) {
      ;(define.settings as any).type = location.state.type
    }
    // //没这个字段查询参与人数的接口会报错
    // if (!define.settings.participantDetail) {
    //   define.settings.participantDetail = []
    // }
    //提示消息设置到templateSettings下
    if (define.settings.successTip) {
      if (define.settings.templateSettings) {
        define.settings.templateSettings = {
          ...define.settings.templateSettings,
          successTip: define.settings.successTip
        }
      } else {
        define.settings.templateSettings = { successTip: define.settings.successTip }
      }

      delete define.settings.successTip
    }
    //按钮消息设置到templateSettings下
    if (define.settings.btnText) {
      if (define.settings.templateSettings) {
        define.settings.templateSettings = {
          ...define.settings.templateSettings,
          btnText: define.settings.btnText
        }
      } else {
        define.settings.templateSettings = { btnText: define.settings.btnText }
      }

      delete define.settings.btnText
    }
    udateTemplate({ ...templateDetail, define })
    updater({ type: 'updatePage', payload: { define } })
  }
  return (
    <>
      {/* {disabled ? <NoticeBar content='当前表单已发布，若要修改请先取消发布' color='alert' closeable /> : ''} */}
      <Box
        sx={{
          height: '100%',
          div: disabled ? { cursor: 'not-allowed' } : {},
          background: 'white',
          filter: disabled ? 'grayscale(100%)' : '',
          pointerEvents: disabled ? 'none' : 'auto'
        }}
        ref={ref}
      >
        <EditorForPC disabled={disabled} onSave={onSave} pageDefine={page} settings={settings} />
      </Box>
    </>
  )
})
