const obj = {
  expandedKeys: [],

  selectPersons: [
    {
      code: '3120221201114',
      name: '邹嘉豪',
      orgName: ['经济学院'],
      type: '学生'
    },
    {
      code: '3120221201111',
      name: '任文旭',
      orgName: ['经济学院'],
      type: '学生'
    },
    {
      code: '3120221201115',
      name: '刘亿祥',
      orgName: ['经济学院'],
      type: '学生'
    }
  ],
  selectedOrg: [
    {
      key: '40001',
      title: '经济学院(2075)',
      isLeaf: false,
      children: [],
      type: '学生',
      nodeType: '学院'
    },
    {
      key: '40015',
      title: '航空航天学院(693)',
      isLeaf: true,
      children: [],
      type: '教职工',
      nodeType: '组织机构'
    }
  ]
}

const NodeTypeMap = {
  班级: 'clazz'
}
function processData(data) {
  const transform = () => {}
  const obj = {}
  data.selectedOrg.forEach((o) => {
    switch (o.type) {
      case '学生': {
        if (!obj.student) {
          obj.student = {}
        }
        const _key = NodeTypeMap[o.nodeType] || 'org'
        if (!obj.student[_key]) {
          obj.student[_key] = []
        }
        obj.student[_key].push({ code: o.key, name: o.title })
        break
      }
      case '教职工': {
        if (!obj.jzg) {
          obj.jzg = {}
        }
        const _key = NodeTypeMap[o.nodeType] || 'org'
        if (!obj.jzg[_key]) {
          obj.jzg[_key] = []
        }
        obj.jzg[_key].push({ code: o.key, name: o.title })
        break
      }
    }
  })
  return obj
}
console.log(JSON.stringify(processData(obj)))
