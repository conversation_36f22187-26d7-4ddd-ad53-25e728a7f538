import { Box } from '@mui/system'
import React, { forwardRef, useContext, useState, useCallback, useMemo } from 'react'
import { Result, Spin } from 'antd'
import { RemoteComponent } from '@/helper'
import { useAppContext } from '@/context/AppContext'
import { UpdateContext } from './context/UpdateContext'
import { Typography } from '@/components'
export default forwardRef((props, ref) => {
  const [userContext, dispach] = useAppContext()
  const updater = useContext<any>(UpdateContext)
  const [{ define, id, taskInfo }, esdispach] = updater
  const userInfo = useMemo(() => {
    const { user, ybUser } = userContext
    return {
      id: user?.id,
      displayName: user?.name,
      manageOrgs: ybUser?.manageOrgs,
      classLeaderAcademy: ybUser?.classLeaderAcademy,
      manageclasses: ybUser?.manageclasses,
      orgs: user.orgs,
      currentIdentity: ybUser?.currentIdentity
    }
  }, [userContext])
  const ErrMsg = useMemo(() => {
    if (!taskInfo?.id && taskInfo?.detail) {
      return {
        title: '当前委派未发布',
        subTitle: '当前表单尚未发布，委派将在表单发布后生效'
      }
    } else {
      return {
        title: '无委派任务',
        subTitle: '当前表单未启用任务委派'
      }
    }
  }, [taskInfo])
  return (
    <>
      {taskInfo && taskInfo?.id ? (
        <Box sx={{ minHeight: '100%', background: 'white' }} ref={ref}>
          {React.createElement(RemoteComponent, {
            _remoteInfo: {
              componentName: 'yiban-frontend-admin.EasyformTaskDetail',
              version: '0.1.0'
            },
            taskId: taskInfo?.id,
            delegate: true,
            title: <Typography sx={{ fontWeight: 'bold' }}>进度跟踪</Typography>,
            user: userInfo,
            fallback: (
              <Spin tip={'正在加载任务详情'}>
                <Box sx={{ height: '90vh' }}></Box>
              </Spin>
            )
          })}
        </Box>
      ) : (
        <Result status='404' title={ErrMsg.title} subTitle={ErrMsg.subTitle} />
      )}
    </>
  )
})
