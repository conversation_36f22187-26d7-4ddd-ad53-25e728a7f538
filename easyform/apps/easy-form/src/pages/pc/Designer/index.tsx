import React, { useEffect, useState, useRef, useContext, useMemo, createContext, useReducer } from 'react'
import { Box, styled, ThemeProvider } from '@mui/system'
import { Breadcrumb, Tag, List, Row, Col, Drawer, Dropdown, ConfigProvider, Input, Button, Radio, message } from 'antd'
import { TabPanel, Typography } from '@/components'
import FormPreview from './FormPreview'
import FormItemSummary from './FormItemSummary'
import { Action, UpdaterProvider, UpdateContext } from './context/UpdateContext'
import Formstatement from './Formstatement'
import FormEdit from './FormEdit'
import produce from 'immer'
import { useOverlayScrollbars } from '@yiban/system'
import TemplateFormSettings from './TemplateFormSettings'
import TaskDetail from './TaskDetail'
import StartForm from './StartForm'
import FormSetting from './FormSetting'
import theme from '@/themes/pc'
import zhCN from 'antd/locale/zh_CN'
import {
  activityInst,
  getActivity,
  loadFormDataByInstFormId,
  getTemplateDefine,
  getActivityList,
  getTempInfo
} from '@/api/easyform'
import { useLocation, useNavigate } from 'react-router-dom'
import {
  EditFilled,
  AppstoreOutlined,
  FundFilled,
  SaveFilled,
  SettingFilled,
  ProfileFilled,
  SkinFilled
} from '@ant-design/icons'
import { ActivityTab } from '@/@define/Activity'
import { useBaseRequest, useEasyFormRequest } from '@/hooks'
import { ActivityTypes } from '@/@define/Activity'
import { getTaskInfo } from '@/api/task'
import TemplateEdit from './TemplateEdit'
import ThemeEdit from './ThemeEdit'

const LayoutBox = styled(Box)(({ theme }) => ({
  height: '100%',
  overflow: 'hidden',
  background: theme.palette.background.default,
  padding: '20px',
  display: 'flex',
  flexDirection: 'column',
  minWidth: '1400px'
}))

const LayoutBodyBox = styled(Box)(({ theme }) => ({
  height: '100%',
  display: 'flex',
  overflow: 'hidden'
  //background: theme.palette.background.default,
  //padding: '20px'
}))

const SideBox = styled(Box)(({ theme }) => ({
  height: '100%',
  width: '250px',
  background: theme.palette.background.paper,
  //margin: '20px',
  borderRadius: '10px',
  display: 'flex',
  flexDirection: 'column',
  '.startButton': {
    background: theme.palette.success.main,
    fontSize: (theme.typography as any).h4.fontSize,
    height: '55%',
    fontWeight: '600',
    width: '70%',
    color: 'white'
  }
}))
const HeaderBox = styled(Box)(({ theme }) => ({
  padding: '0 0 16px 0',
  display: 'flex',
  justifyContent: 'space-between',
  alignItems: 'center'
}))
const ContentBox = styled(Box)(({ theme }) => ({
  height: '100%',
  width: '100%',
  background: theme.palette.background.paper,
  margin: '0 0 0 20px',
  display: 'flex',
  flexDirection: 'column',
  borderRadius: '10px'
  //padding: '24px'
}))

const SideTab = [
  {
    title: '开始',
    icon: (props: any) => <AppstoreOutlined {...props}></AppstoreOutlined>
  },
  {
    title: '设计表单',
    icon: (props: any) => <EditFilled {...props}></EditFilled>
  },
  {
    title: '表单设置',
    icon: (props: any) => <SettingFilled {...props}></SettingFilled>
  },
  // {
  //   title: '预览表单',
  //   icon: (props: any) => <EyeFilled {...props}></EyeFilled>
  // },
  {
    title: '数据统计',
    icon: (props: any) => <FundFilled {...props}></FundFilled>
  },
  {
    title: '进度跟踪',
    icon: (props: any) => <ProfileFilled {...props}></ProfileFilled>
  }
]
const SideTab1 = [
  {
    title: '开始',
    icon: (props: any) => <AppstoreOutlined {...props}></AppstoreOutlined>
  },
  {
    title: '设计表单',
    icon: (props: any) => <EditFilled {...props}></EditFilled>
  },
  {
    title: '表单设置',
    icon: (props: any) => <SettingFilled {...props}></SettingFilled>
  }
]

//export const ActivityContext = createContext({ settings: '', page: '', overflow: '' })

//表单编辑页面
const Designer = () => {
  const location: any = useLocation()
  const [activityInstances, setActivityInstances] = useState<any>([])
  const navigate = useNavigate()
  //const [BarList, setBarList] = useState(location.state && location.state.id ? SideTab : SideTab1)

  const groupContentRef = useRef(null)

  const updater = useContext(UpdateContext)
  const [{ define, id, pageTo, status }, dispach] = updater
  const [active, setActive] = useState((location?.state?.pageTo && parseInt(location?.state?.pageTo)) ?? pageTo ?? 0)
  const [initialize, instance] = useOverlayScrollbars({ options: { scrollbars: { autoHide: 'scroll' } } })
  useEffect(() => {
    groupContentRef.current && initialize(groupContentRef.current)
  }, [initialize])
  useEasyFormRequest(() => activityInst({ instanceId: id }), {
    ready: Boolean(id),
    onSuccess: (res: any) => {
      setActivityInstances(res.data)
    }
  })
  const FormDesc = useMemo(() => {
    return define?.settings?.type == 'NOTICE' ? '公告' : '表单'
  }, [define?.settings?.type])
  const [refs] = React.useState<Record<number, any>>({
    0: React.createRef(),
    1: React.createRef(),
    //2: React.createRef(),
    3: React.createRef(),
    4: React.createRef()
  })
  //(路由跳转进来后)存在活动id，设置活动定义
  const {
    data,
    refreshAsync: refreshDefine,
    loading: activityDefineLoading
  } = useEasyFormRequest(() => getActivity(id), {
    ready: Boolean(id),
    //refreshDeps: [updater],
    onSuccess: (res: any) => {
      dispach({ type: 'updateDefine', payload: { define: res.data } })
      //message.success('表单定义来自活动')
    }
  })
  //(路由跳转进来后)不存在活动id存在模板id，设置为模板定义
  const { runAsync, loading: templateDefineLoading } = useEasyFormRequest(
    () => getTemplateDefine(location.state.templateId),
    {
      ready: Boolean(location.state && location.state.templateId),
      onSuccess: (res: any) => {
        dispach({ type: 'updateDefine', payload: { define: res.data } })
        //message.success('表单定义来自模板')
      }
    }
  )
  const {
    runAsync: fetchData,
    data: activityStatus,
    loading: activityLoading,
    refreshAsync: refreshActivity
  } = useEasyFormRequest(() => getActivityList({ id: id }), {
    //manual: true,
    ready: Boolean(id),
    refreshDeps: [status?.taskId],
    onSuccess: (data: any) => {
      //dispach({ type: 'updateStatus', payload: { status: data.data[0] } })
      if (data.data[0]?.taskId) {
        const tskId = data.data[0]?.taskId
        getTaskData(tskId)
      }
    }
  })
  const { runAsync: getTaskData } = useBaseRequest((id: any) => getTaskInfo(id), {
    manual: true,
    onSuccess: (res: any) => {
      console.log('获取任务信息成功', res)
      dispach({ type: 'updateTask', payload: { id: res?.id, detail: res } })
    }
  })
  useEffect(() => {
    //配置了委派任务但是没有发布，回填settings中的数据
    if (
      define?.settings &&
      define?.settings.info &&
      define?.settings.info.taskProcess &&
      define?.settings.info.taskProcess.length
    ) {
      const task = define?.settings.info.taskProcess
      if (status && !status.taskId) {
        dispach({ type: 'updateTask', payload: { detail: { process: task } } })
      }
    }
  }, [define, status, dispach])
  //不存在活动id证明该表单未创建，不展示统计页面
  const BarList = useMemo(() => {
    return [
      {
        title: '开始',
        key: 0,
        icon: (props: any) => <AppstoreOutlined {...props}></AppstoreOutlined>
      },
      {
        title: `${FormDesc}主题`,
        key: 5,
        icon: (props: any) => <SkinFilled {...props}></SkinFilled>
      },
      {
        title: `设计${FormDesc}`,
        key: 1,
        icon: (props: any) => <EditFilled {...props}></EditFilled>
      },
      {
        title: `${FormDesc}设置`,
        key: 2,
        icon: (props: any) => <SettingFilled {...props}></SettingFilled>
      },
      // {
      //   title: '预览表单',
      //   icon: (props: any) => <EyeFilled {...props}></EyeFilled>
      // },,
      {
        title: '进度跟踪',
        key: 4,
        hidden: define?.settings?.type != 'INFO',
        icon: (props: any) => <ProfileFilled {...props}></ProfileFilled>
      },
      {
        title: '数据统计',
        key: 3,
        hidden: id && define?.settings?.type != 'NOTICE' ? false : true,
        icon: (props: any) => <FundFilled {...props}></FundFilled>
      }
    ]
  }, [id, define, FormDesc])
  // useEffect(() => {
  //   if (status?.enable == 1 && !document.querySelector('.align-form-disabled-token') && [1, 2].indexOf(active) > -1) {
  //     message.warning({
  //       content: '当前活动已发布，若要修改请先取消发布。',
  //       duration: 5,
  //       className: 'align-form-disabled-token'
  //     })
  //   }
  //   // if ([1, 2].indexOf(active) == -1) {
  //   //   document.getElementsByClassName('align-form-disabled-token')[0]?.remove()
  //   // }
  // }, [status, active])
  return (
    <>
      <LayoutBox>
        <HeaderBox>
          <Breadcrumb
            separator='>'
            items={
              location.state && location.state.fromTemp
                ? [
                    {
                      title: (
                        <Typography
                          onClick={() => {
                            navigate('/pc')
                          }}
                          sx={{ fontWeight: 'bold', fontSize: 'h4.fontSize', cursor: 'pointer' }}
                        >
                          开始
                        </Typography>
                      )
                    },
                    {
                      title: (
                        <Typography
                          onClick={() => {
                            navigate('/pc/formtemplates')
                          }}
                          sx={{ fontWeight: 'bold', fontSize: 'h4.fontSize', cursor: 'pointer' }}
                        >
                          选择表单类型
                        </Typography>
                      )
                    },
                    {
                      title: (
                        <Typography
                          onClick={() => {}}
                          sx={{ fontWeight: 'bold', fontSize: 'h4.fontSize', cursor: 'pointer' }}
                        >
                          {(define.settings && (define.settings as any).type) ||
                          (location.state && (location.state as any).type)
                            ? `表单编辑及配置【${
                                ActivityTypes.filter(
                                  (item) =>
                                    item.id ==
                                    ((define.settings && (define.settings as any).type) ||
                                      (location.state && (location.state as any).type))
                                )[0]?.title
                              }类】`
                            : '表单编辑及配置'}
                        </Typography>
                      )
                    }
                  ]
                : location.state && location.state.pageTo == undefined && active !== undefined
                ? [
                    {
                      title: (
                        <Typography
                          onClick={() => {}}
                          sx={{ fontWeight: 'bold', fontSize: 'h4.fontSize', cursor: 'pointer' }}
                        >
                          {(define.settings && (define.settings as any).type) ||
                          (location.state && (location.state as any).type)
                            ? `表单编辑及配置【${
                                ActivityTypes.filter(
                                  (item) =>
                                    item.id ==
                                    ((define.settings && (define.settings as any).type) ||
                                      (location.state && (location.state as any).type))
                                )[0]?.title
                              }类】`
                            : '表单编辑及配置'}
                        </Typography>
                      )
                    }
                  ]
                : [
                    {
                      title: (
                        <Typography
                          onClick={() => {
                            navigate('/pc')
                          }}
                          sx={{ fontWeight: 'bold', fontSize: 'h4.fontSize', cursor: 'pointer' }}
                        >
                          开始
                        </Typography>
                      )
                    },

                    {
                      title: (
                        <Typography
                          onClick={() => {}}
                          sx={{ fontWeight: 'bold', fontSize: 'h4.fontSize', cursor: 'pointer' }}
                        >
                          {(define.settings && (define.settings as any).type) ||
                          (location.state && (location.state as any).type)
                            ? `表单编辑及配置【${
                                ActivityTypes.filter(
                                  (item) =>
                                    item.id ==
                                    ((define.settings && (define.settings as any).type) ||
                                      (location.state && (location.state as any).type))
                                )[0]?.title
                              }类】`
                            : '表单编辑及配置'}
                        </Typography>
                      )
                    }
                  ]
            }
          />
          <Box
            sx={{
              display: 'flex',
              '.previewForm': {
                mr: 2
              }
            }}
          >
            {/* <Dropdown.Button
                type='primary'
                loading={false}
                onClick={() => {
                  setPreviewOpen(true)
                }}
                icon={<SaveFilled />}
                menu={{
                  items: [
                    {
                      label: '保存',
                      key: '1'
                    },
                    {
                      label: '保存并发布',
                      key: '2'
                    }
                  ]
                }}
              >
                <EyeFilled></EyeFilled>
                预览
              </Dropdown.Button> */}
          </Box>
        </HeaderBox>

        <LayoutBodyBox>
          <SideBox>
            {BarList.map((tab, key) => (
              <Box
                key={tab.key}
                sx={{
                  overflow: 'hidden',
                  display: tab.hidden ? 'none' : 'flex',
                  alignItems: 'center',
                  p: 2,
                  borderBottom: '1px solid',
                  cursor: 'pointer',
                  borderColor: 'background.default',
                  borderRadius: tab.key == 0 ? '10px 10px 0 0' : void 0,
                  backgroundColor: active == tab.key ? 'primary.main' : 'auto',
                  ':hover': {
                    color: active == tab.key ? 'white' : 'primary.main'
                  },
                  '&>span': {
                    fontSize: 16
                  }
                }}
                onClick={() => {
                  //数据加载完毕前不允许跳转
                  if (!activityDefineLoading && !templateDefineLoading) {
                    setActive(tab.key)
                  }
                }}
              >
                {tab.icon({ style: { color: active == tab.key ? 'white' : 'inherit', transitionDuration: '0.3s' } })}
                <Box
                  sx={{
                    fontWeight: 'bold',
                    pl: 1,
                    transitionDuration: '0.3s',
                    fontSize: 'h5.fontSize',
                    color: active == tab.key ? 'white' : 'inherit',
                    m: 0
                  }}
                >
                  {tab.title}
                </Box>
              </Box>
            ))}
          </SideBox>
          <ContentBox ref={groupContentRef} sx={{ backgroundColor: 'transparent' }}>
            <Box sx={{ height: '100%' }}>
              <TabPanel value={1} showValue={active}>
                <FormEdit ref={refs[1]}></FormEdit>
              </TabPanel>
              <TabPanel value={2} showValue={active}>
                <FormSetting ref={refs[2]}></FormSetting>
              </TabPanel>
              <TabPanel value={0} showValue={active} isPersistent={false}>
                <StartForm setActive={setActive} ref={refs[0]}></StartForm>
              </TabPanel>
              <TabPanel value={3} showValue={active}>
                <Formstatement ref={refs[3]}></Formstatement>
              </TabPanel>
              <TabPanel value={4} showValue={active}>
                <TaskDetail ref={refs[4]}></TaskDetail>
              </TabPanel>
              <TabPanel value={5} showValue={active}>
                <ThemeEdit ref={refs[5]} isTheme></ThemeEdit>
              </TabPanel>
            </Box>
          </ContentBox>
          {/* <Drawer
              onClose={() => {
                setPreviewOpen(false)
              }}
              placement='bottom'
              height={'100vh'}
              open={previewOpen}
              headerStyle={{ display: 'none' }}
              bodyStyle={{ padding: 0 }}
              style={{ padding: 0 }}
              destroyOnClose
              footer={null}
            >
              <FormPreview
                onClose={() => {
                  setPreviewOpen(false)
                }}
                activityDefine={activityDefine}
              ></FormPreview>
            </Drawer> */}
        </LayoutBodyBox>
      </LayoutBox>
    </>
  )
}

const TempDesigner = () => {
  const location: any = useLocation()
  const [activityInstances, setActivityInstances] = useState<any>([])
  const navigate = useNavigate()
  //const [BarList, setBarList] = useState(location.state && location.state.id ? SideTab : SideTab1)

  const groupContentRef = useRef(null)

  const updater = useContext(UpdateContext)
  const [{ define, id, pageTo = 1, status, templateId }, dispach] = updater
  const [active, setActive] = useState(1)
  const [initialize, instance] = useOverlayScrollbars({ options: { scrollbars: { autoHide: 'scroll' } } })
  useEffect(() => {
    groupContentRef.current && initialize(groupContentRef.current)
  }, [initialize])
  useEasyFormRequest(() => activityInst({ instanceId: id }), {
    ready: Boolean(id),
    onSuccess: (res: any) => {
      setActivityInstances(res.data)
    }
  })

  const [refs] = React.useState<Record<number, any>>({
    1: React.createRef(),
    2: React.createRef()
  })
  //(路由跳转进来后)存在活动id，设置活动定义
  const { data: TepDefine, loading: templateLoading } = useEasyFormRequest(() => getTemplateDefine(templateId), {
    ready: Boolean(templateId),
    onSuccess: (res: any) => {
      if (res?.data) {
        const define = res.data
        dispach({ type: 'updateDefine', payload: { define } })
      } else {
        message.error('获取模板信息失败')
      }
    }
  })

  const {
    runAsync: fetchData,
    data: activityStatus,
    loading: activityLoading,
    refreshAsync: refreshActivity
  } = useEasyFormRequest(() => getActivityList({ id: id }), {
    //manual: true,
    ready: Boolean(id),
    refreshDeps: [],
    onSuccess: (data: any) => {
      //dispach({ type: 'updateStatus', payload: { status: data.data[0] } })
      if (data.data[0]?.taskId) {
        const tskId = data.data[0]?.taskId
        getTaskData(tskId)
      }
    }
  })
  const { runAsync: getTempDetail } = useEasyFormRequest(() => getTempInfo(templateId || ''), {
    ready: Boolean(templateId),
    onSuccess: (res: any) => {
      const data = res?.data
      dispach({ type: 'updateTemplateDetail', payload: data })
    }
  })
  const { runAsync: getTaskData } = useBaseRequest((id: any) => getTaskInfo(id), {
    manual: true,
    onSuccess: (res: any) => {
      console.log('获取任务信息成功', res)
      dispach({ type: 'updateTask', payload: { id: res?.id, detail: res } })
    }
  })
  useEffect(() => {
    //配置了委派任务但是没有发布，回填settings中的数据
    if (
      define?.settings &&
      define?.settings.info &&
      define?.settings.info.taskProcess &&
      define?.settings.info.taskProcess.length
    ) {
      const task = define?.settings.info.taskProcess
      if (status && !status.taskId) {
        dispach({ type: 'updateTask', payload: { detail: { process: task } } })
      }
    }
  }, [define, status, dispach])
  //不存在活动id证明该表单未创建，不展示统计页面
  const BarList = useMemo(() => {
    return [
      {
        title: '设计模板',
        key: 1,
        icon: (props: any) => <EditFilled {...props}></EditFilled>
      },
      {
        title: '模板设置',
        key: 2,
        icon: (props: any) => <SettingFilled {...props}></SettingFilled>
      }
    ]
  }, [])
  // useEffect(() => {
  //   if (status?.enable == 1 && !document.querySelector('.align-form-disabled-token') && [1, 2].indexOf(active) > -1) {
  //     message.warning({
  //       content: '当前活动已发布，若要修改请先取消发布。',
  //       duration: 5,
  //       className: 'align-form-disabled-token'
  //     })
  //   }
  //   // if ([1, 2].indexOf(active) == -1) {
  //   //   document.getElementsByClassName('align-form-disabled-token')[0]?.remove()
  //   // }
  // }, [status, active])
  return (
    <>
      <LayoutBox>
        <HeaderBox>
          <Breadcrumb
            separator='>'
            items={[
              {
                title: (
                  <Typography
                    onClick={() => {
                      navigate('/pc')
                    }}
                    sx={{ fontWeight: 'bold', fontSize: 'h4.fontSize', cursor: 'pointer' }}
                  >
                    开始
                  </Typography>
                )
              },
              {
                title: (
                  <Typography
                    onClick={() => {}}
                    sx={{ fontWeight: 'bold', fontSize: 'h4.fontSize', cursor: 'pointer' }}
                  >
                    {(define.settings && (define.settings as any).type) ||
                    (location.state && (location.state as any).type)
                      ? `模板编辑【${
                          ActivityTypes.filter(
                            (item) =>
                              item.id ==
                              ((define.settings && (define.settings as any).type) ||
                                (location.state && (location.state as any).type))
                          )[0]?.title
                        }类】`
                      : '表单编辑及配置'}
                  </Typography>
                )
              }
            ]}
          />
          <Box
            sx={{
              display: 'flex',
              '.previewForm': {
                mr: 2
              }
            }}
          >
            {/* <Dropdown.Button
                type='primary'
                loading={false}
                onClick={() => {
                  setPreviewOpen(true)
                }}
                icon={<SaveFilled />}
                menu={{
                  items: [
                    {
                      label: '保存',
                      key: '1'
                    },
                    {
                      label: '保存并发布',
                      key: '2'
                    }
                  ]
                }}
              >
                <EyeFilled></EyeFilled>
                预览
              </Dropdown.Button> */}
          </Box>
        </HeaderBox>

        <LayoutBodyBox>
          <SideBox>
            {BarList.map((tab, key) => (
              <Box
                key={tab.key}
                sx={{
                  overflow: 'hidden',
                  display: 'flex',
                  alignItems: 'center',
                  p: 2,
                  borderBottom: '1px solid',
                  cursor: 'pointer',
                  borderColor: 'background.default',
                  borderRadius: tab.key == 1 ? '10px 10px 0 0' : void 0,
                  backgroundColor: active == tab.key ? 'primary.main' : 'auto',
                  ':hover': {
                    color: active == tab.key ? 'white' : 'primary.main'
                  },
                  '&>span': {
                    fontSize: 16
                  }
                }}
                onClick={() => {
                  //数据加载完毕前不允许跳转
                  if (!templateLoading) {
                    setActive(tab.key)
                  }
                }}
              >
                {tab.icon({ style: { color: active == tab.key ? 'white' : 'inherit', transitionDuration: '0.3s' } })}
                <Box
                  sx={{
                    fontWeight: 'bold',
                    pl: 1,
                    transitionDuration: '0.3s',
                    fontSize: 'h5.fontSize',
                    color: active == tab.key ? 'white' : 'inherit',
                    m: 0
                  }}
                >
                  {tab.title}
                </Box>
              </Box>
            ))}
          </SideBox>
          <ContentBox ref={groupContentRef} sx={{ backgroundColor: 'transparent' }}>
            <Box sx={{ height: '100%' }}>
              <TabPanel value={1} showValue={active}>
                <TemplateEdit ref={refs[1]}></TemplateEdit>
              </TabPanel>
              <TabPanel value={2} showValue={active}>
                <TemplateFormSettings ref={refs[2]}></TemplateFormSettings>
              </TabPanel>
              {/* <TabPanel value={0} showValue={active} isPersistent={false}>
                <StartForm setActive={setActive} ref={refs[0]}></StartForm>
              </TabPanel> */}
              {/* <TabPanel value={3} showValue={active}>
                <Formstatement ref={refs[3]}></Formstatement>
              </TabPanel> */}
              {/* <TabPanel value={4} showValue={active}>
                <TaskDetail ref={refs[4]}></TaskDetail>
              </TabPanel> */}
            </Box>
          </ContentBox>
          {/* <Drawer
              onClose={() => {
                setPreviewOpen(false)
              }}
              placement='bottom'
              height={'100vh'}
              open={previewOpen}
              headerStyle={{ display: 'none' }}
              bodyStyle={{ padding: 0 }}
              style={{ padding: 0 }}
              destroyOnClose
              footer={null}
            >
              <FormPreview
                onClose={() => {
                  setPreviewOpen(false)
                }}
                activityDefine={activityDefine}
              ></FormPreview>
            </Drawer> */}
        </LayoutBodyBox>
      </LayoutBox>
    </>
  )
}

export default () => {
  return (
    <UpdaterProvider>
      {(isEditTep: boolean) => (isEditTep ? <TempDesigner></TempDesigner> : <Designer></Designer>)}
    </UpdaterProvider>
  )
}
