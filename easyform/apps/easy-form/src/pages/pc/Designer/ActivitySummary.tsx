import { styled, Box } from '@mui/system'
import { useState, useEffect, useMemo, createRef } from 'react'
import { useLocation } from 'react-router-dom'
import { DotLoading } from 'antd-mobile'
import { EchartsThemeProvider } from '@yiban/system'

import Overview from '../../pcStatistics/overview'

import { useEasyFormRequest } from '@/hooks'
import { getActivityDefine } from '@/api/easyform'
import { getUrlParam } from '@/utils'
// const tabs = ['表单项统计', '详细数据']

const ComponentNameMap: any = {
  Input: '文本输入',
  Checkbox: '多选',
  Radio: '单选',
  Rate: '评分'
}

//活动统计
export default function (props: any) {
  const { state }: any = useLocation()
  const { instanceId, activityId } = props
  const [pageDefine, setPageDefine] = useState<any>()
  const { loading } = useEasyFormRequest(() => getActivityDefine(activityId), {
    ready: Bo<PERSON>an(activityId),
    loadingDelay: 200,
    onSuccess: (res: any) => {
      setPageDefine(res?.data?.page?.componentTree || [])
    }
  })
  const fieldsInfo = useMemo(() => {
    return (pageDefine || []).reduce((p: any, c: any) => {
      const componentType = c.componentName.split('.').pop()
      return {
        ...p,
        [c._id]: {
          componentName: c.componentName,
          componentType: componentType,
          options: c?.props?.option || [],
          componentZhName: ComponentNameMap[componentType] || '未知'
        }
      }
    }, {})
  }, [pageDefine])

  const Graphref = createRef()
  return (
    <EchartsThemeProvider>
      <Box sx={{ py: 2 }}>
        {pageDefine ? (
          <>
            <Overview
              ref={Graphref}
              activityId={activityId}
              instanceId={instanceId}
              pageDefine={pageDefine}
              fieldsInfo={fieldsInfo}
            />
          </>
        ) : (
          <Box className='h-full flex justify-center items-center'>
            <DotLoading color='primary' />
          </Box>
        )}
      </Box>
    </EchartsThemeProvider>
  )
}
