import { Box } from '@mui/system'
import React, { forwardRef, useContext, useState, useCallback } from 'react'
import { useLocation, useNavigate } from 'react-router-dom'
import { saveActivity, exportExcelForInstanceForm, toggleActivityEnable, getActivityList } from '@/api/easyform'
import { useEasyFormRequest } from '@/hooks/useBaseRequest'
import { UpdateContext } from './context/UpdateContext'
import { message, Modal } from 'antd'
import { EditorForPC } from '@/editor-kit'
import { Typography } from '@/components'
import { ExclamationCircleFilled } from '@ant-design/icons'
import { RemoteComponent } from '@/helper'
import { Dialog, NoticeBar } from 'antd-mobile'
const { confirm } = Modal
export default forwardRef((props: any, ref) => {
  const location: any = useLocation()
  const { disabled } = props
  const navigate = useNavigate()
  const [saveLoading, setSaveLoading] = useState(0)
  const [confirmOpen, setConfirmOpen] = useState(false)
  const getInitSetting = useCallback(() => {
    if (location.state?.type == 'APPLY') {
      return {
        name: (location.state && location.state.name) || undefined,
        desc: '',
        templateSettings: {},
        imgs: [],
        apply: { type: 'APPLY', applylimit: false, isTicket: false }
      }
    }
    if (location.state?.type == 'PSQ') {
      return {
        name: (location.state && location.state.name) || undefined,
        desc: '',
        templateSettings: {},
        imgs: [],
        psq: { nametype: 'SM' }
      }
    }
    if (location.state?.type == 'INFO') {
      return {
        name: (location.state && location.state.name) || undefined,
        desc: '',
        templateSettings: {},
        imgs: [],
        info: { nametype: 'SM' }
      }
    }
    return {
      name: (location.state && location.state.name) || undefined,
      desc: '',
      templateSettings: {},
      imgs: [],
      apply: { type: 'APPLY', applylimit: false, isTicket: false }
    }
  }, [location.state])
  const Updater = useContext<any>(UpdateContext)

  const [{ define, id, status }, updater] = Updater
  const { settings = getInitSetting(), page = { componentTree: [] }, workflow = {} } = define
  const { loading: setLoading, runAsync: saveSetting } = useEasyFormRequest((params: any) => saveActivity(params, id), {
    manual: true,
    onSuccess: (res: any) => {
      setSaveLoading(0)
      message.success('保存成功！')
      if (!status?.enable) {
        Dialog.confirm({
          title: '提示',
          content: '保存成功，是否立即发布',
          onConfirm: async () => {
            try {
              await toggleActivity2()
              message.success('发布成功')
              updater({ type: 'updateStatus', payload: { status: 1 } })
            } catch (e) {
              message.error('发布失败')
            }
          }
        })
      }
      //新建表单需要更新id值，更新表单需要更新顶层组件define
      if (!id) {
        updater({ type: 'build', payload: { id: res.data } })
        navigate('/pc/designForm', {
          state: {
            ...(location.state ? location.state : {}),
            id: res.data,
            type: location.state?.type || 'APPLY'
          },
          replace: true
        })
        //updater({ type: 'build', payload: { id: res.data } })
        console.log(res.data)
      }
    },
    onError: () => {
      setSaveLoading(0)
    }
    //ready: Boolean(location.state && location.state.id)
  })
  const { runAsync: toggleActivity, loading: toggleLoading } = useEasyFormRequest(() => toggleActivityEnable(id), {
    manual: true,
    ready: Boolean(id),
    onSuccess: (data: any) => {
      refreshActivity()
    },
    onError: () => {}
  })
  const { runAsync: toggleActivity2, loading: toggleLoading2 } = useEasyFormRequest(() => toggleActivityEnable(id), {
    manual: true,
    ready: Boolean(id),
    onSuccess: (data: any) => {},
    onError: () => {}
  })
  const {
    runAsync: fetchData,
    data: activityStatus,
    loading: activityLoading,
    refreshAsync: refreshActivity
  } = useEasyFormRequest(() => getActivityList({ id: id }), {
    //manual: true,
    ready: Boolean(id),
    refreshDeps: [],
    onSuccess: (data: any) => {
      updater({ type: 'updateStatus', payload: { status: data.data[0] } })
    }
  })
  // const onSave = (pageDefine: any, pageSeting: any) => {
  //   if (id && status.enable == 1) {
  //     confirm({
  //       title: <Typography sx={{ fontWeight: 'bold', fontSize: 'h1.fontSize' }}>当前表单已发布</Typography>,
  //       icon: <ExclamationCircleFilled />,
  //       content: (
  //         <Box>
  //           <Typography sx={{}}>已发布的表单修改后需要重新发布才会生效，是否确定修改？</Typography>
  //         </Box>
  //       ),
  //       onOk() {
  //         toggleActivity()
  //         //todo 或许在保存前需要处理下define
  //         const define = {
  //           page: pageDefine,
  //           settings: { ...settings, ...pageSeting },
  //           workflow
  //         }
  //         //使用了模板
  //         if (location.state?.templateId) {
  //           ;(define.settings as any).templateId = location.state.templateId
  //         }
  //         //设置表单类型
  //         if (location.state?.type) {
  //           ;(define.settings as any).type = location.state.type
  //         }
  //         // //没这个字段查询参与人数的接口会报错
  //         // if (!define.settings.participantDetail) {
  //         //   define.settings.participantDetail = []
  //         // }
  //         //提示消息设置到templateSettings下
  //         if (define.settings.successTip) {
  //           if (define.settings.templateSettings) {
  //             define.settings.templateSettings = {
  //               ...define.settings.templateSettings,
  //               successTip: define.settings.successTip
  //             }
  //           } else {
  //             define.settings.templateSettings = { successTip: define.settings.successTip }
  //           }

  //           delete define.settings.successTip
  //         }
  //         //按钮消息设置到templateSettings下
  //         if (define.settings.btnText) {
  //           if (define.settings.templateSettings) {
  //             define.settings.templateSettings = {
  //               ...define.settings.templateSettings,
  //               btnText: define.settings.btnText
  //             }
  //           } else {
  //             define.settings.templateSettings = { btnText: define.settings.btnText }
  //           }

  //           delete define.settings.btnText
  //         }
  //         saveSetting({ define })
  //         updater({ type: 'updatePage', payload: { define } })
  //       },
  //       onCancel() {
  //         console.log('Cancel')
  //       }
  //     })
  //   } else {
  //     //todo 或许在保存前需要处理下define
  //     const define = {
  //       page: pageDefine,
  //       settings: { ...settings, ...pageSeting },
  //       workflow
  //     }
  //     //使用了模板
  //     if (location.state?.templateId) {
  //       ;(define.settings as any).templateId = location.state.templateId
  //     }
  //     //设置表单类型
  //     if (location.state?.type) {
  //       ;(define.settings as any).type = location.state.type
  //     }
  //     // //没这个字段查询参与人数的接口会报错
  //     // if (!define.settings.participantDetail) {
  //     //   define.settings.participantDetail = []
  //     // }
  //     //提示消息设置到templateSettings下
  //     if (define.settings.successTip) {
  //       if (define.settings.templateSettings) {
  //         define.settings.templateSettings = {
  //           ...define.settings.templateSettings,
  //           successTip: define.settings.successTip
  //         }
  //       } else {
  //         define.settings.templateSettings = { successTip: define.settings.successTip }
  //       }

  //       delete define.settings.successTip
  //     }
  //     //按钮消息设置到templateSettings下
  //     if (define.settings.btnText) {
  //       if (define.settings.templateSettings) {
  //         define.settings.templateSettings = {
  //           ...define.settings.templateSettings,
  //           btnText: define.settings.btnText
  //         }
  //       } else {
  //         define.settings.templateSettings = { btnText: define.settings.btnText }
  //       }

  //       delete define.settings.btnText
  //     }
  //     saveSetting({ define })
  //     updater({ type: 'updatePage', payload: { define } })
  //   }
  // }
  /**
   * @description 重新发布当前表单(就是调用两次接口)
   */
  const rePublish = async () => {
    await toggleActivity2()
    await toggleActivity()
  }
  const onSave = (pageDefine: any, pageSeting: any) => {
    if (disabled) {
      message.warning('请先取消发布')
      return
    }
    const { pageSettings } = pageSeting
    //todo 或许在保存前需要处理下define
    const define = {
      page: pageDefine,
      settings: {
        ...settings,
        ...pageSeting
      },
      workflow
    }
    //编辑器会修改到一些settings属性，在这里特殊处理
    if (pageSettings?.submitText !== undefined) {
      define.settings.templateSettings = { ...define.settings.templateSettings, btnText: pageSettings?.submitText }
    }
    if (pageSettings?.cover) {
      define.settings.imgs = pageSettings?.cover?.src || pageSettings?.cover?.value
    }
    if (location.state?.templateId) {
      //使用了模板
      ;(define.settings as any).templateId = location.state.templateId
    }
    //设置表单类型
    if (location.state?.type) {
      ;(define.settings as any).type = location.state.type
    }
    // //没这个字段查询参与人数的接口会报错
    // if (!define.settings.participantDetail) {
    //   define.settings.participantDetail = []
    // }
    //提示消息设置到templateSettings下
    if (define.settings.successTip) {
      if (define.settings.templateSettings) {
        define.settings.templateSettings = {
          ...define.settings.templateSettings,
          successTip: define.settings.successTip
        }
      } else {
        define.settings.templateSettings = { successTip: define.settings.successTip }
      }

      delete define.settings.successTip
    }
    //按钮消息设置到templateSettings下
    if (define.settings.btnText) {
      if (define.settings.templateSettings) {
        define.settings.templateSettings = {
          ...define.settings.templateSettings,
          btnText: define.settings.btnText
        }
      } else {
        define.settings.templateSettings = { btnText: define.settings.btnText }
      }

      delete define.settings.btnText
    }
    saveSetting({ define })
    updater({ type: 'updatePage', payload: { define } })
    updater({
      type: 'updateSettings',
      payload: { define }
    })
    if (id && status.enable == 1) {
      // rePublish()
      // console.log('rePublish=====')
    }
  }
  return (
    <>
      {/* {disabled ? <NoticeBar content='当前表单已发布，若要修改请先取消发布' color='alert' closeable /> : ''} */}
      <Box
        sx={{
          height: '100%',
          div: disabled ? { cursor: 'not-allowed' } : {},
          background: 'white',
          filter: disabled ? 'grayscale(100%)' : '',
          pointerEvents: disabled ? 'none' : 'auto'
        }}
        ref={ref}
      >
        <EditorForPC disabled={disabled} onSave={onSave} isTheme pageDefine={page} settings={settings} />
      </Box>
    </>
  )
})
