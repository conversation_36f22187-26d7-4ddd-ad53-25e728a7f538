import React, { useEffect, useState, useRef } from 'react'
import { Box, styled, ThemeProvider } from '@mui/system'
import {
  Breadcrumb,
  Tag,
  List,
  Row,
  Popconfirm,
  Col,
  Card,
  Input,
  Button,
  Radio,
  Image,
  message,
  ConfigProvider
} from 'antd'
import { TabPanel, Typography } from '@/components'
import { useLocation, useNavigate } from 'react-router-dom'
import { InfiniteScroll } from 'antd-mobile'
import TempList from './components/TempList'
import theme from '@/themes/pc'
import zhCN from 'antd/locale/zh_CN'
import { ActivityIcon, CheckIcon, CollectionIcon, NoticeIcon } from './icons/icons'
import { useOverlayScrollbars } from '@yiban/system'
import TemplateCard, { TemplateDetailModal } from './components/TemplateCard'
import { getInstanceId, saveActivity } from '@/api/easyform'
import { getInitState } from './Designer/context/UpdateContext'
import { useEasyFormRequest } from '@/hooks/useBaseRequest'
const LayoutBox = styled(Box)(({ theme }) => ({
  height: '100%',
  overflow: 'hidden',
  background: theme.palette.background.default,
  padding: '20px',
  display: 'flex',
  flexDirection: 'column',
  minWidth: '1400px'
}))

const LayoutBodyBox = styled(Box)(({ theme }) => ({
  height: '100%',
  display: 'flex',
  overflow: 'hidden'
  //background: theme.palette.background.default,
  //padding: '20px'
}))

const SideBox = styled(Box)(({ theme }) => ({
  height: '100%',
  width: '400px',
  background: theme.palette.background.paper,
  //margin: '20px',
  borderRadius: '10px',
  display: 'flex',
  flexDirection: 'column',
  padding: '8px',
  paddingTop: '32px',
  '.startButton': {
    background: theme.palette.success.main,
    fontSize: (theme.typography as any).h4.fontSize,
    height: '55%',
    fontWeight: '600',
    width: '70%',
    color: 'white'
  }
}))
const HeaderBox = styled(Box)(({ theme }) => ({
  padding: '0 0 16px 0'
}))
const ContentBox = styled(Box)(({ theme }) => ({
  height: '100%',
  width: '100%',
  background: theme.palette.background.paper,
  margin: '0 0 0 20px',
  display: 'flex',
  flexDirection: 'column',
  borderRadius: '10px',
  padding: '24px'
}))

//表单模板选择页面
export default () => {
  const [active, setActive] = useState({
    title: '活动报名',
    key: 0,
    icon: (props = {}) => <ActivityIcon {...props}></ActivityIcon>,
    des: ['人员登记，多种报名', '扫码签到，流程审批'],
    category: [{ id: 'APPLY', title: '报名类' }]
  })
  const location = useLocation()
  const navigate = useNavigate()
  const handleTabChange = (key: any) => {
    setActive(key)
  }
  const groupContentRef2 = useRef(null)
  const [previewOpen, setPreviewOpen] = useState(false)
  const [prevTemplate, setPrevTemplate] = useState('')
  const [activityName, setActivityName] = useState('')
  const [initialize2, instance2] = useOverlayScrollbars({ options: { scrollbars: { autoHide: 'scroll' } } })
  const [refs] = React.useState<Record<number, any>>({
    0: React.createRef(),
    1: React.createRef(),
    2: React.createRef(),
    3: React.createRef()
  })
  const {
    data,
    runAsync: getinsId,
    loading: activityDefineLoading
  } = useEasyFormRequest((id) => getInstanceId(id), {
    manual: true,
    //refreshDeps: [updater],
    onSuccess: (res: any) => {
      //message.success('表单定义来自活动')
    }
  })
  const { loading: createLoading, runAsync: createForm } = useEasyFormRequest((params: any) => saveActivity(params), {
    manual: true,
    onSuccess: (res: any) => {
      const id = res.data
      if (id) {
        //获取实例id
        getinsId(id)
          .then((res) => {
            const insId = res?.data[0]?.id
            navigate('/pc/designForm', {
              state: { id, enable: false, insId, fromTemp: true }
            })
          })
          .catch((err) => {})
      }
    },
    onError: () => {}
    //ready: Boolean(location.state && location.state.id)
  })
  useEffect(() => {
    groupContentRef2.current && initialize2(groupContentRef2.current)
  }, [initialize2])
  return (
    <LayoutBox>
      <HeaderBox>
        <Breadcrumb
          separator='>'
          items={[
            {
              title: (
                <Typography
                  onClick={() => {
                    navigate('/pc')
                  }}
                  sx={{ fontWeight: 'bold', fontSize: 'h4.fontSize', cursor: 'pointer' }}
                >
                  开始
                </Typography>
              )
            },
            {
              title: (
                <Typography sx={{ fontWeight: 'bold', fontSize: 'h4.fontSize' }} varient={'h6'}>
                  选择表单类型
                </Typography>
              )
            }
          ]}
        />
      </HeaderBox>
      <LayoutBodyBox>
        <SideBox ref={groupContentRef2}>
          <Box>
            {[
              {
                title: '活动报名',
                key: 0,
                icon: (props = {}) => <ActivityIcon {...props}></ActivityIcon>,
                des: ['人员登记，多种报名', '扫码签到，流程审批'],
                category: [
                  { id: 'APPLY', title: '报名类' }
                  //{ id: 2, title: '讲座类' }
                ]
              },
              {
                title: '信息收集',
                key: 1,
                icon: (props = {}) => <CollectionIcon {...props}></CollectionIcon>,
                des: ['预置选项，信息收集', '进度跟踪，同步更新'],
                category: [
                  { id: 'INFO', title: '信息填报' }
                  //{ id: 'VOTE', title: '投票类' }
                ]
              },
              {
                title: '问卷调查',
                key: 2,
                icon: (props = {}) => <CheckIcon {...props}></CheckIcon>,
                des: ['多类题型，支持匿名', '进度跟踪，海量题库'],
                category: [
                  { id: 'PSQ', title: '问卷调查' }
                  // { id: 6, title: '生活类' },
                  // { id: 7, title: '管理类' }
                ]
              },
              {
                title: '通知公告',
                key: 3,
                icon: (props = {}) => <NoticeIcon {...props}></NoticeIcon>,
                des: ['操作友好，安全可靠', '自定内容，快速发布'],
                category: [
                  { id: 'NOTICE', title: '通知公告' }
                  // { id: 6, title: '生活类' },
                  // { id: 7, title: '管理类' }
                ]
              }
            ].map((item, key) => (
              <Box
                key={key}
                sx={{
                  my: 1,
                  '.tabCard': {
                    ':hover': {
                      backgroundColor: '#f8f8f8'
                    },
                    transitionDuration: '0.3s',
                    backgroundColor: active.key == item.key ? 'background.default' : 'auto',
                    boxShadow: active.key == item.key ? '0px 0px 2px 0px #6F6DEA' : 'null'
                    // border: (theme) =>
                    //   active.key == item.key ? `2px solid ${theme.palette.primary.main}` : '2px solid white'
                  }
                }}
                onClick={() => {
                  handleTabChange(item)
                }}
              >
                <Card
                  className='tabCard'
                  bodyStyle={{
                    display: 'flex',
                    justifyContent: 'space-between',
                    paddingLeft: '16px',
                    paddingRight: '16px'
                  }}
                  style={{ cursor: 'pointer' }}
                >
                  <Box>
                    <Typography sx={{ color: 'primary.main', fontWeight: '600', fontSize: 'h6.fontSize', my: 1 }}>
                      {item.title}
                    </Typography>
                    <Box sx={{ color: 'text.secondary', fontSize: 'body2.fontSize' }}>
                      {item.des.map((d, key) => (
                        <Typography key={key}>{d}</Typography>
                      ))}
                    </Box>
                  </Box>
                  <Box sx={{ display: 'flex', justifyContent: 'flex-end', flexDirection: 'column' }}>{item.icon()}</Box>
                </Card>
              </Box>
            ))}
          </Box>
        </SideBox>
        <ContentBox sx={{ p: 4, display: 'flex', flexDirection: 'column' }}>
          <Box
            sx={{
              fontSize: 'h3.fontSize',
              fontWeight: '600',
              display: 'flex',
              alignItems: 'center'
            }}
          >
            {active.icon({ width: 24, height: 24, className: 'titleIcon' })}
            <Typography sx={{ ml: 1 }}>{active.title}</Typography>
          </Box>
          <Box sx={{ p: 1, height: '100%', display: 'flex', flexDirection: 'column' }}>
            <Box sx={{ pb: 2 }}>
              <Box sx={{ display: 'flex', alignItems: 'center', py: 3 }}>
                <Typography
                  sx={{
                    color: 'white',
                    backgroundColor: 'primary.main',
                    height: '32px',
                    width: '32px',
                    fontSize: 'h2.fontSize',
                    fontWeight: 'bold',
                    borderRadius: '50%',
                    display: 'flex',
                    alignItems: 'center',
                    justifyContent: 'center'
                  }}
                >
                  1
                </Typography>
                <Typography sx={{ fontSize: 'h2.fontSize', fontWeight: 'bold', color: 'primary.main', ml: 2 }}>
                  直接创建
                </Typography>
              </Box>
              <Box
                sx={{
                  display: 'flex',
                  alignItems: 'center',
                  '.createForm': {
                    backgroundColor: 'primary.main',
                    color: 'white'
                  }
                }}
              >
                <Typography sx={{ fontSize: 'h4.fontSize', fontWeight: '600', whiteSpace: 'nowrap' }}>
                  标题：
                </Typography>
                <Input
                  onChange={(e) => {
                    setActivityName(e.target.value)
                  }}
                  placeholder='请输入标题'
                />
                <Box sx={{ pl: 2 }}>
                  <Popconfirm
                    title='创建表单'
                    description='即将创建表单，是否确认创建?'
                    okText='确认'
                    cancelText='取消'
                    placement='topLeft'
                    onConfirm={() => {
                      if (activityName.trim() == '') {
                        message.warning('请输入活动名称')
                      } else {
                        // navigate('/pc/designForm', {
                        //   state: {
                        //     name: activityName,
                        //     type: active.category[0].id,
                        //     fromTemp: true
                        //   }
                        // })
                        const init = getInitState({ type: active.category[0].id as any, name: activityName })
                        createForm({
                          define: init
                        })
                      }
                    }}
                  >
                    <Button
                      //className='createForm'
                      type='primary'
                      loading={createLoading}
                    >
                      创建
                    </Button>
                  </Popconfirm>
                </Box>
              </Box>
            </Box>
            <TabPanel value={0} showValue={active.key}>
              <TempList
                onPreview={(template: any) => {
                  setPreviewOpen(true)
                  setPrevTemplate(template)
                }}
                Filters={active.category}
                ref={refs[0]}
              ></TempList>
            </TabPanel>
            <TabPanel value={1} showValue={active.key}>
              <TempList
                onPreview={(template: any) => {
                  setPreviewOpen(true)
                  setPrevTemplate(template)
                }}
                ref={refs[1]}
                Filters={active.category}
              ></TempList>
            </TabPanel>
            <TabPanel value={2} showValue={active.key}>
              <TempList
                onPreview={(template: any) => {
                  setPreviewOpen(true)
                  setPrevTemplate(template)
                }}
                ref={refs[2]}
                Filters={active.category}
              ></TempList>
            </TabPanel>
            <TabPanel value={3} showValue={active.key}>
              <TempList
                onPreview={(template: any) => {
                  setPreviewOpen(true)
                  setPrevTemplate(template)
                }}
                ref={refs[3]}
                Filters={active.category}
              ></TempList>
            </TabPanel>
          </Box>
          {previewOpen ? (
            <TemplateDetailModal
              fromTemp={true}
              template={prevTemplate}
              open={previewOpen && prevTemplate != ''}
              setOpen={setPreviewOpen}
            ></TemplateDetailModal>
          ) : (
            ''
          )}
        </ContentBox>
      </LayoutBodyBox>
    </LayoutBox>
  )
}
