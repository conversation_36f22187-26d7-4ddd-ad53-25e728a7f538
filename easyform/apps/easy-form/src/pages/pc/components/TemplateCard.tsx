import React, { useEffect, useMemo, useRef, useState } from 'react'
import { Card, Image, Modal, Tag, Button, Spin, Popover, Segmented } from 'antd'
import { Box, SxProps } from '@mui/system'
import { Typography } from '@/components'
import { Render } from '@/editor-kit/common'
import { getSpan } from './TempList'
import {
  EyeFilled,
  CloseCircleOutlined,
  MobileOutlined,
  DesktopOutlined,
  ExclamationCircleTwoTone
} from '@ant-design/icons'
import PreviewDetail from './PreviewDetail'
import { ActivityTypes } from '@/@define/Activity'
import { useOverlayScrollbars } from '@yiban/system'
import { PREURL } from '../index'
import { useSize } from 'ahooks'
import { useEasyFormRequest } from '@/hooks/useBaseRequest'
import { getActivity, getInstanceId, getTemplateDefine, saveActivity } from '@/api/easyform'
import { useNavigate } from 'react-router-dom'
export interface TemplateCardProps {
  sx?: SxProps
  cardStyle?: React.CSSProperties
  cardBodyStyle?: React.CSSProperties
  onPreview: (temp: any) => void
  template: Template
  coverStyle?: React.CSSProperties
}
export interface Template {
  name: string
  category: string
  useCount: string
  id: string
  imgs: string
}
//万能表单模板卡片组件
export default ({ sx, cardBodyStyle, cardStyle, coverStyle, onPreview, template }: TemplateCardProps) => {
  const size = useSize(document.body)
  return (
    <Box sx={{ ...sx, height: '100%' }}>
      <Card
        bordered={false}
        style={cardStyle || { width: '100%', cursor: 'pointer' }}
        onClick={() => {
          onPreview(template)
        }}
        hoverable
        bodyStyle={
          cardBodyStyle || {
            padding: '24px 16px 8px 16px',
            borderRadius: '0px',
            display: 'flex',
            flexDirection: 'column',
            border: '1px solid rgb(230, 230, 230)'
          }
        }
      >
        <Box sx={{ display: 'flex', justifyContent: 'space-between', height: '100%' }}>
          <Popover content={template && template.name}>
            <Typography sx={{ fontWeight: 'bold', color: 'primary.main' }} noWrap variant={'h5'}>
              {template && template.name}
            </Typography>
          </Popover>

          <Tag
            bordered={false}
            style={{
              backgroundColor:
                (template && ActivityTypes.filter((type) => type.id == template.category)[0]?.color) || 'blue'
            }}
          >
            <Typography color={'white'} variant={'body2'} sx={{ fontSize: '12px' }}>
              {(template && ActivityTypes.filter((type) => type.id == template.category)[0]?.title) || ''}
            </Typography>
          </Tag>
        </Box>
        {/* <Box
          sx={{
            width: '100%',
            height: `${1.25 * getSpan(size as any)}vw`,
            overflow: 'hidden',
            borderRadius: '20px',
            backgroundSize: 'cover',
            backgroundClip: 'content-box',
            backgroundPosition: 'center',
            backgroundImage: template.imgs
              ? `url(${PREURL + template.imgs})`
              : `url(${PREURL + '3f905fc0b9b34bc8ad7d3c4ce169b44b.jpg'})`,
            p: 1,
            '.ezf-image': {
              //width: '100%'
            },
            ...coverStyle
          }}
        ></Box> */}
        <Box sx={{ display: 'flex', justifyContent: 'space-between', pt: 2 }}>
          <Typography sx={{ color: 'grey', fontSize: 12 }} variant={'body2'}>
            {`使用次数：${(template && template.useCount) || 0}`}
          </Typography>
          <Box
            sx={{
              fontSize: 'body2.fontSize',
              '.editicon': { color: 'primary.main' },
              '.anticon-eye': { color: 'primary.main' }
            }}
          >
            <Button
              onClick={() => {
                onPreview(template)
              }}
              icon={<EyeFilled />}
              size='small'
              type='text'
              style={{ fontSize: 12, color: 'grey' }}
            >
              预览
            </Button>
            {/* <Button size='small' type='text' icon={<EditFilled className='editicon' />}></Button> */}
          </Box>
        </Box>
      </Card>
    </Box>
  )
}
export interface TemplateDetailModalProps {
  open: boolean
  setOpen: (value: boolean) => void
  template?: any
  sx?: SxProps
  fromTemp?: boolean
}
export const TemplateDetailModal = ({ open, setOpen, sx, template, fromTemp = false }: TemplateDetailModalProps) => {
  const [previewType, setPreviewType] = useState(2)
  const navigate = useNavigate()
  const onClose = () => {
    setOpen(false)
  }
  const [initialize, instance] = useOverlayScrollbars({ options: { scrollbars: { autoHide: 'scroll' } } })
  const contentRef = React.useCallback(
    (node) => {
      initialize(node)
    },
    [initialize]
  )
  const {
    runAsync,
    loading: templateDefineLoading,
    data: tempData
  } = useEasyFormRequest(() => getTemplateDefine(template.id), {
    ready: Boolean(template?.id),
    onSuccess: (res: any) => {
      //message.success('表单定义来自模板')
    }
  })
  const {
    data,
    runAsync: getinsId,
    loading: activityDefineLoading
  } = useEasyFormRequest((id) => getInstanceId(id), {
    manual: true,
    //refreshDeps: [updater],
    onSuccess: (res: any) => {
      //message.success('表单定义来自活动')
    }
  })
  const { loading: createLoading, runAsync: createForm } = useEasyFormRequest((params: any) => saveActivity(params), {
    manual: true,
    onSuccess: (res: any) => {
      const id = res.data
      if (id) {
        //获取实例id
        getinsId(id)
          .then((res) => {
            const insId = res?.data[0]?.id
            navigate('../designForm', {
              state: { id, enable: false, insId, pageTo: 5 }
            })
          })
          .catch((err) => {})
      }
    },
    onError: () => {}
    //ready: Boolean(location.state && location.state.id)
  })
  const [alrOpen, setAlrOpen] = useState(false)
  //根据模板数据推断出模板类型是不是NOTICE
  const tempSettings = useMemo(() => {
    if (tempData?.data?.settings) {
      const keys = Object.keys(tempData?.data?.settings)
      if (keys && keys.length > 0 && keys.indexOf('notice') > -1) {
        return { ...tempData?.data?.settings, type: 'NOTICE' }
      } else {
        return tempData?.data?.settings
      }
    }
  }, [tempData?.data?.settings])
  return (
    <Modal
      closeIcon={<CloseCircleOutlined />}
      open={open}
      width={850}
      centered
      destroyOnClose
      onCancel={onClose}
      onOk={onClose}
      footer={
        <Box sx={{ display: 'flex', justifyContent: 'center' }}>
          <Button
            type='primary'
            loading={createLoading}
            onClick={() => {
              setAlrOpen(true)
            }}
          >
            在此模板上创建表单
          </Button>
        </Box>
      }
      title={
        <Box sx={{ py: 3, borderBottom: '1px solid', borderColor: 'divider' }}>
          <Typography sx={{ fontWeight: 'bold', fontSize: 'h3.fontSize', textAlign: 'center', color: 'primary.main' }}>
            {template && template.name}
          </Typography>
        </Box>
      }
    >
      <Spin spinning={templateDefineLoading}>
        <Box>
          <Box sx={{ display: 'flex', justifyContent: 'flex-end', mb: 1 }}>
            <Segmented
              onChange={(value) => {
                console.log(value)
                setPreviewType(value as any)
              }}
              value={previewType}
              options={[
                {
                  value: 2,
                  icon: <DesktopOutlined />
                },
                {
                  value: 1,
                  icon: <MobileOutlined />
                }
              ]}
            />
          </Box>
          <Box
            sx={{
              ...sx,
              m: '0 auto',
              overflow: 'auto',
              width: previewType === 2 ? '100%' : 425,
              height: '70vh',
              px: 6,
              '.h-screen': { height: 'auto' }
            }}
            ref={contentRef}
          >
            <Render
              hideSubmit
              isPC={previewType === 2}
              define={tempData?.data}
              pageDefine={tempData?.data?.page}
              settings={tempSettings}
            />
          </Box>
        </Box>
        <Modal
          title={
            <Typography sx={{ display: 'flex' }}>
              <ExclamationCircleTwoTone />
              <Typography sx={{ pl: 1 }}>创建表单</Typography>
            </Typography>
          }
          open={alrOpen}
          onOk={() => {
            // navigate('../designForm', {
            //   state: {
            //     template: '',
            //     usedBy: '',
            //     templateId: template.id,
            //     type: template.category,
            //     fromTemp
            //   },
            //   replace: true
            // })
            setAlrOpen(false)
            createForm({
              define: {
                page: tempData?.data?.page,
                settings: { ...tempData?.data?.settings, type: template.category, templateId: template.id },
                workflow: tempData?.data?.workflow
              }
            })
          }}
          okText={'确认'}
          cancelText={'取消'}
          onCancel={() => {
            setAlrOpen(false)
          }}
        >
          <Typography>即将基于该模板创建表单，是否确认创建</Typography>
        </Modal>
      </Spin>
    </Modal>
  )
}
