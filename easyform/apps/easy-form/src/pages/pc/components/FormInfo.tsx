import React, { useEffect, useState, useRef, forwardRef, useContext } from 'react'
import { Box, styled } from '@mui/system'
import { getAcitvityDate } from '@/utils'
import { Typography } from '@/components'
import { UpdateContext } from '../Designer/context/UpdateContext'
import {
  CaretDownOutlined,
  FieldTimeOutlined,
  QuestionCircleFilled,
  UserOutlined,
  MoreOutlined
} from '@ant-design/icons'

//表单编辑页面的头部信息组件
export default (props: any) => {
  const { sx, info } = props
  const updater = useContext<any>(UpdateContext)
  const [{ define, id }, dispach] = updater
  const { settings } = define
  return (
    <>
      <Box sx={sx}>
        <Typography
          noWrap
          sx={{ color: 'primary.main', maxWidth: '60%', fontSize: 'h1.fontSize', fontWeight: 'bold', pb: 2 }}
        >
          {settings.name}
        </Typography>
        <Box sx={{ color: 'text.secondary', display: 'flex', alignItems: 'center' }}>
          <FieldTimeOutlined />
          <Typography sx={{ color: 'text.secondary', pl: 1 }} variant={'body2'}>
            {getAcitvityDate(settings.startDateTime || 0, settings.endDateTime || 0, 'YYYY-MM-DD HH:mm')}
          </Typography>
        </Box>
        <Box sx={{ py: 3, color: 'text.secondary', display: 'flex', alignItems: 'center' }}>
          <UserOutlined />
          <Typography sx={{ px: 1, maxWidth: '80%' }} noWrap variant={'body2'}>
            本专科生、研究生、教职工
          </Typography>
          <Typography>|</Typography>
          <Typography sx={{ pl: 1 }} variant={'body2'}>
            {`${(info && info.count) || 0}人`}
          </Typography>
        </Box>
      </Box>
    </>
  )
}
