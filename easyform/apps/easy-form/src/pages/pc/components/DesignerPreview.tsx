import React from 'react'
import { <PERSON><PERSON>, message } from 'antd'
import { Form, Toast, Button as MButton } from 'antd-mobile'
import { useNavigate, useLocation } from 'react-router-dom'
import { styled, Box, SxProps } from '@mui/system'
import { useComponents, PageRender, useLocalComponents } from '@/helper'
import { PageLoading } from '@/components/Loading'
import DefaultTemeplate from '@/@template/standard'
import { getUrlParam } from '@/utils'
import { getActivityDefine } from '@/api/easyform'
import { useEasyFormRequest } from '@/hooks'

const Footer = styled(Box)({
  //   position: 'sticky',
  //   zIndex: 9999,
  padding: '0 16px',
  //   bottom: 0,
  //   height: 72,
  //   left: 0,
  //   right: 0,
  backgroundColor: '#FFF',
  display: 'flex',
  alignItems: 'center',
  justifyContent: 'center'
  //boxShadow: '0 -10px 15px -3px rgb(0 0 0 / 0.1), 0 -4px 6px -4px rgb(0 0 0 / 0.1)'
})

export interface PreviewDetailProps {
  activityId: string
  sx: SxProps
  activityDefine: any //预览数据
}
//表单编辑页面的表单预览组件
export function MobilePreview({ activityId, sx, activityDefine }: PreviewDetailProps) {
  const navigate = useNavigate()
  const location: any = useLocation()
  const [templates, setTemplates] = React.useState<any[]>([])
  const { loadComplete, components } = useLocalComponents(
    activityDefine?.page?.componentTree,
    activityDefine?.page?.remoteDependencies
  )
  const handleFinish = React.useCallback(
    (values) => {
      console.log(values, activityDefine?.page?.componentTree)
      Toast.show({
        content: '预览状态提交无效'
      })
    },
    [activityDefine?.page?.componentTree]
  )
  // React.useEffect(() => {
  //   fetch('/easyform/templates.json').then((res) =>
  //     res.json().then((data) => {
  //       setTemplates(data)
  //     })
  //   )
  // }, [])
  // React.useEffect(() => {
  //   if (templateId && templates.length > 0) {
  //     console.log('t:', templateId === '1')
  //     const _template = templates.find((t) => t.id === templateId)
  //     if (_template && _template.data) {
  //       setState(_template.data)
  //     }
  //   }
  // }, [templateId, templates])

  const handleUseTemplate = React.useCallback(() => {
    if (activityDefine) {
      navigate('/designer', { state: { template: activityDefine, usedBy: location.state?.usedBy }, replace: true })
    }
  }, [location.state?.usedBy, navigate, activityDefine])

  React.useEffect(() => {
    if ((window as any).EASY_FORM && activityDefine) {
      ;(window as any).EASY_FORM.submitActivity = (callbackFn: any) => {
        if (typeof callbackFn === 'function') {
          callbackFn(activityDefine)
        }
      }
    }

    return () => {
      if ((window as any).EASY_FORM?.submitActivity) {
        ;(window as any).EASY_FORM.submitActivity = void 0
      }
    }
  }, [activityDefine])
  React.useEffect(() => {
    if (window.self !== window.top) {
      window.parent.postMessage('SHOW_BACK', '*')
    }
  }, [])
  return (
    <>
      <Box sx={sx}>
        <DefaultTemeplate
          imageUrlPrefix={(window as any).APP_CONFIG.server.fileUrlPrefix}
          desc={activityDefine?.settings?.desc}
          imgs={activityDefine?.settings?.imgs}
          title={activityDefine?.settings?.name}
        >
          <React.Suspense fallback={<PageLoading />}>
            {components ? (
              <Form onFinish={handleFinish}>
                <PageRender componentTree={activityDefine.page.componentTree || []} components={components} />
              </Form>
            ) : null}
          </React.Suspense>
        </DefaultTemeplate>
        <Footer
          sx={{
            position: 'sticky',
            zIndex: 9999,
            padding: '0 16px',
            bottom: 0,
            height: 72,
            left: 0,
            right: 0,
            boxShadow: '0 -10px 15px -3px rgb(0 0 0 / 0.1), 0 -4px 6px -4px rgb(0 0 0 / 0.1)'
          }}
        >
          <MButton
            onClick={() => {
              message.warning('预览状态下提交无效', 3)
            }}
            block
            shape='rounded'
            color='success'
          >
            提交表单
          </MButton>
        </Footer>
      </Box>
    </>
  )
}
export function PcPreview({ activityId, sx, activityDefine }: PreviewDetailProps) {
  const navigate = useNavigate()
  const location: any = useLocation()
  const [templates, setTemplates] = React.useState<any[]>([])
  const { loadComplete, components } = useLocalComponents(
    activityDefine?.page?.componentTree,
    activityDefine?.page?.remoteDependencies
  )
  const handleFinish = React.useCallback(
    (values) => {
      console.log(values, activityDefine?.page?.componentTree)
      Toast.show({
        content: '预览状态提交无效'
      })
    },
    [activityDefine?.page?.componentTree]
  )
  // React.useEffect(() => {
  //   fetch('/easyform/templates.json').then((res) =>
  //     res.json().then((data) => {
  //       setTemplates(data)
  //     })
  //   )
  // }, [])
  // React.useEffect(() => {
  //   if (templateId && templates.length > 0) {
  //     console.log('t:', templateId === '1')
  //     const _template = templates.find((t) => t.id === templateId)
  //     if (_template && _template.data) {
  //       setState(_template.data)
  //     }
  //   }
  // }, [templateId, templates])

  const handleUseTemplate = React.useCallback(() => {
    if (activityDefine) {
      navigate('/designer', { state: { template: activityDefine, usedBy: location.state?.usedBy }, replace: true })
    }
  }, [location.state?.usedBy, navigate, activityDefine])

  React.useEffect(() => {
    if ((window as any).EASY_FORM && activityDefine) {
      ;(window as any).EASY_FORM.submitActivity = (callbackFn: any) => {
        if (typeof callbackFn === 'function') {
          callbackFn(activityDefine)
        }
      }
    }

    return () => {
      if ((window as any).EASY_FORM?.submitActivity) {
        ;(window as any).EASY_FORM.submitActivity = void 0
      }
    }
  }, [activityDefine])
  React.useEffect(() => {
    if (window.self !== window.top) {
      window.parent.postMessage('SHOW_BACK', '*')
    }
  }, [])
  return (
    <>
      <Box sx={{ ...sx, '.h-screen': { height: 'auto' } }}>
        <DefaultTemeplate
          imageUrlPrefix={(window as any).APP_CONFIG.server.fileUrlPrefix}
          desc={activityDefine?.settings?.desc}
          imgs={activityDefine?.settings?.imgs}
          title={activityDefine?.settings?.name}
        >
          <React.Suspense fallback={<PageLoading />}>
            {components ? (
              <Form onFinish={handleFinish}>
                <PageRender componentTree={activityDefine.page.componentTree || []} components={components} />
              </Form>
            ) : null}
          </React.Suspense>
        </DefaultTemeplate>
        <Footer>
          <Button
            type='primary'
            onClick={() => {
              message.warning('预览状态下提交无效', 3)
            }}
          >
            提交表单
          </Button>
        </Footer>
      </Box>
    </>
  )
}
