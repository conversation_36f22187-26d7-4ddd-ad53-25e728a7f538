import React, { useState, useMemo } from 'react'
import { Box, fontSize, styled } from '@mui/system'
import { IViewBaseProps } from '@/pages/statistics/types'
import { Typography } from '@/components'
import { Collapse, Avatar, InfiniteScroll } from 'antd-mobile'
import { getList } from '@/api/easyform'
import { useEasyFormRequest } from '@/hooks'
import ImageList from '@/components/ImageList'
import UploadList from '@/components/UploadList'
import { formatDate } from '@/utils'
const Root = styled('div')({})
const StyledCollapse = styled(Collapse)(({ theme }) => ({
  '& .adm-list': {
    '--border-top': 'none',
    '--border-bottom': 'none',
    borderRadius: '8px',
    overflow: 'hidden'
  },
  '& a.adm-list-item:active:not(.adm-list-item-disabled)': {
    backgroundColor: 'transparent'
  }
}))
const CircleAvatar = styled(Avatar)({
  '--border-radius': '100%',
  '--size': '48px',
  border: '1px solid #FFF'
})
const StyledPanel = styled(Collapse.Panel)(({ theme }) => ({}))
const Content = styled('div')({
  display: 'flex'
})

interface DProps {
  detailList?: any
  values: any
}
const getValue = (v: any, componentName: string) => {
  console.log('componentName===',componentName)
  console.log('v===',v)
  if (componentName.endsWith('ImageUploader')) {
    return <ImageList imgs={typeof v === 'string' ? v.split(',') : v} singleStyle={{ maxWidth: '40%' }} />
  }
  if(componentName.endsWith('Upload')){
    return <UploadList value={v} />
  }
  return (
    <Typography variant='body2' sx={{ color: 'text.secondary' }}>
      {v}
    </Typography>
  )
}
//详情统计页面的用户表单填写信息弹窗
export default React.forwardRef(function (
  { activityId, instanceId, participantDetail, fieldsInfo, pageDefine }: IViewBaseProps | any,
  ref
) {
  console.log('participantDetail=====',participantDetail)
  const Detail = useMemo(
    () => () => {
      return (
        <Box>
          {pageDefine &&
            participantDetail &&
            pageDefine.map((item: any, i: number) => (
              <Box
                key={item._id}
                sx={{
                  py: 1,
                  borderBottom: '1px dashed',
                  borderColor: 'divider',
                  display: 'flex',
                  flexDirection: 'column',
                  justifyItems: 'flex-end'
                }}
              >
                <Typography sx={{ mb: 1, color: 'text.primary', fontWeight: 'bold' }} variant='body2'>
                  {item?.props?.label}
                </Typography>
                {getValue(participantDetail[item._id], item.componentName)}
              </Box>
            ))}
        </Box>
      )
    },
    [pageDefine, participantDetail]
  )
  console.error(participantDetail)
  return (
    <Root>
      {!participantDetail ? (
        <Typography variant='subtitle2' align='center' sx={{ py: 2 }}>
          暂无可查看的数据明细
        </Typography>
      ) : (
        <Box>
          <Box className='flex items-center'>
            <CircleAvatar src='' />
            <Box sx={{ ml: 1.5 }}>
              <Typography sx={{ mb: 0.5, fontWeight: 'bold' }} variant='body2'>
                {participantDetail.creatorName}
              </Typography>
              <Typography variant='subtitle1'>
                {formatDate(participantDetail.createAt, 'YYYY-MM-DD HH:mm') || ''}
              </Typography>
            </Box>
          </Box>
          <Box sx={{ pt: 2 }}>
            <Detail />
          </Box>
        </Box>
      )}
    </Root>
  )
})
