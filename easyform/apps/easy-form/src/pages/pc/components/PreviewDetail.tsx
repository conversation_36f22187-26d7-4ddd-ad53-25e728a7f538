import React from 'react'
import { <PERSON><PERSON> } from 'antd'
import { Form, Toast, Button as MButton } from 'antd-mobile'
import { useNavigate, useLocation } from 'react-router-dom'
import { styled, Box } from '@mui/system'
import { useComponents, PageRender } from '@/helper'
import { PageLoading } from '@/components/Loading'
import DefaultTemeplate from '@/@template/standard'
import { getUrlParam } from '@/utils'
import { getTemplateDefine } from '@/api/easyform'
import { useEasyFormRequest } from '@/hooks'
import { Template } from './TemplateCard'
const Footer = styled(Box)({
  position: 'sticky',
  zIndex: 9999,
  padding: '0 16px',
  paddingBottom: 0,
  bottom: 0,
  //height: 72,
  left: 0,
  right: 0,
  backgroundColor: '#FFF',
  display: 'flex',
  alignItems: 'center',
  justifyContent: 'center'
  //boxShadow: '0 -10px 15px -3px rgb(0 0 0 / 0.1), 0 -4px 6px -4px rgb(0 0 0 / 0.1)'
})

export interface PreviewDetailProps {
  template: Template
  onLoaded?: () => void
}

//万能表单模板卡片的模板预览组件
export default function ({ template, onLoaded }: PreviewDetailProps) {
  const navigate = useNavigate()
  const location: any = useLocation()
  const [templates, setTemplates] = React.useState<any[]>([])
  const [state, setState] = React.useState<any>()
  const { loadComplete, components } = useComponents(state?.page?.componentTree, state?.page?.remoteDependencies)
  const { run: fetchDefine, loading: defineLoading } = useEasyFormRequest(() => getTemplateDefine(template.id), {
    ready: Boolean(template && template.id),
    onSuccess: (res: any) => {
      setState(res.data)
      onLoaded && onLoaded()
    }
  })
  const handleFinish = React.useCallback(
    (values) => {
      console.log(values, state?.componentTree)
      Toast.show({
        content: '预览状态提交无效'
      })
    },
    [state?.componentTree]
  )
  // React.useEffect(() => {
  //   fetch('/easyform/templates.json').then((res) =>
  //     res.json().then((data) => {
  //       setTemplates(data)
  //     })
  //   )
  // }, [])
  // React.useEffect(() => {
  //   if (templateId && templates.length > 0) {
  //     console.log('t:', templateId === '1')
  //     const _template = templates.find((t) => t.id === templateId)
  //     if (_template && _template.data) {
  //       setState(_template.data)
  //     }
  //   }
  // }, [templateId, templates])

  const handleUseTemplate = React.useCallback(() => {
    if (state) {
      navigate('../designForm', {
        state: { template: state, usedBy: location.state?.usedBy, templateId: template.id, type: template.category },
        replace: true
      })
    }
  }, [location.state?.usedBy, navigate, state, template])

  React.useEffect(() => {
    if ((window as any).EASY_FORM && state) {
      ;(window as any).EASY_FORM.submitActivity = (callbackFn: any) => {
        if (typeof callbackFn === 'function') {
          callbackFn(state)
        }
      }
    }

    return () => {
      if ((window as any).EASY_FORM?.submitActivity) {
        ;(window as any).EASY_FORM.submitActivity = void 0
      }
    }
  }, [state])
  React.useEffect(() => {
    if (window.self !== window.top) {
      window.parent.postMessage('SHOW_BACK', '*')
    }
  }, [])
  return (
    <>
      <DefaultTemeplate
        imageUrlPrefix={(window as any).APP_CONFIG.server.fileUrlPrefix}
        desc={state?.settings?.desc}
        imgs={state?.settings?.imgs}
        title={state?.settings?.name}
      >
        <React.Suspense fallback={<PageLoading />}>
          {components ? (
            <Form onFinish={handleFinish}>
              <PageRender componentTree={state.page.componentTree || []} components={components} />
            </Form>
          ) : null}
        </React.Suspense>
      </DefaultTemeplate>
      <Footer>
        <Button disabled={defineLoading} type='primary' onClick={handleUseTemplate}>
          在此模板上编辑
        </Button>
      </Footer>
    </>
  )
}
