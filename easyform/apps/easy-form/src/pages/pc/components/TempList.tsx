import React, { useState, useEffect, useRef, forwardRef } from 'react'
import { Row, Col, Radio, Empty, Spin } from 'antd'
import TemplateCard from './TemplateCard'
import { Box } from '@mui/system'
import Typography from '@/components/Typography'
import { useOverlayScrollbars } from '@yiban/system'
import { InfiniteScroll } from 'antd-mobile'
import { useSize } from 'ahooks'
import { getActivityList, toggleActivityEnable, deleteActivity, templateList } from '@/api/easyform'
import { useEasyFormRequest } from '@/hooks/useBaseRequest'
const MEDIATYPE: any = {
  800: 1,
  1200: 8,
  1800: 6,
  2000: 6,
  2800: 4
}
export const getSpan = ({ width: value = 1 }): number => {
  let span
  for (const key of Object.keys(MEDIATYPE)) {
    if (value > parseInt(key)) {
      span = MEDIATYPE[key]
    }
  }
  return span || 8
}

export default forwardRef((props: any, ref) => {
  const { Filters, onPreview } = props
  const [active, setActive] = useState(Filters.map((f: any) => f.id).join(',') + ',')
  const [templist, setTemplist] = useState([])
  //const [filterType, setFilterType] = useState('')
  const [page, setPage] = useState(1)
  const groupContentRef = useRef(null)
  const size = useSize(document.body)
  const [initialize, instance] = useOverlayScrollbars({ options: { scrollbars: { autoHide: 'scroll' } } })
  const [hasMore, setHasMore] = useState(false)
  const { runAsync: fetchTempList, loading: tempListLoading } = useEasyFormRequest(
    (page = 1) =>
      templateList({
        pageSize: '' + 10,
        pageNo: page,
        category: active
      }),
    {
      onSuccess: (res: any) => {
        const remote = res.data
        console.log('loadMOre')
        const _list: any = [...templist, ...remote]
        if (_list.length < res.total) {
          setHasMore(true)
        } else {
          setHasMore(false)
        }
        setTemplist(_list)
      },
      refreshDeps: [active]
    }
  )
  const loadMore = () => {
    const _page = page + 1
    setPage(_page)
    return fetchTempList(_page)
  }
  useEffect(() => {
    groupContentRef.current && initialize(groupContentRef.current)
  }, [initialize])
  console.log(Filters.map((f: any) => f.id))
  return (
    <Box
      ref={ref}
      sx={{
        overflow: 'hidden',
        height: 'calc(100vh - 280px)',
        display: 'flex',
        flexDirection: 'column'
      }}
    >
      <Box sx={{ display: 'flex', alignItems: 'center', py: 2 }}>
        <Typography
          sx={{
            color: 'white',
            backgroundColor: 'primary.main',
            height: '32px',
            width: '32px',
            fontSize: 'h2.fontSize',
            fontWeight: 'bold',
            borderRadius: '50%',
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'center'
          }}
        >
          2
        </Typography>
        <Typography sx={{ fontSize: 'h2.fontSize', fontWeight: 'bold', color: 'primary.main', ml: 2 }}>
          使用模板
        </Typography>
      </Box>
      <Box
        sx={{
          display: 'flex',
          alignItems: 'center',
          '.createForm': {
            backgroundColor: 'primary.main',
            color: 'white'
          }
        }}
      >
        <Typography sx={{ fontSize: 'h4.fontSize', fontWeight: '600', whiteSpace: 'nowrap' }}>模板类型：</Typography>
        <Box
          sx={{
            '.ant-radio-button-wrapper.ant-radio-button-wrapper-checked': {
              backgroundColor: 'primary.main'
            }
          }}
        >
          <Radio.Group
            onChange={(e) => {
              const value = e.target.value || ''
              setTemplist([])
              setPage(1)
              setActive(value)
            }}
            value={active}
            optionType='button'
            buttonStyle='solid'
          >
            <Radio.Button style={{ padding: '0 32px' }} value={Filters.map((f: any) => f.id).join(',') + ','}>
              全部
            </Radio.Button>
            {Filters.map((type: any) => (
              <Radio.Button key={type.id} style={{ padding: '0 32px' }} value={type.id}>
                {type.title}
              </Radio.Button>
            ))}
          </Radio.Group>
        </Box>
      </Box>
      <Box sx={{ overflow: 'auto', mt: 5, height: '100%', pb: 3 }} ref={groupContentRef}>
        <Box
          sx={{
            '.ezf-card': {
              backgroundColor: 'background.default'
            }
          }}
        >
          <Row
            style={{ width: '100%' }}
            gutter={[
              { xs: 12, sm: 24, md: 24, lg: 32 },
              { xs: 12, sm: 24, lg: 32, md: 24 }
            ]}
          >
            {templist && templist.length ? (
              templist.map((temp, key) => (
                <Col key={key} span={getSpan(size as any)} style={{}}>
                  <TemplateCard
                    //coverStyle={{ height: '230px', width: '230px' }}
                    template={temp}
                    onPreview={(temp: any) => {
                      onPreview(temp)
                    }}
                  ></TemplateCard>
                </Col>
              ))
            ) : !tempListLoading ? (
              <Empty style={{ margin: '0 auto' }}></Empty>
            ) : (
              <Spin style={{ margin: '0 auto' }}></Spin>
            )}
          </Row>
        </Box>
      </Box>
      <InfiniteScroll style={{ padding: 0 }} loadMore={loadMore} hasMore={hasMore}>
        {''}
      </InfiniteScroll>
    </Box>
  )
})
