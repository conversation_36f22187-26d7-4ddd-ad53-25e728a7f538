import React, { useEffect, useState, useRef } from 'react'
import { Box, styled, useTheme, width } from '@mui/system'
import {
  getActivityList,
  toggleActivityEnable,
  deleteActivity,
  templateList,
  getActivity,
  saveActivity
} from '@/api/easyform'
import { getAcitvityDate, isLocalSource } from '@/utils'
import { useQRCode } from 'next-qrcode'
import themes from '@/themes'
import ThemeProvider from '@/ThemeProvider'
import TemplateCard, { TemplateDetailModal } from './components/TemplateCard'
import {
  Layout,
  Space,
  Badge,
  Button,
  Empty,
  List,
  Card,
  Spin,
  Popconfirm,
  Pagination,
  Modal,
  Tag,
  Radio,
  Row,
  Col,
  Popover,
  Image,
  message,
  ConfigProvider,
  Input,
  Dropdown,
  Form,
  Progress
} from 'antd'
import { useLocation, useNavigate } from 'react-router-dom'
import { useOverlayScrollbars } from '@yiban/system'
import { ActivityTypes, Activity_State } from '@/@define/Activity'
import { ActivityIcon } from './icons/icons'
import {
  CaretDownOutlined,
  FieldTimeOutlined,
  QuestionCircleFilled,
  UserOutlined,
  MoreOutlined,
  ArrowDownOutlined,
  ArrowUpOutlined,
  CopyOutlined as IconCopy,
  ShareAltOutlined as IconShare,
  PauseCircleTwoTone,
  PlayCircleTwoTone,
  EditTwoTone,
  FundTwoTone,
  ApiTwoTone,
  DownOutlined,
  CopyOutlined,
  DeleteOutlined
} from '@ant-design/icons'
import zhCN from 'antd/locale/zh_CN'
const { Header, Footer, Sider, Content } = Layout
import { Typography } from '@/components'
import { InfiniteScroll } from 'antd-mobile'
import { useEasyFormRequest } from '@/hooks/useBaseRequest'
import { ParticipateDetailProps } from '../components/ParticipateDetail'
const { Search } = Input
import dayjs from 'dayjs'
export const PREURL = (window as any).APP_CONFIG?.server?.fileUrlPrefix
const LayoutBox = styled(Box)(({ theme }) => ({
  height: '100%',
  display: 'flex',
  background: theme.palette.background.default,
  padding: '20px'
}))

const SideBox = styled(Box)(({ theme }) => ({
  height: '100%',
  width: '300px',
  flexShrink: 0,
  background: theme.palette.background.paper,
  //margin: '20px',
  borderRadius: '10px',
  display: 'flex',
  flexDirection: 'column',
  padding: '8px',
  paddingTop: '32px',
  '.startButton': {
    //background: theme.palette.success.main,
    fontSize: (theme.typography as any).h4.fontSize,
    height: '55%',
    fontWeight: '600',
    width: '70%',
    color: 'white'
  }
}))
const ContentBox = styled(Box)(({ theme }) => ({
  height: '100%',
  flexGrow: 1,
  background: theme.palette.background.paper,
  margin: '0 0 0 20px',
  display: 'flex',
  flexDirection: 'column',
  borderRadius: '10px',
  padding: '16px',
  minWidth: '900px'
}))
const AutoModal = styled(Modal)(() => ({
  '.ezf-modal-content': {
    width: 'inherit'
  }
}))
let loadcount = 1
//解析选择器数据为描述字符
const parseFormRange = (value: any) => {
  console.log('解析数据为描述字符', value)
  return '当前参与人数'
}
export default () => {
  const groupContentRef = useRef(null)
  const groupContentRef2 = useRef(null)
  const [hasMore, setHasMore] = useState(false)
  const [filterType, setFilterType] = useState('')
  const [previewTemp, setPreviewTemp] = useState('')
  const [templist, setTemplist] = useState([])
  const [initialize, instance] = useOverlayScrollbars({ options: { scrollbars: { autoHide: 'scroll' } } })
  const [initialize2, instance2] = useOverlayScrollbars({ options: { scrollbars: { autoHide: 'scroll' } } })
  const [page, setPage] = useState(1)
  const [keywords, setKeyWords] = useState('')
  const [copyOpen, setCopyOpen] = useState(false)
  const [enable, setEnable] = useState<string | boolean>('')
  const [order, setOrder] = useState<string | boolean>(true)
  const [joinUrl, setJoinUrl] = React.useState<string>()
  const [actionActivity, setActionActivity] = useState('')
  const [previewOpen, setPreviewOpen] = useState(false)
  const location = useLocation()
  const navigate = useNavigate()
  const [pageSize, setPageSize] = useState(5)
  useEffect(() => {
    groupContentRef.current && initialize(groupContentRef.current)
    groupContentRef2.current && initialize2(groupContentRef2.current)
  }, [initialize, initialize2])
  const { Canvas } = useQRCode()

  const {
    runAsync: fetchData,
    data: activityList,
    loading: activityListLoading,
    refreshAsync: refreshActivityList
  } = useEasyFormRequest(
    () =>
      getActivityList({
        pageSize: '' + pageSize,
        pageNo: page + '',
        type: filterType,
        keywords,
        enable,
        //isMy: true,
        orderBy: order ? '' : 'createAt asc'
      }),
    {
      manual: false,
      refreshDeps: [page, pageSize, filterType, keywords, enable, order]
    }
  )
  const { runAsync: fetchActivity } = useEasyFormRequest((id: any) => getActivity(id), {
    manual: true
  })
  const { runAsync: buildActivity, loading: copyloading } = useEasyFormRequest(
    (params: any, id?: any) => saveActivity(params, id),
    {
      manual: true
    }
  )
  const { runAsync: deleteAct, loading: deleteLoading } = useEasyFormRequest((params: any) => deleteActivity(params), {
    manual: true,
    onSuccess: () => {
      message.success('删除成功')
      refreshActivityList()
    },
    onError: () => {
      message.error('删除失败')
    }
  })
  const { runAsync: fetchTempList, loading: tempListLoading } = useEasyFormRequest(
    (loadcount) =>
      templateList({
        pageSize: 10,
        pageNo: loadcount || 1
        //type: filterType
      }),
    {
      manual: false,
      onSuccess: (res: any) => {
        const remote = res.data
        const _list: any = [...templist, ...remote]
        // if (_list.length < res.total) {
        // if (_list.length < 5) {
        //   setHasMore(true)
        // } else {
        //   setHasMore(false)
        // }
        setTemplist(_list)
      }
      //refreshDeps: [page, pageSize, filterType]
    }
  )
  const { runAsync: toggleActivity, loading: toggleLoading } = useEasyFormRequest(
    (activityId) => toggleActivityEnable(activityId),
    {
      manual: true,
      onSuccess: () => {
        setActionActivity('')
        refreshActivityList()
      },
      onError: () => {}
    }
  )
  const loadMore = () => {
    loadcount++
    return fetchTempList(loadcount)
  }
  const onPreviewClose = () => {
    setPreviewOpen(false)
  }
  const checkActivityDetail = (params: any) => {
    console.log('params==',params)
    navigate('designForm?', {
      state: {
        ...params
      }
    })
  }
  const [copyForm] = Form.useForm()
  const handlePageChange = (page: number, pageSize: number) => {
    setPage(page)
    setPageSize(pageSize)
  }
  const spanRef = React.useRef<HTMLInputElement>(null)
  const handleCopy = (text: string) => {
    spanRef.current?.select()
    document.execCommand('copy')
    // navigator.clipboard.writeText(text).then(() => {
    //   message.open({
    //     type: 'success',
    //     content: '已成功复制到剪切板'
    //   })
    // })
    message.open({
      type: 'success',
      content: '已成功复制到剪切板'
    })
  }
  const handleFormCopy = async ({ id, name }: any) => {
    try {
      const { data: copyedDefine } = await fetchActivity(id)
      const copyName = name || copyedDefine?.settings.name + '【复制】'
      copyedDefine.settings.name = copyName
      await buildActivity({ define: copyedDefine })
      setCopyOpen(false)
      copyForm.setFieldsValue({})
      message.success('复制成功!')
      fetchData()
    } catch (e) {
      message.error('复制失败!')
    }
  }
  const handleShare = (id: any) => {
    setJoinUrl(`${window.location.origin}/easyform/submit?activityId=${id}`)
  }
  const searchActivity = (keyword: string) => {
    setKeyWords(keyword)
  }
  return (
    <>
      <LayoutBox>
        <SideBox>
          <Box
            sx={{
              display: 'flex',
              justifyContent: 'center',
              alignItems: 'center'
            }}
          >
            <Button type='primary' className='startButton' style={{ height: 'auto', cursor: 'pointer' }}>
              <Typography
                onClick={() => {
                  navigate('formtemplates')
                }}
                sx={{
                  fontWeight: '600',
                  px: 2,
                  py: 1,
                  //color: 'white',
                  fontSize: 'h3.fontSize'
                }}
              >
                开始创建表单
              </Typography>
            </Button>
          </Box>
          <Typography sx={{ fontWeight: '600', py: 2, pl: 2, pb: 0, fontSize: 'h4.fontSize' }} variant={'h6'}>
            常用模板
          </Typography>
          <Box
            sx={{
              height: '90%',
              overflow: 'auto',
              '.ezf-card': {
                backgroundColor: 'background.default'
              }
            }}
            ref={groupContentRef}
          >
            <Spin spinning={!templist.length && tempListLoading}>
              <List
                dataSource={templist}
                split={false}
                style={{ height: '100%', padding: '16px' }}
                // footer={<InfiniteScroll hasMore={hasMore} loadMore={loadMore}></InfiniteScroll>}
                renderItem={(item) => (
                  <List.Item>
                    <TemplateCard
                      template={item}
                      coverStyle={{ height: '140px', width: '220px' }}
                      onPreview={(temp) => {
                        setPreviewTemp(temp)
                        setPreviewOpen(true)
                      }}
                      cardStyle={{ width: 250, cursor: 'pointer', borderRadius: 0 }}
                    ></TemplateCard>
                  </List.Item>
                )}
              />
            </Spin>
          </Box>
        </SideBox>
        <ContentBox>
          <Typography sx={{ fontWeight: 'bold', padding: '0px 0' }} variant={'h1'}>
            已创建表单
          </Typography>
          <Box sx={{ display: 'flex', alignItems: 'center', color: 'grey', my: 2, width: '100%' }}>
            <Box
              sx={{
                '.ezf-input-search-button': {
                  backgroundColor: 'background.default',
                  borderRadius: '5px',
                  border: 'none'
                },
                '.searchInput': {
                  backgroundColor: 'background.default'
                }
              }}
            >
              <Search
                placeholder='关键字搜索'
                className='searchInput'
                style={{ borderRadius: '5px' }}
                onSearch={searchActivity}
                enterButton={false}
                bordered={false}
              />
            </Box>
            <Box
              sx={{
                display: 'flex',
                justifyContent: 'center',
                alignItems: 'center',
                transitionDuration: 0.33,
                color: 'grey',
                cursor: 'pointer',
                ':hover': {
                  color: 'primary.main'
                }
              }}
              onClick={() => {
                setOrder(!order)
              }}
            >
              {order ? (
                <>
                  <Typography sx={{ ml: 2, fontSize: 'body2.fontSize' }}>时间倒序</Typography>
                  <ArrowDownOutlined />
                </>
              ) : (
                <>
                  <Typography sx={{ ml: 2, fontSize: 'body2.fontSize' }}>时间正序</Typography>
                  <ArrowUpOutlined />
                </>
              )}
            </Box>

            <Box sx={{ marginLeft: '16px', fontSize: '1vw' }}>
              <Dropdown
                menu={{
                  selectable: true,
                  defaultSelectedKeys: [''],
                  items: [
                    {
                      label: <span>全部</span>,
                      key: '',
                      onClick: () => {
                        setEnable('')
                      }
                    },
                    {
                      label: <span>已发布</span>,
                      key: 1,
                      onClick: () => {
                        setEnable(true)
                      }
                    },
                    {
                      label: <span>未发布</span>,
                      key: 0,
                      onClick: () => {
                        setEnable(false)
                      }
                    }
                  ]
                }}
              >
                <span onClick={(e) => e.preventDefault()}>
                  <Typography
                    sx={{
                      fontSize: 'body2.fontSize',
                      transitionDuration: 0.33,
                      color: 'grey',
                      display: 'flex',
                      cursor: 'pointer',
                      ':hover': {
                        color: 'primary.main'
                      }
                    }}
                  >
                    {/* 状态{enable === '' ? '' : <Typography>{enable ? ':已发布' : ':未发布'}</Typography>} */}
                    {enable === '' ? '全部' : <Typography>{enable ? '已发布' : '未发布'}</Typography>}
                    <CaretDownOutlined />
                  </Typography>
                </span>
              </Dropdown>
            </Box>
          </Box>
          <Box
            sx={{
              padding: '0px 0 8px 0',
              display: 'flex',
              alignItems: 'center',
              '.ant-radio-button-wrapper.ant-radio-button-wrapper-checked': {
                backgroundColor: 'primary.main'
              }
            }}
          >
            <Typography sx={{ fontWeight: '600', py: 2 }} variant={'h6'}>
              表单类型：
            </Typography>
            <Radio.Group
              onChange={(e) => {
                const value = e.target.value || ''
                setFilterType(value)
              }}
              value={filterType}
              optionType='button'
              buttonStyle='solid'
            >
              <Radio.Button style={{ padding: '0 32px' }} value=''>
                全部
              </Radio.Button>
              {ActivityTypes.filter((item) => item.id != 'VOTE').map((type) => (
                <Radio.Button key={type.id} style={{ padding: '0 32px' }} value={type.id}>
                  {type.title}
                </Radio.Button>
              ))}
            </Radio.Group>
          </Box>
          <Box ref={groupContentRef2} sx={{ height: '100%', overflow: 'auto' }}>
            <Spin style={{}} spinning={activityListLoading || deleteLoading}>
              <Box
                sx={{
                  '.ezf-ribbon-wrapper': {
                    marginTop: 2
                  }
                }}
              >
                {activityList && activityList.data ? (
                  <List
                    dataSource={(activityList && activityList.data) || []}
                    style={{ height: '100%', width: '100%' }}
                    renderItem={(item: any) => (
                      <Badge.Ribbon style={{ marginRight: '8px', top: '-8px' }} text={`访问量：${item.pvCount}`}>
                        <List.Item
                          style={{
                            width: '100%',
                            border: '1px solid #e6e6e6',
                            borderRadius: 3,
                            boxShadow: '0 0 4px 0 #f0f0f0'
                          }}
                        >
                          <Box sx={{ width: '100%' }}>
                            <Box sx={{ display: 'flex', width: '100%' }}>
                              <Box sx={{ width: 'calc(100vw - 800px)', py: 2, pl: 3, pb: 0 }}>
                                <Box
                                  sx={{
                                    display: 'flex',
                                    width: '100%',
                                    pb: 2,
                                    borderBottom: '1px solid #f5f5f5'
                                  }}
                                >
                                  <Popover content={item.name || ''}>
                                    <Typography
                                      onClick={() => {
                                        checkActivityDetail({
                                          id: item.id,
                                          enable: item.enable,
                                          insId: item.latestInstId,
                                          pageTo: 0
                                        })
                                      }}
                                      noWrap
                                      sx={{ fontWeight: '600', fontSize: 16, p: 0, pr: 1, cursor: 'pointer' }}
                                      variant={'h5'}
                                    >
                                      {item.name || ''}
                                    </Typography>
                                  </Popover>
                                  {item.type != '' ? (
                                    <Tag
                                      // bordered={false}
                                      style={{
                                        backgroundColor:
                                          ActivityTypes.filter((type) => type.id == item.type)[0]?.color || 'blue'
                                      }}
                                    >
                                      <Typography color={'white'} sx={{ fontSize: '12px' }} variant={'body2'}>
                                        {ActivityTypes.filter((type) => type.id == item.type)[0]?.title || ''}
                                      </Typography>
                                    </Tag>
                                  ) : (
                                    ''
                                  )}
                                  {
                                    // <Box sx={{ display: 'flex', fontSize: '12px', color: 'text.secondary' }}>
                                    //   <Typography sx={{ whiteSpace: 'nowrap' }}>访问量：</Typography>
                                    //   <Typography>{item.pvCount}</Typography>
                                    // </Box>
                                  }
                                </Box>
                                <Box sx={{ pt: 0 }}>
                                  <Box sx={{ height: 32, display: 'flex' }}>
                                    <Box sx={{ color: 'grey', lineHeight: '32px' }}>
                                      {<FieldTimeOutlined style={{ paddingRight: '8px' }} />}
                                      发布时间：
                                      {getAcitvityDate(
                                        item.startDateTime || 0,
                                        item.endDateTime || 0,
                                        'YYYY-MM-DD HH:mm'
                                      )}
                                    </Box>
                                  </Box>
                                  <Space wrap style={{ marginLeft: '-16px' }}>
                                    <Dropdown
                                      arrow
                                      menu={{
                                        items: [
                                          {
                                            key: 5,
                                            label: (
                                              <Box sx={{ textAlign: 'center' }}>
                                                {item?.type == 'NOTICE' ? '公告主题' : '表单主题'}
                                              </Box>
                                            ),
                                            onClick: ({ key }) => {
                                              checkActivityDetail({
                                                id: item.id,
                                                enable: item.enable,
                                                insId: item.latestInstId,
                                                pageTo: key
                                              })
                                            }
                                          },
                                          {
                                            key: 1,
                                            label: (
                                              <Box sx={{ textAlign: 'center' }}>
                                                {item?.type == 'NOTICE' ? '编辑公告' : '编辑表单'}
                                              </Box>
                                            ),
                                            onClick: ({ key }) => {
                                              checkActivityDetail({
                                                id: item.id,
                                                enable: item.enable,
                                                insId: item.latestInstId,
                                                pageTo: key
                                              })
                                            }
                                          },
                                          {
                                            key: 2,
                                            label: (
                                              <Box sx={{ textAlign: 'center' }}>
                                                {item?.type == 'NOTICE' ? '公告设置' : '表单设置'}
                                              </Box>
                                            ),
                                            onClick: ({ key }) => {
                                              checkActivityDetail({
                                                id: item.id,
                                                enable: item.enable,
                                                insId: item.latestInstId,
                                                pageTo: key
                                              })
                                            }
                                          }
                                        ]
                                      }}
                                      placement='bottom'
                                    >
                                      <Button type='text' icon={<EditTwoTone />}>
                                        {item?.type == 'NOTICE' ? '设计公告' : '设计表单'} <DownOutlined />
                                      </Button>
                                    </Dropdown>
                                    <Button
                                      type='text'
                                      onClick={() => {
                                        if (item?.enable) {
                                          handleShare(item.id)
                                        } else {
                                          message.info(
                                            `若要分享请先发布该${item?.type == 'NOTICE' ? '公告' : '表单'}。`
                                          )
                                        }
                                      }}
                                      icon={<ApiTwoTone />}
                                    >
                                      {item?.type == 'NOTICE' ? '分享公告' : '分享表单'}
                                    </Button>
                                    <Button
                                      type='text'
                                      style={{ display: item?.type == 'NOTICE' ? 'none' : undefined }}
                                      onClick={() => {
                                        checkActivityDetail({
                                          id: item.id,
                                          enable: item.enable,
                                          insId: item.latestInstId,
                                          pageTo: 3
                                        })
                                      }}
                                      icon={<FundTwoTone />}
                                    >
                                      统计分析
                                    </Button>
                                  </Space>
                                </Box>
                              </Box>
                              <Box sx={{ flexGrow: 1, py: 2, pr: 3 }}>
                                <Box
                                  sx={{
                                    display: 'flex',
                                    width: '100%',
                                    pb: 2,
                                    fontSize: 12,
                                    justifyContent: 'space-evenly',
                                    borderBottom: '1px solid #f5f5f5'
                                  }}
                                >
                                  <Box
                                    sx={{
                                      display: 'flex',
                                      alignItems: 'center',
                                      '.ezf-space-item': {
                                        color: item?.enable ? 'success.light' : 'error.main'
                                      }
                                    }}
                                  >
                                    <Box
                                      sx={{
                                        width: '6px',
                                        height: '6px',
                                        borderRadius: '50%',
                                        backgroundColor: item?.enable ? 'success.light' : 'error.main',
                                        marginRight: '5px'
                                      }}
                                    ></Box>
                                    <Typography sx={{ cursor: 'pointer' }}>
                                      <Space>{item?.enable ? '已发布' : '未发布'}</Space>
                                    </Typography>
                                  </Box>
                                  <Box sx={{ display: 'flex' }}>
                                    <Box>参与人数：</Box>
                                    <Box>{item.latestInstParticipateCount || '0'}</Box>
                                  </Box>
                                  <Box>创建时间：{dayjs(item?.latestInstCreateAt).format('YYYY年MM月DD日 HH:mm')}</Box>
                                </Box>
                                <Box sx={{ pt: 4, display: 'flex', justifyContent: 'flex-end' }}>
                                  <Box
                                    sx={{
                                      fontSize: 12,
                                      '.ezf-btn-icon': {
                                        transform: 'translateY(3px)'
                                      }
                                    }}
                                  >
                                    <Button
                                      type='link'
                                      style={{ color: 'gray', fontSize: 12 }}
                                      icon={
                                        item?.enable ? (
                                          <PauseCircleTwoTone style={{ fontSize: 20 }} />
                                        ) : (
                                          <PlayCircleTwoTone style={{ fontSize: 20 }} />
                                        )
                                      }
                                      loading={actionActivity == item.id && toggleLoading}
                                      onClick={() => {
                                        setActionActivity(item.id)
                                        toggleActivity(item.id)
                                      }}
                                    >
                                      {item.enable ? '取消发布' : '发布'}
                                    </Button>
                                    <Box
                                      sx={{
                                        float: 'right',
                                        height: 22,
                                        width: '1px',
                                        background: 'linear-gradient(180deg,#fff 0,#f0f0f0 55%,#fff 100%)'
                                      }}
                                    ></Box>
                                  </Box>
                                  <Box sx={{ display: 'flex', alignItems: 'center' }}>
                                    <Button
                                      size='small'
                                      onClick={() => {
                                        copyForm.setFieldsValue({ id: item?.id, name: item?.name + '【复制】' })
                                        setCopyOpen(true)
                                      }}
                                      style={{ fontSize: 12 }}
                                      icon={<CopyOutlined />}
                                      loading={copyloading}
                                      type='text'
                                    >
                                      复制
                                    </Button>

                                    <Popconfirm
                                      title='删除活动'
                                      description='确定要删除此活动吗?'
                                      onConfirm={() => {
                                        deleteAct(item.id)
                                      }}
                                      //onCancel={cancel}
                                      okText='确定'
                                      cancelText='取消'
                                    >
                                      <Button
                                        icon={<DeleteOutlined />}
                                        style={{ fontSize: 12 }}
                                        size='small'
                                        danger
                                        type='text'
                                      >
                                        删除
                                      </Button>
                                    </Popconfirm>
                                  </Box>
                                </Box>
                              </Box>
                            </Box>
                          </Box>
                        </List.Item>
                      </Badge.Ribbon>
                    )}
                  />
                ) : (
                  <Empty
                    description={<Typography>已创建表单为空</Typography>}
                    style={{ visibility: activityListLoading ? 'hidden' : 'visible' }}
                    image={Empty.PRESENTED_IMAGE_SIMPLE}
                  />
                )}
              </Box>
            </Spin>
          </Box>
          <Modal
            open={copyOpen}
            title={'请输入复制的表单名称'}
            onCancel={() => {
              setCopyOpen(false)
            }}
            onOk={() => {
              copyForm.validateFields().then(() => {
                handleFormCopy(copyForm.getFieldsValue())
              })
            }}
            okButtonProps={{
              loading: copyloading
            }}
          >
            <Form form={copyForm}>
              <Form.Item
                label={'表单名称'}
                rules={[{ required: true, message: '请输入表单名称' }]}
                name={'name'}
                required
              >
                <Input allowClear></Input>
              </Form.Item>
              <Form.Item
                hidden
                label={'表单id'}
                rules={[{ required: true, message: '请输入表单id' }]}
                name={'id'}
                required
              >
                <Input></Input>
              </Form.Item>
            </Form>
          </Modal>
          <Box sx={{ width: '100%', display: 'flex', justifyContent: 'flex-end', pt: 2 }}>
            <Pagination
              onChange={handlePageChange}
              showQuickJumper
              current={page}
              showSizeChanger
              defaultCurrent={1}
              pageSize={pageSize}
              total={(activityList && activityList.total) || 0}
            />
          </Box>
          <AutoModal
            onCancel={() => setJoinUrl('')}
            open={Boolean(joinUrl)}
            footer={null}
            title={
              <div>
                <IconShare style={{ marginRight: 4 }} />
                <span>分享</span>
              </div>
            }
          >
            <Box sx={{ display: 'flex', flexDirection: 'column', alignItems: 'center' }}>
              <Canvas
                text={joinUrl || ''}
                options={{
                  //level: 'L',
                  margin: 2,
                  scale: 5,
                  width: 250,
                  color: {
                    dark: '#000005',
                    light: '#ffffff'
                  }
                }}
              />
              <Box
                onClick={() => handleCopy(joinUrl || '')}
                sx={{
                  cursor: 'pointer',
                  minWidth: 250,
                  width: '100%',
                  py: 1,
                  px: 2,
                  bgcolor: '#f8f8f8',
                  borderRadius: '8px',
                  color: '#999',
                  display: 'flex',
                  alignItems: 'center',
                  justifyContent: 'space-between'
                }}
              >
                <input
                  ref={spanRef}
                  css={{
                    '&:selection': {
                      background: 'transparent',
                      color: 'inherit'
                    }
                  }}
                  style={{
                    wordBreak: 'break-all',
                    display: 'inline-block',
                    overflow: 'hidden',
                    whiteSpace: 'nowrap',
                    background: 'none',
                    textOverflow: 'ellipsis',
                    border: 'none',
                    outline: 'none',
                    flexGrow: 1
                  }}
                  value={joinUrl}
                  readOnly
                />
                {/* <span
              style={{
                wordBreak: 'break-all',
                display: 'inline-block',
                overflow: 'hidden',
                whiteSpace: 'nowrap',
                textOverflow: 'ellipsis'
              }}
            >
              {joinUrl}
            </span> */}
                <IconCopy />
              </Box>
            </Box>
          </AutoModal>
          {previewOpen ? (
            <TemplateDetailModal
              template={previewTemp}
              open={previewOpen}
              setOpen={setPreviewOpen}
            ></TemplateDetailModal>
          ) : (
            ''
          )}
        </ContentBox>
      </LayoutBox>
    </>
  )
}
