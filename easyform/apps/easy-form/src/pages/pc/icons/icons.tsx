export const ActivityIcon = ({ className = '', width = 48, height = 48 }) => (
  <svg
    viewBox='0 0 1024 1024'
    version='1.1'
    className={className}
    xmlns='http://www.w3.org/2000/svg'
    p-id='2563'
    width={width}
    height={height}
  >
    <path d='M840 937.6H184l62.4 70.4h531.2z' fill='#E1E6E9' p-id='2564'></path>
    <path
      d='M777.6 1024H246.4c-4.799 0-9.6-1.6-11.199-4.8l-62.4-70.399c-4.8-4.8-4.8-11.2-3.202-17.599 3.201-6.4 8-9.601 14.4-9.601H840c6.4 0 12.8 3.201 14.4 9.601 3.202 6.4 1.601 12.8-3.2 17.6l-62.401 70.399c-1.599 3.199-6.4 4.799-11.2 4.799z m-524.8-32h516.801l33.6-38.4h-584l33.6 38.4z'
      fill='#193651'
      p-id='2565'
    ></path>
    <path d='M337.6 937.6h348.8l-76.8-104H414.4z' fill='#CFD0D1' p-id='2566'></path>
    <path
      d='M686.4 953.6H337.6c-6.4 0-11.2-3.2-14.4-8-3.201-4.8-1.6-11.2 1.6-16l76.8-104c3.2-4.8 8-6.4 12.8-6.4h195.199c4.799 0 9.6 3.201 12.8 6.4l76.799 104c3.2 4.799 4.799 11.199 1.6 16-3.199 4.8-7.998 8-14.398 8zM369.6 921.6h284.8l-52.8-72H422.4l-52.8 72z'
      fill='#193651'
      p-id='2567'
    ></path>
    <path
      d='M927.999 16h-832c-44.8 0-80 35.202-80 80.001v606.4h992V95.999c0-44.8-35.198-79.998-80-79.998z'
      fill='#2EA2DB'
      p-id='2568'
    ></path>
    <path
      d='M1008 718.4H16c-9.6 0-16-6.4-16-16.001v-606.4C0 43.199 43.199 0 95.999 0h832c52.8 0 95.999 43.199 95.999 95.999v606.4c0.002 8-6.398 16-15.999 16z m-976-32h960V95.999c0-35.2-28.8-64-63.999-64H95.999c-35.2 0-64 28.801-64 64v590.4z'
      fill='#193651'
      p-id='2569'
    ></path>
    <path
      d='M80 638.4V95.998c0-9.6 6.4-16 16.001-16h832c9.6 0 16 6.4 16 16v542.4h-864z m-64 64v51.2c0 44.8 35.202 80 80.001 80h832c44.8 0 80-35.2 80-80v-51.2h-992z'
      fill='#FFFFFF'
      p-id='2570'
    ></path>
    <path
      d='M927.999 849.6h-832C43.199 849.6 0 806.4 0 753.6v-51.2c0-9.6 6.4-16 16-16h992.001c9.601 0 16.001 6.4 16.001 16v51.2c-0.002 52.8-43.203 96-96.003 96z m-896-131.2v35.2c0 35.2 28.801 64 64 64h832c35.2 0 64-28.801 64-64v-35.2h-960z'
      fill='#193651'
      p-id='2571'
    ></path>
    <path d='M603.2 753.6h32v32h-32z m-107.2 0h32v32h-32z m-107.2 0h32v32h-32z' fill='#193651' p-id='2572'></path>
    <path
      d='M196.8 256v52.8c0 43.199 35.2 78.4 78.4 78.4s78.399-35.201 78.399-78.4v-67.2l-156.8 14.4z'
      fill='#FDBF5E'
      p-id='2573'
    ></path>
    <path
      d='M348.8 193.6h-89.6c-33.6 0-62.4 27.2-62.4 62.402h152c17.599 0 30.399-14.4 30.399-30.4 1.6-17.603-12.8-32.001-30.4-32.001z'
      fill='#F16051'
      p-id='2574'
    ></path>
    <path
      d='M359.999 395.2c-22.4 20.8-51.2 33.6-84.8 33.6s-62.4-12.8-84.8-33.6C152 422.4 126.4 465.599 126.4 516.8v72h296v-72c1.6-49.6-24-94.4-62.401-121.6z'
      fill='#4FBF9F'
      p-id='2575'
    ></path>
    <path d='M172.8 500.8h32v32h-32z m87.999 0h32v32h-32z m86.4 0h32v32h-32z' fill='#193651' p-id='2576'></path>
    <path
      d='M894.399 396.8v-73.6h-41.6c-3.202-12.8-8.001-25.6-14.401-36.8l28.8-28.8-52.8-52.8-28.8 28.8c-11.2-6.4-24-11.199-36.8-14.4v-41.6H675.2v41.6c-12.8 3.201-25.6 8-36.799 14.4l-28.8-28.8-52.8 52.8 28.8 28.8c-6.4 11.2-11.2 24-14.4 36.8h-41.6v73.6h41.6c3.201 12.8 8 25.6 14.4 36.8l-28.8 28.8 52.8 52.8 28.8-28.8c11.2 6.4 24 11.199 36.8 14.4v41.6h73.6v-41.6c12.8-3.201 25.6-8 36.799-14.4l28.8 28.8 52.8-52.8-28.8-30.4c6.4-11.199 11.2-23.998 14.4-36.798h41.6v1.598z'
      fill='#F16051'
      p-id='2577'
    ></path>
    <path d='M633.6 358.4a78.4 78.4 0 1 0 156.8 0 78.4 78.4 0 1 0-156.8 0z' fill='#FFFFFF' p-id='2578'></path>
  </svg>
)

export const CollectionIcon = ({ className = '', width = 48, height = 48 }) => (
  <svg
    viewBox='0 0 1024 1024'
    version='1.1'
    xmlns='http://www.w3.org/2000/svg'
    p-id='2875'
    width={width}
    height={height}
  >
    <path d='M468.8 200a94.4 94.4 0 1 0 188.801 0 94.4 94.4 0 1 0-188.801 0z' fill='#FDBF5E' p-id='2876'></path>
    <path
      d='M16 924.8c0 46.4 36.8 83.2 83.2 83.2h825.6c46.4 0 83.2-36.8 83.2-83.2H16z'
      fill='#FFFFFF'
      p-id='2877'
    ></path>
    <path
      d='M1008 908.8H16c-9.6 0-16 6.399-16 16 0 54.4 44.8 99.2 99.2 99.2h825.6c54.4 0 99.2-44.8 99.2-99.2 0-9.601-6.4-16-16-16zM924.8 992H99.2c-32 0-57.6-22.4-65.6-51.2h956.8c-8 28.8-33.6 51.2-65.6 51.2z'
      fill='#193651'
      p-id='2878'
    ></path>
    <path d='M72 419.199h878.4v505.599H72z' fill='#2EA2DB' p-id='2879'></path>
    <path
      d='M952 940.799H72c-9.601 0-16.001-6.4-16.001-16.001v-505.6c0-9.6 6.4-16 16-16h878.4c9.602 0 16.002 6.4 16.002 16v505.6c1.598 8.002-6.4 16-14.4 16z m-864-32h846.4V435.2H88v473.6z'
      fill='#193651'
      p-id='2880'
    ></path>
    <path d='M137.6 483.2h750.399v377.6h-750.4z' fill='#FFFFFF' p-id='2881'></path>
    <path d='M417.6 16H16v256h211.2l112 96v-96h78.4z' fill='#4FBF9F' p-id='2882'></path>
    <path
      d='M339.2 384c-3.2 0-8-1.6-9.6-3.2L222.4 288H16c-9.6 0-16-6.4-16-16.002V16.001C0 6.4 6.4 0 16 0h401.6c9.601 0 16.001 6.4 16.001 16v256c0 9.601-6.4 16.001-16 16.001H355.2v80c0 6.4-3.201 11.2-9.601 14.4-1.599 1.6-4.8 1.6-6.398 1.6zM32 256h195.199c3.2 0 8 1.6 9.6 3.2l84.8 72V272c0-9.6 6.4-16 16.001-16h62.401V32H32v224z'
      fill='#193651'
      p-id='2883'
    ></path>
    <path d='M80 80h273.601v128.001h-273.6z' fill='#FFFFFF' p-id='2884'></path>
    <path d='M123.2 128h31.999v32h-32z m78.399 0h32v32h-32z m76.801 0h32v32h-32z' fill='#193651' p-id='2885'></path>
    <path
      d='M840 16h-89.6c-17.6 0-32 14.401-32 32v75.201H872v-75.2c0-17.602-14.4-32-32-32z'
      fill='#F16051'
      p-id='2886'
    ></path>
    <path
      d='M872 139.2H718.4c-4.8 0-8.001-1.6-11.2-4.8s-6.4-6.4-6.4-11.199v-75.2C700.8 20.8 721.6 0 748.8 0h89.6c27.2 0 48 20.8 48 48v75.201c1.599 9.6-6.4 16-14.4 16z m-139.2-32h121.6V48c0-9.6-6.4-16-16-16h-89.6c-9.6 0-16 6.4-16 16v59.2z'
      fill='#193651'
      p-id='2887'
    ></path>
    <path d='M872 520H716.799L793.6 667.2z' fill='#FDBF5E' p-id='2888'></path>
    <path
      d='M793.6 683.2c-6.4 0-11.2-3.2-14.4-8L702.4 528c-3.2-4.8-1.6-11.2 0-16.001 3.202-4.8 8.001-8 14.401-8h153.6c4.8 0 11.2 3.2 14.4 8a14.53 14.53 0 0 1 0 16L808.004 675.2c-1.603 4.8-8.003 8-14.403 8zM744 536l51.199 97.6 51.2-97.6h-102.4z'
      fill='#193651'
      p-id='2889'
    ></path>
    <path d='M716.799 123.2h153.6v395.199H716.8z' fill='#E1E6E9' p-id='2890'></path>
    <path
      d='M872 536H718.4c-9.601 0-16.001-6.4-16.001-16V123.2c0-9.602 6.4-16.002 16-16.002H872c4.8 0 8 1.6 11.2 4.8 3.2 3.2 4.799 6.4 4.799 11.2v395.199c0 4.8-1.6 8-4.8 11.2C880 532.798 875.2 536 872 536z m-139.2-32h121.6V140.8H732.8V504z'
      fill='#193651'
      p-id='2891'
    ></path>
    <path d='M768 171.2h51.2v299.2H768z' fill='#FFFFFF' p-id='2892'></path>
    <path
      d='M572.8 224h-19.2v-9.601c0-4.8 0-8 1.6-9.601 1.6-1.6 3.201-4.8 6.4-8l12.8-12.8c3.2-3.201 3.2-6.4 3.2-9.601s-1.6-8-3.2-9.601c-3.201-3.201-6.4-4.8-9.601-4.8-4.8 0-8 1.6-11.2 3.201-3.2 3.201-4.799 6.4-4.799 9.601H528c1.6-9.6 4.8-17.599 11.2-22.4 6.4-4.8 14.4-8.001 23.998-8.001s17.6 3.201 24 8c6.4 4.8 9.6 12.8 9.6 22.401 0 6.4-1.6 12.8-4.799 16.001-1.6 3.201-3.2 4.8-4.8 6.4L582.4 200l-4.8 4.799-3.2 3.2c-1.6 3.202-3.201 6.4-3.201 9.602v6.4h1.6z m-8.001 36.799c-3.201 0-6.4-1.6-9.601-3.201s-3.201-4.8-3.201-8 1.6-6.4 3.201-8c3.201-3.202 4.8-3.202 9.601-3.202 3.201 0 6.4 1.6 9.6 3.201s3.202 4.8 3.202 8-1.6 6.4-3.201 8c-3.201 1.603-6.4 3.202-9.601 3.202z'
      fill='#193651'
      p-id='2893'
    ></path>
    <path d='M168 515.2h305.601v206.4h-305.6z' fill='#F16051' p-id='2894'></path>
    <path d='M283.2 574.4v86.4l76.799-43.201z' fill='#FFFFFF' p-id='2895'></path>
    <path d='M560 784h112v75.2H560z' fill='#F16051' p-id='2896'></path>
    <path d='M601.6 806.4v32l28.799-16.001z' fill='#FFFFFF' p-id='2897'></path>
    <path d='M560 680h112v75.2H560z' fill='#F16051' p-id='2898'></path>
    <path d='M601.6 702.4v32l28.799-16z' fill='#FFFFFF' p-id='2899'></path>
    <path d='M560 576h112v75.201H560z' fill='#F16051' p-id='2900'></path>
    <path d='M601.6 598.4v30.4l28.799-14.4z' fill='#FFFFFF' p-id='2901'></path>
    <path d='M734.4 806.4h94.4v32h-94.4z m0-104h94.4v32h-94.4z' fill='#193651' p-id='2902'></path>
  </svg>
)

export const CheckIcon = ({ className = '', width = 48, height = 48 }) => (
  <svg
    viewBox='0 0 1024 1024'
    version='1.1'
    xmlns='http://www.w3.org/2000/svg'
    p-id='3055'
    width={width}
    height={height}
  >
    <path d='M416 484.8a304 304 0 1 0 608 0 304 304 0 1 0-608 0z' fill='#F16051' p-id='3056'></path>
    <path
      d='M600 65.6H72a57.358 57.358 0 0 0-57.6 57.6v777.599a57.358 57.358 0 0 0 57.6 57.6h528a57.358 57.358 0 0 0 57.599-57.6v-777.6c0-32-27.2-57.6-57.6-57.6z'
      fill='#FFFFFF'
      p-id='3057'
    ></path>
    <path
      d='M600 972.8H72c-40 0-72-32.001-72-72V123.2c0-40 32-72 72-72h528c40 0 72 32 72 72v777.599c0 40-33.6 72.002-72 72.002zM72 80a43.02 43.02 0 0 0-43.2 43.2v777.599A43.02 43.02 0 0 0 72 943.998h528a43.02 43.02 0 0 0 43.199-43.2V123.2A43.02 43.02 0 0 0 599.999 80H72z'
      fill='#193651'
      p-id='3058'
    ></path>
    <path d='M72 180.8h528v614.4H72z' fill='#2EA2DB' p-id='3059'></path>
    <path
      d='M238.4 123.2h28.8V152h-28.8z m83.2 0h28.8V152h-28.8z m81.6 0H432V152h-28.8z'
      fill='#193651'
      p-id='3060'
    ></path>
    <path d='M299.2 873.6a36.8 36.8 0 1 0 73.599 0 36.8 36.8 0 1 0-73.598 0z' fill='#FFFFFF' p-id='3061'></path>
    <path
      d='M336 924.8c-28.801 0-51.2-22.4-51.2-51.2 0-28.8 22.401-51.199 51.2-51.199s51.2 22.4 51.2 51.2c0 28.798-24 51.199-51.2 51.199z m0-73.6c-12.8 0-22.401 9.6-22.401 22.4s9.6 22.401 22.4 22.401 22.402-9.6 22.402-22.4S347.199 851.2 336 851.2z'
      fill='#193651'
      p-id='3062'
    ></path>
    <path
      d='M392 608v-33.6l-33.6-6.4c-3.2-11.2-6.4-22.4-12.8-32l19.2-28.8-23.999-24-28.8 19.2c-9.601-6.4-20.8-11.2-32-12.8l-6.4-33.6H240l-6.4 33.6c-11.2 3.201-22.401 6.4-32 12.8l-25.6-17.6-23.999 24 19.2 28.8c-6.4 9.602-11.2 20.801-12.8 32l-33.6 6.4v32l33.6 6.4c3.201 11.2 6.4 22.4 12.8 32l-19.2 28.8 24 24 28.8-19.2c9.601 6.4 20.8 11.2 32 12.8l6.4 33.6h33.6l6.4-33.6c11.2-3.201 22.4-6.4 32-12.8l28.8 19.2 24-24-19.2-28.8c6.4-9.601 11.199-20.8 12.8-32L392 608z m-134.4 30.4c-27.2 0-48-20.801-48-48.001s20.8-48 48-48 48 20.8 48 48-20.8 48-48 48z'
      fill='#FFFFFF'
      p-id='3063'
    ></path>
    <path
      d='M467.2 361.6l-8.001-25.6-27.2 1.6c-4.8-8-9.601-16-17.6-22.4l8-25.6-22.4-12.8-17.6 20.8c-8-3.2-17.599-3.2-27.2-3.2l-12.8-24-25.6 6.4 3.202 27.2c-8 4.8-16.001 9.601-22.4 17.6l-25.6-8.001-12.8 22.4 20.8 17.6c-3.201 8-3.201 17.6-3.201 27.2l-24 12.8 6.4 25.6 27.2-1.6c4.8 8 9.602 16 17.6 22.4l-8 25.6 22.4 12.8 17.6-20.8c8 3.2 17.599 3.2 27.2 3.2l12.8 24 25.6-6.4-1.6-27.2c8-4.8 16-9.601 22.4-17.6l25.6 8 12.8-22.4-20.8-17.6c3.2-8 3.2-17.599 3.2-27.2l24-12.8z m-97.6 52.8c-20.8 6.4-41.6-6.4-48-27.2s6.4-41.601 27.2-48.001 41.6 6.4 48 27.2c6.4 20.8-6.4 43.201-27.2 48z'
      fill='#FFFFFF'
      p-id='3064'
    ></path>
    <path d='M907.2 310.4h-376v275.2h48V688l171.2-102.4h156.8z' fill='#FDBF5E' p-id='3065'></path>
    <path
      d='M571.199 700.8c-4.8-3.2-8-8-8-12.8v-86.4h-33.6a14.34 14.34 0 0 1-14.401-14.4V310.4a14.34 14.34 0 0 1 14.4-14.4h376a14.34 14.34 0 0 1 14.4 14.4v275.2a14.34 14.34 0 0 1-14.4 14.4h-152l-169.6 99.2c-1.598 3.2-7.998 3.2-12.8 1.6z m-25.6-129.601h33.6a14.34 14.34 0 0 1 14.4 14.4v76.8L744 574.397c1.6-1.6 4.8-1.6 8-1.6h142.4V324.8h-348.8V571.2z'
      fill='#193651'
      p-id='3066'
    ></path>
    <path d='M636.8 528h-48V368h260.8v160H736l-99.2 59.2z' fill='#FFFFFF' p-id='3067'></path>
    <path
      d='M728 471.999h-17.599v-9.601c0-4.8 0-8 1.6-9.601s3.202-4.8 6.4-8l11.2-11.2c3.2-3.2 3.2-6.4 3.2-9.6s-1.6-8.001-3.2-9.602c-3.201-3.2-6.4-3.2-9.601-3.2-4.8 0-8 1.6-9.601 3.2-3.201 3.201-4.8 6.4-4.8 9.601h-19.2c1.601-9.6 4.8-17.6 11.2-22.4 6.4-4.8 14.4-8.001 24-8.001s17.599 3.201 23.999 8c6.4 4.8 9.6 12.8 9.6 22.401 0 6.4-1.6 11.2-4.799 16.001-1.6 3.201-3.2 4.8-4.8 4.8l-4.799 4.799-4.8 4.8-3.2 3.2c-1.6 1.6-3.201 6.4-3.201 9.601v4.8H728z m-8 35.2c-3.201 0-6.4-1.6-8-3.2-3.201-1.601-3.201-4.8-3.201-8s1.6-6.4 3.2-8.001c3.202-1.6 4.8-3.201 8.001-3.201s6.4 1.6 8 3.2c3.201 1.601 3.201 4.8 3.201 8.001s-1.6 6.4-3.2 8C726.4 505.6 723.198 507.2 720 507.2z'
      fill='#193651'
      p-id='3068'
    ></path>
  </svg>
)

export const MobileIcon = ({ className = '', width = 24, height = 24, fill = '#2c2c2c' }) => (
  <svg
    width={width}
    height={height}
    viewBox='0 0 1024 1024'
    version='1.1'
    fill={fill}
    xmlns='http://www.w3.org/2000/svg'
    p-id='2615'
  >
    <path
      d='M736 0l-448 0c-52.8 0-96 43.2-96 96l0 832c0 52.8 43.2 96 96 96l448 0c52.8 0 96-43.2 96-96l0-832c0-52.8-43.2-96-96-96zM384 48l256 0 0 32-256 0 0-32zM512 960c-35.36 0-64-28.64-64-64s28.64-64 64-64 64 28.64 64 64-28.64 64-64 64zM768 768l-512 0 0-640 512 0 0 640z'
      fill={fill}
      p-id='2616'
    ></path>
  </svg>
)

export const PcIcon = ({ className = '', width = 24, height = 24, fill = '#2c2c2c' }) => (
  <svg
    viewBox='0 0 1024 1024'
    version='1.1'
    xmlns='http://www.w3.org/2000/svg'
    p-id='3864'
    width={width}
    fill={fill}
    height={height}
  >
    <path
      d='M897.133056 110.717637 126.864898 110.717637c-35.450397 0-64.187905 28.709879-64.187905 64.186881l0 513.473561c0 35.45142 28.737508 64.193021 64.187905 64.193021l320.945663 0 0 96.283904L287.333636 848.855004c-17.704221 0-32.086789 14.387685-32.086789 32.091906l0 32.091906 513.509377 0 0-32.091906c0-17.709337-14.386662-32.091906-32.095999-32.091906L576.187393 848.855004l0-96.283904 320.940546 0c35.45142 0 64.194044-28.742625 64.194044-64.193021L961.321984 174.904519c0-35.477003-28.743648-64.186881-64.194044-64.186881l0 0L897.133056 110.717637zM479.908606 688.37808c0-17.709337 14.381545-32.086789 32.090882-32.086789 17.708314 0 32.090882 14.383592 32.090882 32.086789 0 17.742083-14.383592 32.128745-32.090882 32.128745C494.290151 720.507848 479.908606 706.120163 479.908606 688.37808L479.908606 688.37808 479.908606 688.37808zM126.864898 624.191198 126.864898 174.904519l770.268158 0 0 449.28668L126.864898 624.191198 126.864898 624.191198zM126.864898 624.191198'
      fill={fill}
      p-id='3865'
    ></path>
  </svg>
)
export const ClosePreviewIcon = ({ className = '', width = 24, height = 24, fill = '#2c2c2c' }) => (
  <svg
    viewBox='0 0 1024 1024'
    version='1.1'
    fill={fill}
    xmlns='http://www.w3.org/2000/svg'
    p-id='4943'
    width={width}
    height={height}
  >
    <path
      fill={fill}
      d='M512 128C300.8 128 128 300.8 128 512s172.8 384 384 384 384-172.8 384-384S723.2 128 512 128zM672 627.2c12.8 12.8 12.8 32 0 44.8s-32 12.8-44.8 0L512 556.8l-115.2 115.2c-12.8 12.8-32 12.8-44.8 0s-12.8-32 0-44.8L467.2 512 352 396.8C339.2 384 339.2 364.8 352 352s32-12.8 44.8 0L512 467.2l115.2-115.2c12.8-12.8 32-12.8 44.8 0s12.8 32 0 44.8L556.8 512 672 627.2z'
      p-id='4944'
    ></path>
  </svg>
)

export const NoticeIcon = ({ className = '', width = 48, height = 48, fill = '#2c2c2c' }) => (
  <svg
    viewBox='0 0 1024 1024'
    version='1.1'
    xmlns='http://www.w3.org/2000/svg'
    p-id='2560'
    width={width}
    fill={fill}
    height={height}
  >
    <path
      d='M16.003 795.2v86.4c0 19.2 16 35.202 35.2 35.202h672c19.2 0 35.2-16.001 35.2-35.201v-86.4h-742.4z'
      fill='#E1E6E9'
      p-id='2561'
    ></path>
    <path
      d='M723.201 932.8h-672C22.404 932.8 0.003 910.4 0.003 881.6v-86.4c0-9.601 6.4-16.001 16-16.001h742.402c9.6 0 16 6.4 16 16v86.4c-0.001 27.201-22.402 51.202-51.203 51.202z m-691.2-121.6v70.399c0 9.6 8.001 19.2 19.2 19.2h672c9.6 0 19.2-8 19.2-19.2v-70.4H32z'
      fill='#193651'
      p-id='2562'
    ></path>
    <path
      d='M758.402 795.2V312c0-19.2-16.001-35.2-35.201-35.2h-672c-19.2 0-35.2 16-35.2 35.2v483.2h742.4z'
      fill='#FFFFFF'
      p-id='2563'
    ></path>
    <path
      d='M758.402 811.2h-742.4c-9.6 0-16-6.4-16-16.001V312c0-28.8 22.4-51.199 51.2-51.199H723.2c28.8 0 51.2 22.4 51.2 51.2v483.2c0.002 7.998-6.398 15.999-16 15.999z m-726.4-32H742.4V312c0-9.6-8-19.2-19.2-19.2h-672c-9.6 0-19.2 8.001-19.2 19.2v467.2z'
      fill='#193651'
      p-id='2564'
    ></path>
    <path d='M80.002 340.8h614.4v388.799h-614.4z' fill='#2EA2DB' p-id='2565'></path>
    <path d='M496.002 1008h-217.6l25.6-91.2h167.999z' fill='#FFFFFF' p-id='2566'></path>
    <path
      d='M496.002 1024h-217.6c-4.8 0-9.6-1.6-12.8-6.4-3.2-3.2-4.799-9.6-3.2-14.4l25.6-91.2c1.6-6.4 8-11.2 16-11.2h168.001c8 0 12.8 4.8 16 11.2l24 89.6v6.4c-0.002 9.6-6.402 16-16 16z m-196.8-32h176L459.2 932.8H316.802l-17.6 59.2z'
      fill='#193651'
      p-id='2567'
    ></path>
    <path
      d='M537.603 1024H236.802c-9.601 0-16.001-6.4-16.001-16s6.4-16.002 16-16.002h302.4c9.601 0 16.001 6.4 16.001 16.001-1.6 9.601-8 16.001-17.6 16.001z'
      fill='#193651'
      p-id='2568'
    ></path>
    <path
      d='M976.002 16h-512c-17.599 0-32 14.401-32 32v364.801c0 17.6 14.401 32 32 32h70.4v120l209.6-120h232c17.599 0 32-14.4 32-32v-364.8c0-17.602-14.401-32-32-32z'
      fill='#F16051'
      p-id='2569'
    ></path>
    <path
      d='M526.401 577.599c-4.799-3.201-8-8-8-14.4v-104h-54.398c-27.2 0-48.001-20.8-48.001-48V48c0-27.2 20.8-48.001 48-48.001h512c27.2 0 48 20.8 48 48v364.801c0 27.2-20.8 48-48 48h-228.8l-204.8 118.4c-4.8 1.599-11.2 1.599-16-1.602z m-62.398-545.6c-9.601 0-16.001 6.4-16.001 16.002v364.8c0 9.6 6.4 16 16 16h70.4c9.6 0 16 6.4 16 16.002v92.8l185.6-107.2c3.202-1.601 4.8-1.601 8.001-1.601h232c9.602 0 16.001-6.4 16.001-16.001v-364.8c0-9.601-6.4-16.001-16-16.001H464.003z'
      fill='#193651'
      p-id='2570'
    ></path>
    <path d='M598.401 380.8H496.002V80h448v300.8h-217.6l-128 73.6z' fill='#FFFFFF' p-id='2571'></path>
    <path
      d='M860.803 164.8H577.6c-9.601 0-16.001-6.4-16.001-16.001 0-9.6 6.4-16 16-16h283.2c9.602 0 16.001 6.4 16.001 16 0 8-6.4 16-15.998 16z m0 75.2H577.6c-9.601 0-16.001-6.4-16.001-16s6.4-16.001 16-16.001h283.2c9.602 0 16.001 6.4 16.001 16 0 8.001-6.4 16.002-15.998 16.002z m0 75.2H577.6c-9.601 0-16.001-6.4-16.001-16.001s6.4-16.001 16-16.001h283.2c9.602 0 16.001 6.4 16.001 16 0 8.001-6.4 16.001-15.998 16.001z m-576 524.8h31.999v32h-32zM371.202 840h32v32h-32z m86.4 0h32v32h-32z'
      fill='#193651'
      p-id='2572'
    ></path>
  </svg>
)
