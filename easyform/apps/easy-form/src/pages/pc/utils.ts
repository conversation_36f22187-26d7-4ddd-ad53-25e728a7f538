/**
 * 判断是否是部门管理员
 * @param identity
 * @returns
 */
export function isDepartAdminUser(identity?: string | number) {
  return identity == 3 || identity === 7 || identity === 8
}

/**
 * 判断是否是学工管理员
 * @param identity
 * @returns
 */
export function isXgAdminUser(identity?: string | number) {
  return identity == 6
}
/**
 * 判断是否是超级管理员
 * @param identity
 * @returns
 */
export function isSupperUser(identity?: string | number) {
  return identity == 4
}
/**
 * 判断是否是辅导员
 * @param identity
 * @returns
 */
export function isFdyUser(identity?: string | number) {
  return identity == 5
}


/**
 * 判断是否是超级管理员
 */

export function isAdminUser(adminType?: string | number) {
  return adminType === 'super'
}

/**
 * 判断是否是院系管理员
 */

export function isOrgUser(adminType?: string | number) {
  return adminType === 'org'
}

/**
 * 判断是否是辅导员
 */

export function isCounselorUser(adminType?: string | number) {
  return adminType === 'counselor'
}
