import React from 'react'
import { <PERSON><PERSON>, PullToRefresh, Toast, SpinLoading, Swiper } from 'antd-mobile'
import { useNavigate } from 'react-router-dom'
import { getUrlParam, formatDate } from '@/utils'
import { Box, styled } from '@mui/system'
import { useTicketRequest } from '@/hooks'
import { writeOff, getTicketDetail } from '@/api/ticket'
import ParticipateDetail, { State } from '@/pages/components/ParticipateDetail'
import { LoadingWrapper } from '@/components'
import PageWrapper from '@/@template/PageWrapper'

const Footer = styled(Box)({
  //   position: 'sticky',
  zIndex: 9999,
  padding: '8px 32px',
  //   bottom: 0,
  height: 72,
  //   left: 0,
  //   right: 0,
  backgroundColor: '#F8F8F840',
  display: 'flex',
  alignItems: 'center',
  justifyContent: 'center',
  boxShadow: '0px 0px 10px rgba(0, 0, 0, 0.1)'
  //   boxShadow: '0 -10px 15px -3px rgb(0 0 0 / 0.1), 0 -4px 6px -4px rgb(0 0 0 / 0.1)'
})
const Iframe = styled('iframe')({
  height: '100%',
  width: '100%',
  background: '#FFF',
  border: 'none'
})

export default function () {
  const navigate = useNavigate()
  const [complete, setComplete] = React.useState(false)
  const [[activityId, instFormId, token]] = React.useState([
    getUrlParam('activityId'),
    getUrlParam('instFormId'),
    getUrlParam('token')
  ])

  const [state, setState] = React.useState<string>()
  const { loading, run } = useTicketRequest(writeOff, {
    manual: true,
    onSuccess: () => {
      setComplete(true)
      setState(State.TICKET_USED)
      detailRef.current.swipeTo(2)
    }
  })
  const handleSubmit = () => {
    if (!token) {
      Toast.show({
        icon: 'fail',
        content: '票据不存在'
      })
      return
    }
    run(token)
  }
  const detailRef = React.useRef<any>()
  const handleDetailFinish = React.useCallback((_status) => {
    setState(_status)
  }, [])
  return (
    <PullToRefresh>
      <Box className='h-screen flex flex-col'>
        <PageWrapper>
          <LoadingWrapper>
            <Box className='flex-1 overflow-auto'>
              <ParticipateDetail
                scanSuccessText='已扫码成功'
                onReady={handleDetailFinish}
                ref={detailRef}
                showFormDetail={true}
                activityId={activityId}
                instFormId={instFormId}
                allowBack={false}
              />
            </Box>
            {[State.SUBMITED, State.PASS].includes(state || '') && (
              <Footer>
                <Button onClick={handleSubmit} disabled={loading || complete} block shape='rounded' color='primary'>
                  {complete ? '已确认' : '确认'}
                </Button>
              </Footer>
            )}
          </LoadingWrapper>
        </PageWrapper>
      </Box>
    </PullToRefresh>
  )
}
