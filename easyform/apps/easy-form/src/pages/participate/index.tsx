import React from 'react'
import { Form, Button, Input, Toast } from 'antd-mobile'
import { useLocation } from 'react-router-dom'
import { RemoteComponent } from '@/helper'
import { PageLoading } from '@/components/Loading'

export default function () {
  const { state } = useLocation()
  const componentTree = React.useMemo(() => (state as any)?.componentTree, [state])
  const handleFinish = React.useCallback(
    (values) => {
      console.log(values, componentTree)
      Toast.show({
        content: '预览状态提交无效'
      })
    },
    [componentTree]
  )

  return (
    <React.Suspense fallback={<PageLoading />}>
      <div>
        <Form
          onFinish={handleFinish}
          footer={
            <Button block type='submit' color='primary' size='large'>
              提交
            </Button>
          }
        >
          {componentTree
            ? componentTree.map(({ _id, componentName, props }: any) =>
                React.createElement(RemoteComponent, {
                  _remoteInfo: {
                    componentName: componentName,
                    version: '0.1.0'
                  },
                  _loading: false,
                  key: _id,
                  name: _id,
                  ...props
                })
              )
            : null}
          <Form.Item label='测试' name='testField' required rules={[{ required: true }]}>
            <Input />
          </Form.Item>
        </Form>
      </div>
    </React.Suspense>
  )
}
