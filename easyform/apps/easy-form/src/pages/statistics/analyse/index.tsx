/**
 * 统计图表
 */
import { But<PERSON>, Toast } from 'antd-mobile'
import { PullToRefreshAndLoadMore } from '@/components'
import { useNavigate, useLocation } from 'react-router-dom'
import { useTheme, styled, Box } from '@mui/system'
import { useEffect, useState } from 'react'
import { useEasyFormRequest } from '@/hooks'
import { getStatistic, activityInst } from '@/api/easyform'
import { use } from 'echarts'
import ReactECharts from 'echarts-for-react'
import { Typography } from '@/components'
import Table from '@/components/Table'

const colorSelf = [
  'rgb(72,96,202)',
  'rgb(129,196,98)',
  'rgb(247,190,71)',
  'rgb(231,78,83)',
  'rgb(226,97,193)',
  'rgb(231,78,83)'
]
const columns = [
  {
    title: '选项',
    dataIndex: 'dimension',
    key: 'dimension'
  },
  {
    title: '小计',
    dataIndex: 'data',
    key: 'data',
    sort: true
  }
  // {
  //   title: '比例',
  //   dataIndex: 'scale',
  //   key: 'scale'
  // }
]
export default function Home(props: any) {
  const { instanceId, allDatas } = props
  const location = useLocation()
  // const [allDatas, setAllDatas] = useState<any>([])

  const { loading: getStatisticLoading, run: getStatisticRun } = useEasyFormRequest(getStatistic, {
    manual: true,
    onSuccess: (res: any) => {
      console.log(res, 'res')
      handleResult(res)
    },
    onError: (error: any) => {
      console.log(error, 'error')
      const reuslt = {
        data: [
          {
            title: '单选',
            dimension: ['选项1'],
            data: [1],
            name: 'MO1sOtkw4'
          },
          {
            title: '多选',
            dimension: ['选项1', '选项2'],
            data: [2, 4]
          }
        ],
        total: 0
      }
      // setAllDatas(reuslt?.data)
      // handleResult(reuslt)
    }
  })

  // useEffect(() => {
  //   console.log(location?.state, instanceId, 'location?.state')
  //   instanceId && getStatisticRun({ instanceId: instanceId })
  // }, [getStatisticRun, instanceId, location?.state])

  const handleResult = (reuslt: any) => {
    reuslt.data?.map((item: any) => {
      const handleData: any[] = []
      const data: any[] = []
      item?.dimension.map((it: any, i: any) => {
        handleData.push({
          name: it[i],
          type: 'bar',
          data: [item?.data[i]],
          barWidth: 30
        })
      })
      item?.data.map((it: any, i: any) => {
        data.push({
          value: it,
          itemStyle: {
            color: colorSelf[i]
          }
        })
      })
      console.log(handleData, data, 'handleData')
      item.options = {
        title: {
          text: item?.title
        },
        grid: {
          left: '3%',
          right: '4%',
          bottom: '3%',
          containLabel: true
        },
        yAxis: {
          type: 'value',
          boundaryGap: [0, 0.01]
        },
        xAxis: {
          type: 'category',
          data: item?.dimension,
          axisLabel: {
            interval: 0,
            formatter: function (value: any) {
              const len = value.length
              const length = 4 //控制一行显示个数
              const num = Math.ceil(len / length) //循环次数
              if (num > 1) {
                let str = ''
                for (let i = 0; i < num; i++) {
                  str += value.substring(i * length, (i + 1) * length) + '\n'
                }
                return str
              } else {
                return value
              }
            }
          }
        },
        tooltip: {
          trigger: 'axis',
          axisPointer: {
            type: 'shadow'
          }
        },
        legend: {},
        // series: handleData
        series: [
          {
            barWidth: 30,
            name: [item?.dimension],
            type: 'bar',
            // data: item?.data
            data: data
          }
        ]
        // tooltip: {
        //   trigger: 'axis'
        // }
      }
      // options.series
    })
    // setAllDatas(reuslt.data)
  }

  useEffect(() => {
    console.log(allDatas, 'alldata')
  }, [allDatas])
  return (
    <PullToRefreshAndLoadMore
    // onRefresh={async () => {
    //   instanceId && getStatisticRun({ instanceId: instanceId })
    // }}
    >
      {allDatas?.map((item: any, i: any) => {
        return <Table key={i} columns={columns} dataSource={item} index={i}></Table>
      })}
    </PullToRefreshAndLoadMore>
  )
}
