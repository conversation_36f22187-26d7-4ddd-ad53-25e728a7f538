import React from 'react'
import { Box, styled } from '@mui/system'
import WordCloud from 'react-d3-cloud'

interface dataItem {
  text: string
  value: number
}
interface IProps {
  name: string
  type?: string
  data: dataItem[]
}

const data2 = [
  { text: 'Hey', value: 1000 },
  { text: 'lol', value: 200 },
  { text: 'first impression', value: 800 },
  { text: 'very cool', value: 1000000 },
  { text: 'duck', value: 10000 }
]

export default function ({ name, data }: IProps) {
  return (
    <Box>
      <WordCloud data={data} />
    </Box>
  )
}
