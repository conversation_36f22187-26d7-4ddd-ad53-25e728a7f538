import React from 'react'
import { Box, styled } from '@mui/system'
import { Button, Space, ErrorBlock } from 'antd-mobile'
import { FilterOutline as IconFilter } from 'antd-mobile-icons'
import { IViewBaseProps } from '../types'
import { getStatisticParticipate } from '@/api/easyform'
import { useEasyFormRequest } from '@/hooks'
import { ActionMenu } from '@/components'
import { COUNT_TYPE } from './define'
import { BarChart } from '../graph/components'

export default React.forwardRef(function ({ activityId, instanceId, pageDefine, fieldsInfo }: IViewBaseProps, ref) {
  const [dataset, setDataset] = React.useState({
    dimensions: ['dimensionName', 'count'],
    source: []
  })

  const [params, setParams] = React.useState<any>({
    type: COUNT_TYPE[0].key, //统计维度,
    finishReason: void 0, //流程状态,
    ticketStatus: void 0
  })
  const {
    loading,
    run,
    data: statiData
  } = useEasyFormRequest((params) => getStatisticParticipate(activityId, instanceId, params), {
    manual: true,
    onSuccess: (res: any) => {
      if (Array.isArray(res.data)) {
        const _data = res.data.sort((a: any, b: any) => b.count - a.count)
        setDataset({
          dimensions: ['dimensionName', 'count'],
          source: _data
        })
      }
    }
  })

  const handleAction = (item: any) => {
    setParams((prev: any) => ({ ...prev, type: item.key }))
  }
  React.useEffect(() => {
    run(params)
  }, [params, run])
  React.useImperativeHandle(ref, () => ({
    refresh: () => {
      run(params)
    }
  }))
  return (
    <Box ref={ref}>
      <Box className='flex justify-between items-center'>
        <ActionMenu onAction={handleAction} actions={COUNT_TYPE} />
        {/* <Button size='mini' fill='none'>
          <Space>
            <span>筛选</span>
            <IconFilter />
          </Space>
        </Button> */}
      </Box>
      <Box>
        {statiData?.data?.length > 0 ? '' : <ErrorBlock title='暂无数据' description='' status='empty' />}
        <BarChart unit='人' data={dataset} />
      </Box>
    </Box>
  )
})
