import React from 'react'
import * as echarts from 'echarts'
import ReactEcharts from 'echarts-for-react'
import { withAutoSize, useEchartsTheme } from '@yiban/system'
import { getOption } from './helper'

const defaultDataset = {
  dimensions: [],
  source: []
}
export type ChartProps = any & {
  unit?: string
}
const ProgressBar = React.forwardRef<any, ChartProps>(
  ({ data = defaultDataset, unit = '人', style, width }: ChartProps, ref) => {
    console.log('===graph:', data)
    const echartsTheme = useEchartsTheme()
    const _height = React.useMemo(() => {
      return Math.max(100, (data?.source?.length || 0) * 64)
    }, [data?.source?.length])

    const option = React.useMemo(() => getOption(data, unit, echartsTheme), [data, echartsTheme, unit])
    return (
      <ReactEcharts
        theme={echartsTheme}
        echarts={echarts}
        option={option}
        ref={ref}
        style={{ width: width, height: _height, ...style }}
      />
    )
  }
)
export default ProgressBar
// export default withAutoSize(ProgressBar)
