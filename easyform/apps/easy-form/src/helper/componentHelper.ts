import React from 'react'
import Exception from './exception'
export function loadComponent(scope: string, module: string, child?: string, _window: any = window) {
  return async () => {
    console.log('========scope:', _window[scope], scope, module)
    await __webpack_init_sharing__('default')
    const container = _window[scope] // or get the container somewhere else
    console.log('__webpack_share_scopes__==',__webpack_share_scopes__)
    await container?.init(__webpack_share_scopes__.default)
    try {
      const factory = await _window[scope].get(module)
      const Module = factory()
      return child ? Module[child] : Module
    } catch (err) {
      return Promise.resolve({ default: () => React.createElement(Exception) })
    }
  }
}

const getExposePackageName = (p: string) => {
  return p.replace(/@/g, '$').replace(/\//g, '_').replace(/-/g, '_')
}

const getComponentName = (packageName: string, componentName: string, version?: string) => {
  return `${packageName}.${componentName}`
}

export { getExposePackageName, getComponentName }