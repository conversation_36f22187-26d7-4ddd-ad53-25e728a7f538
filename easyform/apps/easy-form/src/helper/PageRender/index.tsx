import React from 'react'
import { useMemoizedFn } from 'ahooks'
import { Box } from '@mui/system'
import UploadList from '@/components/UploadList'

interface IPageRender {
  componentTree: any
  components: any
  baseData?: any
  isPC?: boolean
  values?: any
}
const getComponent = (componentName: string, components?: Record<string, any>) => {
  const C = (components && components[componentName]?.Component) || Box
  return C
}

// const str = "{{userName}}";
// const regex = /{{(.*?)}}/;
// const matches = str.match(regex);
// const name = matches[1];
// console.log(name); // "user

const getValue = (v: any, baseData?: any) => {
  const reg = /{{(.*?)}}/
  if (reg.test(v)) {
    const key = v.match(reg)[1]
    return baseData ? baseData[key] : void 0
  }
  return v
}

const getProps = (props: any, baseData?: any) => {
  const _props = Object.entries(props).reduce((p, [k, v]) => {
    return { ...p, [k]: getValue(v, baseData) }
  }, {})
  return _props
}

export default function PageRender({ componentTree, components: propComponents, baseData, isPC, values }: IPageRender) {
  const Render = useMemoizedFn((itemData, _components, extraProps?: any, _isPC?: boolean) => {
    const Component = getComponent(itemData.componentName, _components)
    console.log('Component===',Component)
    console.log('itemData===',itemData)
    console.log('componentTree===',componentTree)
    const isHasChildren = Array.isArray(itemData.children) && itemData.children.length > 0
    return React.createElement(
      Component,
      { ...getProps(itemData.props, baseData), ...extraProps, isPC: _isPC },
      isHasChildren ? itemData.children.map((child: any) => Render(child, { key: child._id }, _isPC)) : void 0
    )
  })
  const content = React.useMemo(
    () =>
      componentTree.map((c: any) =>
        Render(c, propComponents, { key: c._id, hidden: !checkIsRender(values, c.settings?.render) }, isPC)
      ),
    [Render, componentTree, isPC, propComponents, values]
  )
  console.log('+++values change:', values)
  console.log('content===',content)
  return content
}

function checkIsRender(values: any, renderSetting?: any) {
  if (!renderSetting) {
    return true
  }
  //所有值满足时
  return Object.keys(renderSetting?.conditions)?.every((item) => {
    return renderSetting?.conditions?.[item]?.value?.includes(values?.[item])
  })
}
