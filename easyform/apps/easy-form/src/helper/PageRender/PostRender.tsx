import React from 'react'
import { useMemoizedFn } from 'ahooks'
import { Box } from '@mui/system'

interface IPageRender {
  componentTree: any
  components: any
  baseData?: any
  isPC?: boolean
}
const getComponent = (componentName: string, components?: Record<string, any>) => {
  // console.log('get===comp:', componentName, components)
  const C = (components && components[componentName]?.Component) || Box
  return C
}

// const str = "{{userName}}";
// const regex = /{{(.*?)}}/;
// const matches = str.match(regex);
// const name = matches[1];
// console.log(name); // "user

const getValue = (v: any, baseData?: any) => {
  const reg = /{{(.*?)}}/
  if (reg.test(v)) {
    const key = v.match(reg)[1]
    return baseData ? baseData[key] : void 0
  }
  return v
}

const getProps = (props: any, baseData?: any) => {
  const _props = Object.entries(props).reduce((p, [k, v]) => {
    return { ...p, [k]: getValue(v, baseData) }
  }, {})
  return _props
}

export const PosWrapper = ({ children, pos, ...other }: any) => {
  return (
    <Box
      className='PostWrapperRoot'
      sx={{
        width: !pos ? '100%' : undefined,
        position: pos ? 'absolute' : 'relative',
        left: pos?.left ?? undefined,
        top: pos?.top ?? undefined,
        zIndex: pos ? 1 : undefined
      }}
    >
      {children}
    </Box>
  )
}
export default function PostRender({ componentTree, components: propComponents, baseData, isPC }: IPageRender) {
  const Render = useMemoizedFn((itemData, _components, extraProps?: any, _isPC?: boolean) => {
    const Component = getComponent(itemData.componentName, _components)
    const isHasChildren = Array.isArray(itemData.children) && itemData.children.length > 0
    const { pos } = itemData
    return (
      <PosWrapper pos={pos}>
        {React.createElement(
          Component,
          { ...getProps(itemData.props, baseData), ...extraProps, isPC: _isPC },
          isHasChildren ? itemData.children.map((child: any) => Render(child, { key: child._id }, _isPC)) : void 0
        )}
      </PosWrapper>
    )
  })
  const content = React.useMemo(
    () => componentTree.map((c: any) => Render(c, propComponents, { key: c._id }, isPC)),
    [Render, componentTree, isPC, propComponents]
  )
  return content
}
