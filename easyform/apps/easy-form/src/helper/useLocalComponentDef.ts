import React from 'react'
import Input from '../localComponents/Input/def'
import TextArea from '../localComponents/TextArea/def'
import Select from '../localComponents/Select/def'
import ImageUploader from '../localComponents/ImageUploader/def'
import Radio from '../localComponents/Radio/def'
import Checkbox from '../localComponents/Checkbox/def'
import Rate from '../localComponents/Rate/def'
import OrgPicker from '../localComponents/OrgPicker/def'
import Label from '../localComponents/Label/def'
import Image from '../localComponents/Image/def'
import HyperText from '../localComponents/HyperText/def'
import Slider from '../localComponents/Slider/def'
import ColorPicker from '../localComponents/ColorPicker/def'
import Upload from '../localComponents/Upload/def'

// 本地组件定义映射表
const localComponentDefs: Record<string, any> = {
  '@yiban/common-mobile.Input': Input,
  '@yiban/common-mobile.TextArea': TextArea,
  '@yiban/common-mobile.Select': Select,
  '@yiban/common-mobile.ImageUploader': ImageUploader,
  '@yiban/common-mobile.Radio': Radio,
  '@yiban/common-mobile.Checkbox': Checkbox,
  '@yiban/common-mobile.Rate': Rate,
  '@yiban/common-mobile.OrgPicker': OrgPicker,
  '@yiban/common-mobile.Label': Label,
  '@yiban/common-mobile.Image': Image,
  '@yiban/common-mobile.HyperText': HyperText,
  '@yiban/common-mobile.Slider': Slider,
  '@yiban/common-mobile.ColorPicker': ColorPicker,
  '@yiban/common-mobile.Upload': Upload,
}

export function useLocalComponentDef(componentName: string) {
  const [def, setDef] = React.useState<any>(null)
  const [loading, setLoading] = React.useState(false)
  const [loadedError, setLoadedError] = React.useState<Error | null>(null)

  React.useEffect(() => {
    if (!componentName) {
      setDef(null)
      return
    }

    setLoading(true)
    try {
      const componentDef = localComponentDefs[componentName]
      if (componentDef) {
        setDef(componentDef)
        setLoadedError(null)
      } else {
        setLoadedError(new Error(`Component definition not found: ${componentName}`))
      }
    } catch (error) {
      setLoadedError(error as Error)
    } finally {
      setLoading(false)
    }
  }, [componentName])

  return [def, { loading, loadedError }]
} 