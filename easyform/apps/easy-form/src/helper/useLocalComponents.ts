import React from 'react'
import { getAllComponentName } from './index'
import {
  Typo<PERSON>,
  ImageList,
  FormItemWrapper,
  Input,
  TextArea,
  Select,
  ImageUploader,
  Radio,
  Checkbox,
  Rate,
  OrgPicker,
  Label,
  Image,
  HyperText,
  Slider,
  ColorPicker,
  Upload,
  UploadList,
  Form
} from '../localComponents/index'

// 本地组件映射表
const localComponents: Record<string, React.ComponentType<any>> = {
  // easy-form 前缀的组件
  'easy-form.Typography': Typography,
  'easy-form.ImageList': ImageList,
  'easy-form.FormItemWrapper': FormItemWrapper,
  'easy-form.Input': Input,
  'easy-form.TextArea': TextArea,
  'easy-form.Select': Select,
  'easy-form.ImageUploader': ImageUploader,
  'easy-form.Radio': Radio,
  'easy-form.Checkbox': Checkbox,
  'easy-form.Rate': Rate,
  'easy-form.OrgPicker': OrgPicker,
  'easy-form.Label': Label,
  'easy-form.Image': Image,
  'easy-form.HyperText': HyperText,
  'easy-form.Slider': Slider,
  'easy-form.ColorPicker': ColorPicker,
  'easy-form.Upload': Upload,
  'easy-form.UploadList': UploadList,
  'easy-form.Form': Form,

  // @yiban/common-mobile 前缀的组件
  '@yiban/common-mobile.Input': Input,
  '@yiban/common-mobile.TextArea': TextArea,
  '@yiban/common-mobile.Select': Select,
  '@yiban/common-mobile.ImageUploader': ImageUploader,
  '@yiban/common-mobile.Radio': Radio,
  '@yiban/common-mobile.Checkbox': Checkbox,
  '@yiban/common-mobile.Rate': Rate,
  '@yiban/common-mobile.OrgPicker': OrgPicker,
  '@yiban/common-mobile.Label': Label,
  '@yiban/common-mobile.Image': Image,
  '@yiban/common-mobile.HyperText': HyperText,
  '@yiban/common-mobile.Slider': Slider,
  '@yiban/common-mobile.ColorPicker': ColorPicker,
  '@yiban/common-mobile.Upload': Upload,
  '@yiban/common-mobile.UploadList': UploadList,
  '@yiban/common-mobile.Form': Form
}

export default function useLocalComponents(
  componentTree: any[],
  remoteDependencies?: Record<string, string>
) {
  const [components, setComponents] = React.useState<Record<string, { Component: React.ComponentType<any> }>>({})

  React.useEffect(() => {
    if (componentTree) {
      const componentList = getAllComponentName(componentTree) as string[]
      const loadedComponents = componentList.reduce<Record<string, { Component: React.ComponentType<any> }>>((acc, componentName) => {
        const Component = localComponents[componentName]
        if (Component) {
          return {
            ...acc,
            [componentName]: { Component }
          }
        }
        return acc
      }, {})
      setComponents(loadedComponents)
    }
  }, [componentTree])

  return { loadComplete: true, components }
} 