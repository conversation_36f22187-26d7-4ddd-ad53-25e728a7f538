import React from 'react'
import {
  Typo<PERSON>,
  ImageList,
  FormItemWrapper,
  Input,
  TextArea,
  Select,
  ImageUploader,
  Radio,
  Checkbox,
  Rate,
  OrgPicker,
  Label,
  Image,
  HyperText,
  Slider,
  ColorPicker,
  Upload,
  UploadList,
  Form
} from '../localComponents/index'

// 本地组件映射表
const localComponents: Record<string, React.ComponentType<any>> = {
  '@yiban/common-mobile.Input': Input,
  '@yiban/common-mobile.TextArea': TextArea,
  '@yiban/common-mobile.Select': Select,
  '@yiban/common-mobile.ImageUploader': ImageUploader,
  '@yiban/common-mobile.Radio': Radio,
  '@yiban/common-mobile.Checkbox': Checkbox,
  '@yiban/common-mobile.Rate': Rate,
  '@yiban/common-mobile.OrgPicker': OrgPicker,
  '@yiban/common-mobile.Label': Label,
  '@yiban/common-mobile.Image': Image,
  '@yiban/common-mobile.HyperText': HyperText,
  '@yiban/common-mobile.Slider': Slider,
  '@yiban/common-mobile.ColorPicker': ColorPicker,
  '@yiban/common-mobile.Upload': Upload,
  '@yiban/common-mobile.UploadList': UploadList,
  '@yiban/common-mobile.Form': Form
}

type TLocalInfo = {
  componentName: string
}

interface ILocalComponent {
  _localInfo: TLocalInfo
  [key: string]: any
}

export default React.forwardRef<any, ILocalComponent>(
  ({ _localInfo: { componentName }, ...other }, ref) => {
    const Component = localComponents[componentName]

    if (!Component) {
      return (
        <Typography sx={{ color: 'text.hint', fontSize: 12, px: 2, py: 0.5 }}>
          未找到本地组件: {componentName}
        </Typography>
      )
    }

    return React.createElement(Component, { ref, ...other })
  }
) 