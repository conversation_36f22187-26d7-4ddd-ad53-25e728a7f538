import React from 'react'
import { useDynamicScripts } from './useFederatedComponent'
import { getAllComponentName, getRemoteEntryUrl, loadComponent, getExposePackageName } from './index'

export default function useComponents(componentTree: any[], remoteDependencies?: Record<string, string>) {
  const [components, setComponents] = React.useState<any>(null)
  const remoteUrls = React.useMemo(
    () =>
      new Set(
        remoteDependencies
          ? Object.entries(remoteDependencies).map(([packageName, version]) =>
              getRemoteEntryUrl(packageName, version as string)
            )
          : []
      ),
    [remoteDependencies]
  )
  const { loadComplete } = useDynamicScripts(remoteUrls)
  React.useEffect(() => {
    if (loadComplete && componentTree) {
      const componentList = getAllComponentName(componentTree)
      setComponents(
        componentList.reduce((p: any, c: any) => {
          const [packageName, moduleName] = c.split('.')
          return {
            ...p,
            [c]: { Component: React.lazy(loadComponent(getExposePackageName(packageName), `./${moduleName}`)) }
          }
        }, {})
      )
    }
  }, [componentTree, loadComplete])

  return { loadComplete, components }
}
