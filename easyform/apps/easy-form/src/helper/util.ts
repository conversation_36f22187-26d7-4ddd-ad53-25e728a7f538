const jp = require('jsonpath')
const getDependencies = (componentTree: any, info: any) => {
  const comps = Array.from(new Set(jp.query(componentTree, '$..componentName')))
  return comps.reduce(
    (p: any, c: any) => ({
      ...p,
      [c.split('.')[0]]: info[c]?.version
    }),
    {}
  )
}

const getAllComponentName = (componentTree: any[]) => {
  return Array.from(new Set(jp.query(componentTree, '$..componentName')))
}

const getRemoteEntryUrl = (packageName: string, version: string) => {
  return `${(window as any).APP_CONFIG.componentRepository}/${packageName}/${version}/remoteEntry.js`
}

export { getDependencies, getAllComponentName, getRemoteEntryUrl }
