import React from 'react'
import { useFederatedComponent } from './index'
import { DotLoading, Skeleton } from 'antd-mobile'
import { Typography } from '@/components'

type TRemoteInfo = {
  version: string
  componentName?: string
  component?: string
}

interface IRemoteComponent {
  _remoteInfo: TRemoteInfo
  _loading?: boolean
  [key: string]: any
}
const Loading = () => {
  return (
    <div className='flex justify-center'>
      <DotLoading />
    </div>
  )
}
export default React.forwardRef<any, IRemoteComponent>(
  (
    { _remoteInfo: { componentName: PComponentName, component, version }, _loading = true, fallback, ...other },
    ref
  ) => {
    const componentName = React.useMemo(() => PComponentName || component || '', [PComponentName, component])
    const [packageName, moduleName] = React.useMemo(() => componentName.split('.'), [componentName])
    const { loadedError, Component } = useFederatedComponent(packageName, moduleName, version)

    const render = React.useMemo(
      () =>
        _loading ? (
          <React.Suspense fallback={fallback || <Skeleton.Paragraph lineCount={2} />}>
            {Component ? (
              React.createElement(Component, { ref: ref, ...other })
            ) : loadedError ? (
              <Typography sx={{ color: 'text.hint', fontSize: 12, px: 2, py: 0.5 }}>组件加载异常</Typography>
            ) : null}
          </React.Suspense>
        ) : Component ? (
          React.createElement(Component, { ref: ref, ...other })
        ) : loadedError ? (
          <Typography sx={{ color: 'text.hint', fontSize: 12, px: 2, py: 0.5 }}>组件加载异常</Typography>
        ) : null,
      [Component, _loading, loadedError, other, ref, fallback]
    )
    return render

    // if (_loading) {
    //   return (
    //     <React.Suspense fallback={<Skeleton.Paragraph lineCount={2} />}>
    //       {Component ? (
    //         React.createElement(Component, { ref: ref, ...other })
    //       ) : loadedError ? (
    //         <Typography sx={{ color: 'text.hint', fontSize: 12 }}>组件加载异常</Typography>
    //       ) : null}
    //     </React.Suspense>
    //   )
    // }
    // return Component ? (
    //   React.createElement(Component, { ref: ref, ...other })
    // ) : loadedError ? (
    //   <div>组件加载异常</div>
    // ) : null
  }
)
