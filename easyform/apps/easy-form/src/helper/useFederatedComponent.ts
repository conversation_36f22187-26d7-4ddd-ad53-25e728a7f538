import React from 'react'
import { getExposePackageName, loadComponent, getComponentName, getRemoteEntryUrl } from './index'

const urlCache = new Set()
/**
 * 批量加载remote
 */
export function useDynamicScripts(scripts: Set<string>, doc = document) {
  const [loadState, setLoadState] = React.useState({})
  // const [scriptElements, setScriptElements] = React.useState<any[]>([])
  React.useEffect(() => {
    if (doc) {
      console.log('===will load scripts:', scripts)
      scripts.forEach((url: string) => {
        if (urlCache.has(url)) {
          setLoadState((prev) => ({ ...prev, [url]: true }))
        } else {
          const element = doc.createElement('script')
          element.src = `${url}?_t=${Date.now()}`
          element.type = 'text/javascript'
          element.async = true
          element.onload = () => {
            urlCache.add(url)
            setLoadState((prev) => ({ ...prev, [url]: true }))
          }
          element.onerror = () => {
            setLoadState((prev) => ({ ...prev, [url]: false }))
          }
          // setScriptElements((prev) => [...prev, element])
          doc.head.appendChild(element)
        }
      })
    }
  }, [doc, scripts])
  const loadComplete = React.useMemo(() => Object.keys(loadState).length === scripts.size, [loadState, scripts.size])
  return { loadState, loadComplete }
}
export function useDynamicScript(url: string | undefined, autoRemove = true) {
  const [ready, setReady] = React.useState(false)
  const [loadedError, setLoadedError] = React.useState(false)
  React.useEffect(() => {
    if (!url) return
    if (urlCache.has(url)) {
      setReady(true)
      setLoadedError(false)
      return
    }
    setReady(false)
    setLoadedError(false)
    const element = document.createElement('script')
    element.src = url
    element.type = 'text/javascript'
    element.async = true
    element.onload = () => {
      urlCache.add(url)
      setReady(true)
    }
    element.onerror = () => {
      setReady(false)
      setLoadedError(true)
    }
    document.head.appendChild(element)
    return () => {
      if (autoRemove) {
        urlCache.delete(url)
        document.head.removeChild(element)
      }
    }
  }, [autoRemove, url])
  return {
    loadedError,
    ready
  }
}
const componentCache = new Map()
export function useFederatedComponent(packageName: string, moduleName: string, version = 'latest') {
  const componentName = React.useMemo(
    () => getExposePackageName(packageName) + '.' + moduleName,
    [moduleName, packageName]
  )
  const [Component, setComponent] = React.useState<any>(componentCache.get(componentName))

  const { ready, loadedError } = useDynamicScript(getRemoteEntryUrl(packageName, version), false)
  // React.useEffect(() => {
  //   if (Component) setComponent(null)
  //   // Only recalculate when key changes
  // }, [])

  React.useEffect(() => {
    if (ready && !Component) {
      if (componentCache.has(componentName)) {
        setComponent(componentCache.get(componentName))
        return
      }
      const Comp = React.lazy(loadComponent(getExposePackageName(packageName), `./${moduleName}`))
      componentCache.set(componentName, Comp)
      setComponent(Comp)
    }
  }, [Component, componentName, moduleName, packageName, ready])

  return { loadedError, Component }
}

const componentDefCache = new Map()

export function useLoadComponentDef(packageName?: string, moduleName?: string, version = 'latest') {
  const [def, setDef] = React.useState<any>(null)
  const [loading, setLoading] = React.useState(false)
  const [error, setError] = React.useState(false)

  const componentName = React.useMemo(
    () => (packageName && moduleName ? getExposePackageName(packageName) + '.' + moduleName : void 0),
    [moduleName, packageName]
  )
  const { ready } = useDynamicScript(
    packageName && moduleName
      ? `${(window as any).APP_CONFIG.componentRepository}/${packageName}/${version}/remoteEntry.js`
      : void 0,
    true
  )
  React.useEffect(() => {
    if (ready && componentName) {
      if (componentDefCache.has(componentName)) {
        setDef(componentDefCache.get(componentName))
        setLoading(false)
        setError(false)
        return
      }
      const remoteDef = loadComponent(getExposePackageName(packageName || ''), `./${moduleName}`, 'def')
      if (typeof remoteDef === 'function') {
        setLoading(true)
        remoteDef()
          .then((res) => {
            // console.log('===res:', res)
            setLoading(false)
            setError(false)
            setDef(res)
            componentDefCache.set(componentName, res)
          })
          .catch((e) => {
            setLoading(false)
            setError(true)
          })
      } else {
        setLoading(false)
        setError(true)
      }
    }
  }, [componentName, def, moduleName, packageName, ready])
  return [def, { loading, error }]
}
