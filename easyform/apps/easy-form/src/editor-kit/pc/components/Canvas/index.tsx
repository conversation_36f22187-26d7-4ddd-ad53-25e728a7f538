import React from 'react'
import { styled, Box } from '@mui/system'
import { useBoolean, useMemoizedFn } from 'ahooks'
import { Form, SpinLoading, Image } from 'antd-mobile'
import { MoreOutline as IconMore } from 'antd-mobile-icons'
import {
  useEditorContext,
  add,
  selectComponent,
  command,
  update,
  updateEditorSettings,
  updatePos
} from '@/editor-kit/context'
import { useDrag, useDrop } from 'react-dnd'
import EditNode from '@/editor-kit/common/EditNode'
import DraggableNode from '@/editor-kit/common/EditNode/draggable'
import { debounce, throttle } from 'lodash'
import { Typography } from '@/components'
import { useLocation } from 'react-router-dom'
import { RemoteComponent, getComponentName, useLoadComponentDef } from '@/helper'
import { DefaultInitValueMap } from '@/@define'

const StyledForm = styled(Form)({
  '--border-inner': 'none',
  '--border-top': 'none',
  '--border-bottom': 'none',
  '& .adm-list.adm-list': {
    '--padding-left': 0,
    '--padding-right': 0
  }
})

const ContentWrapper = styled('div')({
  // padding: 16,
  paddingTop: 16,
  display: 'flex',
  position: 'relative',
  flexDirection: 'column',
  '&>*': {
    // marginBottom: 8
  }
})

const ComponentWrapper = styled(Box)(({ theme }) => ({
  marginTop: 24,
  display: 'flex',
  alignItems: 'center',
  padding: '8px 8px',
  border: `4px dashed ${theme.palette.divider}`,
  maxWidth: 420,
  flexGrow: 1
  // borderRadius: 8
}))
const ComponentItem = styled('div')(({ theme }) => ({
  display: 'flex',
  fontSize: 24,
  color: theme.palette.text.secondary,
  alignItems: 'center',
  flexDirection: 'column',
  justifyContent: 'center',
  cursor: 'pointer'
  // marginRight: 16
}))

const getValue = (v: any, baseData?: any) => {
  const reg = /{{(.*?)}}/
  if (reg.test(v)) {
    const key = v.match(reg)[1]
    return baseData ? baseData[key] : void 0
  }
  return v
}

const getProps = (props: any, baseData?: any) => {
  const _props = Object.entries(props).reduce((p, [k, v]) => {
    return { ...p, [k]: getValue(v, baseData) }
  }, {})
  console.log('===props:', _props, baseData)
  return _props
}

export default React.forwardRef<any, any>(function (props, ref) {
  const contentRef = React.useRef<any>(null)
  const PostRef = React.useRef<any>(null)
  const location: any = useLocation()
  const [{ componentTree, currentComponent, components, settings, componentList, pageSettings }, editorDispatch] =
    useEditorContext()

  const handleSelect = React.useCallback(
    (_id: string) => {
      selectComponent(editorDispatch, _id)
    },
    [editorDispatch]
  )
  const handleCommand = React.useCallback(
    (_id: string, cmd: string) => {
      switch (cmd) {
        default: {
          command(editorDispatch, _id, cmd)
        }
      }
    },
    [editorDispatch]
  )

  const handleAddItem = React.useCallback(
    (componentData: any) => {
      console.log('==componentData:', componentData)
      add(editorDispatch, getComponentName(componentData.packageName, componentData.componentName), {
        ...componentData?.meta?.defaultProps,
        label: '标题'
      })
      setTimeout(() => {
        contentRef.current.scrollTop = contentRef.current.scrollHeight
      }, 200)
    },
    [editorDispatch]
  )

  const lastIndex = React.useMemo(() => componentTree.length - 1, [componentTree.length])

  React.useImperativeHandle(ref, () => ({
    addComponent: (componentData: any) => {
      handleAddItem(componentData)
    }
  }))
  //获取海报的宽高，延迟获取以保证能获取到正确的值
  // const func = React.useMemo(
  //   () =>
  //     throttle(
  //       () => {
  //         updateEditorSettings(editorDispatch, {
  //           ...pageSettings,
  //           formSettings: {
  //             type: 'post',
  //             width: PostRef?.current?.offsetWidth - 24,
  //             height: PostRef?.current?.offsetHeight - 143
  //           }
  //         })
  //       },
  //       300,
  //       { trailing: true, leading: false }
  //     ),
  //   [editorDispatch]
  // )

  // React.useEffect(() => {
  //   //海报模板才需要监听宽高
  //   if (PostRef?.current && pageSettinngs?.formSettings?.type == 'post') {
  //     func()
  //   }
  //   // eslint-disable-next-line react-hooks/exhaustive-deps
  // }, [PostRef?.current?.offsetWidth, PostRef?.current?.offsetHeight, editorSettinngs?.formSettings?.type])

  // const throUpdatePos = throttle(
  //   (_index: any, pos: any, left: any, top: any) => {
  //     updatePos(editorDispatch, [_index, { ...pos, left, top }])
  //   },
  //   25,
  //   { trailing: true, leading: false }
  // )
  // const [, drop] = useDrop(() => ({
  //   accept: 'Component',
  //   hover(item: any, monitor: any) {
  //     const { _index, componentName, pos } = item
  //     const { x, y } = monitor.getDifferenceFromInitialOffset()
  //     const left = x + pos?.left || 0
  //     //限制拖拽范围
  //     const top = y + pos?.top || 0
  //     // if (left < 0) {
  //     //   left = 0
  //     // }
  //     // if (top < 0) {
  //     //   top = 0
  //     // }
  //     throUpdatePos(_index, pos, left, top)
  //   }
  // }))
  // drop(PostRef)

  return (
    <Box className='h-full overflow-auto' sx={{ px: 2 }} ref={contentRef}>
      <StyledForm>
        <ContentWrapper>
          {components ? (
            <>
              {componentTree.length === 0 ? (
                <Typography sx={{ color: 'text.hint', mt: 1 }} align='center'>
                  请添加表单项创建表单
                </Typography>
              ) : (
                componentTree.map(({ componentName, _id, props, pos }: any, i: number) => (
                  <EditNode
                    componentName={components[componentName].name}
                    allowEdit={false}
                    allowUp={i !== 0}
                    allowDown={i !== lastIndex}
                    onCommand={(cmd: string) => handleCommand(_id, cmd)}
                    selected={currentComponent?._id === _id}
                    key={_id}
                    _index={i}
                    // component={React.createElement(Assembly.Radio, { ...props })}
                    component={React.createElement(RemoteComponent, {
                      _remoteInfo: {
                        version: components[componentName]?.version,
                        componentName: componentName
                      },
                      ...getProps(props, DefaultInitValueMap),
                      isPC: true
                    })}
                    // component={(Assembly as any)[componentName]}
                    onClick={() => handleSelect(_id)}
                  />
                ))
              )}
            </>
          ) : (
            <Box className='flex justify-center p-4'>
              <SpinLoading />
            </Box>
          )}
          {settings.type == 'NOTICE' || location?.state?.type == 'NOTICE' ? (
            ''
          ) : (
            <Box className='flex justify-center' sx={{ width: '100%' }}>
              <ComponentWrapper>
                <Typography sx={{ color: 'primary.main', fontWeight: 'bold', mr: 2, fontSize: 14 }}>
                  快捷添加
                </Typography>
                <Box className='flex items-center flex-grow justify-between'>
                  {componentList.quick?.map((c: any) => (
                    <ComponentItem
                      onClick={() => {
                        handleAddItem(c)
                      }}
                      key={c.name}
                    >
                      <Image width={24} height={24} src={c.icon} />
                      <Typography sx={{ fontSize: 12 }}>{c.name}</Typography>
                    </ComponentItem>
                  ))}
                </Box>
                {/* <Box sx={{ color: 'text.hint', fontSize: 24, ml: 1 }} onClick={handleClick}>
              <IconMore />
            </Box> */}
              </ComponentWrapper>
            </Box>
          )}
        </ContentWrapper>
      </StyledForm>

      {/* </React.Suspense> */}
    </Box>
  )
})
