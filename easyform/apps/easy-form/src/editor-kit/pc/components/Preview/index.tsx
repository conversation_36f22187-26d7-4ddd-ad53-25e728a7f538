import React from 'react'
import { Box, styled } from '@mui/system'
import { Typography } from '@/components'
import { Drawer } from 'antd'
import { MobileOutlined, LaptopOutlined, CloseCircleOutlined } from '@ant-design/icons'
import { Render } from '@/editor-kit/common'

export const StyledPreviewBtn = styled(Box, { shouldForwardProp: (prop) => prop !== 'selected' })<any>(
  ({ theme, selected }) => ({
    width: 80,
    height: '100%',
    display: 'flex',
    flexDirection: 'column',
    justifyContent: 'center',
    alignItems: 'center',
    transition: 'background-color .35s ease-in-out,color .35s ease-in-out',
    color: selected ? '#FFF' : '#232323',
    backgroundColor: selected ? theme.palette.primary.main : void 0,
    cursor: 'pointer',
    borderLeft: `1px solid ${theme.palette.divider}`,
    '&:last-of-type': {
      borderRight: `1px solid ${theme.palette.divider}`
    },
    '&:hover': {
      backgroundColor: selected ? void 0 : '#f8f8f8'
    },
    '& p': {
      fontWeight: 'normal'
    }
  })
)

const StyledIframe = styled('iframe')({
  outline: 'none',
  border: 'none'
})

export type PreviewProps = {
  onClose?: () => void
  open?: boolean
  pageDefine?: any
  settings?: any
  pageSettings?: any
  define?: any
}

export default function ({ onClose, open, pageDefine, settings, define = {}, pageSettings }: PreviewProps) {
  const [type, setType] = React.useState<'PC' | 'MOBILE'>('MOBILE')
  return (
    <Drawer
      bodyStyle={{ padding: 0 }}
      headerStyle={{ padding: 0, fontWeight: 'normal' }}
      title={
        <Box className='flex justify-center items-center' sx={{ height: 60 }}>
          <StyledPreviewBtn selected={type === 'MOBILE'} onClick={() => setType('MOBILE')}>
            <MobileOutlined />
            <Typography sx={{ fontSize: 12, mt: 0.5 }}>手机预览</Typography>
          </StyledPreviewBtn>
          <StyledPreviewBtn selected={type === 'PC'} onClick={() => setType('PC')}>
            <LaptopOutlined />
            <Typography sx={{ fontSize: 12, mt: 0.5 }}>电脑预览</Typography>
          </StyledPreviewBtn>
          <StyledPreviewBtn onClick={onClose}>
            <CloseCircleOutlined />
            <Typography sx={{ fontSize: 12, mt: 0.5 }}>关闭预览</Typography>
          </StyledPreviewBtn>
        </Box>
      }
      height='100vh'
      placement='bottom'
      open={open}
      onClose={onClose}
      closable={false}
      destroyOnClose={true}
    >
      <Box className='h-full w-full flex justify-center' sx={{ bgcolor: 'background.default' }}>
        <Box sx={{ height: '100%', width: type === 'MOBILE' ? 375 : '100%' }}>
          <Render
            define={define}
            pageSettings={pageSettings}
            isPC={type === 'PC'}
            pageDefine={pageDefine}
            settings={settings}
          />
        </Box>
      </Box>
    </Drawer>
  )
}
