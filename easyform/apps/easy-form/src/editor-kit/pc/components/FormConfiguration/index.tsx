import { Typography } from '@/components'
import { Box } from '@yiban/system'
import React, { useEffect, useMemo, useRef, useState } from 'react'
import { useEditorContext, update, updateEditorSettings } from '@/editor-kit/context'
import { Collapse, Space } from 'antd-mobile'
import { Form, Switch, Input, Checkbox, ColorPicker } from 'antd'
import { useOverlayScrollbars } from '@yiban/system'
import { ImageUploader } from '@/components'
import { isLocalSource } from '@/utils'
import BkgList from './components/BkgList'
import { styled } from '@mui/system'
import { ThemeColorPicker } from '@/pages/designer/editor/components/PageSettings'
const StyledCollPanel = styled(Collapse.Panel)<any>(({ theme }: any) => ({}))
export default (props: any) => {
  const [{ pageSettings, settings }, dispach] = useEditorContext()
  const [activeCol, setActiveCol] = useState<any>([])
  const handleChangeBKG = (value?: any, isUpload = false, isColor = false) => {
    if (isColor) {
      updateEditorSettings(dispach, {
        background: { color: [value] }
      })
    } else {
      updateEditorSettings(dispach, {
        background: isUpload ? { src: value.length ? value : [] } : { value }
      })
    }
  }
  const ref = useRef()
  const [initialize, instance] = useOverlayScrollbars({ options: { scrollbars: { autoHide: 'scroll' } } })
  const handleChangeCover = (value?: any, isUpload = false) => {
    updateEditorSettings(dispach, {
      cover: isUpload ? { src: value.length ? value : [] } : { value }
    })
  }
  useEffect(() => {
    ref.current && initialize(ref.current)
  }, [initialize])
  console.log(2120, pageSettings)
  const showSubmitSetting = useMemo(() => {
    return settings.type !== 'NOTICE'
  }, [settings.type])
  return (
    <>
      <Box sx={{ py: 3, height: '100%' }} ref={ref}>
        {/* <Typography variant='subtitle2' align='center'>
          请选择对应表单模板
        </Typography> */}
        <Collapse
          onChange={(active: any) => {
            setActiveCol(active)
          }}
        >
          <StyledCollPanel
            key='1'
            title={
              <Typography
                sx={{
                  fontWeight: 'bold',
                  fontSize: 'body1.fontSize'
                }}
              >
                背景图片
              </Typography>
            }
            style={{
              boxShadow:
                '0 1px 2px 0 rgba(0, 0, 0, 0.03), 0 1px 6px -1px rgba(0, 0, 0, 0.02), 0 2px 4px 0 rgba(0, 0, 0, 0.02)',
              backgroundColor: activeCol && activeCol.includes('1') ? 'rgb(240 236 255)' : undefined,
              borderLeft: activeCol && activeCol.includes('1') ? '4px solid #6f6dea' : '4px solid white'
            }}
          >
            <BkgList
              onChange={(record: any, isU: boolean, isC: boolean) => {
                handleChangeBKG(record?.value || record?.color || undefined, isU, isC)
              }}
              value={pageSettings?.background?.value?.[0] || pageSettings?.background?.color?.[0]}
            ></BkgList>

            <Box
              sx={{
                '.adm-list-item-content': {
                  borderTop: 'none'
                },
                '.adm-form-item-vertical': {
                  paddingLeft: 0
                }
              }}
            >
              <Typography sx={{ fontWeight: 600, color: 'black', fontSize: 12 }}>自定义背景</Typography>
              <ImageUploader
                value={pageSettings?.background?.src || pageSettings?.background?.value || []}
                maxCount={1}
                className={'bkg-Uploader'}
                imageUrlPrefix={pageSettings?.background?.value ? '' : undefined}
                onChange={(file: any) => {
                  handleChangeBKG(file, true)
                }}
              ></ImageUploader>
            </Box>
            <Box
              sx={{
                '.ezf-checkbox-wrapper': {
                  fontSize: 12,
                  color: (theme: any) => theme.palette.text.secondary
                }
              }}
            >
              <Space>
                <Checkbox
                  checked={!pageSettings?.hideMobileBKG}
                  onChange={(e: any) => {
                    const value = e?.target?.checked
                    updateEditorSettings(dispach, {
                      hideMobileBKG: !value
                    })
                  }}
                >
                  背景图应用到移动端
                </Checkbox>
              </Space>
            </Box>
          </StyledCollPanel>
          <StyledCollPanel
            key='2'
            style={{
              boxShadow:
                '0 1px 2px 0 rgba(0, 0, 0, 0.03), 0 1px 6px -1px rgba(0, 0, 0, 0.02), 0 2px 4px 0 rgba(0, 0, 0, 0.02)',
              backgroundColor: activeCol && activeCol.includes('2') ? 'rgb(240 236 255)' : undefined,
              borderLeft: activeCol && activeCol.includes('2') ? '4px solid #6f6dea' : '4px solid white'
            }}
            title={
              <Typography
                sx={{
                  fontWeight: 'bold',
                  fontSize: 'body1.fontSize'
                }}
              >
                页眉页脚
              </Typography>
            }
          >
            <Box>
              <Space direction='vertical' style={{ width: '100%', color: 'black', fontSize: 12 }}>
                <Box
                  sx={{
                    display: 'flex',
                    alignItems: 'center',
                    justifyContent: 'space-between',
                    pb: 1
                    //borderBottom: '1px solid rgba(0,0,0,0.1)'
                  }}
                >
                  <Box sx={{ fontWeight: 600, color: 'black', fontSize: 12 }}>显示页眉图片</Box>
                  <Switch
                    checked={!pageSettings?.hideCover}
                    onChange={(value: boolean) => {
                      updateEditorSettings(dispach, {
                        hideCover: !value
                      })
                    }}
                  ></Switch>
                </Box>
                <Box
                  sx={{
                    '.adm-form-item-vertical': {
                      paddingLeft: 0
                    },
                    '.adm-list-item-content': {
                      borderTop: 'none'
                    }
                  }}
                >
                  <Box sx={{ fontWeight: 600, color: 'black', fontSize: 12 }}>设置页眉图片</Box>

                  <ImageUploader
                    value={pageSettings?.cover?.src || pageSettings?.cover?.value || []}
                    maxCount={1}
                    imageUrlPrefix={pageSettings?.cover?.src ? undefined : ''}
                    onChange={(file: any) => {
                      handleChangeCover(file, true)
                    }}
                  ></ImageUploader>
                </Box>
                <Box>
                  <Form.Item
                    colon={false}
                    label={<Typography sx={{ fontWeight: 600, color: 'black', fontSize: 12 }}>页脚文案内容</Typography>}
                  >
                    <Input
                      size='small'
                      value={pageSettings?.footerText}
                      placeholder=''
                      allowClear
                      onChange={(e: any) => {
                        updateEditorSettings(dispach, {
                          footerText: e?.target?.value
                        })
                      }}
                    />
                  </Form.Item>
                </Box>
              </Space>
            </Box>
          </StyledCollPanel>
          <StyledCollPanel
            key={'3'}
            style={{
              boxShadow:
                '0 1px 2px 0 rgba(0, 0, 0, 0.03), 0 1px 6px -1px rgba(0, 0, 0, 0.02), 0 2px 4px 0 rgba(0, 0, 0, 0.02)',
              backgroundColor: activeCol && activeCol.includes('3') ? 'rgb(240 236 255)' : undefined,
              borderLeft: activeCol && activeCol.includes('3') ? '4px solid #6f6dea' : '4px solid white'
            }}
            title={
              <Typography
                sx={{
                  fontWeight: 'bold',
                  fontSize: 'body1.fontSize'
                }}
              >
                显示设置
              </Typography>
            }
          >
            <Space direction='vertical'>
              <Box sx={{ fontWeight: 600, color: 'black', fontSize: 12 }}>显示项设置</Box>
              <Box
                sx={{
                  '.ezf-checkbox-wrapper': {
                    fontSize: 12,
                    color: (theme: any) => theme.palette.text.secondary
                  }
                }}
              >
                <Space wrap>
                  <Checkbox
                    checked={!pageSettings?.hideTitle}
                    onChange={(e: any) => {
                      const value = e?.target?.checked
                      updateEditorSettings(dispach, {
                        hideTitle: !value
                      })
                    }}
                  >
                    显示标题文本
                  </Checkbox>
                  <Checkbox
                    checked={!pageSettings?.hideDescription}
                    onChange={(e: any) => {
                      const value = e?.target?.checked
                      updateEditorSettings(dispach, {
                        hideDescription: !value
                      })
                    }}
                  >
                    显示描述文本
                  </Checkbox>
                  <Checkbox
                    style={{ display: !showSubmitSetting ? 'none' : undefined }}
                    checked={!pageSettings?.hideSubmitButton}
                    onChange={(e: any) => {
                      const value = e?.target?.checked
                      updateEditorSettings(dispach, {
                        hideSubmitButton: !value
                      })
                    }}
                  >
                    显示提交按钮
                  </Checkbox>
                  <Checkbox
                    style={{ display: !showSubmitSetting ? 'none' : undefined }}
                    checked={pageSettings?.stickySubmit}
                    onChange={(e: any) => {
                      const value = e?.target?.checked
                      updateEditorSettings(dispach, {
                        stickySubmit: value
                      })
                    }}
                  >
                    固定按钮到窗口
                  </Checkbox>
                </Space>
              </Box>
            </Space>
            <Space direction='vertical' style={{ display: !showSubmitSetting ? 'none' : undefined }}>
              <Box sx={{ fontWeight: 600, color: 'black', fontSize: 12 }}>按钮自定义</Box>
              <Form.Item
                colon={false}
                label={
                  <Typography sx={{ color: (theme: any) => theme.palette.text.secondary, fontSize: 12 }}>
                    提交按钮文字
                  </Typography>
                }
              >
                <Input
                  size='small'
                  value={pageSettings?.submitText}
                  placeholder='提交'
                  allowClear
                  onChange={(e: any) => {
                    updateEditorSettings(dispach, {
                      submitText: e?.target?.value
                    })
                  }}
                />
              </Form.Item>
            </Space>
          </StyledCollPanel>
          <StyledCollPanel
            style={{
              boxShadow:
                '0 1px 2px 0 rgba(0, 0, 0, 0.03), 0 1px 6px -1px rgba(0, 0, 0, 0.02), 0 2px 4px 0 rgba(0, 0, 0, 0.02)',
              backgroundColor: activeCol && activeCol.includes('4') ? 'rgb(240 236 255)' : undefined,
              borderLeft: activeCol && activeCol.includes('4') ? '4px solid #6f6dea' : '4px solid white'
            }}
            key={'4'}
            title={
              <Typography
                sx={{
                  fontWeight: 'bold',
                  fontSize: 'body1.fontSize'
                }}
              >
                文字格式
              </Typography>
            }
          >
            <Box>
              <Form.Item
                colon={false}
                label={
                  <Typography sx={{ color: (theme: any) => theme.palette.text.secondary, fontSize: 12 }}>
                    标题文字颜色
                  </Typography>
                }
              >
                <ColorPicker
                  showText
                  size='small'
                  value={pageSettings?.titleColor || 'black'}
                  onChange={(color: any) => {
                    updateEditorSettings(dispach, {
                      titleColor: color?.toRgbString()
                    })
                  }}
                ></ColorPicker>
              </Form.Item>
            </Box>
            <Box>
              <Form.Item
                colon={false}
                label={
                  <Typography sx={{ color: (theme: any) => theme.palette.text.secondary, fontSize: 12 }}>
                    描述文字颜色
                  </Typography>
                }
              >
                <ColorPicker
                  showText
                  value={pageSettings?.descriptionColor || 'black'}
                  size='small'
                  onChange={(color: any) => {
                    updateEditorSettings(dispach, {
                      descriptionColor: color?.toRgbString()
                    })
                  }}
                ></ColorPicker>
              </Form.Item>
            </Box>
            <Box>
              <Box sx={{ fontWeight: 600, color: 'black', fontSize: 12 }}>设置主题色</Box>
              <ThemeColorPicker
                value={pageSettings?.themeColor}
                onChange={(color: any) => {
                  updateEditorSettings(dispach, {
                    themeColor: color,
                    descriptionColor: color,
                    titleColor: color
                  })
                }}
              ></ThemeColorPicker>
            </Box>
          </StyledCollPanel>
        </Collapse>
      </Box>
    </>
  )
}
