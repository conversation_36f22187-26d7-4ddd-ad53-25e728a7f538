import React, { useState } from 'react'
import { useEditorContext, updateEditorSettings } from '@/editor-kit/context'
import { Space, Image, Badge } from 'antd-mobile'
import { Box } from '@mui/system'
const BKGs = [
  { order: 1, color: 'rgba(55, 93, 115, 0.3)' },
  { order: 1, color: 'rgba(143, 85, 155, 0.3)' },
  { order: 1, color: 'rgba(209, 219, 229, 0.3)' },
  { order: 1, color: 'rgba(33, 85, 153, 0.5)' },
  { order: 1, color: 'rgba(0, 31, 69, 0.8)' },
  { order: 1, color: 'rgba(213, 121, 95, 0.6)' },
  { order: 1, color: 'rgba(227, 227, 255, 0.7)' },
  { order: 1, color: 'rgba(255, 237, 173, 0.5)' },
  { order: 1, color: 'rgba(226, 185, 53, 0.5)' }
]
const SelectWrapper = ({
  onChange,
  children,
  key,
  active,
  value
}: {
  active?: any
  key?: any
  onChange?: (record: any) => void
  children?: any
  value?: any
}) => {
  const isActive = active == value
  return (
    <Badge
      style={{ '--right': '19px', '--top': '8px', borderRadius: 0, display: isActive ? undefined : 'none' }}
      color='#9ba4b2'
      content='使用中'
    >
      <Box
        sx={{
          border: (theme: any) => `2px solid ${isActive ? theme.palette.primary.main : 'white'}`
        }}
      >
        {children}
      </Box>
    </Badge>
  )
}
export default ({ onChange, value = 'none', width, height }: any) => {
  console.log(2120, value)
  return (
    <>
      <Box>
        <Space wrap>
          {BKGs.map((item: any, index) => {
            return (
              <SelectWrapper key={index} value={item?.value || item?.color} active={value}>
                {item?.value ? (
                  <Image
                    onClick={() => {
                      onChange && onChange(item)
                    }}
                    width={width ?? 55}
                    height={height ?? 80}
                    src={item?.value}
                  ></Image>
                ) : (
                  <Box
                    sx={{
                      display: 'flex',
                      alignItems: 'center',
                      width: width ?? 55,
                      height: height ?? 80,
                      backgroundColor: item?.color,
                      fontSize: 12,
                      justifyContent: 'center',
                      color: 'black'
                    }}
                    onClick={() => {
                      onChange && onChange(item, undefined, true)
                    }}
                  ></Box>
                )}
              </SelectWrapper>
            )
          })}
          <SelectWrapper value={'none'} active={value}>
            <Box
              sx={{
                display: 'flex',
                alignItems: 'center',
                width: width ?? 55,
                height: height ?? 80,
                backgroundColor: '#f7f9fa',
                fontSize: 12,
                justifyContent: 'center',
                color: 'black'
              }}
              onClick={() => {
                onChange && onChange('')
              }}
            >
              <Box>无背景</Box>
            </Box>
          </SelectWrapper>
        </Space>
      </Box>
    </>
  )
}
