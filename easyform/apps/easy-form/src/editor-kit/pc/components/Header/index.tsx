import React from 'react'
import { Box, display, styled } from '@mui/system'
import { EyeOutlined as IconPreview, SaveOutlined as IconSave, SkinOutlined, EditOutlined } from '@ant-design/icons'
import { Popover, Button, Segmented, Radio } from 'antd'
import { Typography } from '@/components'
const StyledButton = styled(Button)(({ theme }) => ({
  height: '100%',
  borderRadius: 0,
  display: 'flex',
  alignItems: 'center',
  justifyContent: 'center',
  borderLeft: `1px solid ${theme.palette.divider}`,
  color: theme.palette.primary.main,
  transition: 'color .35s ease-in-out',
  cursor: 'pointer',
  '&:hover': {
    color: theme.palette.primary.main
  }
}))
const StyledButton2 = styled(Button)(({ theme }) => ({
  height: '100%',
  borderRadius: 0,
  display: 'flex',
  alignItems: 'center',
  justifyContent: 'center',
  //borderLeft: `1px solid ${theme.palette.divider}`,
  color: theme.palette.primary.main,
  transition: 'color .35s ease-in-out',
  cursor: 'pointer',
  '&:hover': {
    color: theme.palette.primary.main
  }
}))
export type HeaderProps = {
  onSave?: () => void
  onPreview?: () => void
}

export default function ({ onSave, onPreview }: HeaderProps) {
  return (
    <Box
      className='flex items-center justify-between'
      sx={{ height: '40px', borderTop: '1px solid', borderColor: 'divider' }}
    >
      {/* <Popover content={'预览'} placement='left'>
        <StyledButton sx={{ color: 'black' }} onClick={onPreview}>
          <IconPreview />
        </StyledButton>
      </Popover> */}
      {/* <Popover content={'保存'} placement='right'>
        <StyledButton sx={{ color: 'black' }} onClick={onSave}>
          <IconSave />
        </StyledButton>
      </Popover> */}
      <Box>
        {/* <Segmented
          options={[
            {
              label: (
                <Box sx={{ display: 'flex' }}>
                  <EditOutlined />
                  <Typography>页面设计</Typography>
                </Box>
              ),
              value: 'SETTING'
            },
            {
              label: (
                <Box sx={{ display: 'flex' }}>
                  <EditOutlined />
                  <Typography>页面设计</Typography>
                </Box>
              ),
              value: 'THEME'
            }
          ]}
        ></Segmented> */}
        {/* <Radio.Group>
          <Radio.Button value='a'>页面设计</Radio.Button>
          <Radio.Button value='b'>页面外观</Radio.Button>
        </Radio.Group> */}
      </Box>
      <Box sx={{ display: 'flex' }}>
        <StyledButton type='link' icon={<IconPreview />} onClick={onPreview}>
          预览
        </StyledButton>
        <StyledButton type='link' icon={<IconSave></IconSave>} onClick={onSave}>
          保存
        </StyledButton>
      </Box>
    </Box>
  )
}
