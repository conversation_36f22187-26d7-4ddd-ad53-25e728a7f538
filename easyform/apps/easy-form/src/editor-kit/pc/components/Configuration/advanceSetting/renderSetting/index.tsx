import React from 'react'
import { Box, styled } from '@mui/system'
import { <PERSON><PERSON>, <PERSON><PERSON> } from 'antd-mobile'
import {
  SetOutline as IconSetting,
  AddCircleOutline as IconAdd,
  LeftOutline as IconBack,
  CloseOutline as IconClose,
  ExclamationCircleOutline as IconInfo,
  RightOutline as IconRight
} from 'antd-mobile-icons'
import produce from 'immer'

import { Typography } from '@/components'
import { useBoolean } from 'ahooks'
import OptionSelect from './components/OptionSelect'
import Relation, { RelationDesc } from '@/@define/relation'

interface IProps {
  onClose: () => void
  checkSubject: any[]
  onOk?: (data: any) => void
  dataset?: any
  title?: string
}

const Circle = styled('span')(({ theme }) => ({
  width: 8,
  height: 8,
  borderRadius: '50%',
  backgroundColor: theme.palette.divider,
  display: 'inline-block'
}))

export default function ({ onClose, checkSubject, onOk, dataset, title }: IProps) {
  const [condition, setCondition] = React.useState<'AND' | 'OR'>('AND')
  const [settingsData, setSettingsData] = React.useState<any>({})
  const [showSelectSubject, { toggle: toggelSelectSubject }] = useBoolean(false)
  const [showOptionCondition, { toggle: toggleOptionCondition }] = useBoolean(false)
  const [currentOptionCondition, setCurrentOptionCondition] = React.useState<any>()
  React.useEffect(() => {
    setSettingsData(dataset?.conditions || {})
    setCondition(dataset?.expression || 'AND')
  }, [dataset])
  const subjectDataMap = React.useMemo(() => {
    return checkSubject.reduce((p: any, c) => {
      return { ...p, [c.data._id]: { options: c.data.props.options || [], title: c.data.props.label } }
    }, {})
  }, [checkSubject])
  React.useEffect(() => {
    console.log('==check:', checkSubject, dataset)
  }, [checkSubject, dataset])
  const handleOptionSelectOk = (v: any) => {
    console.log('settings options:', v)
    toggelSelectSubject()
    setSettingsData(v)
  }
  const handleSetOptionCondition = (subjectId: string, expression: string, isUpdate?: boolean) => {
    if (isUpdate) {
      setSettingsData(
        produce(settingsData, (draft: any) => {
          draft[subjectId].expression = expression
        })
      )
    } else {
      setCurrentOptionCondition({ subjectId: subjectId, expression: expression })
      toggleOptionCondition()
    }
  }

  const handleOk = () => {
    if (typeof onOk === 'function') {
      onOk(Object.keys(settingsData).length > 0 ? { expression: condition, conditions: settingsData } : void 0)
    }
  }

  return (
    <>
      <Box>
        <Box sx={{ px: 2, py: 1, bgcolor: '#FFF' }} className='flex flex-col'>
          <Box className='flex items-center'>
            <Typography
              onClick={onClose}
              color='textSecondary'
              sx={{
                '&>*': {
                  mr: 0.5
                }
              }}
            >
              显示设置
            </Typography>
            <div className='flex-grow' />
            <Box
              className='flex items-center'
              sx={{
                border: 1,
                borderColor: 'secondary.main',
                fontSize: 13,
                p: 0.5,
                borderRadius: 1.5
              }}
            >
              <Typography
                onClick={toggelSelectSubject}
                color='secondary'
                sx={{
                  '&>*': {
                    mr: 0.5
                  }
                }}
              >
                <IconAdd />
                添加条件
              </Typography>
            </Box>
          </Box>
        </Box>
        <Box className='flex flex-col' sx={{ px: 2, py: 1 }}>
          <Typography variant='body2' sx={{ mb: 0.5 }}>
            <IconInfo style={{ marginRight: 4 }} />
            {title || '无标题'}
          </Typography>
          <Typography variant='subtitle2'>当满足设置的条件时才显示该表单项</Typography>
        </Box>
        <Box sx={{ px: 2 }}>
          {Object.keys(settingsData).map((k) => (
            <Box key={k} sx={{ borderRadius: 2, border: 1, borderColor: 'divider', bgcolor: '#FFF', mb: 1 }}>
              <Box sx={{ px: 1.5, py: 1 }}>
                <Typography variant='body2'>{subjectDataMap[k]?.title}</Typography>
              </Box>
              <Box
                sx={{
                  px: 1.5,
                  '&>*': {
                    mb: 1
                  }
                }}
              >
                {settingsData[k].valueIndex.map((vi: number) => (
                  <Typography key={vi} variant='subtitle1'>
                    <Circle sx={{ mr: 0.5 }} />
                    {subjectDataMap[k]?.options[vi].label}
                  </Typography>
                ))}
              </Box>
              <Box
                className='flex items-center justify-center'
                sx={{ px: 1.5, py: 1, borderTop: 1, borderColor: 'divider', fontSize: 12 }}
              >
                <IconSetting />
                <Box sx={{ ml: 0.5 }} onClick={() => handleSetOptionCondition(k, settingsData[k].expression)}>
                  {RelationDesc[settingsData[k].expression]}
                </Box>
              </Box>
            </Box>
          ))}
        </Box>
        {Object.keys(settingsData).length > 0 && (
          <Box sx={{ px: 2, py: 1 }}>
            <Typography variant='body2'>设置为多题条件时,多题间为：</Typography>
            <Box
              sx={{ mt: 1, px: 1.5, py: 1, border: 1, borderColor: 'divider', borderRadius: 1 }}
              className='flex justify-between items-center'
            >
              <Typography className='w-full flex justify-between items-center' variant='subtitle1'>
                且关系（需满足所有关联条件）
                <IconRight />
              </Typography>
            </Box>
          </Box>
        )}
        <Box className='p-4' sx={{ '&>*': { mb: 2 } }}>
          <Button block shape='rounded' onClick={() => setSettingsData({})}>
            清除
          </Button>
          <Button onClick={handleOk} block shape='rounded' color='primary'>
            保存
          </Button>
        </Box>
      </Box>
      <OptionSelect
        onOk={handleOptionSelectOk}
        checkSubject={checkSubject}
        value={settingsData}
        open={showSelectSubject}
        onClose={toggelSelectSubject}
      />
      <Picker
        onConfirm={(v) => {
          handleSetOptionCondition(currentOptionCondition?.subjectId, v[0] as string, true)
        }}
        value={currentOptionCondition ? [currentOptionCondition.expression] : void 0}
        visible={showOptionCondition}
        onClose={toggleOptionCondition}
        columns={[Relation]}
      />
    </>
  )
}
