import React from 'react'
import { Box, styled } from '@mui/system'
import { <PERSON><PERSON>, <PERSON>up, Collapse, List, Checkbox } from 'antd-mobile'
import {
  SetOutline as IconSetting,
  AddCircleOutline as IconAdd,
  LeftOutline as IconBack,
  CloseOutline as IconClose
} from 'antd-mobile-icons'
import { Typography } from '@/components'
import { useBoolean } from 'ahooks'
import produce from 'immer'

interface IOptionSelect {
  value?: any
  open?: boolean
  onClose?: () => void
  onOk?: (v: any) => void
  checkSubject: any
}

export default function ({ value, checkSubject, open, onClose, onOk }: IOptionSelect) {
  /**
   * optionsSettingValue:
   *  {题目ID:[0,1,2]}勾选选项索引
   */
  const [optionsSettingValue, setOptionsSettingValue] = React.useState<any>({})
  React.useEffect(() => {
    setOptionsSettingValue(value || {})
  }, [value])
  const handleCheckOption = (subjectId: string, optionIndex: number) => {
    console.log('handle===checkoptions', subjectId, optionIndex, optionsSettingValue)
    setOptionsSettingValue(
      produce(optionsSettingValue, (draft: any) => {
        if (draft[subjectId]) {
          const exitOptionIndex = draft[subjectId].valueIndex.findIndex((o: number) => o === optionIndex)
          if (exitOptionIndex === -1) {
            draft[subjectId].valueIndex.push(optionIndex)
          } else {
            draft[subjectId].valueIndex.splice(exitOptionIndex, 1)
          }
        } else {
          draft[subjectId] = { expression: 'INCLUDE', valueIndex: [optionIndex] }
        }
      })
    )
  }
  const handleOk = () => {
    if (typeof onOk === 'function') {
      const result: any = {}
      Object.keys(optionsSettingValue).forEach((k) => {
        if (optionsSettingValue[k].valueIndex.length > 0) {
          result[k] = optionsSettingValue[k]
        }
      })
      onOk(result)
    }
  }
  return (
    <Popup
      visible={open}
      onMaskClick={onClose}
      position='bottom'
      bodyStyle={{ height: '90vh', borderTopLeftRadius: '8px', borderTopRightRadius: '8px' }}
    >
      <Box className='h-full overflow-y-auto' sx={{ p: 1.5 }}>
        <Box className='flex items-center justify-between'>
          <Typography>选择要关联表单项</Typography>
          <IconClose onClick={onClose} />
        </Box>
        <Box sx={{ mt: 1.5 }}>
          <Collapse accordion>
            {checkSubject.map(({ data, index }: any) => (
              <Collapse.Panel
                key={data._id}
                title={
                  <Box>
                    <Typography
                      color={
                        optionsSettingValue[data._id] && optionsSettingValue[data._id].valueIndex.length > 0
                          ? 'primary'
                          : void 0
                      }
                    >{`${index + 1}. ${data.props.label}`}</Typography>
                  </Box>
                }
              >
                <Box>
                  {data.props?.options?.map((o: any, i: number) => (
                    <Box key={i} sx={{ py: 1 }} className='flex items-center justify-between'>
                      <Typography color='textSecondary'>{o.label}</Typography>
                      <Checkbox
                        onChange={() => handleCheckOption(data._id, i)}
                        checked={optionsSettingValue[data._id] && optionsSettingValue[data._id].valueIndex.includes(i)}
                      />
                    </Box>
                  ))}
                </Box>
              </Collapse.Panel>
            ))}
          </Collapse>
        </Box>
        <Box sx={{ mt: 3 }}>
          <Button onClick={handleOk} block shape='rounded' color='primary'>
            确认
          </Button>
        </Box>
      </Box>
    </Popup>
  )
}
