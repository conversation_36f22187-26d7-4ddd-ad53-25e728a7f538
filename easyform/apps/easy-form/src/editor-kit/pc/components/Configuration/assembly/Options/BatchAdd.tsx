import React from 'react'
import { Pop<PERSON>, Button, TextArea } from 'antd-mobile'
import { Box, styled } from '@mui/system'

const defaultTags = [
  {
    name: '满意',
    value: ['很不满意', '不满意', '一般', '满意', '很满意']
  },
  {
    name: '认同度',
    value: ['很不同意', '不同意', '一般', '同意', '很同意']
  },
  {
    name: '合适',
    value: ['优秀', '良好', '一般', '较差', '非常差']
  },
  {
    name: '频率',
    value: ['经常', '有时', '很少', '从不']
  }
]

const Tag = styled('button')<{ active?: boolean }>(({ theme, active }) => ({
  padding: '4px 8px',
  border: 'none',
  borderRadius: '99999px',
  backgroundColor: active ? theme.palette.primary.main : theme.palette.background.default,
  fontSize: 13,
  color: active ? '#FFF' : theme.palette.text.secondary,
  transition: 'color .35s ease-in-out,background-color .35s ease-in-out'
}))

const StyledTextArea = styled(TextArea)({
  height: '100%',
  background: '#FFF',
  padding: '16px',
  borderRadius: '8px',
  '& textarea': {
    height: '100%'
  }
})

interface BatchAddProps {
  open?: boolean
  onSubmit?: (value: string) => void
  onClose?: () => void
}
export default function ({ open, onClose, onSubmit }: BatchAddProps) {
  const [currentTag, setCurrentTag] = React.useState<string>()
  const [text, setText] = React.useState<string>('')
  const handleSelectTag = (tag: any) => {
    setCurrentTag(tag.name)
    setText(tag.value.join('\n'))
  }
  const handleSubmit = () => {
    if (typeof onSubmit === 'function') {
      onSubmit(text)
    }
  }
  return (
    <Popup visible={open} bodyStyle={{ height: '100vh' }}>
      <Box className='h-full flex flex-col' sx={{ bgcolor: 'background.default' }}>
        <Box className='grid grid-cols-4 gap-3' sx={{ p: 2, bgcolor: '#FFF' }}>
          {defaultTags.map((t) => (
            <Tag active={t.name === currentTag} onClick={() => handleSelectTag(t)} key={t.name}>
              {t.name}
            </Tag>
          ))}
        </Box>
        <Box className='flex-1' sx={{ p: 2 }}>
          <StyledTextArea placeholder='每行一个选项' value={text} onChange={setText} />
        </Box>
        <Box sx={{ px: 2, pb: 4 }}>
          <Button onClick={handleSubmit} block className='mb-4' color='primary' shape='rounded'>
            确定
          </Button>
          <Button onClick={onClose} block shape='rounded'>
            取消
          </Button>
        </Box>
      </Box>
    </Popup>
  )
}
