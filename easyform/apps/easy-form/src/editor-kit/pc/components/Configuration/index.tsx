import React from 'react'
import { Box } from '@mui/system'
import { Typography } from '@/components'
import { useMemoizedFn } from 'ahooks'
import { useEditorContext, update } from '@/editor-kit/context'
import ConfigPanel from './ConfigPanel'
import { useLoadComponentDef } from '@/helper'

export default function () {
  const [{ componentTree, currentComponent, components, componentList }, editorDispatch] = useEditorContext()
  const currentComponentInfo = React.useMemo(
    () =>
      components && currentComponent
        ? [
            components[currentComponent.componentName].packageName,
            components[currentComponent.componentName].componentName,
            components[currentComponent.componentName].version
          ]
        : [],
    [components, currentComponent]
  )
  const [def, { loading: defLoading, loadedError: defLoadedError }] = useLoadComponentDef(...currentComponentInfo)
  const handleFinishSetting = useMemoizedFn((propsAndSettings) => {
    console.log('==finish settings:', propsAndSettings)
    update(editorDispatch, propsAndSettings)
  })
  // React.useEffect(() => {
  //   console.log('===当前组件定义:', def)
  // }, [def])
  return currentComponent ? (
    <ConfigPanel
      def={def}
      values={currentComponent?.props}
      currentIndex={currentComponent?.index}
      settings={currentComponent?.settings}
      onFinish={handleFinishSetting}
    />
  ) : (
    <Box sx={{ py: 3 }}>
      <Typography variant='subtitle2' align='center'>
        请添加组件或选择画布中组件
      </Typography>
    </Box>
  )
}
