import React from 'react'
import { Box } from '@mui/system'
import { Typography } from '@/components'
import { useMemoizedFn } from 'ahooks'
import { useEditorContext, update } from '@/editor-kit/context'
import ConfigPanel from './ConfigPanel'
import { useLoadComponentDef } from '@/helper'
import { useLocalComponentDef } from '@/helper/useLocalComponentDef'

export default function () {
  const [{ componentTree, currentComponent, components, componentList }, editorDispatch] = useEditorContext()
  const currentComponentInfo = React.useMemo(
    () =>
      components && currentComponent
        ? [
            components[currentComponent.componentName].packageName,
            components[currentComponent.componentName].componentName,
            components[currentComponent.componentName].version
          ]
        : [],
    [components, currentComponent]
  )

  // 使用本地组件定义
  const [localDef, { loading: localDefLoading, loadedError: localDefLoadedError }] = useLocalComponentDef(
    currentComponent?.componentName
  )

  // 使用远程组件定义
  const [remoteDef, { loading: remoteDefLoading, loadedError: remoteDefLoadedError }] = useLoadComponentDef(
    ...currentComponentInfo
  )

  // 优先使用本地组件定义，如果没有则使用远程组件定义
  const def = localDef || remoteDef
  const loading = localDefLoading || remoteDefLoading
  const loadedError = localDefLoadedError || remoteDefLoadedError

  const handleFinishSetting = useMemoizedFn((propsAndSettings) => {
    console.log('==finish settings:', propsAndSettings)
    update(editorDispatch, propsAndSettings)
  })
  // React.useEffect(() => {
  //   console.log('===当前组件定义:', def)
  // }, [def])
  console.log('def=-==',def)
  return currentComponent ? (
    <ConfigPanel
      def={def}
      values={currentComponent?.props}
      currentIndex={currentComponent?.index}
      settings={currentComponent?.settings}
      onFinish={handleFinishSetting}
    />
  ) : (
    <Box sx={{ py: 3 }}>
      <Typography variant='subtitle2' align='center'>
        请添加组件或选择画布中组件
      </Typography>
    </Box>
  )
}
