import React, { useRef } from 'react'
import { Box, styled } from '@mui/system'
import { Form } from '@yiban/common-mobile'
import { Tabs } from 'antd'
import { Button, List, Popup, Toast, Input } from 'antd-mobile'
import { CloseOutline as IconClose } from 'antd-mobile-icons'
import { getComponent } from './helper'
import { useBoolean } from 'ahooks'
import { RenderSetting } from './advanceSetting'
import { useEditorContext } from '@/editor-kit/context'
import { getAllCheckItem } from '@/pages/designer/context/editorUtils'

const tabs = [
  {
    key: 'BASE',
    label: '基础属性'
  }
]

interface IProps {
  def?: any
  onClose?: () => void
  values?: any
  settings?: any
  onFinish?: (values: any) => void
  currentIndex?: number
}
const StyledForm = styled(Form)<any>(({ theme }) => ({
  '& .adm-list-body,& .adm-list-item-content': {
    borderTop: 'none',
    borderBottom: 'none'
  },
  '& .adm-list.adm-list': {
    '--padding-left': 0
  },
  '& .adm-list-item-content-main': {
    borderTop: 'none',
    padding: '8px 0'
  }
}))

const StyledTabs = styled(Tabs)({
  height: '100%',
  '& .ant-tabs-nav': {
    padding: '0 8px',
    height: '40px',
    marginBottom: 0
  },
  '& .ant-tabs-content-holder': {
    padding: '0 8px',
    height: 'calc(100% - 40px)',
    overflow: 'auto'
  }
})

export default React.forwardRef<HTMLDivElement, IProps>(
  ({ def, onClose, values, settings, onFinish, currentIndex }, ref) => {
    const [{ componentTree }] = useEditorContext()
    const [checkSubject, setCheckSubject] = React.useState<any[]>([])
    const [form] = Form.useForm()
    const formRef = useRef(null)
    const [renderSettings, setRenderSettings] = React.useState<any>(void 0)
    const getDefComponentProps = (props: any = {}) => {
      const { value, ...other } = props
      return other
    }
    const properties: any = React.useMemo(() => {
      return {
        ...def?.properties
      }
    }, [def?.properties])
    const handleSubmit = React.useCallback(() => {
      form.submit()
    }, [form])
    const handleFinish = React.useCallback(
      (values) => {
        // console.log(formRef?.current)
        console.log('==config:', values)
        if (typeof onFinish === 'function') {
          console.log('===finish:', { props: values, settings: { render: renderSettings } })
          onFinish({ props: values, settings: { render: renderSettings } })
        }
      },
      [onFinish, renderSettings]
    )
    const handleValuesChange = React.useCallback(
      (changedValues, allValues) => {
        console.log(changedValues)
        if (typeof onFinish === 'function') {
          onFinish({ props: changedValues })
        }
      },
      [onFinish]
    )
    const initValue: any = React.useMemo(() => {
      return {
        initialValue: '',
        ...Object.keys(properties).reduce(
          (p: any, c: string) => (properties[c]?.props?.value ? { ...p, [c]: properties[c]?.props?.value } : { ...p }),
          {}
        ),
        ...values
      }
    }, [properties, values])

    const checkItems = getAllCheckItem(componentTree, currentIndex || 0)
    const handleSetRender = React.useCallback(() => {
      const checkItems = getAllCheckItem(componentTree, currentIndex || 0)
      if (checkItems.length) {
        console.log('====check subject:', checkItems)
        setCheckSubject(checkItems)
      }
    }, [componentTree, currentIndex])
    const handleRenderSettingOk = (v: any) => {
      setRenderSettings(v)
      handleSubmit()
      Toast.show({
        content: '保存成功'
      })
    }
    React.useEffect(() => {
      setRenderSettings(settings?.render)
    }, [settings?.render])
    React.useEffect(() => {
      form.setFieldsValue(initValue)
    }, [form, initValue])
    //currentIndex 变化时重置表单
    React.useEffect(() => {
      form.resetFields()
      form.setFieldsValue(initValue)
    }, [currentIndex, form])
    React.useEffect(() => {
      console.log('def:', properties)
    }, [properties])

    const onChange = (key: any) => {
      if (key === 'High') {
        handleSetRender()
      }
    }
    return (
      <>
        <Box className='h-full relative' sx={{ px: 0 }} ref={ref}>
          <StyledTabs
            defaultActiveKey='Base'
            items={[
              {
                key: 'Base',
                label: '基础属性',
                children: (
                  <Box sx={{ px: 1 }}>
                    <StyledForm
                      onValuesChange={handleValuesChange}
                      // initialValues={initValue}
                      onFinish={handleFinish}
                      form={form}
                      ref={formRef}
                    >
                      {Object.keys(properties).map(
                        (key) =>
                          React.createElement(getComponent(properties[key]?.component), {
                            key: key,
                            name: key,
                            label: properties[key].label,
                            ...getDefComponentProps(properties[key].props),
                            isPC: true
                          })
                        // <Form.Item name={key} key={key} label={properties[key].label} rules={properties[key].rules}>
                        //   {React.createElement(getComponent(properties[key]?.component), {
                        //     ...getDefComponentProps(properties[key].props),
                        //     noFormItem: true
                        //   })}
                        // </Form.Item>
                      )}
                    </StyledForm>
                  </Box>
                )
              },
              {
                key: 'High',
                label: '高级属性',
                children: (
                  <Box sx={{ px: 1 }}>
                    <StyledForm
                      onValuesChange={handleValuesChange}
                      // initialValues={initValue}
                      onFinish={handleFinish}
                      form={form}
                      ref={formRef}
                    >
                      {checkItems.length ? (
                        <Box>
                          <RenderSetting
                            title={values?.label}
                            dataset={renderSettings}
                            onOk={handleRenderSettingOk}
                            checkSubject={checkSubject}
                            onClose={() => {}}
                          />
                        </Box>
                      ) : (
                        <Box>此题目之前没有选择题，不能进行显示设置</Box>
                      )}

                      {/* <Box className='p-3' sx={{ '&>*': { mb: 2 } }}>
                        <Button onClick={handleSubmit} block shape='rounded' color='primary'>
                          确认
                        </Button>
                        <Button block shape='rounded' onClick={onClose}>
                          取消
                        </Button>
                      </Box> */}
                    </StyledForm>
                  </Box>
                )
              }
            ]}
            onChange={onChange}
          />
        </Box>
      </>
    )
  }
)
