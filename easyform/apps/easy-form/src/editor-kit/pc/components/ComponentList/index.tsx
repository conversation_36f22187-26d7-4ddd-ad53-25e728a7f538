import React from 'react'
import { Box, styled } from '@mui/system'
import { Typography } from '@/components'

export type ComponentListProps = {
  componentList: any[]
}

const ItemWrapper = styled(Box)(({ theme }) => ({
  display: 'flex',
  alignItems: 'center',
  width: '50%',
  flexShrink: 0,
  margin: '12px 0',
  cursor: 'pointer',
  '&>p': {
    transition: 'color .35s ease-in-out'
  },
  '&:hover': {
    p: {
      color: theme.palette.primary.main
    }
  }
}))
const Item = React.forwardRef<any, any>(({ icon, name }) => {
  return (
    <ItemWrapper>
      <img src={icon} style={{ width: 18 }} />
      <Typography sx={{ ml: 0.5, fontSize: 13 }}>{name}</Typography>
    </ItemWrapper>
  )
})

const ListWrapper = styled(Box)(({ theme }) => ({
  display: 'flex',
  flexWrap: 'wrap',
  justifyContent: 'space-between'
}))
export default function ({ componentList = [] }: ComponentListProps) {
  return (
    <Box>
      {componentList.map((l) => (
        <Box key={l.id}>
          <Typography sx={{ fontSize: 14, fontWeight: 500 }}>{l.name}</Typography>
          <ListWrapper>
            {l.widgets.map((c: any) => (
              <Item key={`${c.packageName}/${c.componentName}`} name={c.name} icon={c.icon} />
            ))}
          </ListWrapper>
        </Box>
      ))}
    </Box>
  )
}
