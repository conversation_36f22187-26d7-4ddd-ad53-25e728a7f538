import React from 'react'
import { Box } from '@mui/system'
import { Bad<PERSON>, Collapse, Selector, SelectorOption, SelectorProps, Image, Space } from 'antd-mobile'
import { ImageUploader } from '@/components'
import Typography from '@/components/Typography'
import { Form } from 'antd'
import { THEME, THEMESETTINGS, themetype } from '@/@define/index'
import { useEditorContext, updateEditorSettings, updateSettings, add } from '@/editor-kit/context'
interface TemListProps extends SelectorProps<any> {
  options: Array<{ image?: string } & SelectorOption<any>>
  value?: any
  onChange?: (value: any) => void
}
const defaultTopBanner = (window as any).PUBLIC_URL + '/template/default.jpg'
export const TemList = (props: TemListProps) => {
  const { options, value, onChange, ...others } = props
  const handleOnChange = (newValue: any) => {
    if (newValue) {
      onChange && onChange(newValue)
    } else {
      onChange && onChange(value)
    }
  }
  return (
    <>
      <Box
        sx={{
          '.adm-badge-content': {
            fontSize: 12
          },
          '.adm-badge-wrapper': {
            width: '100%'
          }
        }}
      >
        <Space style={{ padding: '0 16px', width: '100%' }} direction='vertical'>
          {options.map((option, key) => (
            <Badge
              key={key}
              style={{
                '--right': '24px',
                '--top': '8px',
                fontSize: '12px',
                borderRadius: 0,
                display: value == option.value ? undefined : 'none'
              }}
              color='#9ba4b2'
              content='使用中'
            >
              <Box
                sx={{
                  height: 75,
                  position: 'relative',
                  cursor: 'pointer',
                  border: (theme: any) => `2px solid ${value == option.value ? theme.palette.primary.main : 'white'}`,
                  '.adm-image-img': {
                    objectFit: 'cover!important',
                    objectPosition: 'center!important'
                  }
                }}
                onClick={() => {
                  handleOnChange(option.value)
                }}
              >
                <Image
                  style={{ height: '100%' }}
                  src={option.cover || '/easyform/assets/BKGs/DEFAULT_cover.png'}
                ></Image>
                <Typography
                  sx={{
                    color: '#333333',
                    position: 'absolute',
                    fontWeight: '600',
                    top: '50%',
                    left: '50%',
                    fontSize: 20,
                    transform: 'translate(-50%,-50%)',
                    '-webkit-text-stroke': '0.5px #eee'
                  }}
                >
                  {option.label}
                </Typography>
              </Box>
            </Badge>
          ))}
        </Space>
      </Box>
    </>
  )
}
export const MobileTemList = (props: TemListProps) => {
  const { options, value, onChange, ...others } = props
  const handleOnChange = (newValue: any) => {
    if (newValue) {
      onChange && onChange(newValue)
    } else {
      onChange && onChange(value)
    }
  }
  return (
    <>
      <Box
        sx={{
          '.adm-badge-content': {
            fontSize: 16
          },
          '.adm-badge-wrapper': {
            width: '100%'
          }
        }}
      >
        <Space style={{ padding: '0 16px', width: '100%' }} direction='vertical'>
          {options.map((option, key) => (
            <Badge
              key={key}
              style={{
                '--right': '30px',
                '--top': '11px',
                fontSize: '12px',
                borderRadius: 0,
                padding: '2px 0',
                display: value == option.value ? undefined : 'none'
              }}
              color='#9ba4b2'
              content='使用中'
            >
              <Box
                sx={{
                  //height: 64,
                  position: 'relative',
                  cursor: 'pointer',
                  border: (theme: any) => `2px solid ${value == option.value ? theme.palette.primary.main : 'white'}`,
                  '.adm-image-img': {
                    objectFit: 'cover!important',
                    objectPosition: 'right!important',
                    aspectRatio: ' 4 / 1'
                  }
                }}
                onClick={() => {
                  handleOnChange(option.value)
                }}
              >
                <Image
                  style={{ height: '100%' }}
                  src={option.cover || '/easyform/assets/BKGs/DEFAULT_cover.png'}
                ></Image>
                <Typography
                  sx={{
                    color: '#333333',
                    position: 'absolute',
                    fontWeight: '600',
                    top: '50%',
                    fontSize: 26,
                    left: '50%',
                    transform: 'translate(-50%,-50%)',
                    '-webkit-text-stroke': '0.5px #eee'
                  }}
                >
                  {option.label}
                </Typography>
              </Box>
            </Badge>
          ))}
        </Space>
      </Box>
    </>
  )
}
export default () => {
  const [{ pageSettings, settings }, dispach] = useEditorContext()

  const handleTemChange = (value?: themetype) => {
    updateEditorSettings(
      dispach,
      value && value.length
        ? {
            theme: value,
            ...THEMESETTINGS[value]
          }
        : undefined,
      true
    )
  }
  return (
    <>
      <Box
        className='flex items-center'
        sx={{ px: 3, height: '40px', borderBottom: '1px solid rgba(0,0,0,0.5)', borderColor: 'divider' }}
      >
        <Typography sx={{ fontSize: 13, color: '#999' }}>主题</Typography>
      </Box>
      <Box
        sx={{
          p: 1,
          overflow: 'auto',
          height: 'calc(100% - 40px)',
          ' ::-webkit-scrollbar': {
            width: 5,
            backgroundColor: 'transparent'
          },
          '::-webkit-scrollbar-thumb': {
            backgroundColor: '#c0c0c0',
            width: 5,
            borderRadius: 2
          }
        }}
      >
        {/* <Collapse.Panel
            key='1'
            title={<Typography sx={{ fontWeight: 'bold', fontSize: 'body1.fontSize' }}>背景图片</Typography>}
          >
            <Form layout='vertical'>
              <Form.Item
                label={
                  <Typography
                    sx={{
                      color: (theme: any) => theme.typography.subtitle2.color,
                      fontSize: (theme: any) => theme.typography.subtitle2.fontSize
                    }}
                  >
                    自定义背景
                  </Typography>
                }
              >
                <ImageUploader
                  value={editorSettinngs?.background?.src || []}
                  style={{ borderTop: 'none' }}
                  maxCount={1}
                  onChange={(file: any) => {
                    handleChangeBKG(file)
                  }}
                ></ImageUploader>
              </Form.Item>
            </Form>
          </Collapse.Panel> */}

        <TemList onChange={handleTemChange} value={pageSettings?.theme || 'DEFAULT'} options={THEME}></TemList>
      </Box>
    </>
  )
}
