import React from 'react'
import { Box } from '@mui/system'
import ComponentList from './ComponentList'
import { Segmented } from 'antd'
import { EditorProvider, useEditorContext, updateSettings, init, setComponents } from '@/editor-kit/context'
import { Typography } from '@/components'
import { Component, Tree, Template, Setting } from '@/assets/icon'

interface IPanelProps {
  onClose?: () => void
  onAddComponent?: (d: any) => void
}
const PanelComponent: React.FC<IPanelProps> = ({ onClose, onAddComponent }) => {
  const [{ componentList, components, currentComponent }, editorDispatch] = useEditorContext()

  return (
    <Box>
      <Box sx={{ p: 1, px: 3 }}>
        <ComponentList onAdd={onAddComponent} componentList={componentList?.list} />
      </Box>
    </Box>
  )
}

export default PanelComponent
