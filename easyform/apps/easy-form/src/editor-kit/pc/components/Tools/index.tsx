import React, { useState } from 'react'
import { Box, styled } from '@mui/system'
import { Tooltip, Button, Segmented } from 'antd'
import { TabPanel } from '@/components'
import ToolPanel from './Toolpanel'
import { Component, Tree, Template, Setting } from '@/assets/icon'
import { SkinFilled } from '@ant-design/icons'

export const ToolType = {
  TREE: 'TREE',
  COMPONENT: 'COMPONENT',
  STORE: 'STORE',
  ACTION: 'ACTION',
  COMB: 'COMB',
  TEMPLATE: 'TEMPLATE',
  TOOL: 'TOOL',
  SETTING: 'SETTING'
}

interface IToolData {
  title: string
  icon: any
  id: string
  iconProps?: any
}
const DefaultTools: IToolData[] = [
  {
    id: ToolType.COMPONENT,
    icon: Component,
    title: '组件库'
  },
  {
    id: ToolType.TREE,
    icon: Tree,
    title: '大纲'
  },
  {
    id: ToolType.SETTING,
    icon: Setting,
    title: '页面设置'
  }
]

const StyledButton = styled(But<PERSON>, { shouldForwardProp: (prop) => prop !== 'active' })<any>(({ theme, active }) => ({
  color: active ? theme.palette.primary.main : void 0,
  marginBottom: 8
}))

export default function Tools({ onAddComponent, onTabChange, isTheme }: any) {
  const [currentPanel, setCurrentPanel] = React.useState<string>(ToolType.COMPONENT)

  const handleClick = React.useCallback(
    (e) => {
      const which = e.currentTarget.getAttribute('data-id')
      onTabChange && onTabChange(which)
      if (currentPanel === which) {
        setCurrentPanel('')
      } else {
        setCurrentPanel(which)
      }
    },
    [onTabChange, currentPanel]
  )
  return (
    <Box className='h-full flex ' sx={{ flexDirection: 'column' }}>
      {/* <Box
        className='flex flex-col items-center shrink-0'
        sx={{ width: 40, pt: 1, borderRight: 1, borderColor: 'divider' }}
      >
        {DefaultTools.map(({ id, title, icon: Icon, iconProps }) => (
          <Tooltip key={id} placement='right' title={title}>
            <StyledButton
              active={currentPanel === id}
              type='text'
              data-id={id}
              onClick={handleClick}
              icon={<Icon width={16} height={16} />}
            />
            <Icon data-id={id} onClick={handleClick} style={{ cursor: 'pointer', marginBottom: 24 }} {...iconProps} />
          </Tooltip>
        ))}
      </Box> */}
      <Box
        className='flex items-center'
        sx={{
          px: 1,
          py: isTheme ? 0 : 2,
          //borderBottom: '1px solid',
          justifyContent: 'center',
          borderColor: 'divider',
          '.ezf-segmented': {
            //width: '100%',
            '.ezf-segmented-group': {
              justifyContent: 'center'
            }
          }
        }}
      >
        {!isTheme ? (
          <Segmented
            onChange={(value: any) => {
              onTabChange(value)
              setCurrentPanel(value)
            }}
            value={currentPanel}
            options={[
              {
                label: (
                  <Box sx={{ display: 'flex', alignItems: 'center' }}>
                    <Box sx={{ mb: -0.5, pr: 1 }}>
                      <Component width={16} height={16}></Component>
                    </Box>
                    <Box>组件</Box>
                  </Box>
                ),
                value: 'COMPONENT'
              },
              {
                label: (
                  <Box sx={{ display: 'flex', alignItems: 'center' }}>
                    <Box sx={{ mb: -0.5, pr: 1 }}>
                      <Tree width={16} height={16}></Tree>
                    </Box>
                    <Box>大纲</Box>
                  </Box>
                ),
                value: 'TREE'
              }
            ]}
          />
        ) : (
          ''
        )}
      </Box>
      <Box className='h-full flex-grow' sx={{ height: 'calc(100% - 64px)!important' }}>
        <ToolPanel onAddComponent={onAddComponent} value={isTheme ? ToolType.SETTING : currentPanel} />
      </Box>
    </Box>
  )
}
