import React, { useEffect, useRef } from 'react'
import { Box } from '@mui/system'
import { EditorProvider, useEditorContext, updateSettings, init, setComponents } from '@/editor-kit/context'
import { Typography } from '@/components'
import Tree from './Tree'
import { useOverlayScrollbars } from '@yiban/system'
interface IPanelProps {
  onClose?: () => void
  onAddComponent?: (d: any) => void
}
const PanelComponent: React.FC<IPanelProps> = ({ onClose, onAddComponent }) => {
  //   const [{ componentList, components, currentComponent }, editorDispatch] = useEditorContext()
  const [initialize, instance] = useOverlayScrollbars({ options: { scrollbars: { autoHide: 'scroll' } } })

  const toolPanel = useRef(null)
  useEffect(() => {
    toolPanel.current && initialize(toolPanel.current)
  }, [initialize])
  return (
    <Box sx={{ height: '100%', px: 2 }}>
      <Box
        className='flex items-center'
        sx={{ px: 1, height: '40px', borderBottom: '1px solid', borderColor: 'divider' }}
      >
        <Typography sx={{ fontSize: 13, color: '#999' }}>大纲</Typography>
      </Box>
      <Box sx={{ p: 1, height: 'calc(100% - 40px)' }} ref={toolPanel}>
        <Tree />
      </Box>
    </Box>
  )
}

export default PanelComponent
