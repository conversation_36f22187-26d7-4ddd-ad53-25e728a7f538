import React from 'react'
import { Box } from '@mui/system'
import { useBoolean } from 'ahooks'

import TabPanel from '@/components/TabPanel'
import { ToolType } from './index'
import PanelComponent from './Panels/PanelComponent'
import PanelTree from './Panels/PanelTree'
import PanelSetting from './Panels/PanelSetting'
export interface IToolPanelProps {
  value: number | string
  onClose?: () => void
  onAddComponent?: (componentData: any) => void
}

interface IWrapperProps {
  isCover?: boolean
  width?: number
  left?: number
  open?: boolean
  children?: React.ReactNode
  isScroll?: boolean
}
const Wrapper = (props: IWrapperProps) => {
  const { width = 264, children, left = 40, isCover = false, isScroll = true } = props
  return (
    <Box
      sx={{
        // bgcolor: 'background.default',
        width: width,
        boxShadow: 1,
        height: '100%',
        borderRight: 1,
        borderColor: 'divider',
        overflow: isScroll ? 'auto' : 'hidden',
        position: isCover ? 'absolute' : 'static',
        left: isCover ? left : undefined,
        zIndex: 1000
      }}
    >
      {children}
    </Box>
  )
}

export default function ToolPanel(props: IToolPanelProps) {
  const { value, onClose, onAddComponent } = props
  const [isOpenDataSetting, { setTrue: openDataSetting, setFalse: closeDataSetting }] = useBoolean(false)
  const [currentItem, setCurrentItem] = React.useState<any>()
  const handleOpenDataSetting = React.useCallback(
    (d: string | Record<string, any>) => {
      openDataSetting()
      setCurrentItem(d)
    },
    [openDataSetting]
  )

  return (
    <>
      <TabPanel position='static' showValue={value} value={ToolType.COMPONENT}>
        <Wrapper>
          <PanelComponent onClose={onClose} onAddComponent={onAddComponent} />
        </Wrapper>
      </TabPanel>
      <TabPanel position='static' showValue={value} value={ToolType.TREE}>
        <Wrapper>
          <PanelTree />
        </Wrapper>
      </TabPanel>
      <TabPanel position='static' showValue={value} value={ToolType.SETTING}>
        <Wrapper>
          <PanelSetting></PanelSetting>
        </Wrapper>
      </TabPanel>
    </>
  )
}
