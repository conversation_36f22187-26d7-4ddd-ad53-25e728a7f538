import React from 'react'
import { Box, styled } from '@mui/system'
import { Typography } from '@/components'
import { useEditorContext } from '@/editor-kit/context'
import { useLocation } from 'react-router-dom'

export type ComponentListProps = {
  componentList: any[]
  onAdd?: (d: any) => void
}

const ItemWrapper = styled(Box)(({ theme }) => ({
  display: 'flex',
  alignItems: 'center',
  width: '50%',
  flexShrink: 0,
  margin: '12px 0',
  cursor: 'pointer',
  userSelect: 'none',
  '&>p': {
    transition: 'color .35s ease-in-out'
  },
  '&:hover': {
    p: {
      color: theme.palette.primary.main
    }
  }
}))
const Item = React.forwardRef<any, any>(({ icon, name, ...other }) => {
  return (
    <ItemWrapper {...other}>
      <img src={icon} style={{ width: 18 }} />
      <Typography sx={{ ml: 0.5, fontSize: 13 }}>{name}</Typography>
    </ItemWrapper>
  )
})

const ListWrapper = styled(Box)(({ theme }) => ({
  display: 'flex',
  flexWrap: 'wrap',
  justifyContent: 'space-between'
}))
export default function ({ componentList = [], onAdd }: ComponentListProps) {
  const handleAdd = (d: any) => {
    if (typeof onAdd === 'function') {
      onAdd(d)
    }
  }
  const location: any = useLocation()
  const [{ components, componentTree, settings }, editorDispatch] = useEditorContext()
  //通知公告不显示表单组件
  const showTypes = settings?.type == 'NOTICE' || location?.state?.type == 'NOTICE' ? ['base'] : ['base', 'layout']
  console.log('type', settings, location)
  return (
    <Box>
      {componentList.map((l) =>
        showTypes.includes(l.id) ? (
          <Box key={l.id} sx={{ pb: 1 }}>
            <Typography sx={{ fontSize: 14, fontWeight: 600 }}>{l.name}</Typography>
            <ListWrapper>
              {l.widgets.map((c: any) => (
                <Item
                  onClick={() => handleAdd(c)}
                  key={`${c.packageName}/${c.componentName}`}
                  name={c.name}
                  icon={c.icon}
                />
              ))}
            </ListWrapper>
          </Box>
        ) : (
          ''
        )
      )}
    </Box>
  )
}
