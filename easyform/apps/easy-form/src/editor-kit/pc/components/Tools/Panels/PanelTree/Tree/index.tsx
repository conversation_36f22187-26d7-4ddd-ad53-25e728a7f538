import React from 'react'
import { Box, styled } from '@mui/system'
import { useDrag, useDrop } from 'react-dnd'
import { Typography } from '@/components'
import { useEditorContext, selectComponent, move } from '@/editor-kit/context'
import { DragOutlined } from '@ant-design/icons'

type ItemProps = {
  _id: string
  title: string
  index: number
  selected?: boolean
  componentName?: string
  onClick?: () => void
  move: (originIndex: number, targetIndex: number) => void
}
const ItemRoot = styled(Box, { shouldForwardProp: (prop) => prop !== 'selected' })<Pick<ItemProps, 'selected'>>(
  ({ theme, selected }) => ({
    padding: 8,
    border: '1px solid',
    borderColor: selected ? theme.palette.primary.main : theme.palette.divider,
    color: selected ? theme.palette.primary.main : theme.palette.text.sencondary,
    fontSize: 13,
    transition: 'all .35s ease-in-out',
    display: 'flex',
    alignItems: 'center',
    marginBottom: 12,
    borderRadius: 4,
    cursor: 'pointer',
    '&:hover': {
      backgroundColor: '#f8f8f8'
    }
  })
)
const Tag = styled('span')(({ theme }) => ({
  display: 'flex',
  fontSize: 12,
  padding: '0px 4px',
  color: theme.palette.secondary.main,
  border: `1px solid ${theme.palette.secondary.main}`,
  borderRadius: 4,
  transform: 'scale(0.8)',
  pointerEvents: 'none',
  flexShrink: 0
}))
const Item = ({ title, index, _id, selected, componentName, onClick, move }: ItemProps) => {
  const ref = React.useRef<HTMLDivElement>(null)
  const [{ isDragging }, drag] = useDrag(
    () => ({
      type: 'Component',
      item: { index, _id, title },
      collect: (monitor) => ({
        isDragging: monitor.isDragging()
      })
    }),
    [_id, index]
  )
  const [, drop] = useDrop(
    () => ({
      accept: 'Component',
      hover(item: any, monitor: any) {
        if (!ref.current) {
          return
        }
        if (item.index === index) {
          return
        }
        const dragIndex = item.index
        const hoverIndex = index
        const hoverBoundingRect = ref.current?.getBoundingClientRect()
        const hoverMiddleY = (hoverBoundingRect.bottom - hoverBoundingRect.top) / 2
        const clientOffset = monitor.getClientOffset()
        const hoverClientY = clientOffset.y - hoverBoundingRect.top

        if (dragIndex < hoverIndex && hoverClientY < hoverMiddleY) {
          return
        }
        if (dragIndex > hoverIndex && hoverClientY > hoverMiddleY) {
          return
        }
        move(dragIndex, hoverIndex)
        item.index = hoverIndex
      }
    }),
    [move, index]
  )
  drag(drop(ref))
  return (
    <ItemRoot ref={ref} sx={{ opacity: isDragging ? 0 : 1 }} selected={selected} onClick={onClick}>
      <DragOutlined />
      <Typography sx={{ fontSize: 13, ml: 0.5 }}>{`${index + 1}.${title}`}</Typography>
      <Box sx={{ flexGrow: 1 }} />
      {componentName && <Tag>{componentName}</Tag>}
    </ItemRoot>
  )
}

export default function () {
  const [{ components, currentComponent, componentTree }, editorDispatch] = useEditorContext()
  const handleSelect = React.useCallback(
    (_id: string) => {
      selectComponent(editorDispatch, _id)
    },
    [editorDispatch]
  )
  const handleMove = React.useCallback(
    (originIndex: number, targetIndex: number) => {
      move(editorDispatch, originIndex, targetIndex)
    },
    [editorDispatch]
  )

  return (
    <Box>
      {componentTree.map((c: any, i: number) => (
        <Item
          _id={c._id}
          selected={currentComponent?._id === c._id}
          key={c._id}
          title={c.props?.label}
          componentName={components[c.componentName].name}
          index={i}
          onClick={() => handleSelect(c._id)}
          move={handleMove}
        />
      ))}
    </Box>
  )
}
