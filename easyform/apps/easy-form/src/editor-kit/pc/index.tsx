import { HTML5Backend } from 'react-dnd-html5-backend'
import { DndProvider } from 'react-dnd'
import { EditorProvider } from '@/editor-kit/context'
import EditorView, { EditorViewProps } from './EditorView'

export default function (props: EditorViewProps) {
  console.log('==props:', props)
  return (
    <DndProvider backend={HTML5Backend}>
      <EditorProvider>
        <EditorView {...props} />
      </EditorProvider>
    </DndProvider>
  )
}
