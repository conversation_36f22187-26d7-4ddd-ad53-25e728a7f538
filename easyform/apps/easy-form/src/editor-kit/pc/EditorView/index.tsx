import React, { useContext, useState } from 'react'
import { Box, styled } from '@mui/system'
import { useBoolean } from 'ahooks'
import { Drawer, Segmented } from 'antd'
import { Editor, Configuration, Tools, FormConfiguration, Header, Preview } from '../components'
import {
  EditorProvider,
  useEditorContext,
  updateSettings,
  init,
  updateEditorSettings,
  setComponents
} from '@/editor-kit/context'
import { getDependencies } from '@/helper'
import Typography from '@/components/Typography'
import { DesktopOutlined, EditOutlined, MobileOutlined, SkinOutlined } from '@ant-design/icons'
import Render from '@/editor-kit/common/Render'
import { UpdateContext } from '@/pages/pc/Designer/context/UpdateContext'

const Root = styled(Box)({
  height: '100%'
})
const EditorRoot = styled(Box)({
  height: '100%',
  display: 'flex'
  // display: 'grid',
  // gridTemplateColumns: '240px 1fr 300px'
})
const Wrapper = styled(Box)({
  height: '100%',
  overflow: 'auto',
  flexShrink: 0
})

export type EditorViewProps = {
  pageDefine?: any
  settings?: any
  disabled?: boolean
  isTheme?: boolean //是否是主题编辑器
  onSave?: (pageDefine: any, pageSetings?: any) => void
}

export default function ({
  pageDefine: propPageDefine,
  disabled,
  isTheme,
  settings: propSettings,
  onSave
}: EditorViewProps) {
  const [isSetTheme, setIsSetTheme] = useState(isTheme)
  const [openPreview, { toggle: togglePreview }] = useBoolean(false)
  const canvasRef = React.useRef<any>(null)
  const [previewType, setPreviewType] = React.useState(1)
  const groupContentRef2 = React.useRef(null)
  const [curTab, setCurTab] = React.useState()
  const updater = useContext<any>(UpdateContext)
  const [{ define, id, status }, dispach] = updater
  const [{ components, componentTree, settings, pageSettings }, editorDispatch] = useEditorContext()
  React.useEffect(() => {
    fetch('/easyform/componentList.json').then((res) =>
      res.json().then((comps: any) => {
        console.log(comps)
        setComponents(editorDispatch, comps)
      })
    )
  }, [editorDispatch])
  const handleAddComponent = (componentData: any) => {
    canvasRef.current.addComponent(componentData)
  }
  const handleSave = () => {
    if (typeof onSave === 'function') {
      onSave(
        {
          componentTree: componentTree,
          remoteDependencies: getDependencies(componentTree, components)
        },
        {
          pageSettings: pageSettings
        }
      )
    }
  }
  React.useEffect(() => {
    updateEditorSettings(editorDispatch, {
      ...propSettings?.pageSettings,
      submitText: propSettings?.templateSettings?.btnText
    })
  }, [editorDispatch, disabled, propSettings?.pageSettings, propSettings?.templateSettings?.btnText])
  React.useEffect(() => {
    console.log('====init:', propPageDefine)
    init(editorDispatch, { ...propPageDefine })
  }, [editorDispatch, propPageDefine])
  //原来的代码当保存settings时会触发页面重新渲染，这里把pages和settings分开
  React.useEffect(() => {
    console.log('====updateSettings:', propSettings)
    updateSettings(editorDispatch, { ...propSettings })
  }, [propSettings, editorDispatch])
  return (
    <>
      <Root>
        <EditorRoot sx={{ border: '1px solid', borderColor: 'divider' }}>
          <Wrapper>
            <Tools
              onAddComponent={handleAddComponent}
              isTheme={isSetTheme}
              onTabChange={(value: any) => {
                setCurTab(value)
              }}
            />
            {/* <ComponentList componentList={componentList?.list} /> */}
          </Wrapper>
          <Wrapper
            sx={{
              borderLeft: '1px solid',
              borderRight: '1px solid',
              borderColor: 'divider',
              width: 'min-content',
              flexGrow: 1
            }}
          >
            <Box sx={{ overflow: 'inherit', height: 'calc(100% - 40px)' }}>
              <Box className={'h-full'} ref={groupContentRef2}>
                {/* <Box
                  sx={{
                    position: 'sticky',
                    top: 0,
                    background: 'white',
                    zIndex: 2,
                    height: 50,
                    p: 0.5,
                    //borderBottom: '1px solid rgb(238, 238, 238)',
                    display: 'flex',
                    alignItems: 'center',
                    justifyContent: 'center'
                  }}
                >
                  <Segmented
                    onChange={(value) => {
                      setPreviewType(value as any)
                    }}
                    value={previewType}
                    options={[
                      {
                        value: 1,
                        label: (
                          <Box sx={{ display: 'flex', alignItems: 'center' }}>
                            <MobileOutlined />
                            <Typography sx={{ pl: 1 }}>电脑预览</Typography>
                          </Box>
                        )
                      },
                      {
                        value: 2,
                        label: (
                          <Box sx={{ display: 'flex', alignItems: 'center' }}>
                            <DesktopOutlined />
                            <Typography sx={{ pl: 1 }}>手机预览</Typography>
                          </Box>
                        )
                      }
                    ]}
                  />
                </Box> */}
                {/* <Box sx={{ height: 'calc(100% - 50px)', pb: 1 }}> */}
                <Box sx={{ height: 'calc(100% - 0px)', pb: 1 }}>
                  {/* <Box
                    className='h-full'
                    sx={{ bgcolor: '#FFF', width: previewType === 1 ? '100%' : 400, pt: 3, margin: '0 auto' }}
                  >
                    <Editor ref={canvasRef} isPC={previewType === 1} />
                  </Box> */}
                  <Box className='h-full' sx={{ bgcolor: '#FFF', width: 400, pt: 3, margin: '0 auto' }}>
                    <Editor isTheme={isSetTheme} ref={canvasRef} isPC={false} />
                  </Box>
                </Box>
              </Box>
            </Box>
            <Header onSave={handleSave} onPreview={togglePreview} />
          </Wrapper>
          <Wrapper sx={{ width: 310, p: 1 }}>
            {/* <Box
              sx={{
                width: '100%',
                display: 'flex',
                justifyContent: 'center',
                pt: 2,
                '.ezf-segmented': {
                  width: '100%',
                  '.ezf-segmented-group': {
                    justifyContent: 'center'
                  }
                }
              }}
            >
              <Segmented
                onChange={(value: any) => {
                  setCurTab(value)
                }}
                options={[
                  {
                    value: 'SETTING',
                    label: (
                      <Box sx={{ display: 'flex' }}>
                        <EditOutlined />
                        <Typography>页面设计</Typography>
                      </Box>
                    )
                  },
                  {
                    value: 'THEME',
                    icon: (
                      <Box sx={{ display: 'flex' }}>
                        <SkinOutlined />
                        <Typography>页面外观</Typography>
                      </Box>
                    )
                  }
                ]}
              />
            </Box> */}
            {curTab !== 'SETTING' && !isSetTheme ? <Configuration /> : <FormConfiguration></FormConfiguration>}
          </Wrapper>
        </EditorRoot>
      </Root>
      <Preview
        define={define}
        settings={settings}
        pageSettings={pageSettings}
        pageDefine={{ componentTree }}
        open={openPreview}
        onClose={togglePreview}
      />
    </>
  )
}
