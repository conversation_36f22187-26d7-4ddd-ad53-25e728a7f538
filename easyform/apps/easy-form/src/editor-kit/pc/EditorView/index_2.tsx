import React from 'react'
import { Box, styled } from '@mui/system'
import { ComponentList, Canvas, Configuration } from '../components'
import { EditorProvider, useEditorContext, updateSettings, init, setComponents } from '@/editor-kit/context'
const Root = styled(Box)({
  height: '100%',
  background: 'transparent',
  display: 'grid',
  gap: 16,
  gridTemplateColumns: '240px 1fr 300px'
})
const Wrapper = styled(Box)({
  borderRadius: 12,
  background: '#FFF',
  height: '100%',
  overflow: 'auto',
  padding: 12
})

export default function () {
  const [{ componentTree: contextComponentTree, componentList, components }, editorDispatch] = useEditorContext()
  React.useEffect(() => {
    fetch('/easyform/componentList.json').then((res) =>
      res.json().then((comps: any) => {
        // console.log(comps)
        setComponents(editorDispatch, comps)
      })
    )
  }, [editorDispatch])
  return (
    <Root>
      <Wrapper>
        <ComponentList componentList={componentList?.list} />
      </Wrapper>
      <Wrapper>
        <Canvas />
      </Wrapper>
      <Wrapper>{/* <Configuration  /> */}</Wrapper>
    </Root>
  )
}
