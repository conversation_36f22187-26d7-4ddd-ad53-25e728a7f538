import React from 'react'
import { Form, Button, Toast } from 'antd-mobile'
import { useComponents, PageRender } from '@/helper'
import PostRender from '@/helper/PageRender/PostRender'
import { PageLoading } from '@/components/Loading'
import StandardTemeplate from '@/@template/FormTemplate'
import PostTemeplate from '@/@template/post'
import { useAppContext } from '@/context/AppContext'
import PageWrapper from '@/@template/PageWrapper'
import StyledForm, { PostForm } from '@/@template/StyledForm'
import { THEMESETTINGS } from '@/@define'
import { processFormDefine } from '@/pages/runtime'
export type RenderProps = {
  settings?: any
  pageDefine?: any
  define?: any
  isPC?: boolean
  hideSubmit?: boolean
  pageSettings?: any
}

export default function Render({
  settings = {},
  define = {},
  pageDefine = {},
  isPC,
  hideSubmit,
  pageSettings
}: RenderProps) {
  const [
    {
      user: { name, id, extra: userData = {} }
    }
  ] = useAppContext()
  const [form] = Form.useForm()
  const templateRef = React.useRef<any>(null)
  const { loadComplete, components } = useComponents(pageDefine?.componentTree, pageDefine?.remoteDependencies)
  const handleFinish = React.useCallback((values) => {
    // Toast.show({
    //   content: '预览状态提交无效'
    // })
  }, [])
  const BaseData = React.useMemo(() => {
    return {
      userName: userData.name || '方楠',
      phone: userData.mobile || '18856856868',
      org: Array.isArray(userData.orgs) ? userData.orgs[0]?.name : void 0,
      speciality: userData.speciality?.name,
      class: userData.class?.name,
      code: userData.code
    }
  }, [userData.class, userData.code, userData.mobile, userData.name, userData.orgs, userData.speciality])
  const handleSubmit = React.useCallback(() => {
    form.submit()
  }, [form])
  const [subscribeFields, setSubscribeFields] = React.useState([])
  // const [_define, setDefine] = React.useState<any>({})
  // React.useEffect(() => {
  //   if (define) {
  //     const [_define, _subscribeFields] = processFormDefine(JSON.parse(JSON.stringify(define)))
  //     setSubscribeFields(_subscribeFields)
  //     setDefine(_define)
  //   }
  // }, [define])
  return (
    <>
      <PageWrapper settings={settings} isPC={isPC}>
        <StandardTemeplate
          isPC={isPC}
          imageUrlPrefix={(window as any).APP_CONFIG.server.fileUrlPrefix}
          ref={templateRef}
          onSubmit={hideSubmit ? void 0 : handleSubmit}
          btnText={settings.templateSettings?.btnText}
          settings={settings}
          pageSettings={pageSettings || settings?.pageSettings || THEMESETTINGS.DEFAULT}
        >
          <React.Suspense fallback={<PageLoading />}>
            {components ? (
              <StyledForm isPC={isPC} form={form} onFinish={handleFinish}>
                <Form.Subscribe to={subscribeFields}>
                  {(values) => (
                    <PageRender
                      isPC={isPC}
                      values={values}
                      baseData={BaseData}
                      componentTree={pageDefine.componentTree || []}
                      components={components}
                    />
                  )}
                </Form.Subscribe>
              </StyledForm>
            ) : null}
          </React.Suspense>
        </StandardTemeplate>
      </PageWrapper>
    </>
  )
}
