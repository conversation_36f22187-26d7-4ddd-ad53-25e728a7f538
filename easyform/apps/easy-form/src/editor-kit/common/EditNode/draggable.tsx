import React from 'react'
import { styled } from '@mui/system'
import {
  SetOutline as IconEdit,
  DeleteOutline as IconDelete,
  LocationFill,
  UpCircleOutline as IconUp,
  DownCircleOutline as IconDown,
  PicturesOutline as IconCopy
} from 'antd-mobile-icons'
import { useDrag, useDrop } from 'react-dnd'
import { useEditorContext, selectComponent, updatePos } from '@/editor-kit/context'

const ToolBarRoot = styled('div')({
  height: 0,
  position: 'relative',
  overflow: 'hidden',
  transition: 'height .35s ease-in-out'
  // '&::before': {
  //   content: '" "',
  //   display: 'block',
  //   position: 'absolute',
  //   top: 0,
  //   left: 0,
  //   right: 0,
  //   height: 0,
  //   borderTop: '1px solid #eee'
  // }
})

const Root = styled('div', { shouldForwardProp: (prop) => prop !== 'selected' })<any>(
  ({ theme, selected, pos, isDragging }: any) => ({
    transition: 'height .35s ease-in-out',
    height: 'auto',
    // borderRadius: '6px',
    //backgroundColor: '#FFF',
    // border: `2px dashed ${selected ? theme.palette.divider : 'transparent'}`,
    [ToolBarRoot as any]: {
      height: selected ? 36 : 0
    },
    opacity: isDragging ? 1 : 1,
    width: pos?.active ? 'fit-content' : undefined,
    position: pos ? 'absolute' : 'relative',
    left: pos?.left ?? undefined,
    top: pos?.top ?? undefined,
    zIndex: pos ? (selected ? 9 : 1) : undefined, //选中的元素层级最高，防止堆叠无法选中
    p: 1
    // '.adm-list-item': {
    //   backgroundColor: 'transparent'
    // }
  })
)

const Content = styled('div', { shouldForwardProp: (prop) => prop !== 'selected' })<any>(
  ({ theme, selected, active }: any) => ({
    padding: '0px 12px',
    '& > *': {
      pointerEvents: 'none'
    },
    transition: 'all .35s ease-in-out',
    height: 'auto',
    width: active ? 'fit-content' : undefined,
    borderRadius: '6px',
    cursor: active && selected ? 'move' : undefined,
    // backgroundColor: '#FFF',
    border: `2px dashed ${selected ? (active ? '#1ea0fa' : theme.palette.divider) : 'transparent'}`
  })
)
// const Content = styled('div')({
//   padding: '8px 8px',
//   '& > *': {
//     pointerEvents: 'none'
//   }
// })

const ToolBtnRoot = styled('div')<any>(({ theme, active }: any) => ({
  fontSize: 12,
  cursor: 'pointer',
  display: 'flex',
  marginLeft: 12,
  color: !active ? theme.palette.primary.main : '#1ea0fa',
  padding: '2px 4px',
  borderRadius: '4px',
  border: `1px solid ${active ? '#1ea0fa' : theme.palette.primary.main}`,
  alignItems: 'center',
  transition: 'background-color 0.35s ease-in-out'
}))

interface IEditNodeProps {
  selected?: boolean
  component?: any
  onClick?: () => void
  allowUp?: boolean
  allowDown?: boolean
  componentName?: string
  allowEdit?: boolean
  canDrag?: boolean //是否可以拖拽定位
  [key: string]: any
}

const tools = [
  {
    title: '删除',
    icon: <IconDelete />,
    cmd: 'DELETE'
  },
  {
    title: '复制',
    icon: <IconCopy />,
    cmd: 'COPY'
  }
]

const Tag = styled('span')(({ theme }) => ({
  display: 'flex',
  fontSize: 12,
  padding: '0px 4px',
  color: theme.palette.secondary.main,
  border: `1px solid ${theme.palette.secondary.main}`,
  borderRadius: 4,
  position: 'absolute',
  right: -2,
  top: -2,
  transform: 'scale(0.8)',
  pointerEvents: 'none'
}))

export default React.forwardRef<HTMLDivElement, IEditNodeProps>(
  (
    {
      onClick,
      selected,
      component,
      onCommand,
      _index,
      allowUp,
      allowDown,
      allowPos = true,
      allowEdit = true,
      componentName,
      canDrag,
      pos,
      ...other
    },
    ref
  ) => {
    const { props } = component
    const { position } = props
    const [{ components, currentComponent, componentTree }, editorDispatch] = useEditorContext()
    const editRef = React.useRef(null)
    const toolList = React.useMemo(() => {
      return (
        allowEdit
          ? [
              {
                title: '编辑',
                icon: <IconEdit />,
                cmd: 'EDIT'
              }
            ]
          : []
      )
        .concat(tools)
        .concat(
          allowPos
            ? [
                {
                  title: '定位',
                  icon: <LocationFill />,
                  cmd: 'POS'
                }
              ]
            : []
        )
        .concat(
          pos?.active
            ? []
            : [
                {
                  title: '下移',
                  icon: <IconDown />,
                  cmd: 'DOWN'
                },
                {
                  title: '上移',
                  icon: <IconUp />,
                  cmd: 'UP'
                }
              ].filter(({ cmd }) => (cmd === 'UP' && allowUp) || (cmd === 'DOWN' && allowDown))
        )
    }, [allowEdit, allowPos, allowUp, allowDown, pos?.active])
    const ToolBtn = React.useCallback(
      ({ icon, title, cmd, onCommand, active }: any) => {
        const handleClick = (e: any) => {
          e.stopPropagation()
          if (typeof onCommand === 'function') {
            onCommand(
              cmd,
              editRef.current
                ? { top: (editRef.current as any).offsetTop, left: (editRef.current as any).offsetLeft }
                : undefined
            )
          }
        }
        return (
          <ToolBtnRoot active={active} onClick={handleClick}>
            {icon}
            <span>{title}</span>
          </ToolBtnRoot>
        )
      },
      [editRef]
    )
    const [{ isDragging }, drag, dragPreview] = useDrag(() => {
      return {
        type: 'Component',
        item: () => ({
          //item定义为对象时会在初始化时执行一次，在这里会导致定位bug，所以改用函数（函数只会在拖拽时执行）
          _index,
          componentName,
          pos: {
            ...pos,
            top: editRef.current && (editRef.current as any).offsetTop,
            left: editRef.current && (editRef.current as any).offsetLeft
          }
        }),
        collect: (monitor) => {
          return {
            isDragging: monitor.isDragging()
          }
        }
      }
    }, [pos, editRef.current])
    drag(pos?.active && selected ? editRef : null) //当前元素开启了拖拽且该元素被选中了才可以拖拽

    return (
      <Root isDragging={isDragging} selected={selected} pos={pos} ref={ref || editRef} onClick={onClick}>
        <div ref={dragPreview}></div>
        <Content selected={selected} active={Boolean(pos?.active)}>
          {component}
          {/* {component ? React.createElement(component, { _prefix: `${_index + 1}.`, ...other }) : <span>Not Found</span>} */}
        </Content>
        <ToolBarRoot>
          <div className='h-full flex items-center justify-end'>
            {toolList.map(({ title, icon, cmd }) => (
              <ToolBtn
                onCommand={onCommand}
                key={cmd}
                active={Boolean(pos?.active) && cmd == 'POS'}
                title={title}
                cmd={cmd}
                icon={icon}
              />
            ))}
          </div>
        </ToolBarRoot>
        {selected && <Tag>{componentName}</Tag>}
      </Root>
    )
  }
)
