import React from 'react'
import { styled } from '@mui/system'
import {
  SetOutline as IconEdit,
  DeleteOutline as IconDelete,
  UpCircleOutline as IconUp,
  DownCircleOutline as IconDown,
  PicturesOutline as IconCopy
} from 'antd-mobile-icons'

const ToolBarRoot = styled('div')({
  height: 0,
  position: 'relative',
  overflow: 'hidden',
  transition: 'height .35s ease-in-out'
  // '&::before': {
  //   content: '" "',
  //   display: 'block',
  //   position: 'absolute',
  //   top: 0,
  //   left: 0,
  //   right: 0,
  //   height: 0,
  //   borderTop: '1px solid #eee'
  // }
})

const Root = styled('div', { shouldForwardProp: (prop) => prop !== 'selected' })<any>(({ theme, selected }: any) => ({
  transition: 'height .35s ease-in-out',
  height: 'auto',
  // borderRadius: '6px',
  backgroundColor: '#FFF',
  // border: `2px dashed ${selected ? theme.palette.divider : 'transparent'}`,
  [ToolBarRoot as any]: {
    height: selected ? 36 : 0
  },
  position: 'relative'
}))

const Content = styled('div', { shouldForwardProp: (prop) => prop !== 'selected' })<any>(
  ({ theme, selected }: any) => ({
    padding: '0px 12px',
    '& > *': {
      pointerEvents: 'none'
    },
    transition: 'all .35s ease-in-out',
    height: 'auto',
    borderRadius: '6px',
    // backgroundColor: '#FFF',
    border: `2px dashed ${selected ? theme.palette.divider : 'transparent'}`
  })
)
// const Content = styled('div')({
//   padding: '8px 8px',
//   '& > *': {
//     pointerEvents: 'none'
//   }
// })

const ToolBtnRoot = styled('div')(({ theme }) => ({
  fontSize: 12,
  cursor: 'pointer',
  display: 'flex',
  marginLeft: 12,
  color: theme.palette.primary.main,
  padding: '2px 4px',
  borderRadius: '4px',
  border: `1px solid ${theme.palette.primary.main}`,
  alignItems: 'center',
  transition: 'background-color 0.35s ease-in-out'
}))

interface IEditNodeProps {
  selected?: boolean
  component?: any
  onClick?: () => void
  allowUp?: boolean
  allowDown?: boolean
  componentName?: string
  allowEdit?: boolean
  [key: string]: any
}

const tools = [
  {
    title: '删除',
    icon: <IconDelete />,
    cmd: 'DELETE'
  },
  {
    title: '复制',
    icon: <IconCopy />,
    cmd: 'COPY'
  }
]

const Tag = styled('span')(({ theme }) => ({
  display: 'flex',
  fontSize: 12,
  padding: '0px 4px',
  color: theme.palette.secondary.main,
  border: `1px solid ${theme.palette.secondary.main}`,
  borderRadius: 4,
  position: 'absolute',
  right: -2,
  top: -2,
  transform: 'scale(0.8)',
  pointerEvents: 'none'
}))

const ToolBtn = ({ icon, title, cmd, onCommand }: any) => {
  const handleClick = (e: any) => {
    e.stopPropagation()
    if (typeof onCommand === 'function') {
      onCommand(cmd)
    }
  }
  return (
    <ToolBtnRoot onClick={handleClick}>
      {icon}
      <span>{title}</span>
    </ToolBtnRoot>
  )
}

export default React.forwardRef<HTMLDivElement, IEditNodeProps>(
  (
    { onClick, selected, component, onCommand, _index, allowUp, allowDown, allowEdit = true, componentName, ...other },
    ref
  ) => {
    const toolList = React.useMemo(() => {
      return (
        allowEdit
          ? [
              {
                title: '编辑',
                icon: <IconEdit />,
                cmd: 'EDIT'
              }
            ]
          : []
      )
        .concat(tools)
        .concat(
          [
            {
              title: '下移',
              icon: <IconDown />,
              cmd: 'DOWN'
            },
            {
              title: '上移',
              icon: <IconUp />,
              cmd: 'UP'
            }
          ].filter(({ cmd }) => (cmd === 'UP' && allowUp) || (cmd === 'DOWN' && allowDown))
        )
    }, [allowDown, allowEdit, allowUp])
    return (
      <Root selected={selected} ref={ref} onClick={onClick}>
        <Content selected={selected}>
          {component}
          {/* {component ? React.createElement(component, { _prefix: `${_index + 1}.`, ...other }) : <span>Not Found</span>} */}
        </Content>
        <ToolBarRoot>
          <div className='h-full flex items-center justify-end'>
            {toolList.map(({ title, icon, cmd }) => (
              <ToolBtn onCommand={onCommand} key={cmd} title={title} cmd={cmd} icon={icon} />
            ))}
          </div>
        </ToolBarRoot>
        {selected && <Tag>{componentName}</Tag>}
      </Root>
    )
  }
)
