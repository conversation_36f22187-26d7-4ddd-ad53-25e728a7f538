import { getUniqueId } from '@/utils'
import { cloneDeep } from 'lodash'
const findTargetNode = (tree: any[], targetId: string) => {
  const index = tree.findIndex((n) => n._id === targetId)
  return index === -1 ? [null, -1] : [tree[index], index]
}
const getAllCheckItem = (tree: any[], endIndex: number) => {
  const result = []
  for (let i = 0; i < endIndex; i++) {
    if (/(.Checkbox|.Radio)$/.test(tree[i].componentName)) {
      result.push({ data: tree[i], index: i })
    }
  }
  return result
}
const cloneNode = (sourceNode: any) => {
  //获取唯一标识
  const id = getUniqueId()
  //克隆source节点
  const newNode = cloneDeep(sourceNode)
  //应用id
  newNode._id = id
  newNode.props.name = id
  return newNode
}
export { findTargetNode, getAllCheckItem, cloneNode }
