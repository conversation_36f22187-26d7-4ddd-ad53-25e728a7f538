import React from 'react'
import produce from 'immer'
import { Action_Editor } from './ActionType'
import { getUniqueId } from '@/utils'
import { findTargetNode, cloneNode } from './editorUtils'
import { getComponentName } from '@/helper'
import { THEMESETTINGS } from '@/@define/index'
interface IState {
  schemaVersion?: string
  componentTree?: any
  currentComponent?: any
  components?: any
  settings?: any
  componentList?: any
  defaultWorkflow?: any
  pageSettings?: PageSettings
}

interface IAction {
  type: Action_Editor
  payload?: any
  [key: string]: any
}
interface IProvider {
  children?: React.ReactNode
}
export interface PageSettings {
  disabled?: boolean //当前表单是否禁用
  theme?: 'default' //表单主题
  background?: { src?: string[]; value?: string[]; color?: string[] } //背景图片，src是用户上传的背景图，value是提供的背景图
  hideCover?: boolean //是否显示页眉封面
  cover?: { src?: string[]; value?: string[] } //页眉
  hideDescription?: boolean //是否显示描述
  hideTitle?: boolean //是否显示标题
  hideSubmitButton?: boolean //是否显示提交按钮
  submitText?: string //提交按钮的文本
  titleColor?: string //表单标题的颜色
  descriptionColor?: string //描述文本颜色
  hideMobileBKG?: boolean //是否隐藏移动端的背景图
  stickySubmit?: boolean //是否将按钮固定到窗口底部
  themeColor?: any //主题色
  footerText?: any //页脚
}
const initState: IState = {
  schemaVersion: '1.0.0',
  currentComponent: null,
  componentTree: [],
  components: null,
  componentList: {
    quick: [],
    list: []
  },
  settings: {},
  pageSettings: THEMESETTINGS.DEFAULT
}
type TContext = [IState, React.Dispatch<IAction>]

const reducer = (state: IState, action: IAction) => {
  const { type, payload } = action
  return produce(state, (draft) => {
    switch (type) {
      case Action_Editor.INIT: {
        const { settings, componentTree, defaultWorkflow } = payload
        //settings单独通过updatesettings更新
        //draft.settings = settings
        draft.componentTree = componentTree || []
        draft.defaultWorkflow = defaultWorkflow || {}
        break
      }
      case Action_Editor.ADD: {
        if (state?.pageSettings?.disabled) return
        const { componentName, props } = payload
        const _id = getUniqueId()
        draft.componentTree.push({ _id: _id, componentName, props: { ...props, name: _id } })
        break
      }
      case Action_Editor.UPDATE_EDITOR_SETTING: {
        draft.pageSettings = action?.noAssign ? { ...payload } : { ...draft.pageSettings, ...payload }
        break
      }
      case Action_Editor.SET_COMPONENTS: {
        draft.componentList = payload
        draft.components = payload.list
          .map((l: any) => l.widgets)
          .flat()
          .reduce((p: any, c: any) => ({ ...p, [getComponentName(c.packageName, c.componentName)]: { ...c } }), {})
        break
      }
      case Action_Editor.UPDATE: {
        const { props, settings } = payload
        if (draft.currentComponent) {
          const [node] = findTargetNode(draft.componentTree, draft.currentComponent._id)
          node.props = { ...node.props, ...props }
          const _settings = { ...node.settings, ...settings }
          node.settings = Object.keys(_settings).length > 0 ? _settings : void 0
          /**
           * 修改当前选中节点
           */
          draft.currentComponent = { ...draft.currentComponent, ...node }
        }

        break
      }
      case Action_Editor.SELECT: {
        if (state?.pageSettings?.disabled) return
        const [node, i] = findTargetNode(draft.componentTree, payload)
        if (node?._id === draft.currentComponent?._id) {
          draft.currentComponent = null
        } else {
          draft.currentComponent = { ...node, index: i }
        }
        break
      }
      case Action_Editor.POS: {
        if (payload) {
          const [index, pos] = payload
          const node = draft.componentTree[index]
          node.pos = pos
        }

        break
      }
      case Action_Editor.MOVE: {
        if (state?.pageSettings?.disabled) return
        const { originIndex, targetIndex } = payload
        const _origin = draft.componentTree[originIndex]
        draft.componentTree.splice(originIndex, 1)
        draft.componentTree.splice(targetIndex, 0, _origin)
        break
      }
      case Action_Editor.COMMAND: {
        const { cmd, targetId } = payload
        const [node, index] = findTargetNode(draft.componentTree, targetId)
        if (!node) {
          return
        }
        switch (cmd) {
          case 'UP': {
            const targetIndex = index - 1
            if (targetIndex < 0) {
              return
            }
            draft.componentTree.splice(index, 1)
            draft.componentTree.splice(targetIndex, 0, node)
            draft.currentComponent.index = targetIndex
            break
          }
          case 'DOWN': {
            const targetIndex = index + 1
            if (targetIndex > draft.componentTree.length - 1) {
              return
            }
            draft.componentTree.splice(index, 1)
            draft.componentTree.splice(targetIndex, 0, node)
            draft.currentComponent.index = targetIndex
            break
          }
          case 'COPY': {
            draft.componentTree.splice(index, 0, cloneNode(node))
            break
          }
          case 'DELETE': {
            draft.componentTree.splice(index, 1)
            if (draft.currentComponent?._id === targetId) {
              draft.currentComponent = void 0
              return
            }
            break
          }
          case 'POS':
            console.log(2120)
            draft.componentTree[index].pos = !node.pos?.active ? { ...node.pos, active: !node.pos?.active } : undefined
            break
          default:
        }
        break
      }
      case Action_Editor.UPDATE_SETTINGS: {
        draft.settings = { ...draft.settings, ...payload }
        break
      }
      case Action_Editor.UPDATE_TEMPLATE_SETTING: {
        draft.settings.templateSettings = { ...draft.settings.templateSettings, ...payload }
        break
      }
      default:
    }
  })
}

const Context = React.createContext<TContext>([initState, (action) => action])

const EditorProvider: React.FC<IProvider> = ({ children }) => {
  const _reducer = React.useReducer(reducer, initState)
  return <Context.Provider value={_reducer}>{children}</Context.Provider>
}

const useEditorContext = () => {
  const _context = React.useContext(Context)
  if (_context === undefined) {
    throw new Error('must be used within a EditorProvider')
  }
  return _context
}
const add = (dispatch: React.Dispatch<IAction>, componentName: string, props: any) => {
  dispatch({
    type: Action_Editor.ADD,
    payload: { componentName, props }
  })
}
const update = (dispatch: React.Dispatch<IAction>, data: any) => {
  dispatch({
    type: Action_Editor.UPDATE,
    payload: data
  })
}
const updateSettings = (dispatch: React.Dispatch<IAction>, data: any) => {
  dispatch({
    type: Action_Editor.UPDATE_SETTINGS,
    payload: data
  })
}
const updateTemplateSettings = (dispatch: React.Dispatch<IAction>, data: any) => {
  dispatch({
    type: Action_Editor.UPDATE_TEMPLATE_SETTING,
    payload: data
  })
}
const updateEditorSettings = (dispatch: React.Dispatch<IAction>, data: any, noAssign?: boolean) => {
  dispatch({
    type: Action_Editor.UPDATE_EDITOR_SETTING,
    payload: data,
    noAssign
  })
}
const selectComponent = (dispatch: React.Dispatch<IAction>, _id: string) => {
  dispatch({
    type: Action_Editor.SELECT,
    payload: _id
  })
}
const command = (dispatch: React.Dispatch<IAction>, _id: string, cmd: string) => {
  dispatch({
    type: Action_Editor.COMMAND,
    payload: { targetId: _id, cmd }
  })
}

const setComponents = (dispatch: React.Dispatch<IAction>, componentData: any) => {
  dispatch({
    type: Action_Editor.SET_COMPONENTS,
    payload: componentData
  })
}
const init = (dispatch: React.Dispatch<IAction>, data: any) => {
  dispatch({
    type: Action_Editor.INIT,
    payload: data
  })
}

const move = (dispatch: React.Dispatch<IAction>, originIndex: number, targetIndex: number) => {
  dispatch({
    type: Action_Editor.MOVE,
    payload: {
      originIndex: originIndex,
      targetIndex: targetIndex
    }
  })
}

const updatePos = (dispatch: React.Dispatch<IAction>, data?: any) => {
  dispatch({ type: Action_Editor.POS, payload: data })
}
export {
  useEditorContext,
  EditorProvider,
  selectComponent,
  add,
  command,
  update,
  setComponents,
  updateSettings,
  updateTemplateSettings,
  init,
  move,
  updateEditorSettings,
  updatePos
}
