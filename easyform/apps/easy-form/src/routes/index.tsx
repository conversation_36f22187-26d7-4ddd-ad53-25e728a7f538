import React from 'react'
import type { RouteObject } from 'react-router-dom'
import { BasicLayout, PcLayout, BaseLayout } from '@/layouts'

const NotFound = React.lazy(() => import('@/pages/exception/404'))
const NoAccess = React.lazy(() => import('@/pages/exception/access'))
const Playground = React.lazy(() => import('@/pages/playground'))
const Designer = React.lazy(() => import('@/pages/designer'))
const ActivityList = React.lazy(() => import('@/pages/activity-list'))
const TemplateList = React.lazy(() => import('@/pages/classification'))
const ClassTemplate = React.lazy(() => import('@/pages/classificationTemplate'))
const EasyFormDesigner = React.lazy(() => import('@/pages/pc/Designer'))
const Preview = React.lazy(() => import('@/pages/preview'))
const RunActivity = React.lazy(() => import('@/pages/runtime'))
const Template = React.lazy(() => import('@/pages/template'))
const TemplateDetail = React.lazy(() => import('@/pages/template/detail'))
const Statistics = React.lazy(() => import('@/pages/statistics'))
const Test = React.lazy(() => import('@/pages/test'))
const Processwk = React.lazy(() => import('@/pages/processwk'))
const TaskDetail = React.lazy(() => import('@/pages/taskDetail'))
const Pc = React.lazy(() => import('@/pages/pc'))
const Templates = React.lazy(() => import('@/pages/pc/Templates'))
const Submited = React.lazy(() => import('@/pages/submited'))
const TicketVerify = React.lazy(() => import('@/pages/ticket-verify'))
const TicketWriteOff = React.lazy(() => import('@/pages/ticket-writeoff'))
const AdDetail = React.lazy(() => import('@/pages/ad-detail'))
const PcEditor = React.lazy(() => import('@/pages/pc/Designer/Editor'))
const ShareForm = React.lazy(() => import('@/pages/share'))
const RecordList = React.lazy(() => import('@/pages/runtime/log/list'))
const SubmitLog = React.lazy(() => import('@/pages/runtime/log/submit'))
const LogDetail = React.lazy(() => import('@/pages/runtime/log/detail'))
const Activity = React.lazy(() => import('@/pages/pc/activity'))
const Joined = React.lazy(() => import('@/pages/pc/joined'))
const EzformTemplate = React.lazy(() => import('@/pages/pc/template'))

export const routes: RouteObject[] = [
  {
    path: '/pc',
    element: <PcLayout />,
    children: [
      {
        index: true,
        element: <Pc />
      },
      {
        path: 'designForm',
        element: <EasyFormDesigner />
      },
      {
        path: 'formtemplates',
        element: <Templates />
      },
      {
        path: 'editor',
        element: <PcEditor />
      }
    ]
  },
  {
    path: '/ezform',
    element: <BaseLayout />,
    children: [
      {
        // index: true,
        path: 'pc',
        element: <Pc />
      },
      {
        path: 'pc/designForm',
        element: <EasyFormDesigner />
      },
      {
        path: 'pc/formtemplates',
        element: <Templates />
      },
      {
        path: 'pc/editor',
        element: <PcEditor />
      },
      {
        path: 'activity',
        element: <Activity />
      },
      {
        path: 'joined',
        element: <Joined />
      },
      {
        path: 'template',
        element: <EzformTemplate />
      }
    ]
  },
  {
    path: '/',
    element: <BasicLayout />,
    children: [
      // {
      //   index: true,
      //   element: <Portal />
      // },
      {
        index: true,
        element: <ActivityList />
      },
      {
        path: 'creation',
        element: <Template />
      },
      {
        path: 'creation2',
        element: <TemplateList />
      },
      {
        path: 'template',
        element: <ClassTemplate></ClassTemplate>
      },
      // {
      //   path: 'pc',
      //   element: <Pc />,
      //   children: []
      // },
      // {
      //   path: 'designForm',
      //   element: <EasyFormDesigner></EasyFormDesigner>
      // },
      // {
      //   path: 'formtemplates',
      //   element: <Templates />
      // },

      {
        path: 'previewtd',
        element: <TemplateDetail />
      },
      {
        path: 'taskDetail',
        element: <TaskDetail></TaskDetail>
      },
      {
        path: 'designer',
        element: <Designer />
      },
      {
        path: 'preview',
        element: <Preview />
      },
      {
        path: 'statistics',
        element: <Statistics />
      },
      {
        path: 'processwk',
        element: <Processwk />
      },
      {
        path: 'share',
        element: <ShareForm />
      },
      {
        path: 'submited',
        element: <Submited />
      },
      {
        path: 'recordlist',
        element: <RecordList />
      },
      {
        path: 'submitlog',
        element: <SubmitLog />
      },
      {
        path: 'logdetail',
        element: <LogDetail />
      }
    ]
  },
  {
    path: 'ticketverify',
    element: <TicketVerify />
  },
  {
    path: '/ticket/detail',
    element: <TicketWriteOff />
  },
  {
    path: '/test',
    element: <Test />
  },
  {
    path: '/submit',
    element: <RunActivity />
  },
  {
    path: '/addetail',
    element: <AdDetail />
  },
  {
    path: 'noaccess',
    element: <NoAccess />
  },
  {
    path: '*',
    element: <NotFound />
  }
]
