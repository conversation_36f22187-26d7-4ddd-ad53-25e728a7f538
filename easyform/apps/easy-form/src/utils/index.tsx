import dayjs from 'dayjs'
const shortid = require('shortid')
import tinycolor from 'tinycolor2'

const getUrlParam = (variable: string) => {
  const query = window.location.search.substring(1)
  const vars = query.split('&')
  for (let i = 0; i < vars.length; i++) {
    const pair = vars[i].split('=')
    if (pair[0] == variable) {
      return pair[1]
    }
  }
  return void 0
}
const getHashUrlParam = (variable: string) => {
  const hash = window.location.hash.split('?')[1]
  const result = hash?.split('&')?.reduce(function (res: any, item) {
    const parts = item.split('=')
    res[parts[0]] = parts[1]
    return res
  }, {})
  return result && result[variable]
}

const getUniqueId = (prefix = '') => {
  return `${prefix}${shortid.generate()}`.replace(/-|_/g, 'F')
}

const FriendlyTimeThreshold = {
  NOW: 1000 * 60 * 2, // 5分钟,
  HOUR: 1000 * 60 * 60, // 1小时
  DAY: 24 * 60 * 60 * 1000, // 1天
  MONTH: 24 * 60 * 60 * 1000 * 30, //1月
  YEAR: 24 * 60 * 60 * 1000 * 365 //1年
}
/**
 * @description 判断路径资源是否是项目本身资源
 * @src 资源的路径
 */
const isLocalSource = (src: string) => {
  if (!src) return
  return src.includes('easyform/assets') || src.includes('easyform/template')
}
const getFriendlyTimeDiff = (diffTimestamp: number, sufix = '前') => {
  if (diffTimestamp < FriendlyTimeThreshold.NOW) {
    return '刚刚'
  }
  if (diffTimestamp < FriendlyTimeThreshold.HOUR) {
    return Math.floor(diffTimestamp / (60 * 1000)) + '分钟' + sufix
  }
  if (diffTimestamp < FriendlyTimeThreshold.DAY) {
    return Math.floor(diffTimestamp / (60 * 60 * 1000)) + '小时' + sufix
  }
  if (diffTimestamp < FriendlyTimeThreshold.MONTH) {
    return Math.floor(diffTimestamp / (24 * 60 * 60 * 1000)) + '天' + sufix
  }
  if (diffTimestamp < FriendlyTimeThreshold.YEAR) {
    return Math.floor(diffTimestamp / (24 * 60 * 60 * 1000 * 30)) + '个月' + sufix
  }
  return Math.floor(diffTimestamp / (24 * 60 * 60 * 1000 * 365)) + '年' + sufix
}

const getRemainTimeDiff = (diffTimestamp: number, sufix = '剩余') => {
  if (diffTimestamp < FriendlyTimeThreshold.HOUR) {
    return sufix + Math.floor(diffTimestamp / (60 * 1000)) + '分钟'
  }
  if (diffTimestamp < FriendlyTimeThreshold.DAY) {
    return sufix + Math.floor(diffTimestamp / (60 * 60 * 1000)) + '小时'
  }
  if (diffTimestamp < FriendlyTimeThreshold.MONTH) {
    return sufix + Math.floor(diffTimestamp / (24 * 60 * 60 * 1000)) + '天'
  }
  if (diffTimestamp < FriendlyTimeThreshold.YEAR) {
    return sufix + Math.floor(diffTimestamp / (24 * 60 * 60 * 1000 * 30)) + '个月'
  }
  return sufix + Math.floor(diffTimestamp / (24 * 60 * 60 * 1000 * 365)) + '年'
}

const getFriendlyTime = (date: string) => {
  const diffTimestamp = dayjs().valueOf() - dayjs(date).valueOf()
  return getFriendlyTimeDiff(diffTimestamp)
}
const getAcitvityDate = (start?: string, end?: string, format?: string) => {
  if (end && start) {
    return dayjs(start).format(format || 'MM-DD HH:mm') + ' 至 ' + dayjs(end).format(format || 'MM-DD HH:mm')
  } else if (start) {
    return dayjs(start).format(format || 'MM-DD HH:mm') + ' 开始'
  } else if (end) {
    return dayjs(end).format(format || 'MM-DD HH:mm') + ' 结束'
  } else {
    return '不限时间'
  }
}
const getRemainTime = (date?: string) => {
  const diffTimestamp = dayjs(date).valueOf() - dayjs().valueOf()
  return getRemainTimeDiff(diffTimestamp)
}
const formatDate = (date: string | number, format = 'YYYY-MM-DD') => {
  return dayjs(date).format(format)
}
const filterSpecialChar = (str: string) => {
  const regex =
    // eslint-disable-next-line no-misleading-character-class
    /[\u{1F600}-\u{1F64F}\u{1F300}-\u{1F5FF}\u{1F680}-\u{1F6FF}\u{1F900}-\u{1F9FF}\u{2600}-\u{26FF}\u{2700}-\u{27BF}\u{1F1E6}-\u{1F1FF}\u{1F3FB}-\u{1F3FF}]/gu
  return str.replace(regex, '')
}
const getAvatarByUserId = (id: string) => {
  return `${(window as any).APP_CONFIG.server.yiban}/msg/avatar/${id}`
}

export function setAlpha(color: string, opacity: number) {
  return tinycolor(color).setAlpha(opacity).toRgbString()
}
export {
  getUniqueId,
  getUrlParam,
  isLocalSource,
  getHashUrlParam,
  getFriendlyTime,
  getAcitvityDate,
  getRemainTime,
  formatDate,
  filterSpecialChar,
  getAvatarByUserId
}
