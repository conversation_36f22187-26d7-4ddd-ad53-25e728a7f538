import axios from 'axios'
import { Toast } from 'antd-mobile'
import { errorHandler } from './index'

const service = axios.create({
  baseURL: (window as any).APP_CONFIG.server.easyform,
  timeout: 30000
})

service.interceptors.request.use((config: any) => {
  return config
}, errorHandler)

service.interceptors.response.use((response: any) => {
  if (response.status === 200 && response.data?.code === 'SUCCESS') {
    return Promise.resolve(response.data.result)
  }
  const errorMsg = response.data?.msg

  if (errorMsg) {
    Toast.show({
      content: errorMsg,
      icon: 'fail'
    })
  }
  return Promise.reject(void 0)
}, errorHandler)

export default service
