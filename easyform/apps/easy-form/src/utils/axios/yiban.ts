import axios from 'axios'
import { Toast } from 'antd-mobile'
import { errorHandler } from './index'

const service = axios.create({
  baseURL: (window as any).APP_CONFIG.server.yiban,
  timeout: 10000
})

service.interceptors.request.use((config: any) => {
  return config
}, errorHandler)

service.interceptors.response.use((response: any) => {
  if (response.status === 200) {
    return Promise.resolve(response.data.data)
  }
  return Promise.reject(void 0)
}, errorHandler)

export default service
