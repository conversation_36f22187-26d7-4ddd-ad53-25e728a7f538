import axios from 'axios'
import { Toast } from 'antd-mobile'
import { errorHandler } from './index'

const service = axios.create({
  baseURL: (window as any).APP_CONFIG.server.ticket,
  timeout: 30000
})

service.interceptors.request.use((config: any) => {
  return config
}, errorHandler)

service.interceptors.response.use((response: any) => {
  if (response.status === 200 && response.data?.code === 'SUCCESS') {
    return Promise.resolve(response.data.data)
  }
  const errorMsg = response.data?.message

  if (errorMsg) {
    Toast.show({
      content: errorMsg,
      icon: 'fail'
    })
  }
  return Promise.reject(errorMsg)
}, errorHandler)

export default service
