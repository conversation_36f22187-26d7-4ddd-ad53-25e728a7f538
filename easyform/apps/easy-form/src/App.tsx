import React from 'react'
import { <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> as Router } from 'react-router-dom'
import { Suspense } from 'react'
import Theme<PERSON>rovider from '@/ThemeProvider'
import { AppProvider, TAppState } from '@/context/AppContext'
import themes from '@/themes'
import { PageLoading } from '@/components/Loading'
import { useRoutes } from 'react-router-dom'
import { routes } from '@/routes'
import { getUserInfo } from '@/api/easyform'
import { getUserInfo as getYBUserInfo } from '@/api'
import { useBaseRequest, useEasyFormRequest } from '@/hooks'
import { DotLoading } from 'antd-mobile'
import { IdentityTypes } from '@/@define'
import { getAvatarByUserId } from '@/utils'
import { ConfigProvider } from 'antd'

const THEME = {
  STUDENT: themes.blue,
  FDY: themes.default
}

function Routes() {
  const elements = useRoutes(routes)
  return elements
}

const checkIsCounselor = (manageClass: any[], category: string | number) => {
  return manageClass.findIndex((d) => d.category === 2) !== -1
}

export default function App() {
  const [theme] = React.useState(THEME.FDY)
  const [appData, setAppData] = React.useState<TAppState | null>(null)
  //获取易班身份信息
  // useBaseRequest(getYBUserInfo, {
  //   ready: Boolean(appData?.user),
  //   onSuccess: (data: any) => {
  //     console.log('yiban')
  //     setAppData((prev) => ({
  //       user: prev?.user || {
  //         id: '100',
  //         name: 'Little',
  //         remark: '大气学院',
  //         icon: '',
  //         category: 1,
  //         isCounselor: true,
  //         extra: {}
  //       },
  //       ybUser: data
  //     }))
  //   }
  // })
  const { loading } = useEasyFormRequest(getUserInfo, {
    onSuccess: (data: any) => {
      console.log(data.data)
      const { name, code, speciality, category, manageClasses, adminType, orgs, ...other } = data.data
      const isCounselor = checkIsCounselor(manageClasses, category)
      setAppData({
        user: {
          id: code,
          name,
          remark: other.class?.name || (other.orgs ? other.orgs[0]?.name : ''),
          icon: getAvatarByUserId(code),
          category: category,
          isCounselor: isCounselor,
          adminType: adminType,
          orgs: orgs,
          extra: data.data
        }
      })
      // if (isCounselor) {
      //   setTheme(THEME.FDY)
      // }
    },
    onError: () => {
      setAppData((prev) => ({
        user: {
          id: '100',
          name: 'Little',
          remark: '大气学院',
          icon: '',
          category: 1,
          isCounselor: true,
          extra: {}
        }
      }))
    }
  })
  React.useEffect(() => {
    console.log('appData===',appData)
  }, [appData])

  return (
    <Suspense fallback={<PageLoading />}>
      {appData?.user ? (
        <ConfigProvider prefixCls={'ezf'}>
          <AppProvider data={appData}>
            <ThemeProvider theme={theme}>
              <Router basename={(window as any).PUBLIC_URL}>
                <Routes />
              </Router>
            </ThemeProvider>
          </AppProvider>
        </ConfigProvider>
      ) : (
        <div className='h-screen flex items-center justify-center'>
          <DotLoading />
        </div>
      )}
    </Suspense>
  )
}
