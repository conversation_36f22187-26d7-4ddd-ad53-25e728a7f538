/**
 * 主体类型
 */
export type SubjectType = 'U' | 'P' | 'PS' | 'O' | 'C' | 'G' | 'A'

export interface IUserGroup {
  code: string
  id: string
  name: string
}

export interface IUserRoles {
  code: string
  name: string
}

export interface IUserOrg {
  code: string
  id: string
  name: string
}

export interface IUserClass {
  category: number
  code: string
  id: string
  name: string
}

export interface IUserPoses {
  code: string
  id: string
  name: string
  orgcode: string
  possetcodes: string[]
}

export interface IUserPossets {
  code: string
  id: string
  name: string
}

export interface IUserInfo {
  adminType: number
  isAdmin?: boolean
  assistant: any[]
  category: number | string
  class: any
  code: string
  groups: IUserGroup[]
  id: string
  identityType: number[]
  currentIdentity: number
  manageclasses?: IUserClass[]
  manageOrgs?: IUserOrg[]
  name: string
  orgs: IUserOrg[]
  poses: IUserPoses[]
  possets: IUserPossets[]
  speciality: any
}

export interface ISubjectMember {
  id?: string | number
  code: string | number
  name?: string
}
export interface ISubjectMembers {
  total?: number
  persons?: ISubjectMember[]
}

export interface IIdentityItem {
  id: number
  name: string
  identityType: number
  identity?: number
  sort: number
  role?: string | string[]
}

export interface IIdentityList {
  items?: IIdentityItem[]
}

export interface IUserRole {
  /**
   * @name 判断学院级管理员
   */
  departAdmin: boolean // 判断学院级管理员
  /**
   * @name 判断校级管理员（学工管理员）
   */
  xgAdmin: boolean // 判断校级管理员（学工管理员）

  /**
   * @name 判断校级管理员
   */
  schoolAdmin: boolean // 判断校级管理员
  /**
   * @name 超级管理员判断
   */
  supper: boolean // 超级管理员判断
  /**
   * @name 辅导员
   */
  fdy: boolean // 辅导员
  /**
   * @name 教职工
   */
  staff: boolean // 教职工
  /**
   * @name 学生
   */
  student: boolean // 学生
  /**
   * @name 校领导判断
   */
  schoolLeader: boolean // 校领导判断
  /**
   * @name 判断学院领导
   */
  academyLeader: boolean // 判断学院领导
  /**
   * @name 判断部门领导
   */
  departLeader: boolean // 判断部门领导
  /**
   * @name 其他用户
   */
  otherUser: boolean // 其他

  /**
   * @name 学校级领导用户
   */
  schoolLevelUser: boolean // 学校级用户
  /**
   * @name 学院级领导用户
   */
  academyLevelUser: boolean // 学院级领导用户

  /**
   * @name 学院副书记
   */
  academyDeputySecretary: boolean // 学院副书记

  /**
   * @name 学院团委书记
   */
  academyLeagueSecretary: boolean //学院团委书记
  /**
   * @name 学院学工办主任
   */
  studentOfficeDirector: boolean // 学工办主任
  [key: number]: boolean
}

type RoleCodeType = 'admin' | 'hum'
export interface IUserData extends IUserInfo, IUserRole {
  displayName?: string
  auth?: boolean
  role?: Array<RoleCodeType | { roleCode: RoleCodeType }>
  _role?: IIdentityItem
  identites: IIdentityItem[]
  canCreateTask?: boolean
  canCreateActivity?: boolean
  roleType?: 'admin' | 'user'
  [key: string]: any
}
