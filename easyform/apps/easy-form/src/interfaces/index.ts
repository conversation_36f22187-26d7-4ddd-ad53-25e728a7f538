export interface IIndexPicLink {
    /**
     * 图片地址
     */
    pic?:string;
    /**
     * 外链地址
     */
    url?:string;

    /**
     * 标题
     */
    title?:string;

    /**
     * 简介
     */
    desc?:string;

    /**
     * 排序
     */
    sort?:number;

    /**
     * 原图地址
     */
    originPic?:string;
}

/**
 * 首页应用链接
 */
export interface IIndexAppLink {
    appReleaseId:number | string;
    appName:string;
    icon?:string;
    url?:string;
    pubSort?:number | string;
    appId:number | string;
}

/**
 * 首页配置
 */
export interface IConfigResult {
    topPics?: IIndexPicLink[]; // 顶部图片
    apps?:IIndexAppLink[]; // 默认应用
    taskPics?:IIndexPicLink[]; // 活动海报
    tasks?:IIndexPicLink[]; // 热门活动
    friendLinks?:IIndexPicLink[]; // 友情链接
}

/**
 * 选项定义
 */
export interface OptionType<T = any>{
    label?:string;
    value:T;
    [key:string]:any;
}