import { ReactNode } from 'react'
import { SubjectType } from './user'
export enum CreatorRole {
  SUPER_ADMIN = 'SUPER_ADMIN', // 校级或系统管理
  DEPART_ADMIN = 'DEPART_ADMIN', // 院系或部门管理员
  ASSISTANT = 'ASSISTANT' // 辅导员
}

export type CreatorRoleType = keyof typeof CreatorRole

export type AssignmentRangeFilterType =
  | 'academy'
  | 'class'
  | 'student'
  | 'academy_fdy'
  | 'academy_yxgly'
  | 'academy_class'
  | 'academy_student'

export type TaskType = 'EZForm'

export type TaskProcessNameType = 'yxgly' | 'fdy' | 'bks'

export type TaskProcessActionType = 'xp' | 'cl' // 下派 和 处理 操作

export type TaskProcessActions<T = string> = {
  [key in TaskProcessActionType]: T
}
export type TaskTypeMaps = {
  [key in TaskType]: string
}
export type TaskProcessActionOptions = {
  /**
   * 标记色
   */
  color: string
}

/**
 * 任务流程选择人员过滤器
 */
export enum AssignmentTargetFilter {
  SelectClassAssistantFromAcademy = 'SelectClassAssistantFromAcademy', // 选择指定学院下的辅导员
  SelectStudentFromClassAssistant = 'SelectStudentFromClassAssistant', // 选择指定辅导员带的学生
  SelectAllClassAssistant = 'SelectAllClassAssistant', // 选择全校所有的班级辅导员
  SelectStudentFromAcademy = 'SelectStudentFromAcademy', // 选择学院内的学生
  SelectStudentFromClass = 'SelectStudentFromClass', // 选择指定班级的学生
  SelectStudentByCurrentUser = 'SelectStudentByCurrentUser', // 选择当前登录人的所带班的学生
  SelectAssistantByCurrentAcademyLeader = 'SelectAssistantByCurrentAcademyLeader', // 根据当前登陆院系管理员筛选自己院系的辅导员
  SelectStudentByCurrentAcademyLeader = 'SelectStudentByCurrentAcademyLeader' // 根据当前登陆院系管理员筛选自己院系的学生
}

export type AssignmentTargetFilterType = keyof typeof AssignmentTargetFilter

/**
 * ITaskProcess
 */
export interface ITaskProcess<T = string> {
  id: T
  name?: string
  type?: string
  assignmentType?: SubjectType | ''
  assignmentTargetFilter?: AssignmentTargetFilterType | ''
  assignmentTargetId?: string
  assignmentTargetName?: string
  autoAssign?: boolean
  expandRangeAssignment?: boolean
  createTime?: string
  subName?: string
  creatorOrg?: string
  needTarget?: boolean
}

/**
 * task state
 */
export type TaskStateType = 'prepare' | 'running' | 'end'

/**
 * 任务
 */
export interface ITaskData extends ITaskItem {
  state: TaskStateType
}

/**
 * 创建数据项
 */
export interface ITaskItem {
  active?: boolean
  createTime?: string
  creatorId?: string | number
  creatorName?: string
  creatorOrg?: string
  creatorIdentity?: string | number
  endTime?: string
  payload?: string
  process?: ITaskProcess[]
  startTime?: string
  description?: string
  processId?: string
  assignmentKey?: string
  assignmentState?: string
  title?: string
  type?: string
  state?: string
  formId?: string
  url?: string
  Counts?: {
    [key: string]: {
      DoneCount: number
      TotalCount: number
    }
  }
  _key: string
  _todo?: IUndoTask
  privilegeApprove?: any
}

/**
 * 待办事项
 */
export interface IUndoTask {
  _key: string | number
  task?: ITaskItem
  state: string
  processId: string
  taskKey: string
  id?: any
  createTime: string
  assignee: {
    userId: string
    userName: string
  }
  url?: string
}

/**
 * 委派人
 */
export interface IAssignee {
  assigneeId: string
  assigneeName: string
  authorityScope?: string
  authorityScopeId?: string
  bizId?: string
  type?: string
}

export type AssignmentType = 'unread' | 'read' | 'done'

/**
 * 委派
 */
export interface IAssignment {
  assignee: IAssignee
  completeTime: string
  createTime: string
  processId: string
  state: AssignmentType
  taskKey: string
  task?: ITaskItem
  url?: string
  rootTask: ITaskItem
  _key: string | number
  id: string | number
}

/**
 * 任务状态选项
 */
export type TaskOptionsType<T extends string> = {
  [key in T]: {
    text?: string
    status?: 'default' | 'success' | 'processing' | 'error'
  }
}

/**
 * 下派流程选项定义
 */
export interface ITaskProcessOption<T = string> {
  label?: ReactNode
  value: T
  checked?: boolean
  name: string
  disabled?: boolean
}

/**
 * 任务
 */
export interface IUndoTaskData {
  title: string
  _key: string
  creatorName: string
  creatorId: string
  creatorIdentity?: string | number
  process: ITaskProcess[]
  createTime: string
  endTime?: string
  startTime?: string
  state?: string
  type: string
  id: string
  processId?: string
  assignmentKey?: string
  assignmentState?: string
  task?: ITaskData
  url?: string
  _todo?: IUndoTask
}

export type ApproverType = SubjectType

export interface ITaskApprover {
  approverType?: ApproverType
  approverName?: string
  approverId?: string
  accepted?: boolean
  done?: boolean
  handleTime?: string | null
}

/**
 * 任务检测结果
 */
export interface ITaskCheckData {
  /**
   * 是否越权
   */
  privilege?: boolean
  approver?: ITaskApprover
}
