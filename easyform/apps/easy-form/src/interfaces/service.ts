/**
 * 接口响应数据
 */
export interface IResultData<T = any> {
  code?: number
  data?: T
  msg?: string
  [key: string]: any
}

export type DataItemsType<T> = {
  items: T[]
}

/**
 * 万能表单接口响应数据
 */
export interface IEzFormResultData<T = any> {
  code: 'SUCCESS' | 'FAIL'
  msg?: string
  result?: {
    data: T
    total?: number
  } | null
}

/**
 * 数据中心接口响应数据
 */
export interface IDGPlusResultData<T = any> {
  code: 0 | 1
  msg?: string
  data?: T
}
