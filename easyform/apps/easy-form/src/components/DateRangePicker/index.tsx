import React from 'react'
import { <PERSON><PERSON>, <PERSON>Picker, Toast, DatePickerProps } from 'antd-mobile'
import { styled } from '@mui/system'
import { CloseCircleFill as IconClear } from 'antd-mobile-icons'
import dayjs from 'dayjs'

const StyledButton = styled(Button)<any>(({ theme }) => ({
  color: theme.palette.text.secondary,
  fontSize: 14,
  position: 'relative'
}))
const StyledClear = styled(IconClear)<any>(({ theme }) => ({
  position: 'absolute',
  right: 4,
  color: theme.palette.text.hint,
  top: '50%',
  transform: 'translateY(-50%)'
}))

interface IProps {
  startTitle?: string
  endTitle?: string
  onChange?: (v: string[]) => void
  value?: string[]
}
const formatTypes: any = {
  day: 'YYYY-MM-DD',
  year: 'YYYY',
  month: 'YYYY-MM',
  hour: 'YYYY-MM-DD HH:mm',
  minute: 'YYYY-MM-DD HH:mm',
  second: 'YYYY-MM-DD HH:mm:ss'
}
type TProps = IProps & Pick<DatePickerProps, 'precision'>
export default React.forwardRef<HTMLDivElement, TProps>(
  ({ startTitle = '选择开始时间', endTitle = '选择结束时间', onChange, value, precision = 'day' }, ref) => {
    const [openStart, setOpenStart] = React.useState(false)
    const [openEnd, setOpenEnd] = React.useState(false)
    const [startDate, setStartDate] = React.useState<Date>()
    const [endDate, setEndDate] = React.useState<Date>()
    const [innerValue, setInnerValue] = React.useState<string[]>([])
    React.useEffect(() => {
      if (startDate) {
        // console.log((startDate as any).format())
        setInnerValue((prev) => [dayjs(startDate).format(formatTypes[precision]), prev[1]])
      }
    }, [precision, startDate])
    const handleStartChange = (v?: Date) => {
      if (typeof onChange === 'function') {
        onChange([v ? dayjs(v).format(formatTypes[precision]) : '', innerValue[1] || ''])
      } else {
        setStartDate(v)
        setInnerValue([v ? dayjs(v).format(formatTypes[precision]) : '', innerValue[1] || ''])
      }
    }
    const handleEndChange = (v?: Date) => {
      if (typeof onChange === 'function') {
        onChange([innerValue[0] || '', v ? dayjs(v).format(formatTypes[precision]) : ''])
      } else {
        setEndDate(v)
        setInnerValue([innerValue[0] || '', v ? dayjs(v).format(formatTypes[precision]) : ''])
      }
    }
    React.useEffect(() => {
      if (Array.isArray(value)) {
        setInnerValue(value)
        if (value[0]) {
          setStartDate(new Date(value[0]?.replaceAll('-', '/')))
        }
        if (value[1]) {
          setEndDate(new Date(value[1]?.replaceAll('-', '/')))
        }
      }
    }, [value])
    return (
      <>
        <div className='flex items-center justify-between' ref={ref}>
          <StyledButton onClick={() => setOpenStart(true)} block>
            {innerValue[0] || startTitle}
            {innerValue[0] && (
              <StyledClear
                onClick={(e: any) => {
                  e.stopPropagation()
                  handleStartChange(void 0)
                }}
              />
            )}
          </StyledButton>
          <div className='w-8'></div>
          <StyledButton onClick={() => setOpenEnd(true)} block>
            {innerValue[1] || endTitle}
            {innerValue[1] && (
              <StyledClear
                onClick={(e: any) => {
                  e.stopPropagation()
                  handleEndChange(void 0)
                }}
              />
            )}
          </StyledButton>
        </div>
        <DatePicker
          min={new Date()}
          max={endDate}
          onConfirm={handleStartChange}
          precision={precision}
          onClose={() => setOpenStart(false)}
          visible={openStart}
        />
        <DatePicker
          onConfirm={handleEndChange}
          min={startDate || new Date()}
          precision={precision}
          onClose={() => setOpenEnd(false)}
          visible={openEnd}
        />
      </>
    )
  }
)
