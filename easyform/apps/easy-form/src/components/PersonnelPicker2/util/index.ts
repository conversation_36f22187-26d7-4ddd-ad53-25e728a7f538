export function transformToList(selected: any = {}) {
  let _res: any[] = []
  Object.entries(selected).forEach(([k, v]: any) => {
    if (Array.isArray(v)) {
      /**不包含子类别 */
      _res = _res.concat(v.map((d) => ({ ...d, type: k })))
    } else {
      _res = _res.concat(
        Object.entries(v).reduce((p: any, [_k, _v]: any) => {
          return [...p, ..._v.map((i: any) => ({ ...i, type: k, nodeType: _k }))]
        }, [])
      )
    }
  })
  return _res
}

export const Categories = [
  {
    key: 'student',
    label: '学生'
  },
  {
    key: 'jzg',
    label: '教职工'
  },
  {
    key: 'group',
    label: '组'
  },
  {
    key: 'pos',
    label: '岗位'
  },
  {
    key: 'posset',
    label: '职称'
  }
]
export const TextType: any = {
  student: '学生',
  jzg: '教职工',
  org: '机构',
  posset: '岗位集',
  clazz: '班级',
  user: '',
  pos: '岗位',
  cat: '自动筛选',
  group: '用户组',
  extra: '分类筛选'
}

export function getFrindlyInfo(selected: any) {
  const res: any = []
  Object.entries(selected).forEach(([k, v]: any) => {
    if (Array.isArray(v)) {
      if (v.length > 0) {
        res.push(`${v.length}个${TextType[k] || '自动筛选'}`)
      }
    } else {
      Object.entries(v).forEach(([_k, _v]: any) => {
        if (_v.length > 0) {
          res.push(`${_v.length}个${TextType[_k] ? TextType[_k] + '的' : ''}${TextType[k]}`)
        }
      })
    }
  })
  return res.join(',')
}
