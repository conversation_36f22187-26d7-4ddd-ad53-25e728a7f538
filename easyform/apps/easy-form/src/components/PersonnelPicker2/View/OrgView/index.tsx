import React from 'react'
import { Box, styled } from '@mui/system'
import { <PERSON><PERSON>, Tabs, DotLoading } from 'antd-mobile'
import { LocationFill as IconLocation } from 'antd-mobile-icons'
import List from '../../List'
import { Collapse, TabPanel, Typography } from '@/components'
import { useEasyFormRequest } from '@/hooks'
import { loadOrg } from '../../api'
import { IViewProps, breadcrumbsDataItem } from '../index'
import OrgList from './OrgList'
import PositionList from './PositionList'
// import PositionList from './PositionList'
// import ClazzList from './ClazzList'
// import { IconRole } from '../../icon'
import { CategoryItem } from '../../index'

interface IOrgViewProps extends IViewProps {
  code?: string
  name?: string
  categories?: CategoryItem[]
  clazzsCount?: number
  postionsCount?: number
  breadcrumbs?: breadcrumbsDataItem[]
  onBreadClick?: (index: number, d: breadcrumbsDataItem) => void
  disabledPermission?: boolean
  nodeType?: string
}

const StyledTabs = styled(Tabs)(({ theme }) => ({
  '.adm-tabs-tab': {
    color: theme.palette.text.secondary,
    fontSize: 14
  },
  '& .adm-tabs-content': {
    padding: '8px 12px'
  }
}))

const defaultRoles = [
  {
    code: 'student_in_org:ALL',
    name: '所有学生'
  },
  {
    code: 'techer_in_org:ALL',
    name: '所有老师'
  },
  {
    code: 'techer_in_org',
    name: '当前机构下所有老师'
  },
  {
    code: 'student_in_org',
    name: '当前机构下所有学生'
  }
]

const formatCatCode = (code: string, orgCode: string) => {
  return code.includes(':') ? code : `${code}:${orgCode}`
}

export default function ({
  code: orgCode,
  onEnterView,
  onCheck,
  selected,
  categories,
  name: orgName = '',
  clazzsCount,
  postionsCount,
  breadcrumbs,
  onBreadClick,
  viewType,
  disabledPermission,
  nodeType: propNodeType = 'org'
}: IOrgViewProps) {
  const handleChangeView = (code: string, viewType: string, nodeType: string, name?: string) => {
    if (typeof onEnterView === 'function') {
      onEnterView({ code: code, name: name, nodeType: nodeType }, viewType)
    }
  }

  const lastBreadcrumbsIndex = React.useMemo(() => (breadcrumbs?.length || 0) - 1, [breadcrumbs?.length])
  const handleBreadcrumbClick = React.useCallback(
    (i: number, d: breadcrumbsDataItem) => {
      if (typeof onBreadClick === 'function') {
        onBreadClick(i, d)
      }
    },
    [onBreadClick]
  )
  return (
    <Box>
      <Box sx={{ p: 1, overflowX: 'auto' }} className='flex items-center'>
        {/* <IconLocation /> */}
        {breadcrumbs?.map(({ id, name }, i) => (
          <React.Fragment key={i}>
            <Typography
              className='shrink-0'
              nowrap
              onClick={i === lastBreadcrumbsIndex ? void 0 : () => handleBreadcrumbClick(i, { id, name })}
              sx={{ color: i === lastBreadcrumbsIndex ? 'text.primary' : void 0 }}
              variant='subtitle2'
            >
              {name}
            </Typography>
            {i !== lastBreadcrumbsIndex && (
              <Typography variant='subtitle2' sx={{ mx: 0.5 }}>
                /
              </Typography>
            )}
          </React.Fragment>
        ))}
      </Box>
      <Box sx={{ p: 1.5 }}>
        {viewType === 'pos' ? (
          <PositionList
            viewType={viewType}
            disabledPermission={disabledPermission}
            selected={selected}
            onCheck={onCheck}
            orgCode={orgCode}
            onEnterView={handleChangeView}
            nodeType={propNodeType}
          />
        ) : (
          <OrgList
            viewType={viewType}
            disabledPermission={disabledPermission}
            selected={selected}
            onCheck={onCheck}
            orgCode={orgCode}
            onEnterView={handleChangeView}
            nodeType={propNodeType}
          />
        )}
      </Box>
    </Box>
  )
}

export const InfiniteScrollContent = ({ hasMore }: { hasMore?: boolean }) => {
  return (
    <>
      {hasMore ? (
        <>
          <DotLoading />
        </>
      ) : null}
    </>
  )
}
