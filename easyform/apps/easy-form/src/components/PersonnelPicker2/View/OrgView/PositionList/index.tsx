import React from 'react'
import { Box, styled } from '@mui/system'
import { Button, Tabs, DotLoading, InfiniteScroll, Avatar, SearchBar } from 'antd-mobile'
import List from '../../../List'
import { Collapse, TabPanel, Typography } from '@/components'
import { useEasyFormRequest, useBaseRequest } from '@/hooks'
import { loadOrg, loadUser } from '../../../api2'
import { loadPosition } from '../../../api'
import { IconOrg, IconLevel, IconPosition } from '../../../icon'
import { InfiniteScrollContent } from '../index'

const ServerDataNodeTypeMap: any = {
  class: 'clazz',
  position: 'pos'
}
const formatNodeType = (originNodeType?: string) => {
  return originNodeType ? ServerDataNodeTypeMap[originNodeType] || originNodeType : void 0
}

const StyleIconLevel = styled(IconLevel)(({ theme }) => ({
  color: theme.palette.primary.main,
  marginRight: 4
}))

const StyledButton = styled(Button)({
  '& span': {
    display: 'flex',
    alignItems: 'center',
    fontSize: 12
  }
})

export const BtnEnter = ({ onClick, text = '进入下级', ...other }: any) => {
  return (
    <StyledButton
      size='small'
      className='flex items-center shrink-0'
      onClick={onClick}
      fill='none'
      color='primary'
      {...other}
    >
      <StyleIconLevel width='14px' height='14px' />
      {text}
    </StyledButton>
  )
}

interface IOrgListProps {
  orgCode?: string
  viewType?: any
  onEnterView: (orgCode: string, viewType: string, nodeType: string, orgName?: string) => void
  onCheck?: (type: string, itemData: any, checked?: boolean) => void
  selected?: any
  disabledPermission?: boolean
  nodeType: string
}

export default function ({
  orgCode,
  onEnterView,
  onCheck,
  selected,
  disabledPermission,
  viewType,
  nodeType
}: IOrgListProps) {
  const [{ pageNo, pageSize }, setPagination] = React.useState({ pageNo: 1, pageSize: 20 })
  const [hasMore, setHasMore] = React.useState(Boolean(orgCode))
  const [list, setList] = React.useState<any>([])
  const [userList, setuserList] = React.useState<any>([])
  const [keywords, setKeywords] = React.useState<string>()

  const handleCheck = (type: string, itemData: any, checked?: boolean) => {
    if (typeof onCheck === 'function') {
      onCheck(type, itemData, checked)
    }
  }

  const { data, loading } = useBaseRequest(() => loadOrg(viewType, orgCode), {
    manual: false,
    loadingDelay: 200
  })
  const { runAsync: requestUser } = useEasyFormRequest(loadPosition, {
    manual: true
  })
  const loadMoreUser = async () => {
    const _userData = await requestUser(orgCode, { pageNo, pageSize, keyword: keywords, orgCode: orgCode })
    const _hasMore = _userData.total > pageSize * pageNo
    setHasMore(_hasMore)
    setuserList([...userList, ...(_userData.data || [])])
    if (_hasMore) {
      setPagination((prev) => ({ ...prev, pageNo: prev.pageNo + 1 }))
    }
  }
  const handleChangeView = (code: string, nodeType: string, name?: string) => {
    if (typeof onEnterView === 'function') {
      onEnterView(code, viewType, nodeType, name)
    }
  }

  const handleSearch = async (sv: string) => {
    setKeywords(sv)
    const _userData = await requestUser(viewType, nodeType, { pageNo: 1, pageSize: 20, keyword: sv, orgCode: orgCode })
    const _hasMore = _userData.totalNum > pageSize * pageNo
    setHasMore(_hasMore)
    setuserList([...(_userData.items || [])])
    setPagination((prev) => ({ ...prev, pageNo: _hasMore ? 1 + 1 : 1 }))
  }
  const handleClearSearch = async () => {
    setKeywords(void 0)
    const _userData = await requestUser(viewType, nodeType, {
      pageNo: 1,
      pageSize: 20,
      keyword: void 0,
      orgCode: orgCode
    })
    const _hasMore = _userData.totalNum > pageSize * pageNo
    setHasMore(_hasMore)
    setuserList([...(_userData.items || [])])
    setPagination((prev) => ({ ...prev, pageNo: _hasMore ? 1 + 1 : 1 }))
  }

  const filterOrgList = React.useMemo(() => list.filter((i: any) => i.name.includes(keywords || '')), [keywords, list])

  const checkSelected = (code: string, paramNodeType?: string) => {
    const _nodeType = paramNodeType || nodeType
    // _nodeType = ServerDataNodeTypeMap[_nodeType] || _nodeType
    if (['student', 'jzg'].includes(viewType)) {
      return !!(selected && selected[viewType]
        ? selected[viewType][_nodeType]?.find((n: any) => n.code === code)
        : false)
    } else {
      return !!(selected ? selected[viewType]?.find((n: any) => n.code === code) : false)
    }
  }
  React.useEffect(() => {
    if (data) {
      setList(data.items || [])
    }
  }, [data])
  return (
    <Box>
      <Box>
        <SearchBar onSearch={handleSearch} onClear={handleClearSearch} placeholder='请输入关键字搜索' />
      </Box>
      {loading ? (
        <Box className='flex justify-center' sx={{ p: 2 }}>
          <DotLoading />
        </Box>
      ) : (
        <List>
          {filterOrgList.length === 0 && userList.length === 0 ? (
            <Typography sx={{ mt: 2 }} align='center' variant='subtitle2'>
              暂无数据
            </Typography>
          ) : (
            <>
              {filterOrgList.map((l: any) => (
                <List.Item
                  // checked={checkSelected(l.code, formatNodeType(l.nodeType))}
                  // onCheck={(c) => {
                  //   handleCheck(viewType, { code: l.code, name: l.name, nodeType: formatNodeType(l.nodeType) }, c)
                  // }}
                  key={l.code}
                  prefix={<IconOrg style={{ color: '#999' }} width='16px' height='16px' />}
                  checkable={false}
                  title={l.name}
                  arrow={
                    <BtnEnter
                      disabled={checkSelected(l.code, formatNodeType(l.nodeType))}
                      onClick={() => handleChangeView(l.code, formatNodeType(l.nodeType), l.name)}
                    />
                  }
                />
              ))}
              {userList.map((l: any) => (
                <List.Item
                  checked={checkSelected(l.code, formatNodeType(l.nodeType || 'user'))}
                  onCheck={(c) => {
                    handleCheck(
                      viewType,
                      { code: l.code, name: l.name, nodeType: formatNodeType(l.nodeType || 'user') },
                      c
                    )
                  }}
                  prefix={<IconPosition style={{ color: '#999' }} width='16px' height='16px' />}
                  // prefix={<Avatar src='' style={{ '--size': '24px', '--border-radius': '50%' }} />}
                  key={l.code}
                  checkable
                  title={`${l.name} (${l.code})`}
                />
              ))}
            </>
          )}
          <InfiniteScroll hasMore={hasMore} loadMore={loadMoreUser}>
            <InfiniteScrollContent hasMore={hasMore} />
          </InfiniteScroll>
        </List>
      )}
    </Box>
  )
}
