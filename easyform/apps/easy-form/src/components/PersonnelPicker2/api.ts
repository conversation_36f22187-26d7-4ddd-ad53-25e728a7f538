const api = {
  org: '/cuc',
  position: '/cuc/position',
  clazz: '/cuc/clazz',
  user: '/cuc/user',
  group: '/cuc/group',
  positionSet: '/cuc/positionSet'
}

export function loadUser(params: any, disabledPermission?: boolean) {
  return {
    url: api.user,
    method: 'get',
    headers: { isTicket: disabledPermission ? disabledPermission : void 0 },
    params: params
  }
}

export function loadClazz(orgCode: string, params: any) {
  return {
    url: `${api.clazz}/${orgCode}`,
    method: 'get',
    params: params
  }
}

export function loadGroup(params: any) {
  return {
    url: `${api.group}`,
    method: 'get',
    params: params
  }
}
export function loadPositionSet(params: any) {
  return {
    url: `${api.positionSet}`,
    method: 'get',
    params: params
  }
}

export function loadPosition(orgCode: string, params: any) {
  return {
    url: `${api.position}/${orgCode}`,
    method: 'get',
    params: params
  }
}

export function loadOrg(orgCode?: string, disabledPermission?: boolean) {
  return {
    url: api.org,
    method: 'GET',
    headers: {
      isTicket: disabledPermission ? disabledPermission : void 0
    },
    params: {
      orgCode: orgCode
    }
  }
}
