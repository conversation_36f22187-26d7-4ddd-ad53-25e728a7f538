import React from 'react'
import { Box, styled } from '@mui/system'
import { <PERSON><PERSON>, <PERSON><PERSON>, <PERSON>wi<PERSON>, DotLoading, Avatar, Ellipsis } from 'antd-mobile'
import { Dropdown } from 'antd'
import { DownOutlined } from '@ant-design/icons'
import { LeftOutline as IconBack, CloseOutline as IconClose } from 'antd-mobile-icons'
import { useBoolean, useMemoizedFn } from 'ahooks'
import produce from 'immer'

import { useEasyFormRequest, useBaseRequest } from '@/hooks'
import { Typography, Collapse } from '@/components'
import List from './List'
import {
  OrgView as MyOrgView,
  ListView,
  GroupView,
  PositionsetView,
  ClazzUserView,
  ClazzView,
  breadcrumbsDataItem
} from './View'
import * as Api from './api'
import * as Api2 from './api2'
import { IconOrg, IconPosition, IconClazz, IconRole } from './icon'

import { getFrindlyInfo, transformToList, TextType, Categories } from './util'

const OrgView = React.memo(MyOrgView)

const RootCategory = [
  {
    key: 'org',
    name: '按架构选'
  },
  {
    key: 'org',
    name: '选岗位集'
  },
  {
    key: 'clazz',
    name: '选班级'
  }
]

const RoundRect = styled(Box)(({ theme }) => ({
  display: 'flex',
  justifyContent: 'center',
  alignItems: 'center',
  borderRadius: 8,
  width: 36,
  height: 36,
  backgroundColor: theme.palette.divider
}))
const StyledButton = styled(Button, { shouldForwardProp: (prop) => prop !== 'hasSelected' })<any>(
  ({ theme, hasSelected }) => ({
    fontSize: '14px',
    color: theme.palette.text.secondary
  })
)
const MiniButton = styled(Button)({
  '--adm-font-size-main': '12px',
  padding: '2px 4px'
})

export type CategoryItem = {
  key: string
  label: string
  options?: { name: string; code: string }[]
}

export type ExtendItem = {
  key: string
  label: string
  options: { name: string; code: string }[]
}
interface IPersonalPicker {
  value?: any
  onChange?: (value: any, desc?: string) => void
  text?: string
  renderValue?: (value: any) => any
  categories?: CategoryItem[]
  orgCode?: string
  extra?: any[]
  extend?: {
    categories: CategoryItem[]
    data: Record<string, any>
  }
}

const Views: any = {
  student: OrgView,
  jzg: OrgView,
  org: OrgView,
  pos: OrgView,
  posset: PositionsetView,
  group: GroupView,
  // clazzUser: ClazzUserView,
  clazz: ClazzView
}

interface IHeaderProps {
  closeable?: boolean
  title?: string
  onBack?: () => void
  onClose?: () => void
}
const Header = ({ closeable, title = '', onBack, onClose }: IHeaderProps) => {
  return (
    <Box sx={{ height: '48px', bgcolor: '#FFF', px: 1.5 }} className='flex items-center justify-between'>
      <Box>
        <Button fill='none' size='small' onClick={onBack}>
          <IconBack />
          返回
        </Button>
        {closeable && <IconClose onClick={onClose} style={{ marginLeft: 16 }} />}
      </Box>
      <Typography variant='body2'>添加{title}</Typography>
      <div />
    </Box>
  )
}

const ContentWrapper = styled(Box)({
  height: 'calc(100vh - 100px)',
  overflow: 'auto'
})

const FooterWrapper = styled(Box)(({ theme }) => ({
  display: 'flex',
  alignItems: 'center',
  justifyContent: 'space-between',
  height: '60px',
  width: '100%',
  // borderTop: `1px solid ${theme.palette.divider}`,
  backgroundColor: theme.palette.background.default,
  padding: '0 12px'
}))

const IconType: any = {
  org: <IconOrg width='16px' height='16px' style={{ color: '#999' }} />,
  posset: <IconPosition width='16px' height='16px' style={{ color: '#999' }} />,
  clazz: <IconClazz width='16px' height='16px' style={{ color: '#999' }} />,
  pos: <IconPosition width='16px' height='16px' style={{ color: '#999' }} />,
  cat: <IconRole width='16px' height='16px' style={{ color: '#999' }} />,
  user: <Avatar src='' style={{ '--size': '24px', '--border-radius': '50%' }} />
}

export default React.forwardRef<any, IPersonalPicker>(
  ({ text = '请添加参与者', value, onChange, renderValue, categories = Categories, orgCode, extra, extend }, ref) => {
    const [open, { toggle: toggleOpen }] = useBoolean(false)
    const [openSelected, { toggle: toggleSelected }] = useBoolean(false)
    const swiperRef = React.useRef<any>(null)
    const [extraViews, setExtraViews] = React.useState<any>([])
    const [currentIndex, setCurrentIndex] = React.useState(0)
    /**
     *{
       org:[],
       user:[],
       posset:[],
       clazz:[]
       pos:[]
       cat:[]
     }
     */
    const [selected, setSelected] = React.useState<any>({})

    /**
     * 组织机构面包屑数据
     */
    const [breadcrumbs, setBreadcrumbs] = React.useState<breadcrumbsDataItem[]>([])

    const handleSelected = useMemoizedFn((type: string, itemData: any) => {
      console.log('handleselected:', type, itemData)
      setSelected(
        produce(selected, (draft: any) => {
          if (['jzg', 'student'].includes(type)) {
            const _childType = itemData.nodeType || 'org'
            if (!draft[type]) {
              draft[type] = {
                [_childType]: [itemData]
              }
            } else {
              if (!draft[type][_childType]) {
                draft[type][_childType] = [itemData]
              } else {
                const _existIndex = draft[type][_childType].findIndex((n: any) => n.code === itemData.code)
                if (_existIndex !== -1) {
                  draft[type][_childType].splice(_existIndex, 1)
                } else {
                  draft[type][_childType].push(itemData)
                }
              }
            }
          } else {
            if (!draft[type]) {
              draft[type] = [itemData]
            } else {
              const _existIndex = draft[type].findIndex((n: any) => n.code === itemData.code)
              if (_existIndex !== -1) {
                draft[type].splice(_existIndex, 1)
              } else {
                draft[type].push(itemData)
              }
            }
          }
        })
      )
    })
    /**
     * 加载初始数据
     */
    // const { data, loading } = useEasyFormRequest(() => Api.loadOrg(void 0, disabledPermission), {
    //   ready: open,
    //   loadingDelay: 200
    // })

    const handleEnterView = useMemoizedFn((viewType: string, extra: any) => {
      console.log('enter view:', viewType, extra)
      setExtraViews((prev: any) =>
        produce(prev, (draft: any) => {
          draft.push({ viewType: viewType, extra: extra })
        })
      )
      setTimeout(() => {
        swiperRef.current.swipeNext()
      }, 100)
    })
    const handleDestroyView = useMemoizedFn((destroyAll?: boolean, targetIndex?: number) => {
      if (destroyAll) {
        swiperRef.current.swipeTo(0)
        setBreadcrumbs([])
        setTimeout(() => {
          setExtraViews([])
        }, 500)
      } else {
        if (targetIndex === void 0) {
          const lastIndex = extraViews.length - 1
          setBreadcrumbs((prev) =>
            produce(prev, (draft) => {
              draft.splice(prev.length - 1, 1)
            })
          )
          // if (extraViews[lastIndex].viewType === 'org') {
          //   setBreadcrumbs((prev) =>
          //     produce(prev, (draft) => {
          //       draft.splice(prev.length - 1, 1)
          //     })
          //   )
          // }
          swiperRef.current.swipePrev()
          setTimeout(() => {
            setExtraViews((prev: any) =>
              produce(prev, (draft: any) => {
                draft.splice(lastIndex, 1)
              })
            )
          }, 500)
        } else {
          const len = breadcrumbs.length - 1 - targetIndex
          setBreadcrumbs((prev) =>
            produce(prev, (draft) => {
              draft.splice(targetIndex + 1, len)
            })
          )
          swiperRef.current.swipeTo(targetIndex)
          setTimeout(() => {
            setExtraViews((prev: any) =>
              produce(prev, (draft: any) => {
                /**
                 * extara views 额外的view
                 */
                draft.splice(targetIndex, len)
              })
            )
          }, 500)
        }
      }
    })
    const handleIndexChange = useMemoizedFn((index: number) => {
      console.log('===change:', index)
      setCurrentIndex(index)
    })
    const handleBack = useMemoizedFn(() => {
      if (currentIndex === 0) {
        toggleOpen()
      } else {
        handleDestroyView()
      }
    })

    const handleOk = useMemoizedFn((e: React.MouseEvent) => {
      e.stopPropagation()
      console.log(selected)
      if (typeof onChange === 'function') {
        onChange(selected, selectedText)
      }
      toggleOpen()
    })

    const selectedText = React.useMemo(() => getFrindlyInfo(selected), [selected])

    const selectedList = React.useMemo(() => transformToList(selected), [selected])

    console.log(selectedList, selected)

    React.useEffect(() => {
      if (value) {
        setSelected(value)
      }
    }, [value])

    React.useImperativeHandle(ref, () => ({
      toggle: (category?: any) => {
        setCurrentCategory(category || { key: 'student', label: '学生' })
        setBreadcrumbs([])
        toggleOpen()
      }
    }))

    const handleEnter = useMemoizedFn((target, viewType) => {
      handleEnterView(viewType, target)
      if (['student', 'jzg', 'pos'].includes(viewType)) {
        setTimeout(() => {
          setBreadcrumbs(
            produce(breadcrumbs, (draft) => {
              draft.push({ id: target.code, name: target.name })
            })
          )
        }, 300)
      }
    })

    const handleBreadClick = useMemoizedFn((index: number) => {
      handleDestroyView(false, index)
    })

    // const [initOrg, initManageClass] = React.useMemo(
    //   () => [data?.data?.orgs, data?.data?.manageClasses],
    //   [data?.data?.manageClasses, data?.data?.orgs]
    // )

    // React.useEffect(() => {
    //   if (initOrg) {
    //     setBreadcrumbs([{ id: initOrg[0].code, name: initOrg[0].name }])
    //   }
    // }, [initOrg])

    const [currentCategory, setCurrentCategory] = React.useState<any>()
    const handleAdd = (item: any) => {
      console.log(item)
      setCurrentCategory(item)
      setBreadcrumbs([])
      toggleOpen()
    }
    return (
      <>
        {renderValue ? (
          renderValue(selected)
        ) : (
          <Box sx={{ width: '100%', display: 'grid', gridTemplateColumns: '1fr auto', gap: '8px' }}>
            <Box
              onClick={toggleSelected}
              sx={{
                border: '1px solid',
                borderColor: 'divider',
                borderRadius: '4px',
                display: 'flex',
                flexWrap: 'wrap',
                alignItems: 'center',
                p: 0.5
              }}
            >
              <Typography sx={{ color: '#999' }} variant='body2'>
                {selectedText || text}
              </Typography>
            </Box>
            <Dropdown menu={{ items: categories, onClick: handleAdd }} trigger={['click']}>
              <Button style={{ color: '#999' }} size='small'>
                添加 <DownOutlined />
              </Button>
            </Dropdown>
          </Box>
          // <Box>
          //   <StyledButton onClick={toggleOpen}>
          //     <Ellipsis content={selectedText || text} />
          //   </StyledButton>
          // </Box>
        )}
        <Popup destroyOnClose visible={open}>
          <Box className='h-screen relative'>
            <Header
              title={TextType[currentCategory?.key]}
              onClose={() => handleDestroyView(true)}
              closeable={currentIndex > 1}
              onBack={handleBack}
            />
            <ContentWrapper>
              <Swiper
                style={{ height: '100%' }}
                onIndexChange={handleIndexChange}
                ref={swiperRef}
                loop={false}
                allowTouchMove={false}
                indicator={() => null}
              >
                <Swiper.Item>
                  <Box className='h-full overflow-auto'>
                    {currentCategory ? (
                      Views[currentCategory.key] ? (
                        React.createElement(Views[currentCategory.key], {
                          breadcrumbs,
                          viewType: currentCategory.key,
                          selected,
                          onCheck: handleSelected,
                          onEnterView: handleEnter,
                          onBreadClick: handleBreadClick,
                          code: orgCode
                        })
                      ) : (
                        <ListView
                          dataset={extend?.data ? extend.data[currentCategory?.key] : []}
                          selected={selected}
                          onCheck={handleSelected}
                          viewType={currentCategory.key}
                        />
                      )
                    ) : null}
                  </Box>
                </Swiper.Item>
                {extraViews.map(({ viewType, extra }: any, i: number) => (
                  <Swiper.Item key={i}>
                    <Box className='h-full overflow-auto'>
                      {React.createElement(Views[viewType], {
                        ...extra,
                        onEnterView: handleEnter,
                        selected: selected,
                        viewType: viewType,
                        onCheck: handleSelected,
                        breadcrumbs: breadcrumbs,
                        onBreadClick: handleBreadClick
                      })}
                    </Box>
                  </Swiper.Item>
                ))}
              </Swiper>
            </ContentWrapper>
            <FooterWrapper
              onClick={() => {
                selectedList.length > 0 && toggleSelected()
              }}
            >
              <Typography variant='subtitle2'>{selectedText ? `已选择：${selectedText}` : '请选择'}</Typography>
              <Button style={{ flexShrink: 0 }} onClick={handleOk} color='primary' size='small'>
                确定
              </Button>
              {/* <Button onClick={handleOk} color='primary' size='small' disabled={!selectedText}>
                确定
              </Button> */}
            </FooterWrapper>
          </Box>
        </Popup>
        <Popup
          onMaskClick={toggleSelected}
          bodyStyle={{ height: '80vh', borderTopLeftRadius: '8px', borderTopRightRadius: '8px' }}
          visible={openSelected}
        >
          <Box className='flex items-center justify-between' sx={{ height: 32, px: 1.5 }}>
            <div />
            <Typography variant='subtitle1'>已选择</Typography>
            <IconClose onClick={toggleSelected} />
          </Box>
          <Box className='overflow-auto' sx={{ height: 'calc(100% - 32px)', px: 1.5 }}>
            <List>
              {selectedList.length > 0 ? (
                selectedList.map(({ code, name, type, nodeType }) => (
                  <List.Item
                    key={code}
                    title={`${name}(${TextType[type || nodeType]})`}
                    prefix={IconType[nodeType || type]}
                    arrow={
                      <MiniButton
                        onClick={() => handleSelected(type, { code, name, nodeType })}
                        fill='outline'
                        color='danger'
                        size='mini'
                      >
                        移除
                      </MiniButton>
                    }
                  />
                ))
              ) : (
                <Typography variant='subtitle2' align='center' sx={{ my: 3 }}>
                  您的选择列表为空
                </Typography>
              )}
            </List>
          </Box>
        </Popup>
      </>
    )
  }
)
