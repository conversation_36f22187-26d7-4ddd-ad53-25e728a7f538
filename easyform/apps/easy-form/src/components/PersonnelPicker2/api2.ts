const orgApi: any = {
  student: {
    url: '/selector/org/xs_orgTree',
    params: {
      category: 'xs_ss'
    }
  },
  jzg: {
    url: '/selector/org/orgTree',
    params: {
      category: 'jzg_ss'
    }
  },
  pos: {
    url: '/selector/org/position',
    params: {
      category: 'org'
    }
  }
}
/**
 * 实际为members,并非仅仅是用户
 */
const userApi: any = {
  student: {
    url: '/selector/member/xs_ss',
    params: {}
  },
  jzg: {
    url: '/selector/member/jzg_ss',
    params: {}
  },
  pos: {
    url: '/selector/org/position',
    params: {
      category: 'org'
    }
  }
}

const OrgCodeKeyMap: any = {
  org: 'orgCode',
  class: 'claCode',
  clazz: 'claCode',
  student: 'orgCode',
  jzg: 'parent',
  pos: 'parent'
}

const MemberCodeKeyMap: any = {
  org: 'orgCode',
  class: 'claCode',
  clazz: 'claCode',
  student: 'orgCode',
  jzg: 'parent',
  pos: 'parent'
}
export function loadOrg(type: string, orgCode?: string) {
  return {
    url: orgApi[type]?.url,
    method: 'get',
    params: {
      ...orgApi[type]?.params,
      [OrgCodeKeyMap[type]]: orgCode
    }
  }
}

export function loadUser(type: string, nodeType: string, params: any = {}) {
  const { orgCode, ...other } = params
  return {
    url: userApi[type]?.url,
    method: 'get',
    params: {
      ...userApi[type]?.params,
      ...other,
      [OrgCodeKeyMap[type === 'pos' ? type : nodeType]]: orgCode
    }
  }
}

export function loadPosition(parent?: string) {
  return {
    url: '/selector/org/position',
    method: 'GET',
    params: {
      parent: parent
    }
  }
}
