import React, { useState, useEffect } from 'react'
import { useTheme, styled, Box } from '@mui/system'
import { Popup, Space, Button, Selector } from 'antd-mobile'
import Typography from '@/components/Typography'
import { CheckOutline as IconCheck } from 'antd-mobile-icons'

const StyledPopup = styled(Popup)(() => ({
  '& .adm-popup-close-icon': {
    right: 'auto'
  }
}))

const StyledDiv = styled(Selector)(() => ({
  padding: '0 6%',
  width: '100%',

  paddingBottom: '67px',
  '.adm-space-horizontal': {
    display: 'grid',
    justifyContent: 'space-between',
    gridTemplateColumns: ' 22% 22% 22% 22%'
  },

  '& .adm-selector-item': {
    width: '100%',
    height: '100%',
    padding: '0px',
    display: 'flex',
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: '#fff'
  },
  '.adm-space-item': {
    marginRight: '0px',
    position: 'relative',
    height: '60px',
    display: 'flex',
    justifyContent: 'center',
    alignItems: 'center',
    border: '1px solid rgb(223,223,223)',
    borderRadius: 5,
    marginBottom: 10,
    paddingBottom: '0 !important',
    fontSize: 12,
    color: 'rgb(21,21,28)'
  }
  // '.adm-space-item:nth-child(4n)':{
  //     marginRight:'0 !important'
  // }
}))

const StyledTypography = styled(Typography)(() => ({
  padding: '12px 0 58px',
  font: '14px/20px ""',
  textAlign: 'center',
  fontWeight: 'bold'
}))

export default function TypeCom(props: any) {
  const { visible, CloseType, typeDatas, types, ChangeType, AllType } = props
  const [isvisible, setIsvisible] = useState(visible)
  const [typeData, setTypeData] = useState(typeDatas)
  const [type, setType] = useState<any>([AllType])

  useEffect(() => {
    setIsvisible(visible)
  }, [visible])
  const Close = () => {
    setIsvisible(false)
    CloseType(false)
    ChangeType(type)
  }

  useEffect(() => {
    console.log(types, 'type')
    setType(types)
  }, [types])
  const changeOption = (arr: any[]) => {
    const result = getArrDifference(arr, type)
    console.log(arr.length > type.length, result)

    if (arr.length > type.length) {
      if (result.includes(AllType)) {
        console.log(111)
        setType([AllType])
      } else {
        console.log(
          arr?.filter((item) => item == AllType),
          'arr?.filter((item)=>item==0)'
        )
        const resultnew = arr?.filter((item) => item !== AllType)
        setType(JSON.parse(JSON.stringify(resultnew)))
      }
    } else {
      setType(arr)
    }

    // if(arr.includes(0)&&){
    // setType(arr?.map((item)=>item!==0))

    // }
  }
  const getArrDifference = (arr1: any, arr2: any) => {
    return arr1.concat(arr2).filter((v: any, i: any, arr: any[]) => {
      return arr.indexOf(v) === arr.lastIndexOf(v)
    })
  }
  useEffect(() => {
    console.log(type, 'type')
  }, [type])

  return (
    <StyledPopup visible={isvisible} showCloseButton closeOnMaskClick onClose={Close}>
      <StyledTypography sx={{ color: '#1C1C24' }}>选择筛选项</StyledTypography>
      {/* <StyledDiv > */}
      <StyledDiv
        options={typeData}
        defaultValue={type}
        value={type}
        multiple={true}
        onChange={(arr, extend) => changeOption(arr)}
      />
      {/* {typeDatas?.map((item: any, i: any) => {
          return <StyledType key={i} selected={type.includes(item.id)} onClick={() => changType(item?.id)}>{item?.name}<SelectedTag selected={type.includes(item.id)}>
          <IconCheck />
        </SelectedTag></StyledType>
        })} */}
      {/* </StyledDiv> */}
    </StyledPopup>
  )
}
