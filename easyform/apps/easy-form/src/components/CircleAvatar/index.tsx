import { styled } from '@mui/system'
import { Avatar } from 'antd-mobile'
const CircleAvatar = styled(Avatar, { shouldForwardProp: (prop) => prop !== 'size' && prop !== 'borderColor' })<{
  size?: string | number
  borderColor?: string
}>(({ size = '56px', borderColor }) => ({
  '--size': size,
  borderRadius: '100%',
  border: borderColor ? `1px solid ${borderColor}` : void 0
}))

export default CircleAvatar
