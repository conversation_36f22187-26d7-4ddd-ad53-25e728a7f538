import React from 'react'
import tinycolor from 'tinycolor2'
import { Box, styled } from '@mui/system'
import { Typography, CircleAvatar } from '@/components'
import { formatDate } from '@/utils'

const StatusMap: any = {
  1: {
    text: '通过',
    color: '#4caf50'
  },
  9: {
    text: '已提交',
    color: '#EE8152'
  },
  0: {
    text: '未通过',
    color: '#f44336'
  },
  2: {
    text: '已退回',
    color: '#EF9E26'
  }
}
type DataItem = {
  title: string
  avatar?: string
  name: string
  date: string
  status: 1 | 0 | undefined
}
export type HistoryProps = {
  data: DataItem[]
}

const ItemRoot = styled(Box)(({ theme }) => ({
  display: 'grid',
  gridTemplateColumns: '18px 1fr',
  columnGap: 12
}))

const Span = styled('div')({
  display: 'flex',
  borderRadius: '50%',
  width: 14,
  height: 14,
  alignItems: 'center',
  justifyContent: 'center'
})

const Circle = ({ color = '#ddd' }: any) => {
  return (
    <Span sx={{ p: '1px', width: '18px', height: '18px', border: `1px solid ${color}` }}>
      <Span sx={{ bgcolor: color }} />
    </Span>
  )
}
const Line = styled('div')({
  width: 0,
  borderLeft: '1px dashed #ddd',
  flexGrow: 1
})

const ItemContent = styled(Box, { shouldForwardProp: (prop) => prop !== 'color' })<any>(({ color, theme }) => ({
  padding: 16,
  borderRadius: 8,
  border: color ? `1px solid ${color}` : void 0,
  backgroundColor: color ? tinycolor(color).setAlpha(0.2).toRgbString() : theme.palette.background.default
}))

const Tag = styled(Box)({
  padding: '2px 8px',
  borderRadius: '6px',
  color: '#FFF',
  fontSize: 12
})

const Item = ({ isFirst, isLast, title, name, status, date }: any) => {
  const [color, statusText] = React.useMemo(() => [StatusMap[status]?.color, StatusMap[status]?.text], [status])
  return (
    <ItemRoot>
      <Box className='flex flex-col items-center'>
        {isFirst ? <Box sx={{ height: '16px' }} /> : <Line sx={{ height: '16px', flexGrow: 0 }} />}
        <Circle color={color} />
        <Line />
      </Box>
      <ItemContent sx={{ mb: isLast ? 0 : 2 }} color={color}>
        <Box sx={{ mb: 2 }} className='flex items-center justify-between'>
          <Typography sx={{ fontSize: 14, fontWeight: 500 }}>{title}</Typography>
          <Tag sx={{ bgcolor: color || '#999' }}>{statusText || '待审批'}</Tag>
        </Box>
        <Box className='flex items-center justify-between'>
          <Box className='flex items-center'>
            <CircleAvatar src='' size={'24px'} />
            <Typography nowrap variant='subtitle1' sx={{ ml: 1 }}>
              {name}
            </Typography>
          </Box>
          <Typography variant='subtitle2' sx={{ ml: 1 }}>
            {date ? formatDate(date, 'MM-DD HH:mm') : ''}
          </Typography>
        </Box>
      </ItemContent>
    </ItemRoot>
  )
}

export default function ({ data }: HistoryProps) {
  const lastIndex = React.useMemo(() => data.length - 1, [data.length])
  return (
    <Box>
      {data.map(({ title, name, status, date }, i) => (
        <Item
          key={i}
          isFirst={i === 0}
          isLast={i === lastIndex}
          title={title}
          name={name}
          date={date}
          status={status}
        />
      ))}
    </Box>
  )
}
