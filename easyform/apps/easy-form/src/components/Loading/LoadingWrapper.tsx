import React from 'react'
import { Box } from '@mui/system'
import { SpinLoading } from 'antd-mobile'
import Typography from '../Typography'

export interface LoadingWrapperProps {
  loading?: boolean
  loadingText?: string
  height?: number | string
  width?: number | string
  sx?: Record<string, any>
}

const Loading = ({
  loadingText,
  width = '100%',
  height = '100%',
  sx
}: Pick<LoadingWrapperProps, 'loadingText' | 'width' | 'height' | 'sx'>) => {
  return (
    <Box className='flex items-center justify-center flex-col' sx={{ width: width, height: height, ...sx }}>
      <SpinLoading />
      {loadingText && (
        <Typography sx={{ mt: 1 }} variant='subtitle2'>
          {loadingText}
        </Typography>
      )}
    </Box>
  )
}

const LoadingWrapper: React.FC<LoadingWrapperProps> = ({
  width = '100%',
  height = '100%',
  sx,
  loading,
  loadingText,
  children
}) => {
  return <>{loading ? <Loading width={width} height={height} sx={sx} loadingText={loadingText} /> : children}</>
}

export default LoadingWrapper
