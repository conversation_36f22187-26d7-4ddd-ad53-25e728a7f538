import React from 'react'
import { Box } from '@mui/system'
import { SpinLoading } from 'antd-mobile'
import Typography from '../Typography'

export interface LoadingProps {
  loading?: boolean
  loadingText?: string
  sx?: Record<string, any>
}

const Loading = ({
  loadingText,

  sx
}: Pick<LoadingProps, 'loadingText' | 'sx'>) => {
  return (
    <Box className='flex items-center justify-center flex-col' sx={{ ...sx }}>
      <SpinLoading color='primary' />
      {loadingText && (
        <Typography sx={{ mt: 1 }} variant='subtitle2'>
          {loadingText}
        </Typography>
      )}
    </Box>
  )
}

const DataLoading: React.FC<LoadingProps> = ({ sx, loading, loadingText }) => {
  return (
    <Box
      className='absolute top-0 left-0 right-0 bottom-0 flex justify-center items-center'
      sx={{ bgcolor: 'rgba(255,255,255,0.1)' }}
    >
      <Loading />
    </Box>
  )
}

export default DataLoading
