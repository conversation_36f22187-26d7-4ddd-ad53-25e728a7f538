import React from 'react'
import { Box, styled } from '@mui/system'
import { Typography } from '@/components'
import { DownFill } from 'antd-mobile-icons'
export interface ITabPanelProps {
  [key: string]: any
}
const StyledTable = styled('table')<any>(({ theme }) => ({
  borderCollapse: 'collapse',
  border: 'none',
  width: '100%',
  background: '#fff',
  th: {
    background: 'rgb(236,237,242)',
    fontSize: 'var(--adm-font-size-6)',
    lineHeight: 1.4,
    textAlign: 'left'
  },
  td: {
    fontSize: 'var(--adm-font-size-6)'
    // color: ' rgba(0,0,0,0.5)'
  },
  'td,th': {
    border: '1px solid #ddd',
    padding: '10px 14px'
  }
}))
const StyleDiv = styled('div')<any>(({ theme }) => ({
  background: '#fff',
  marginBottom: '10px',
  padding: '20px 16px'
}))
const StyleThSort = styled('div')<any>(({ theme }) => ({
  display: 'flex',
  flexDirection: 'column',
  float: 'right'
}))
const StyledDownFill = styled(DownFill)<any>(({ theme }) => ({
  color: 'rgb(183,184,189)',
  fontSize: 10
}))
export default function Table(props: ITabPanelProps) {
  const { columns, dataSource, index, ...other } = props
  const sortFun = (diff: any) => {}
  return (
    <StyleDiv>
      <Typography sx={{ fontWeight: 'bold', marginBottom: '14px', fontSize: 'var(--adm-font-size-10)' }}>
        {index + 1}.{dataSource?.title}
        <span style={{ color: '#ccc', fontSize: '14px', marginLeft: '6px' }}>
          {dataSource?.type?.includes('Radio') ? '[单选]' : '[多选]'}
        </span>
      </Typography>
      <StyledTable>
        {columns?.map((item: any, i: any) => {
          return (
            <th key={item?.key}>
              {item?.title}
              {item?.sort ? (
                <StyleThSort>
                  <StyledDownFill
                    style={{ transform: 'rotate(180deg)', marginBottom: '-1.5px' }}
                    onClick={sortFun('order')}
                  />
                  <StyledDownFill style={{ marginTop: '-1.5px' }} onClick={sortFun('down')} />
                </StyleThSort>
              ) : null}
            </th>
          )
        })}
        {dataSource?.dimension?.map((item: any, i: any) => {
          return (
            <tr key={i}>
              {Object.entries(dataSource)?.map((it: any[], ii: any) => {
                console.log(it, ii, 'itemi')
                if (it[0] == 'dimension' || it[0] == 'data') {
                  return <td key={i + ii}>{it[1][i]}</td>
                }
                // return <td key={i + ii}>{it[1][i]}</td>
              })}
            </tr>
          )
        })}
      </StyledTable>
    </StyleDiv>
  )
}
