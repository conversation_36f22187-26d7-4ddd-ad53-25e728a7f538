import React from 'react'
import { Button, ButtonProps, Space, Popover, PopoverProps, PopoverMenuProps } from 'antd-mobile'
import { DownFill, DownFill as IconDown } from 'antd-mobile-icons'
import { Typography } from '@/components'

export type ActionMenuProps = {
  title?: string
} & Omit<ButtonProps, 'children'> &
  Pick<PopoverMenuProps, 'actions' | 'onAction'>

export default React.forwardRef<HTMLButtonElement, ActionMenuProps>(({ title, actions, onAction, ...other }, ref) => {
  const [activeMenu, setActiveMenu] = React.useState(actions[0])
  const handleItemAction = (item: any) => {
    setActiveMenu(item)
    if (typeof onAction === 'function') {
      onAction(item)
    }
  }
  return (
    <Popover.Menu actions={actions} onAction={handleItemAction} trigger='click' placement='bottom-start'>
      <Typography color='primary' {...other}>
        {activeMenu.text}
        <IconDown style={{ marginLeft: 4 }} />
      </Typography>
    </Popover.Menu>
  )
})
