/* eslint-disable */
// @ts-nocheck
import { ITaskCheckData, ITaskItem, ITaskProcess } from '@/interfaces/task'
import { createFormTask, updateFormTask } from '@/api/task'
import { checkTask } from '@/api/ezform'
import { YB_EZFORM_URL } from '@/commons/service'
import { css } from '@emotion/css'
import { BetaSchemaForm, ProFormInstance } from '@sui/pro-components'
import { ProlayoutIframe, useIsMobile } from '@sui/pro-layout'
import { CancelToken, request, useContextState, useNavigate, utils } from '@sui/runtime'
import { Button, Drawer, Space, message, notification, Form } from 'antd'
import React, {
  Fragment,
  ReactNode,
  useCallback,
  useEffect,
  useMemo,
  useImperativeHandle,
  useRef,
  useState
} from 'react'
import TaskProcessPicker, { AssignmentRangeType, CreatorRoleType } from './components'
import moment from 'moment'
import { TASK_PROCESS_OPTIONS } from '@/commons/task'
import { Input } from 'antd-mobile'

/**
 * 处理表单定义
 * @param data
 * @returns
 */
const parsePayloadString = (data?: any) => {
  return typeof data === 'object' && data ? JSON.stringify(data.define || data) : ''
}

export const processOptions: ITaskProcess[] = TASK_PROCESS_OPTIONS.map((v) => {
  return {
    id: v.value,
    ...v
  }
})

const cls = css({
  // '.ant-checkbox-group': {
  //   display: 'block'
  // },
  // '.ant-checkbox-wrapper': {
  //   display: 'flex',
  //   margin: 0,
  //   padding: '6px 0'
  // }
})

interface ITaskCreateDrawerProps {
  /**
   * 任务创建者ID
   */
  creatorId?: string | number
  /**
   * 任务创建者姓名
   */
  creatorName?: string
  form?: any
  value?: any
  onTargetDetailChange?: (value: any) => any
  /**
   * 创建者所属部门
   */
  creatorOrg?: string

  /**
   * 打开快速创建面板
   */
  open?: boolean

  /**
   * 创建面板标题
   */
  title?: any

  /**
   * 创建成功后是否跳转到详情页面
   */
  jumpToDetail?: boolean

  /**
   * 跳转时带url参数来来自哪里
   */
  jumpFrom?: string

  /**
   * 面板宽度
   */
  width?: number | string

  hiddenCreateButton?: boolean
  taskTitle?: boolean

  /**
   * 创建成功回调
   * @param data
   * @returns
   */
  onSuccess?: (data: ITaskItem) => false | void

  /**
   * 关闭回调
   * @returns
   */
  onClose?: () => void

  /**
   * 修改任务
   */
  task?: ITaskItem

  /**
   * 是否创建表单
   */
  createForm?: boolean

  /**
   * 创建任务回调
   * @description 当返回false时，阻止内部请求创建接口
   * @param values 创建的任务数据
   * @returns
   */
  onCreateTask?: (values: any) => any

  /**
   * 确定按钮文本
   */
  confirmText?: ReactNode
  onChange?: (value: any) => any
  user?: any
}
export const updateTaskData = (values: any, assignmentRangeRef) => {
  console.log('values===',values)
  if (!values || !assignmentRangeRef) return
  const process = values.process || []
  const processVals = processOptions.filter((opt) => process.indexOf(opt.id) > -1)
  values.process = processVals
    .map((opt) => {
      return assignmentRangeRef.current?.[opt.id]
    })
    .filter(Boolean)

  console.warn(values)
  if (moment.isMoment(values.startTime)) {
    values.startTime = values.startTime.minute(0).second(0).millisecond(0).toISOString()
  }
  if (moment.isMoment(values.endTime)) {
    values.endTime = values.endTime.minute(0).second(0).millisecond(0).toISOString()
  }
  // return {
  //   ...task,
  //   ...values,
  //   //   creatorId: creatorId,
  //   //   creatorName: creatorName,
  //   //   creatorOrg: values.creatorOrg || orgName,
  //   //   payload: parsePayloadString(formData) || _task.payload,
  // };
  return values.process
}

export default React.forwardRef(
  function TaskCreateDrawer2(props: ITaskCreateDrawerProps, ref) {
    // const { context } = useContextState<{
    //   admin?: boolean;
    //   supper?: boolean;
    //   fdy?: boolean;
    //   departAdmin?: boolean;
    //   system?: boolean;
    //   xgAdmin?: boolean;
    // }>();
    const {
      user,
      creatorId = user.id,
      creatorName = user.displayName,
      creatorOrg,
      createForm,
      open,
      title = '快速创建任务',
      task,
      onSuccess,
      onCreateTask,
      onClose,
      jumpToDetail,
      taskTitle = '派发过程',
      confirmText,
      hiddenCreateButton = false,
      jumpFrom,
      width = '480px'
    } = props
    const userInfo = user || {}
    const [isMobile] = useIsMobile()
    let orgs: any[] = (userInfo && userInfo.orgs ? userInfo.orgs : []).concat([])
    let createorRoleType: CreatorRoleType
    if (user.fdy) {
      createorRoleType = 'ASSISTANT'
    }
    if (user.departAdmin) {
      createorRoleType = 'DEPART_ADMIN'
    }
    if (user.supper || user.system || user.xgAdmin) {
      createorRoleType = 'SUPER_ADMIN'
    }

    if (creatorOrg) {
      orgs = [
        {
          name: creatorOrg
        }
      ]
    }
    const orgName = creatorOrg || (orgs[0] ? orgs[0].name : undefined)
    const [publishPanelVisible, setPublishPanelVisible] = useState(open === true)
    const [createFormVisible, setCreateFormVisible] = useState(false)
    const isUpdate = task ? true : false
    const { _key, process = [], formId, ..._task } = task || ({} as any)
    const action = isUpdate ? '更新' : '创建'
    const assignmentRangeRef = useRef<AssignmentRangeType>()
    console.log('task==',task)
    console.log('isUpdate==',isUpdate)
    const formCount = useRef(0)
    const [formData, setFormData] = useState(null)
    const [taskData, setTaskData] = useState<null | ITaskItem>(null)
    const formRef = useRef<ProFormInstance<any>>()
    const [createLoading, setCreateLoading] = useState(false)
    const [processRangeError, setProcessRangeError] = useState('')
    const cancelCheckRef = useRef<CancelToken>()
    const [taskCheckData, setTaskCheckData] = useState<ITaskCheckData>()
    const taskPrivilegeRef = useRef<boolean>(false)
    //const navigate = useNavigate();
    const [showBack, setShowBack] = React.useState(false)
    const [iframeNode, setIframeNode] = React.useState<HTMLIFrameElement>()
    const handleIframeRef = React.useCallback((node: HTMLIFrameElement) => {
      setIframeNode(node)
    }, [])

    const closeHandler = () => {
      setPublishPanelVisible(false)
      setCreateFormVisible(false)
      setFormData(null)
      typeof onClose === 'function' && onClose()
    }

    const visibleChangeHandler = (v: boolean) => {
      setPublishPanelVisible(v)
      typeof onClose === 'function' && !v && onClose()
    }

    const closeFormDrawerHandler = () => {
      setCreateFormVisible(false)
      typeof onClose === 'function' && onClose()
    }

    const updateTaskData = (values: any) => {
      const process = values.process || []
      const processVals = processOptions.filter((opt) => process.indexOf(opt.id) > -1)
      values.process = processVals
        .map((opt) => {
          return assignmentRangeRef.current?.[opt.id]
        })
        .filter(Boolean)

      console.warn(values)
      if (moment.isMoment(values.startTime)) {
        values.startTime = values.startTime.minute(0).second(0).millisecond(0).toISOString()
      }
      if (moment.isMoment(values.endTime)) {
        values.endTime = values.endTime.minute(0).second(0).millisecond(0).toISOString()
      }
      return {
        ...task,
        ...values,
        creatorId: creatorId,
        creatorName: creatorName,
        creatorOrg: values.creatorOrg || orgName,
        payload: parsePayloadString(formData) || _task.payload
      }
    }

    const nextHanlder = () => {
      if (formRef.current) {
        formRef.current
          .validateFields()
          .then((values) => {
            setTaskData(updateTaskData(values))
            if (createForm) {
              setCreateFormVisible(true)
              setPublishPanelVisible(false)
            }
          })
          .catch((reason) => {
            console.warn(reason)
            message.warning('请检查任务配置是否填写正确')
          })
      }
    }

    const createTaskHandler = async (getValues?: any) => {
      if (getValues === true) {
        if (formRef.current) {
          try {
            const values = await formRef.current.validateFields()
            return updateTaskData(values)
          } catch (reason) {
            console.warn(reason)
            message.warning('请检查任务配置是否填写正确')
          }
        }
      } else {
        if (formRef.current) {
          formRef.current
            .validateFields()
            .then((values) => {
              _createTask(updateTaskData(values))
            })
            .catch((reason) => {
              console.warn(reason)
              message.warning('请检查任务配置是否填写正确')
            })
        }
      }
    }

    const createTaskRequest = (data: any) => {
      setCreateLoading(true)
      ;(isUpdate ? updateFormTask : createFormTask)({
        data: data,
        pathParams: isUpdate ? { id: _key } : {}
      })
        .then((res) => {
          if (res && res.data) {
            notification.success({
              message: action + '成功'
            })
            formCount.current++
            closeHandler()
            if (typeof onSuccess !== 'function' || onSuccess(res.data) !== false) {
              //jumpToDetail && res?.data?._key && navigate(`/task-my/${res.data._key}` + (jumpFrom ? `?from=${jumpFrom}` : ""));
            }
          } else {
            notification.error({
              message: action + '失败',
              description: res && res.msg ? res.msg : '超时或服务出错，请稍后重试'
            })
          }
        })
        .catch((reason) => {
          notification.error({
            message: action + '失败',
            description: reason.response && reason.response.msg ? reason.response.msg : '网络或服务故障，请稍后重试'
          })
        })
        .finally(() => {
          setCreateLoading(false)
        })
    }

    const _createTask = (data?: any) => {
      if (formRef.current) {
        formRef.current
          .validateFields()
          .then(() => {
            if (typeof onCreateTask === 'function') {
              const res = onCreateTask(data)
              if (res === false) {
                closeHandler()
              } else {
                createTaskRequest(data)
              }
            } else {
              createTaskRequest(data)
            }
          })
          .catch(() => {
            setPublishPanelVisible(true)
            setCreateFormVisible(false)
          })
      }
    }

    const createTaskFormHandler = () => {
      if (
        iframeNode &&
        iframeNode.contentWindow['EASY_FORM'] &&
        iframeNode.contentWindow['EASY_FORM'].submitActivity &&
        iframeNode.contentWindow['EASY_FORM'].submitActivity
      ) {
        iframeNode.contentWindow['EASY_FORM'].submitActivity((data: any) => {
          _createTask({
            ...taskData,
            payload: parsePayloadString(data) || _task.payload
          })
          setFormData(data)
        })
      } else if (formData) {
        createTaskHandler()
      } else {
        console.warn('wy:', (iframeNode?.contentWindow as any)?.EASY_FORM)
        message.warning('请检查表单是否已配置')
      }
    }

    const formatCheckData = (value: any) => {
      if (value) {
        return {
          privilege: value.isExceedAuth,
          approver: {
            approverId: value.asignments[0]?.code,
            approverName: value.asignments[0]?.name,
            approverType: (value.asignments[0]?.type as string)?.toLocaleUpperCase()
          }
        }
      }
    }
    const checkTaskHandler = (privilege?: boolean) => {
      // 前端预判断是否越权的结果
      cancelCheckRef.current && cancelCheckRef.current.silent() // 静默取消上次请求
      cancelCheckRef.current = request.createCancelToken()
      if (privilege) {
        const values = formRef.current?.getFieldsValue()
        const _taskData: ITaskItem = values && updateTaskData(values)
        const hasProcess = _taskData && _taskData.process && _taskData.process.length ? true : false
        hasProcess &&
          checkTask({
            //cancelToken: cancelCheckRef.current.token,
            process: _taskData?.process
          })
            .then((res: any) => {
              if (res && (res.data || res.result.data)) {
                setTaskCheckData(formatCheckData(res.data) as any)
              } else {
                setTaskCheckData({
                  privilege: false
                })
              }
            })
            .catch(() => {
              setTaskCheckData({
                privilege: false
              })
            })
      } else {
        setTaskCheckData({
          privilege: false
        })
      }
    }

    const onAssignmentRangeChange = useCallback(
      (assignmentRange: AssignmentRangeType, vals?: string[], privilege?: boolean) => {
        assignmentRangeRef.current = assignmentRange
        if (formRef.current) {
          const _process = formRef.current.getFieldValue('process')
          if (_process && _process.length) {
            // 当设置了下派流程，时触发检验
            formRef.current.validateFields(['process'])
          }
          //taskPrivilegeRef.current !== privilege && checkTaskHandler(privilege);
          checkTaskHandler(privilege)
        }
        taskPrivilegeRef.current = privilege === true
      },
      []
    )

    useEffect(() => {
      setPublishPanelVisible(open === true)
      if (!open) {
        assignmentRangeRef.current = null
      }
    }, [open])

    useEffect(() => {
      if (!createorRoleType && open) {
        message.warn('无权限创建任务')
        visibleChangeHandler(false)
      }
    }, [createorRoleType, open])

    if (!createorRoleType) {
      return null
    }

    const handlePostMessage = React.useCallback((event: any) => {
      if (event.data === 'SHOW_BACK') {
        setShowBack(true)
      } else if (event.data === 'HIDE_BACK') {
        setShowBack(false)
      }
    }, [])
    React.useEffect(() => {
      if (iframeNode) {
        window.addEventListener('message', handlePostMessage)
        return () => {
          window.removeEventListener('message', handlePostMessage)
        }
      }
    }, [iframeNode])
    useImperativeHandle(ref, () => ({
      getTaskValue: createTaskHandler
    }))
    const _confirmText = confirmText ? confirmText : `立即${action}`

    return (
      <Fragment>
        <BetaSchemaForm<any>
          layoutType='Form'
          //visible={publishPanelVisible}
          //width={isMobile ? "100vw" : width}
          // drawerProps={{
          //   contentWrapperStyle: isMobile
          //     ? {}
          //     : {
          //         minWidth: 500,
          //       },
          // }}
          initialValues={Object.assign(
            {
              type: createForm ? 'EZForm' : '',
              process: (process || []).map((v: ITaskProcess | string) => (typeof v === 'object' ? v.id : v))
            },
            _task
          )}
          columns={[
            {
              dataIndex: 'type',
              title: '任务类型',
              valueType: 'radio',
              hideInForm: true,
              fieldProps: {
                disabled: isUpdate,
                options: [
                  {
                    value: 'EZForm',
                    label: '万能表单'
                  },
                  {
                    value: '',
                    label: '普通任务'
                  }
                ],
                style: {
                  // 只有一个选项暂时隐藏
                  display: 'none'
                }
              },
              formItemProps: {
                required: createForm ? true : false, // 非普通类型时必填
                rules: [
                  {
                    required: createForm ? true : false
                  }
                ],
                noStyle: true
              }
            },
            {
              dataIndex: 'title',
              title: '标题',
              hideInForm: true,
              formItemProps: {
                required: false,
                rules: [
                  {
                    required: false
                  }
                ]
              }
            },
            {
              dataIndex: 'description',
              title: '说明',
              hideInForm: true,
              valueType: 'textarea'
            },
            {
              dataIndex: 'startTime',
              title: '开始时间',
              hideInForm: true,
              valueType: 'dateTime',
              fieldProps: {
                format: 'YYYY-MM-DD HH:00:00',
                showTime: {
                  format: 'HH:00:00'
                }
              }
            },
            {
              dataIndex: 'endTime',
              title: '结束时间',
              hideInForm: true,
              valueType: 'dateTime',
              fieldProps: {
                format: 'YYYY-MM-DD HH:00:00',
                showTime: {
                  format: 'HH:00:00'
                }
              }
            },
            {
              dataIndex: 'process',
              title: taskTitle,
              renderFormItem: (schema) => {
                return (
                  <TaskProcessPicker
                    user={user} //用户信息
                    rangeError={processRangeError}
                    onAssignmentRangeChange={onAssignmentRangeChange} //当委派信息变化时
                    createorRoleType={createorRoleType} //创建者的身份
                    disabled={isUpdate}
                    process={process}
                    taskCheckData={taskCheckData} //任务是否越权
                  />
                )
              },
              formItemProps: {
                required: true,
                rules: [
                  {
                    required: true,
                    message: '至少选择一个环节'
                  },
                  {
                    validator(rule, value) {
                      let err: string = ''
                      if (
                        value &&
                        value[0] &&
                        assignmentRangeRef.current &&
                        assignmentRangeRef.current[value[0]] &&
                        assignmentRangeRef.current[value[0]].needTarget
                      ) {
                        err = `必须选择至少一个指定对象`
                      }
                      setProcessRangeError(err)
                      return err ? Promise.reject(err) : Promise.resolve()
                    }
                  }
                ]
              }
            },
            /**
             * 当只有一个所属机构时，不让选择
             */
            orgs && orgs.length > 1
              ? {
                  dataIndex: 'creatorOrg',
                  valueType: 'select',
                  title: '所属机构',
                  fieldProps: {
                    allowClear: false,
                    clearIcon: false,
                    defaultValue: orgs[0].name,
                    options: orgs.map((org: any) => {
                      return {
                        label: org.name,
                        value: org.name
                      }
                    })
                  }
                }
              : {
                  dataIndex: 'creatorOrg',
                  hideInForm: true
                }
          ]}
          title={title}
          //onVisibleChange={visibleChangeHandler}
          dateFormatter={(v) => v.toISOString()}
          submitter={{
            render: (props) => {
              // 暂不支持修改任务的表单
              return [
                createForm ? (
                  <Button
                    key='createform'
                    type='primary'
                    ghost={formData || isUpdate || formId ? true : false}
                    onClick={nextHanlder}
                  >
                    下一步：配置表单
                  </Button>
                ) : null,
                !(isUpdate || hiddenCreateButton) ?
                <Button
                  hidden={isUpdate || hiddenCreateButton}
                  loading={createLoading}
                  key='create'
                  onClick={isUpdate || !createForm ? createTaskHandler : undefined}
                  type='primary'
                  disabled={createForm && !formData && !isUpdate}
                >
                  {_confirmText}
                </Button> : null
              ]
            }
          }}
          formRef={formRef}
          onFinish={async (values: any) => {
            setTaskData(updateTaskData(values))
            return true
          }}
          className={cls}
        />

        <Drawer
          title='配置表单'
          extra={showBack && <Button onClick={() => window.history.go(-1)}>重选模版</Button>}
          bodyStyle={{ padding: 0, position: 'relative', backgroundColor: 'rgb(242, 242, 242)' }}
          footer={
            <div style={{ textAlign: 'right' }}>
              <Space>
                <Button
                  type='primary'
                  ghost
                  onClick={() => {
                    setCreateFormVisible(false)
                    setPublishPanelVisible(true)
                  }}
                >
                  上一步：填写任务
                </Button>
                {!(isUpdate || hiddenCreateButton) ?
                <Button
                  hidden={isUpdate || hiddenCreateButton}
                  loading={createLoading}
                  onClick={createTaskFormHandler}
                  type='primary'
                >
                  {_confirmText}
                </Button> : null
                }
              </Space>
            </div>
          }
          width={isMobile ? '100vw' : width}
          maskClosable={false}
          open={createFormVisible}
          onClose={closeFormDrawerHandler}
        >
          <ProlayoutIframe
            iframeRef={handleIframeRef}
            key={'create-form-' + formCount.current}
            src={YB_EZFORM_URL + (formId ? `?activityId=${formId}&taskId=${_key}&usedBy=task` : '?usedBy=task')}
          />
        </Drawer>
      </Fragment>
    )
  }
)
