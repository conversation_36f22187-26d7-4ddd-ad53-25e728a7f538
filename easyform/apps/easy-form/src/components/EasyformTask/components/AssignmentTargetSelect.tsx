/* eslint-disable */
// @ts-nocheck
import { Alert, Select, SelectProps } from 'antd'
import React, { useCallback, useEffect, useMemo } from 'react'

interface AssignmentTargetSelectProps extends Omit<SelectProps, 'options'> {
  options?: Array<{
    label?: string
    value?: string
    code?: string
    name?: string
  }>
  hiddenEmptyOptionsAlert?: any
  emptyText?: string
}

export default function AssignmentTargetSelect(props: AssignmentTargetSelectProps) {
  const { options, emptyText, hiddenEmptyOptionsAlert, onChange, ...others } = props
  const opts = useMemo<SelectProps['options']>(() => {
    return (options || []).map((item) => {
      return {
        label: item.label || item.name,
        value: item.value || item.code
      }
    })
  }, [options])

  const changeHandler = useCallback(
    (v, opt) => {
      typeof onChange === 'function' && onChange(v, opt)
    },
    [onChange]
  )

  useEffect(() => {
    return () => {
      typeof onChange === 'function' && onChange(undefined, undefined)
    }
  }, [])

  if (!opts.length) {
    return !hiddenEmptyOptionsAlert ? <Alert message={emptyText || '没有可选的对象'} showIcon /> : <div></div>
  }
  return <Select {...others} options={opts} onChange={changeHandler} labelInValue />
}
