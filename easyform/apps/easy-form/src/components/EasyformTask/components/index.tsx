/* eslint-disable */
// @ts-nocheck
import {
  AssignmentTargetFilter,
  ITaskCheckData,
  ITaskProcess,
  ITaskProcessOption,
  TaskProcessActionType,
  TaskProcessNameType
} from '@/interfaces/task'
import {
  TASK_PROCESS_ACTIONS,
  TASK_PROCESS_ACTIONS_OPTIONS,
  TASK_PROCESS_OPTIONS,
  getProcessAssignmentName,
  parseTaskProcessOptions
} from '@/commons/task'
import { CheckCircleFilled, TeamOutlined } from '@ant-design/icons'
import { css } from '@emotion/css'
import { Alert, Avatar, CheckboxOptionType, Select, Space, Switch, Tag, Timeline, Tooltip } from 'antd'
import classNames from 'classnames'
import React, { Fragment, ReactNode, useEffect, useMemo, useRef, useState } from 'react'
import AssignmentRangePicker, { AssignmentRangeFilterType } from './AssignmentRangePicker'
import { CancelToken, request, useContextState, utils } from '@sui/runtime'
import AssignmentTargetSelect from './AssignmentTargetSelect'
import { ISubjectMember } from '@/interfaces/user'
import { ISubjectMembersServiceQuery, getSubjectMembers } from '@/api/cuc'

export enum CreatorRole {
  SUPER_ADMIN = 'SUPER_ADMIN', // 校级或系统管理
  DEPART_ADMIN = 'DEPART_ADMIN', // 院系或部门管理员
  ASSISTANT = 'ASSISTANT' // 辅导员
}
type AssignmentRangeFilterProps = { type: AssignmentRangeFilterType; title?: string }
export type CreatorRoleType = keyof typeof CreatorRole
export type AssignmentRangeType = {
  [key in TaskProcessNameType]?: ITaskProcess
}
export interface ITaskProcessPickerProps {
  value?: string[]
  onChange?: (value?: string[]) => void
  className?: string
  options?: ITaskProcessOption<TaskProcessNameType>[]
  disabled?: boolean
  user?: any
  process?: any
  createorRoleType?: CreatorRoleType
  /**
   * 下派范围改变回调
   * @param options
   * @param value
   * @param privilege
   * @returns
   */
  onAssignmentRangeChange?: (options: AssignmentRangeType, value?: string[], privilege?: boolean) => void
  rangeError?: string
  /**
   * 越权数据
   */
  taskCheckData?: ITaskCheckData
}

const jscls = css({
  paddingTop: 12,
  '.ant-timeline-item-head-custom': {
    top: '4px'
  },
  '.ant-switch-small': {
    verticalAlign: 'middle'
  },
  '.ant-timeline-item': {
    paddingBottom: 12
  },
  '.ant-timeline-item-last': {
    paddingBottom: 0,
    '& > .ant-timeline-item-content': {
      minHeight: 'auto'
    }
  },
  '.ant-tag': {
    float: 'left',
    margin: 0
  }
})

const clss = css({
  marginTop: 8,
  marginLeft: 32
})

export type AssignmentRangeStateType<T = string> = {
  [key in TaskProcessNameType]?: T
}

export const taskProcessOptions: ITaskProcessOption<TaskProcessNameType>[] =
  parseTaskProcessOptions<TaskProcessNameType>(TASK_PROCESS_OPTIONS)

export default function TaskProcessPicker(props: ITaskProcessPickerProps) {
  const {
    value,
    onChange,
    className,
    process,
    user,
    options,
    disabled,
    createorRoleType,
    taskCheckData,
    onAssignmentRangeChange,
    rangeError
  } = props
  console.log('createorRoleType===',createorRoleType)
  const cls = classNames(className, jscls)
  const [valueState, setValueState] = useState<string[]>()
  const [rangeState, setRangeState] = useState<AssignmentRangeStateType>()
  const [warningMsg, setWarningMsg] = useState<string>()
  const [approvers, setApprovers] = useState<ISubjectMember[]>([]) //应该是越权时任务的审核人
  //委派目标
  const [assignmentTarget, setAssignmentTarget] = useState<
    AssignmentRangeStateType<{
      label: string
      value: string
    }>
  >()
  //请求的取消句柄
  const cancelCheckRef = useRef<CancelToken>()
  const timerRef = useRef<any>()
  //const { context } = useContextState();
  const userInfo = user || {}

  //根据委派信息获取越权任务审核人
  const getApprovers = (params: ISubjectMembersServiceQuery) => {
    cancelCheckRef.current && cancelCheckRef.current.silent()
    cancelCheckRef.current = request.createCancelToken()
    getSubjectMembers({
      cancelToken: cancelCheckRef.current.token, //通过token取消上一次请求

      params: Object.assign(
        {
          length: 10
        },
        params
      )
    })
      .then((res) => {
        if (res && res.result.data && res.result.data.persons && res.result.data.persons.length) {
          setApprovers(res.result.data.persons)
        } else {
          setApprovers([])
        }
      })
      .catch(() => {
        setApprovers([])
      })
  }

  /**
   *
   * 处理派发范围切换
   */
  const changeRangeState = (name: TaskProcessNameType, value: string, isAutoAssign?: boolean) => {
    if (!isAutoAssign) {
      let warnMsg: boolean = false
      if (createorRoleType === 'ASSISTANT') {
        if (/^__ASSISTANT__(all|benyuanxi)/.test(value)) {
          warnMsg = true
        }
      }
      if (createorRoleType === 'DEPART_ADMIN') {
        if (/^__DEPART_ADMIN__(all)/.test(value)) {
          warnMsg = true
        }
      }
      setRangeState((v) =>
        Object.assign({}, v, {
          [`${name}`]: value
        })
      )
      setWarningMsg(warnMsg ? '超出权限范围的任务将在审核通过后发布' : '')
    } else {
      setRangeState((v) =>
        Object.assign({}, v, {
          [`${name}`]: value
        })
      )
    }
  }

  const changeAssignmentTarget = (name: TaskProcessNameType, value?: any, opts?: any) => {
    setAssignmentTarget(value ? { [`${name}`]: value } : undefined)
  }

  const items = useMemo<ITaskProcessOption<TaskProcessNameType>[]>(() => {
    const _vals = (typeof value === 'undefined' ? valueState : value) || []
    const opts = (options || taskProcessOptions).filter((opt) => {
      if (createorRoleType === 'ASSISTANT') {
        return opt.value === 'bks'
      }
      if (createorRoleType === 'DEPART_ADMIN') {
        return opt.value === 'fdy' || opt.value === 'bks'
      }
      return true
    })
    const vals = opts.filter((opt) => _vals.indexOf(opt.value) > -1)

    return opts.map((option) => {
      console.log('00000--',option)
      const name = option.value
      const processDetail = process?.filter((p) => p?.id == name)?.[0]
      const valIndex = vals.findIndex((v) => v.value === name)
      const checked = valIndex > -1
      let action: TaskProcessActionType
      let options: CheckboxOptionType[] = []
      let isAutoAssign = false
      let filterType: AssignmentRangeFilterProps
      if (checked) {
        action = 'xp'
        if (valIndex === vals.length - 1) {
          // 最后一个处理
          action = 'cl'
        }
        if (valIndex === 0) {
          if (createorRoleType === 'SUPER_ADMIN') {
            if (name === 'yxgly') {
              options = [
                {
                  label: '手动委派',
                  value: 'manual'
                },
                {
                  label: '所有院系',
                  value: `__${createorRoleType}__all_${name}`
                },
                {
                  label: '指定院系',
                  value: `__${createorRoleType}__$zd_${name}`
                }
              ]
            }
            if (name === 'fdy') {
              options = [
                {
                  label: '手动委派',
                  value: 'manual'
                },
                {
                  label: '全校所有辅导员',
                  value: `__${createorRoleType}__all_${name}`
                },
                {
                  label: '指定院系辅导员',
                  value: `__${createorRoleType}__yx$zd_${name}`
                },
                {
                  label: '指定辅导员',
                  value: `__${createorRoleType}__$zd_${name}`
                }
              ]
            }
            if (name == 'bks') {
              options = [
                {
                  label: '手动委派',
                  value: 'manual'
                },
                {
                  label: '全校本科生',
                  value: `__${createorRoleType}__all_${name}`
                },
                {
                  label: '指定院系',
                  value: `__${createorRoleType}__yx$zd_${name}`
                },
                {
                  label: '指定班级',
                  value: `__${createorRoleType}__bj$zd_${name}`
                },
                {
                  label: '指定学生',
                  value: `__${createorRoleType}__$zd_${name}`
                }
              ]
            }
          }

          if (createorRoleType === 'DEPART_ADMIN') {
            if (name === 'fdy') {
              options = [
                {
                  label: '手动委派',
                  value: 'manual'
                },
                {
                  label: '全校辅导员',
                  value: `__${createorRoleType}__all_${name}`
                },
                {
                  label: '指定本院系的辅导员',
                  value: `__${createorRoleType}__byx$zd_${name}`
                }
              ]
            }
            if (name === 'bks') {
              options = [
                {
                  label: '手动委派',
                  value: 'manual'
                },
                {
                  label: '全校学生',
                  value: `__${createorRoleType}__all_${name}`
                },
                {
                  label: '本院系学生',
                  value: `__${createorRoleType}__benyuanxi$zd_${name}`
                },
                {
                  label: '指定本院系班级',
                  value: `__${createorRoleType}__byxbj$zd_${name}`
                },
                {
                  label: '指定本院系学生',
                  value: `__${createorRoleType}__byx$zd_${name}`
                }
              ]
            }
          }

          if (createorRoleType === 'ASSISTANT') {
            if (name == 'bks') {
              options = [
                {
                  label: '手动委派',
                  value: 'manual'
                },
                {
                  label: '全校学生',
                  value: `__${createorRoleType}__all_${name}`
                },
                {
                  label: '本院系学生',
                  value: `__${createorRoleType}__benyuanxi$zd_${name}`
                },
                {
                  label: '本班级学生',
                  value: `__${createorRoleType}__benbanji$zd_${name}`
                },
                {
                  label: '指定本班级学生',
                  value: `__${createorRoleType}__bbj$zd_${name}`
                }
              ]
            }
          }
        } else {
          isAutoAssign = true
          if (name == 'bks') {
            options = [
              { label: '手动委派', value: 'manual' },
              { label: '自动委派', value: `__${'_bks' || createorRoleType}__auto` }
            ]
          }

          if (name == 'fdy') {
            options = [
              { label: '手动委派', value: 'manual' },
              { label: '自动委派', value: `__${'_fdy' || createorRoleType}__auto` }
            ]
          }
        }
      }
      const hasUpdateData = processDetail?.assignmentTargetName
      //创建者是辅导员，派发范围是本班级，需要展示本班级下拉选择
      let isClassSelectFilter: boolean =
        checked &&
        createorRoleType === 'ASSISTANT' &&
        rangeState &&
        rangeState[name] === `__${createorRoleType}__benbanji$zd_${name}`
          ? true
          : false
      //创建者是院系领导且派发范围是本院系，需要展示本院系下拉选择
      let isAcademySelectFilter: boolean =
        checked &&
        (createorRoleType === 'ASSISTANT' || createorRoleType === 'DEPART_ADMIN') &&
        rangeState &&
        rangeState[name] === `__${createorRoleType}__benyuanxi$zd_${name}`
          ? true
          : false
      //当前需要指定（zd）选择的群体，需要展示对应群体的选择器
      let isPickerFilter: boolean =
        rangeState &&
        rangeState[name] &&
        /\$zd/.test(rangeState[name]) &&
        checked &&
        !isClassSelectFilter &&
        !isAcademySelectFilter
          ? true
          : false
      if (isPickerFilter) {
        //获取指定群体的类型(班级，院系，辅导员，学生..)
        filterType = getAssignmentRangeFilter(rangeState[name].replace(`__${createorRoleType}__`, ''))
      }
      return {
        ...option,
        label: (
          <Fragment>
            <Space>
              <span>{option.label}</span>
              {/* 下派或处理渲染 */}
              {action && TASK_PROCESS_ACTIONS[action] ? (
                <Tag color={TASK_PROCESS_ACTIONS_OPTIONS[action].color}>{TASK_PROCESS_ACTIONS[action]}</Tag>
              ) : null}
            </Space>
            {/* 禁用情况下（更新任务数据时），不渲染人员选择器 */}
            {false ? null : (
              <Fragment>
                {options && options.length ? (
                  <Fragment>
                    <div className={clss}>
                      <Select
                        key={name}
                        disabled={disabled}
                        defaultValue={isAutoAssign ? `___${name}__auto` : 'manual'}
                        placeholder='选择范围'
                        onChange={(v) => changeRangeState(name, v, isAutoAssign)}
                        style={{ width: '180px' }}
                        options={options}
                      />
                    </div>
                    {(isPickerFilter || hasUpdateData) && (
                      <div className={clss}>
                        <AssignmentRangePicker
                          {...filterType}
                          disabled={disabled}
                          status={rangeError ? 'error' : undefined}
                          key={name}
                          user={user}
                          onChange={(v, opts) => changeAssignmentTarget(name, v, opts)}
                          manageOrgs={userInfo.manageOrgs}
                          value={processDetail?.assignmentTargetName}
                          manageClasses={userInfo.manageclasses}
                          roleType={createorRoleType}
                        />
                      </div>
                    )}
                    {(isAcademySelectFilter || hasUpdateData) && (
                      <div className={clss}>
                        <AssignmentTargetSelect
                          disabled={disabled}
                          hiddenEmptyOptionsAlert={hasUpdateData}
                          placeholder='选择指定的学院'
                          value={processDetail?.assignmentTargetName}
                          status={rangeError ? 'error' : undefined}
                          key={name}
                          onChange={(v, opt) => changeAssignmentTarget(name, v, opt)}
                          options={createorRoleType === 'ASSISTANT' ? userInfo.classLeaderAcademy : userInfo.manageOrgs}
                        />
                      </div>
                    )}
                    {(isClassSelectFilter || hasUpdateData) && (
                      <div className={clss}>
                        <AssignmentTargetSelect
                          disabled={disabled}
                          hiddenEmptyOptionsAlert={hasUpdateData}
                          placeholder='选择指定的班级'
                          status={rangeError ? 'error' : undefined}
                          key={name}
                          value={processDetail?.assignmentTargetName}
                          onChange={(v, opt) => changeAssignmentTarget(name, v, opt)}
                          options={userInfo.manageclasses}
                        />
                      </div>
                    )}
                  </Fragment>
                ) : null}
              </Fragment>
            )}
          </Fragment>
        ),
        checked
      }
    })
  }, [options, value, valueState, createorRoleType, rangeState, rangeError, disabled])

  const changeHandler = (val?: string[]) => {
    setValueState(val)
    typeof onChange === 'function' && !disabled && onChange(val)
  }

  const checkedChange = (value: string, checked: boolean) => {
    const val = (valueState || []).filter((v) => v !== value)
    if (checked) {
      val.push(value)
    }
    const vals = (options || taskProcessOptions).filter((opt) => val.indexOf(opt.value) > -1)
    changeHandler(vals.map((v) => v.value))
  }

  useEffect(() => {
    let range: AssignmentRangeStateType = {}
    if (valueState && valueState.length) {
      for (let index = 0; index < valueState.length; index++) {
        const _k = valueState[index]
        if (_k && rangeState && rangeState[_k]) {
          range[_k] = rangeState[_k]
        }
      }
    }
    setRangeState(range)
  }, [valueState])

  useEffect(() => {
    timerRef.current && clearTimeout(timerRef.current)
    timerRef.current = setTimeout(() => {
      const assignmentRange: AssignmentRangeType = {}
      const _vals: string[] = (typeof value === 'undefined' ? valueState : value) || []
      const opts = (options || taskProcessOptions).filter((opt) => {
        if (createorRoleType === 'ASSISTANT') {
          return opt.value === 'bks'
        }
        if (createorRoleType === 'DEPART_ADMIN') {
          return opt.value === 'fdy' || opt.value === 'bks'
        }
        return true
      })
      const vals = opts.filter((opt) => _vals.indexOf(opt.value) > -1).map((opt) => opt.value)
      for (let index = 0; index < vals.length; index++) {
        const name = vals[index] as TaskProcessNameType
        const action = index === vals.length - 1 ? 'cl' : 'xp'
        const assignmentName = getProcessAssignmentName(name, action)
        if ((rangeState && rangeState[name] && rangeState[name] !== 'manual') || (index > 0 && !rangeState[name])) {
          const needTarget = /\$zd_/.test(rangeState[name]) && !index
          assignmentRange[name] = getAssignmentRange(
            rangeState[name] ? rangeState[name] : `___${name}__auto`,
            name,
            assignmentName,
            createorRoleType
          )
          if (assignmentRange[name]) {
            //指定了确定的委派目标
            if (assignmentTarget && assignmentTarget[name]) {
              assignmentRange[name].assignmentTargetId = assignmentTarget[name].value
              assignmentRange[name].assignmentTargetName = assignmentTarget[name].label
            } else {
              assignmentRange[name].needTarget = needTarget
            }
          }
        } else {
          const assignmentData: ITaskProcess<TaskProcessNameType> = {
            name: assignmentName,
            id: name,
            assignmentTargetFilter: '',
            assignmentType: 'U',
            assignmentTargetId: '',
            type: '',
            assignmentTargetName: '',
            autoAssign: false,
            expandRangeAssignment: false
          }
          if (name === 'yxgly') {
            assignmentData.assignmentTargetId = 'poss_xgbzr'
            assignmentData.assignmentType = 'PS'
          } else if (name === 'fdy') {
            assignmentData.assignmentType = 'U'
            assignmentData.assignmentTargetFilter = AssignmentTargetFilter.SelectClassAssistantFromAcademy
          } else if (name === 'bks') {
            assignmentData.assignmentType = 'G'
            assignmentData.assignmentTargetFilter = AssignmentTargetFilter.SelectStudentByCurrentUser
          }
          assignmentRange[name] = assignmentData
        }
      }
      typeof onAssignmentRangeChange === 'function' &&
        !disabled &&
        onAssignmentRangeChange(assignmentRange, vals, warningMsg ? true : false)
    }, 100)
  }, [
    rangeState,
    options,
    onAssignmentRangeChange,
    warningMsg,
    value,
    valueState,
    disabled,
    assignmentTarget,
    createorRoleType
  ])

  useEffect(() => {
    return () => {
      timerRef.current && clearTimeout(timerRef.current)
    }
  }, [])

  useEffect(() => {
    if (
      taskCheckData &&
      taskCheckData.approver &&
      taskCheckData.approver.approverType &&
      taskCheckData.approver.approverId
    ) {
      getApprovers({
        subjectType: taskCheckData.approver.approverType,
        subjectCode: taskCheckData.approver.approverId
      })
    } else {
      setApprovers([])
    }
  }, [taskCheckData])

  let msg: ReactNode
  let avatars: ReactNode = null
  if (warningMsg) {
    const approver = taskCheckData && taskCheckData.approver && taskCheckData.approver.approverName
    const membersNames = (approvers || []).map((i) => i.name).filter(Boolean)
    // 最多显示3个
    msg = (
      <Fragment>
        越权任务将在
        {approver ? (
          <Tag style={{ marginLeft: 8 }}>
            {approver}
            {membersNames ? `（${membersNames.slice(0, 3).join('、')}${membersNames.length > 3 ? '...' : ''}）` : null}
          </Tag>
        ) : null}
        审核通过后发布 {avatars}
      </Fragment>
    )
  }

  return (
    <Fragment>
      <Timeline className={cls}>
        <Timeline.Item key={'_create'} color='blue'>
          创建者
        </Timeline.Item>
        {items.map((item, index) => {
          return (
            <Timeline.Item
              key={item.value + index}
              dot={item.checked ? <CheckCircleFilled /> : undefined}
              color={item.checked ? 'blue' : 'gray'}
            >
              <Switch
                disabled={disabled}
                onChange={(checed) => checkedChange(item.value, checed)}
                size='small'
                defaultChecked={item.checked}
                checked={item.checked}
              />{' '}
              {item.label}
            </Timeline.Item>
          )
        })}
      </Timeline>
      {msg && <Alert message={msg} type='warning' showIcon />}
    </Fragment>
  )
}

/**
 * 指定条件类型
 * @param key
 * @returns
 */
function getAssignmentRangeFilter(key: string): AssignmentRangeFilterProps | undefined {
  if (key === '$zd_yxgly') {
    return {
      type: 'academy_yxgly'
    }
  }
  if (key === '$zd_fdy' || key === 'byx$zd_fdy') {
    return {
      type: 'academy_fdy'
    }
  }

  if (key === 'byxbj$zd_bks') {
    return {
      type: 'academy_class'
    }
  }

  if (key === 'byx$zd_bks') {
    return {
      type: 'academy_student'
    }
  }

  if (key === '$zd_bks' || key === 'bbj$zd_bks') {
    return {
      type: 'student'
    }
  }

  if (/^(yx\$zd)/.test(key)) {
    return {
      type: 'academy'
    }
  }
  if (/^bj\$zd/.test(key)) {
    return {
      type: 'class'
    }
  }
}

function getAssignmentRange(
  key: string,
  id: TaskProcessNameType,
  name: string,
  roleType: CreatorRoleType
): ITaskProcess<TaskProcessNameType> {
  if (id) {
    const createAssignmentData = (
      opts?: Omit<ITaskProcess<TaskProcessNameType>, 'id'>
    ): ITaskProcess<TaskProcessNameType> => {
      return Object.assign(
        {
          id,
          name,
          type: '',
          assignmentType: '',
          assignmentTargetFilter: '',
          assignmentTargetId: '',
          assignmentTargetName: '',
          autoAssign: true,
          expandRangeAssignment: true
        },
        opts
      )
    }
    const ranges: { [key: string]: ITaskProcess<TaskProcessNameType> } = {
      // 系统/学工管理员
      [`__${CreatorRole.SUPER_ADMIN}__all_yxgly`]: createAssignmentData({
        assignmentType: 'PS',
        assignmentTargetId: 'poss_xgbzr',
        assignmentTargetName: '学工管理员岗位集',
        expandRangeAssignment: false
      }),
      [`__${CreatorRole.SUPER_ADMIN}__$zd_yxgly`]: createAssignmentData({
        assignmentType: 'P',
        expandRangeAssignment: false
      }),
      [`__${CreatorRole.SUPER_ADMIN}__all_fdy`]: createAssignmentData({
        assignmentTargetFilter: AssignmentTargetFilter.SelectAllClassAssistant
      }),
      [`__${CreatorRole.SUPER_ADMIN}__yx$zd_fdy`]: createAssignmentData({
        assignmentType: 'O',
        assignmentTargetFilter: AssignmentTargetFilter.SelectClassAssistantFromAcademy
      }),
      [`__${CreatorRole.SUPER_ADMIN}__$zd_fdy`]: createAssignmentData({
        assignmentType: 'A'
      }),
      [`__${CreatorRole.SUPER_ADMIN}__all_bks`]: createAssignmentData({
        assignmentType: 'G',
        assignmentTargetId: 'gro_bks',
        assignmentTargetName: '本科生'
      }),
      [`__${CreatorRole.SUPER_ADMIN}__yx$zd_bks`]: createAssignmentData({
        assignmentType: 'O',
        assignmentTargetFilter: AssignmentTargetFilter.SelectStudentFromAcademy
      }),
      [`__${CreatorRole.SUPER_ADMIN}__bj$zd_bks`]: createAssignmentData({
        assignmentType: 'C',
        assignmentTargetFilter: AssignmentTargetFilter.SelectStudentFromClass
      }),
      [`__${CreatorRole.SUPER_ADMIN}__$zd_bks`]: createAssignmentData({
        assignmentType: 'U'
      }),
      // 部门管理员
      [`__${CreatorRole.DEPART_ADMIN}__all_fdy`]: createAssignmentData({
        assignmentTargetFilter: AssignmentTargetFilter.SelectAllClassAssistant
      }),
      [`__${CreatorRole.DEPART_ADMIN}__byx$zd_fdy`]: createAssignmentData({
        assignmentType: 'A'
      }),
      [`__${CreatorRole.DEPART_ADMIN}__all_bks`]: createAssignmentData({
        assignmentType: 'G',
        assignmentTargetId: 'gro_bks',
        assignmentTargetName: '本科生'
      }),
      [`__${CreatorRole.DEPART_ADMIN}__benyuanxi$zd_bks`]: createAssignmentData({
        assignmentType: 'O',
        assignmentTargetFilter: AssignmentTargetFilter.SelectStudentFromAcademy
      }),
      [`__${CreatorRole.DEPART_ADMIN}__byxbj$zd_bks`]: createAssignmentData({
        assignmentType: 'C',
        assignmentTargetFilter: AssignmentTargetFilter.SelectStudentFromClass
      }),
      [`__${CreatorRole.DEPART_ADMIN}__byx$zd_bks`]: createAssignmentData({
        assignmentType: 'U'
      }),
      [`__${'_fdy' || CreatorRole.DEPART_ADMIN}__auto`]: createAssignmentData({
        assignmentTargetFilter: AssignmentTargetFilter.SelectClassAssistantFromAcademy,
        assignmentType: 'O',
        assignmentTargetId: '{authorityScopeId}'
      }),
      // 辅导员
      [`__${CreatorRole.ASSISTANT}__all_bks`]: createAssignmentData({
        assignmentType: 'G',
        assignmentTargetId: 'gro_bks',
        assignmentTargetName: '本科生'
      }),
      [`__${CreatorRole.ASSISTANT}__benyuanxi$zd_bks`]: createAssignmentData({
        assignmentType: 'O',
        assignmentTargetFilter: AssignmentTargetFilter.SelectStudentFromAcademy
      }),
      [`__${CreatorRole.ASSISTANT}__benbanji$zd_bks`]: createAssignmentData({
        assignmentType: 'C',
        assignmentTargetFilter: AssignmentTargetFilter.SelectStudentFromClass
      }),
      [`__${CreatorRole.ASSISTANT}__bbj$zd_bks`]: createAssignmentData({
        assignmentType: 'U'
      }),
      [`__${'_bks' || CreatorRole.ASSISTANT}__auto`]: createAssignmentData({
        assignmentType: 'C',
        assignmentTargetFilter: AssignmentTargetFilter.SelectStudentFromClass,
        assignmentTargetId: '{authorityScopeId}'
      })
    }
    return ranges[key]
  }
}
