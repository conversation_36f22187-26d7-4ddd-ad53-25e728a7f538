/* eslint-disable */
// @ts-nocheck
import { API_CUC, CUC_API_PREFIX } from '@/api/cuc'
import { SuiPersonnelPicker, SuiPersonnelPickerProps } from '@sui/rc-personnel'
import React, { CSSProperties, Fragment, ReactNode, useEffect, useMemo, useState } from 'react'
import { parseClassLeaderSet, parsePositionSet } from '@/components/AssignmentSelector/utils'
import { Dropdown, Select, SelectProps, Space, Typography, Input, Modal } from 'antd'
import { TeamOutlined } from '@ant-design/icons'
import { useBrowserClient, useContextState, utils } from '@sui/runtime'
import { AssignmentRangeFilterType, CreatorRoleType } from '@/interfaces/task'
export { type AssignmentRangeFilterType } from '@/interfaces/task'

export interface AssignmentRangePickerProps
  extends Pick<
    SuiPersonnelPickerProps,
    'modalProps' | 'onChange' | 'children' | 'labelInValue' | 'multiple' | 'onOk' | 'onCancel'
  > {
  type: AssignmentRangeFilterType
  title?: string
  disabledKeys?: string[]
  user?: any
  style?: CSSProperties
  roleType?: CreatorRoleType
  disabled?: boolean
  manageClasses?: Array<{
    code: string
    id: string
    name: string
  }>
  manageOrgs?: Array<{
    code: string
    id: string
    name: string
  }>
  status?: SelectProps['status']
  value?: any
}

export default function AssignmentRangePicker(props: AssignmentRangePickerProps) {
  const {
    type,
    roleType,
    title,
    disabledKeys,
    disabled,
    value,
    style,
    user,
    status,
    onChange,
    children,
    manageClasses = [],
    manageOrgs = [],
    multiple,
    labelInValue,
    ...others
  } = props
  const [options, setOptions] = useState<any[]>([])
  console.log('value--', value)
  //   const { context = {} } = useContextState();
  //   const user = context.user || {};
  const userInfo = user || {}
  const browser = useBrowserClient()
  const [modalVisible, setModalVisible]= useState(false)

  useEffect(() => {
    return () => {
      typeof onChange === 'function' && onChange(undefined, undefined)
    }
  }, [])
  const changeHandler = (values: any, options: any[]) => {
    setOptions(
      options
        ? [].concat(options).map((item) => {
            const parentNode =
              item.parentNode || (item.dataRef && item.dataRef.parentOrg ? item.dataRef.parentOrg : undefined)
            return {
              value: item.code,
              title: parentNode ? `${parentNode.name}(${item.name})` : item.name,
              label: parentNode ? (
                <Fragment>
                  {parentNode.name} <Typography.Text type='secondary'>({item.name})</Typography.Text>
                </Fragment>
              ) : (
                item.name
              )
            }
          })
        : []
    )
    typeof onChange === 'function' && onChange(values, options)
  }

  const pickerProps = useMemo<SuiPersonnelPickerProps>(() => {
    console.log('AssignmentRangePicker - type:', type, 'roleType:', roleType)
    let _pickerProps: SuiPersonnelPickerProps = {}
    if (type === 'academy_yxgly' && roleType === 'SUPER_ADMIN') {
      _pickerProps = {
        api: CUC_API_PREFIX + API_CUC.getPositionsetXgbzr,
        title: '指定院系',
        successAjax: (res) => {
          console.log('res---', res)
          res.result.data = parsePositionSet(res.result.data || [], disabledKeys, {
            childrenAttrs: 'positions'
          })
        },
        treeNodeTitleRender: (node, title) => {
          let menus = []
          if (node.isLeaf && node._ref && node._ref.persons) {
            menus = node._ref.persons.map((person: any) => {
              return {
                label: person.name,
                key: person.code
              }
            })
          }
          return (
            <Space>
              {title}
              {menus && menus.length ? (
                <Dropdown menu={{ items: menus }}>
                  <TeamOutlined />
                </Dropdown>
              ) : null}
            </Space>
          )
        },
        targetNodeTitleRender: (item, defaultTitle, index) => {
          return item.parent ? (
            <Fragment>
              {item.parent.name} <Typography.Text type='secondary'>({defaultTitle})</Typography.Text>
            </Fragment>
          ) : (
            defaultTitle
          )
        },
        dataAttr: 'data',
        modalWidth: 600,
        searchOrg: false,
        search: false
      }
    }
    if (type == 'academy_fdy' && roleType == 'SUPER_ADMIN') {
      const api = CUC_API_PREFIX + API_CUC.getAcademyClassLeaders
      _pickerProps = {
        api: CUC_API_PREFIX + API_CUC.getPositionsetXgbzr,
        title: '指定辅导员',
        successAjax: (res) => {
          res.result.data = parsePositionSet(res.result.data || [], disabledKeys, {
            childrenAttrs: [],
            checkable: false
          })
        },
        successUserAjax: (res) => {
          res.result.data = parseClassLeaderSet(res.result.data || [], disabledKeys)
        },
        extraAjaxSettings: {
          data: null
        },
        extraUserAjaxSettings: {
          data: null
        },
        user_api: api,
        userDataAttr: 'data',
        getUserApiUrl: (defaultUrl, params, isSearch, keyword) => {
          if (!params.code) {
            return defaultUrl
          }
          return utils.add_url_query(
            utils.paramsUrlPath(api, {
              id: params.code
            }),
            {
              type: 2
            }
          )
        },
        nodeKeyAttr: 'code',
        userKeyAttr: 'code',
        userTitleAttr: 'name',
        nodeTitleAttr: 'name',
        dataAttr: 'data',
        searchOrg: false,
        search: false
      }
    }

    if (type === 'academy_fdy' && roleType === 'DEPART_ADMIN') {
      const api = CUC_API_PREFIX + API_CUC.getAcademyClassLeaders
      _pickerProps = {
        api: api,
        title: '指定本院系的辅导员',
        initTreeData: (manageOrgs || []).map((c) => {
          return {
            ...c,
            isLeaf: false,
            checkable: false
          }
        }),
        extraAjaxSettings: {
          data: null,
          params: {
            type: 2
          }
        },
        successAjax: (res) => {
          res.result.data = parseClassLeaderSet(res.result.data || [], disabledKeys)
        },
        defaultExpandAll: true,
        getApiUrl: (defaultUrl, params, isSearch, keyword) => {
          if (!params.code) {
            return defaultUrl
          }
          return utils.paramsUrlPath(api, {
            id: params.code
          })
        },
        nodeKeyAttr: 'code',
        userKeyAttr: 'code',
        userTitleAttr: 'name',
        nodeTitleAttr: 'name',
        dataAttr: 'data',
        modalWidth: 600,
        searchOrg: false,
        search: false
      }
    }

    if ((type === 'academy_class' || type === 'academy_student') && roleType === 'DEPART_ADMIN') {
      const api = CUC_API_PREFIX + API_CUC.getAcademyClassLeaders
      const userApi = CUC_API_PREFIX + API_CUC.getClassStudents
      _pickerProps = {
        api: api,
        title: type == 'academy_class' ? '指定本院系班级' : '指定本院系学生',
        initTreeData: (manageOrgs || []).map((c) => {
          return {
            ...c,
            isLeaf: false,
            checkable: false
          }
        }),
        extraAjaxSettings: {
          data: null,
          params: {
            type: 2
          }
        },
        successAjax: (res) => {
          res.result.data = parseClassLeaderSet(res.result.data || [], disabledKeys, {
            types: [],
            childrenAttrs: [],
            checkable: type === 'academy_class'
          }).sort((a: any, b: any) => {
            return a.name < b.name ? -1 : 0
          })
        },
        getApiUrl: (defaultUrl, params, isSearch, keyword) => {
          if (!params.code) {
            return defaultUrl
          }
          return utils.paramsUrlPath(api, {
            id: params.code
          })
        },
        nodeKeyAttr: 'code',
        userKeyAttr: 'code',
        userTitleAttr: 'name',
        nodeTitleAttr: 'name',
        dataAttr: 'data',
        modalWidth: 600,
        searchOrg: false,
        search: false
      }
      if (type === 'academy_student') {
        Object.assign<SuiPersonnelPickerProps, SuiPersonnelPickerProps>(_pickerProps, {
          extraUserAjaxSettings: {
            data: null
          },
          successUserAjax: (res) => {
            res.result.data = (res.result.data || []).map((d: any) => {
              return {
                ...d,
                isLeaf: true
              }
            })
          },
          user_api: userApi,
          getUserApiUrl: (defaultUrl, params, isSearch, keyword) => {
            if (!params.code) {
              return defaultUrl
            }
            return utils.paramsUrlPath(userApi, {
              id: params.code
            })
          },
          modalWidth: undefined
        })
      }
    }

    if (type === 'academy' || type === 'class') {
      const userApi = CUC_API_PREFIX + API_CUC.getAcademyClassLeaders
      _pickerProps = {
        api: CUC_API_PREFIX + API_CUC.getPositionsetXgbzr,
        title: type === 'academy' ? '指定院系' : '指定班级',
        successAjax: (res) => {
          res.result.data = parsePositionSet(res.result.data || [], disabledKeys, {
            childrenAttrs: []
          })
        },
        nodeKeyAttr: 'code',
        userKeyAttr: 'code',
        userTitleAttr: 'name',
        nodeTitleAttr: 'name',
        dataAttr: 'data',
        modalWidth: 600,
        searchOrg: false,
        search: false
      }
      if (type === 'class') {
        Object.assign<SuiPersonnelPickerProps, SuiPersonnelPickerProps>(_pickerProps, {
          user_api: userApi,
          successUserAjax: (res) => {
            res.result.data = parseClassLeaderSet(res.result.data || [], disabledKeys, {
              types: [],
              childrenAttrs: [],
              checkable: type === 'class'
            }).sort((a: any, b: any) => {
              return a.name < b.name ? -1 : 0
            })
          },
          extraUserAjaxSettings: {
            data: null,
            params: {
              type: 2
            }
          },
          getUserApiUrl: (defaultUrl, params, isSearch, keyword) => {
            if (!params.code) {
              return defaultUrl
            }
            return utils.paramsUrlPath(userApi, {
              id: params.code
            })
          },
          modalWidth: undefined
        })
      }
    }

    if (type === 'student' && roleType === 'SUPER_ADMIN') {
      const classesApi = CUC_API_PREFIX + API_CUC.getAcademyClassLeaders
      const userApi = CUC_API_PREFIX + API_CUC.getClassStudents
      _pickerProps = {
        api: CUC_API_PREFIX + API_CUC.getPositionsetXgbzr,
        title: '指定学生',
        successAjax: (res, node) => {
          res.result.data = !node
            ? parsePositionSet(res.result.data || [], disabledKeys, {
                childrenAttrs: [],
                checkable: false,
                isLeaf: false
              })
            : parseClassLeaderSet(res.result.data || [], disabledKeys, {
                types: [],
                childrenAttrs: [],
                checkable: false
              }).sort((a: any, b: any) => {
                return a.name < b.name ? -1 : 0
              })
        },
        extraAjaxSettings: {
          data: null
        },
        getApiUrl: (defaultUrl, params, isSearch, keyword) => {
          if (!params.code) {
            return defaultUrl
          }
          return utils.add_url_query(
            utils.paramsUrlPath(classesApi, {
              id: params.code
            }),
            {
              type: 2
            }
          )
        },
        user_api: userApi,
        successUserAjax: (res) => {
          res.result.data = (res.result.data || []).map((d: any) => {
            const disabled = disabledKeys && disabledKeys.indexOf(d.code) > -1 ? true : false
            return {
              ...d,
              isLeaf: true,
              disabled: disabled
            }
          })
        },
        getUserApiUrl: (defaultUrl, params) => {
          if (!params.code) {
            return defaultUrl
          }
          return utils.paramsUrlPath(userApi, {
            id: params.code
          })
        },
        extraUserAjaxSettings: {
          data: null
        },
        nodeKeyAttr: 'code',
        userKeyAttr: 'code',
        userTitleAttr: 'name',
        nodeTitleAttr: 'name',
        dataAttr: 'data',
        searchOrg: false,
        search: false
      }
    }

    if (type == 'student' && roleType == 'ASSISTANT') {
      const api = CUC_API_PREFIX + API_CUC.getClassStudents
      _pickerProps = {
        api: api,
        title: '指定学生',
        initTreeData: (manageClasses || []).map((c) => {
          return {
            ...c,
            isLeaf: false,
            checkable: false
          }
        }),
        extraAjaxSettings: {
          data: null
        },
        successAjax: (res) => {
          res.result.data = (res.result.data || []).map((d: any) => {
            const disabled = disabledKeys && disabledKeys.indexOf(d.code) > -1 ? true : false
            return {
              ...d,
              isLeaf: true,
              disabled: disabled
            }
          })
        },
        getApiUrl: (defaultUrl, params, isSearch, keyword) => {
          if (!params.code) {
            return defaultUrl
          }
          return utils.paramsUrlPath(api, {
            id: params.code
          })
        },
        nodeKeyAttr: 'code',
        userKeyAttr: 'code',
        userTitleAttr: 'name',
        nodeTitleAttr: 'name',
        dataAttr: 'data',
        modalWidth: 600,
        searchOrg: false,
        search: false
      }
    }

    return _pickerProps
  }, [type, roleType, disabledKeys])

  const _multiple = typeof multiple === 'boolean' ? multiple : pickerProps.multiple

  return (
    <>
    <SuiPersonnelPicker
      {...others}
      checkedKeys={disabledKeys}
      multiple={_multiple === true}
      value={value}
      disabled={disabled}
      {...pickerProps}
      key={type}
      size={browser.isMobile ? 'mini' : undefined}
      onChange={changeHandler}
      title={title || pickerProps.title}
      labelInValue={labelInValue !== false}
    >
      {children && typeof children !== 'function'
        ? children
        : (_, state) => {
            return typeof children === 'function' ? (
              children(_, state)
            ) : (
              <Select
                disabled={disabled}
                status={status}
                style={Object.assign({ width: '100%' }, style)}
                placeholder={pickerProps.placeholder || '选择指定对象'}
                onClick={state.open}
                // onClick={()=>setModalVisible(true)}
                onClear={state.clear}
                value={state.value?.value}
                mode={pickerProps.multiple ? 'multiple' : undefined}
                options={options}
                open={false}
                dropdownRender={null}
              />
            )
          }}
    </SuiPersonnelPicker>
    <Modal visible={modalVisible}>11</Modal>
    </>
  )
}
