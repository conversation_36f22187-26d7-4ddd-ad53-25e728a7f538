import React, { useState, useEffect, useRef } from 'react'
import { styled, Box, css } from '@mui/system'
import { Image } from 'antd-mobile'
import { CheckOutline as IconCheck } from 'antd-mobile-icons'
import themes from '@/themes'
interface IItemData {
  id: string | number
  name?: string
  url?: string
  componentName: string
  packageName: string
  version: string
}
interface IImageSelect {
  dataset: IItemData[]
  value?: IItemData
  onChange?: (d: IItemData) => void
}

const Root = styled('div')({
  display: 'flex',
  justifyContent: 'space-between'
})

const SelectedTag = styled('div')<any>(({ theme, selected }) => ({
  width: 28,
  height: 28,
  backgroundColor: theme.palette.primary.main,
  color: '#FFF',
  opacity: selected ? 1 : 0,
  transition: 'opacity .3s ease-in-out',
  position: 'absolute',
  bottom: 0,
  right: 0,
  lineHeight: 1,
  clipPath: 'polygon(0% 100%, 100% 0%, 100% 100%)',
  '&>*': {
    position: 'absolute',
    bottom: 0,
    right: 0
  }
}))
const ImageWrapper = styled('div')<any>(({ theme, selected }) => ({
  width: '30%',
  position: 'relative',
  borderRadius: 4,
  overflow: 'hidden',
  height: 130,
  border: '1px solid',
  borderColor: selected ? theme.palette.primary.main : 'transparent',
  transition: 'border .3s ease-in-out'
}))

export default React.forwardRef<HTMLDivElement, IImageSelect>(({ dataset, value, onChange }, ref) => {
  const handleSelect = (d: IItemData) => {
    if (typeof onChange === 'function') {
      onChange(d)
    }
  }
  return (
    <Root ref={ref}>
      {dataset.map(({ url, ...other }) => (
        <ImageWrapper onClick={() => handleSelect(other)} selected={value?.id === other.id} key={other.id}>
          <Image src={url} fit='scale-down' />
          <SelectedTag selected={value?.id === other.id}>
            <IconCheck />
          </SelectedTag>
        </ImageWrapper>
      ))}
    </Root>
  )
})
