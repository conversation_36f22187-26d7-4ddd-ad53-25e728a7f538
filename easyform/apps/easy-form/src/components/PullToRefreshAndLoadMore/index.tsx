import React from 'react'
import { PullToRefresh, InfiniteScroll, PullToRefreshProps, InfiniteScrollProps, DotLoading } from 'antd-mobile'
import { styled, Box } from '@mui/system'
import { Typography } from '@/components'

interface IPullToRefreshAndLoadMore {
  infiniteScrollProps?: InfiniteScrollProps
}

const StyledInfiniteScroll = styled(InfiniteScroll)(({ theme }) => ({
  backgroundColor: theme.palette.background.default,
  padding: 16
}))

type TProps = IPullToRefreshAndLoadMore & PullToRefreshProps

export default React.forwardRef<any, TProps>(({ children, infiniteScrollProps, ...other }, ref) => {
  return (
    <PullToRefresh {...other}>
      {children}
      {infiniteScrollProps && (
        <StyledInfiniteScroll {...infiniteScrollProps}>
          {(hasMore, failed, retry) =>
            hasMore ? (
              <Box className='flex items-center'>
                <Typography sx={{ fontSize: 12 }} color='inherit'>
                  加载中
                </Typography>
                <DotLoading />
              </Box>
            ) : (
              <Typography variant='hint'>没有更多了</Typography>
            )
          }
        </StyledInfiniteScroll>
      )}
    </PullToRefresh>
  )
})
