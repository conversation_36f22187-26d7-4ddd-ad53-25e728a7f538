import React from 'react'
import { styled, Box } from '@mui/system'
import { Typography } from '@/components'

interface IStepItemData {
  id: string | number
  title: string
}
interface IStepper {
  steps: string[] | IStepItemData[]
  activeStep?: number
  onChange?: (stepIndex: number) => void
  [key: string]: any
}

const Root = styled('div')({
  display: 'flex',
  alignItems: 'center',
  justifyContent: 'space-between'
})

const Circle = styled('span')<any>(({ theme, active }) => ({
  width: 24,
  height: 24,
  borderRadius: '100%',
  display: 'flex',
  justifyContent: 'center',
  alignItems: 'center',
  border: `2px solid ${theme.palette.secondary.main}`,
  color: active ? '#FFF' : theme.palette.secondary.main,
  fontWeight: '500',
  background: active ? theme.palette.secondary.main : '#FFF',
  transition: 'all .35s ease-in-out'
}))

interface IStepItem {
  title: string
  index: number
  active?: boolean
  onClick?: (i: number) => void
}
const StepItem = ({ title, index, active, onClick }: IStepItem) => {
  const handleClick = () => {
    if (typeof onClick === 'function') {
      onClick(index)
    }
  }
  return (
    <Box onClick={handleClick} className='flex items-center'>
      <Circle active={active}>{index + 1}</Circle>
      <Typography color='#FFF' sx={{ fontSize: 14, ml: 0.5 }}>
        {title}
      </Typography>
    </Box>
  )
}

export default React.forwardRef<HTMLDivElement, IStepper>(({ steps, activeStep = 0, onChange, ...other }, ref) => {
  const handleChange = (i: number) => {
    if (typeof onChange === 'function') {
      onChange(i)
    }
  }
  return (
    <Root {...other} ref={ref}>
      {steps.map((s, i) => (
        <StepItem
          onClick={handleChange}
          active={activeStep === i}
          index={i}
          title={typeof s === 'object' ? s.title : s}
          key={i}
        />
      ))}
    </Root>
  )
})
