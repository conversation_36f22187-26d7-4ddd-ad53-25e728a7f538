/*
 * @Author: zhongfw <EMAIL>
 * @Date: 2023-12-13 13:47:58
 * @LastEditors: zhongfw <EMAIL>
 * @LastEditTime: 2023-12-14 10:46:05
 * @FilePath: \easyform\apps\easy-form\src\components\TabPanel\index.tsx
 * @Description:
 * Copyright (c) 2023 by Align, All Rights Reserved.
 */
import React from 'react'
import { Box } from '@mui/system'
export interface ITabPanelProps {
  children?: React.ReactNode
  showValue: number | string
  value: number | string
  height?: number | string
  width?: number | string
  position?: 'relative' | 'absolute' | 'fixed' | 'static'
  isPersistent?: boolean
  isPreRender?: boolean //是否预渲染组件（某些情况下需要使用未激活Tab组件的实例,开启预渲染渲染组件以便使用实例）
}
export default function TabPanel(props: ITabPanelProps) {
  const {
    children,
    showValue,
    value,
    height = '100%',
    width = 'auto',
    position = 'relative',
    isPersistent = true,
    isPreRender,
    ...other
  } = props
  const [rendered, setRendered] = React.useState<boolean>(showValue === value)
  React.useEffect(() => {
    if (!rendered && showValue === value) {
      setRendered(true)
    }
  }, [rendered, showValue, value])
  return (
    <Box hidden={showValue !== value} style={{ height: height, position: position, width: width }} {...other}>
      {((isPersistent && rendered) || showValue === value || isPreRender) && children}
    </Box>
  )
}
