import React, { useState, useMemo, useEffect } from 'react'
import { useTheme, styled, Box } from '@mui/system'
import { List, Form, CheckList, Popup, Button } from 'antd-mobile'
import SelfData from './SelfData'
import SelfWeekOrMonth from './SelfWeekOrMonth'

import { useEasyFormRequest } from '@/hooks'
import { mobilePublish } from '@/api/easyform'
import { request } from '@sui/runtime'
const StyledDiv = styled('div')<any>(({ noBorder }) => ({
  '.adm-list-item-content-main': {
    fontSize: ' var(--adm-font-size-7)',
    color: ' var(--adm-color-text-secondary)'
  }
}))
const basicColumnsWeek = [
  [
    { label: '周日', value: 'SUN' },
    { label: '周一', value: 'MON' },
    { label: '周二', value: 'TUES' },
    { label: '周三', value: 'WED' },
    { label: '周四', value: 'THUR' },
    { label: '周五', value: 'FRI' },
    { label: '周六', value: 'SAT' }
  ]
]
const basicColumnsObj: any = {
  Sun: '周日',
  Mon: '周一',
  Tues: '周二',
  Wed: '周三',
  Thur: '周四',
  Fri: '周五',
  Sat: '周六'
}
const resultMonth: any[] = [],
  basicColumnsMonth: any[] = []
for (let i = 1; i <= 31; i++) {
  resultMonth.push({ label: i + '日', value: i.toString() })
}
basicColumnsMonth.push(resultMonth)
const basicColumnsObjMonth: any = {}
resultMonth?.map((item) => {
  basicColumnsObjMonth[item.value] = item.label
})
let isInit = true
export default React.forwardRef<any, any>(function CycleClockTime(
  { value, onChange, cycleClockVal, appAssets, ...other }: any,
  ref
) {
  const [visible, setVisible] = useState(false)
  const [visibleWeekOrMonth, setVisibleWeekOrMonth] = useState(false)
  const [basicColumns, setBasicColumns] = useState<any>([])

  const sureFun = (data?: any[]) => {
    console.log(data, 'data')
    if (data) onChange(data)
    closeVis()
  }
  const closeVis = () => {
    if (cycleClockVal == 'day' || Array.isArray(cycleClockVal)) {
      setVisible(false)
    } else {
      setVisibleWeekOrMonth(false)
    }
  }
  useEffect(() => {
    console.log(cycleClockVal, 'cycleClockVal', isInit, value)
    cycleClockVal == 'week' ? setBasicColumns(basicColumnsWeek) : setBasicColumns(basicColumnsMonth)
    if (!isInit && cycleClockVal) {
      if (cycleClockVal == 'day' || Array.isArray(cycleClockVal)) {
        onChange(['00:00', '23:59'])
      } else {
        onChange([])
      }
    }
    isInit = false
    // !isInit && cycleClockVal && onChange([])
  }, [cycleClockVal])
  useEffect(() => {
    console.log(value, value.includes('00:00' && '23:59'), 'value打卡时段')
  }, [value])
  useEffect(() => {
    isInit = true
  }, [])
  return (
    <StyledDiv>
      <List mode='card'>
        <List.Item
          extra={
            cycleClockVal == 'day' || Array.isArray(cycleClockVal)
              ? value.includes('00:00' && '23:59')
                ? '不限'
                : value?.join(' - ')
              : cycleClockVal == 'week'
              ? basicColumnsObj[value]
              : basicColumnsObjMonth[value]
          }
          onClick={() => {
            isInit = false
            if (cycleClockVal == 'day' || Array.isArray(cycleClockVal)) setVisible(true)

            // : setVisibleWeekOrMonth(true)
          }}
        >
          打卡时段
        </List.Item>
      </List>

      <Popup
        visible={visible}
        onMaskClick={() => {
          setVisible(false)
        }}
        bodyStyle={{ height: '40vh' }}
      >
        {cycleClockVal == 'day' || Array.isArray(cycleClockVal) ? (
          <SelfData sureFun={sureFun} value={value} visible={visible} />
        ) : null}
      </Popup>
      {/* <SelfWeekOrMonth sureFun={sureFun} value={value} visible={visibleWeekOrMonth} data={basicColumns} /> */}
    </StyledDiv>
  )
})
