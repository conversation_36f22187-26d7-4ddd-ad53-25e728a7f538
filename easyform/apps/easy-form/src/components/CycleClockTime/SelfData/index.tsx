import React, { useCallback, useEffect, useState } from 'react'
import { <PERSON><PERSON>, DatePicker, Toast, DatePickerProps, Picker, Form } from 'antd-mobile'
import { styled } from '@mui/system'
import { PickerColumn, PickerValue } from 'antd-mobile/es/components/picker-view'

const StyledButton = styled(Button)<any>(({ theme }) => ({
  color: theme.palette.primary.main,
  fontSize: 14,
  border: 'none',
  backgroundColor: '#f4f4f4',
  width: '120px'
}))
const StyledDiv = styled('div')<any>(({ theme }) => ({
  '& .adm-form-item-horizontal': {
    margin: '50px 20px'
  },
  '& .adm-form-item-label': {
    display: 'flex',
    alignItems: 'center',
    fontWeight: 'bold',
    color: '#000'
  },
  '& .textbold': {
    fontWeight: 'bold'
  }
}))
const StyledHeader = styled('div')<any>(({ theme }) => ({}))

interface IProps {
  startTitle?: string
  endTitle?: string
  onChange?: (v: string[]) => void
  value?: string[]
  sureFun: (v?: any[]) => void
  visible: boolean
}
const formatTypes: any = {
  day: 'YYYY-MM-DD',
  year: 'YYYY',
  month: 'YYYY-MM',
  hour: 'YYYY-MM-DD HH:mm',
  minute: 'YYYY-MM-DD HH:mm'
}
const basicColumns: any[] = []
const result: any[] = [],
  result1: any[] = []
for (let i = 0; i <= 59; i++) {
  if (i <= 23) {
    result.push({ label: i < 10 ? '0' + i + '时' : i + '时', value: i < 10 ? '0' + i : i.toString() })
  }
  result1.push({ label: i < 10 ? '0' + i + '分' : i + '分', value: i < 10 ? '0' + i : i.toString() })
}
basicColumns.push(result)
basicColumns.push(result1)

type TProps = IProps & Pick<DatePickerProps, 'precision'>
export default React.forwardRef<HTMLDivElement, TProps>(
  (
    { startTitle = '选择时分', endTitle = '选择时分', onChange, value, precision = 'minute', visible, sureFun },
    ref
  ) => {
    const [openStart, setOpenStart] = React.useState(false)
    const [openEnd, setOpenEnd] = React.useState(false)
    const [startDate, setStartDate] = React.useState<any[]>()
    const [endDate, setEndDate] = React.useState<any>()
    const [innerValue, setInnerValue] = React.useState<string[]>([])
    const [val, setVal] = useState<any>(value || [])

    React.useEffect(() => {
      if (Array.isArray(val)) {
        if (val[0]) {
          setStartDate(val[0]?.split(':'))
        }
        if (val[1]) {
          setEndDate(val[1]?.split(':'))
        }
      }
    }, [val])

    const handleVal = (v: any[], i: number) => {
      console.log(v, i, val, 'vi')
      const vals: any[] = JSON.parse(JSON.stringify(val))
      vals[i] = v?.join(':')
      setVal(vals)
    }
    useEffect(() => {
      console.log(value, startDate, endDate, 'value')
      setVal(value || [])
    }, [value, visible])
    return (
      <StyledDiv>
        {/* <div className='flex items-center justify-between' ref={ref}> */}
        <StyledHeader className='adm-picker-header'>
          <a className='adm-picker-header-button' role='button' onClick={() => sureFun()}>
            取消
          </a>
          <span className='textbold'>请设置</span>
          <a
            className='adm-picker-header-button'
            role='button'
            onClick={() => {
              console.log(val, 'val')
              sureFun(val)
              // console.log(ST)
            }}
          >
            确定
          </a>
        </StyledHeader>
        <Form.Item label='打卡时段：' layout='horizontal'>
          <div className='flex items-center justify-between' ref={ref}>
            <Picker
              columns={basicColumns}
              value={startDate}
              onConfirm={(v: any) => {
                handleVal(v, 0)
                console.log(v, 'vstartDate')

                setStartDate(v)
              }}
            >
              {(_, actions) => (
                <StyledButton onClick={actions.open} block>
                  {' '}
                  {(val && val[0]) || startTitle}
                </StyledButton>
              )}
            </Picker>
            -
            <Picker
              columns={basicColumns}
              value={endDate}
              onConfirm={(v: any) => {
                handleVal(v, 1)
                setEndDate(v)
              }}
            >
              {(_, actions) => (
                <StyledButton onClick={actions.open} block>
                  {' '}
                  {(val && val[1]) || endTitle}
                </StyledButton>
              )}
            </Picker>
          </div>
        </Form.Item>
      </StyledDiv>
    )
  }
)
