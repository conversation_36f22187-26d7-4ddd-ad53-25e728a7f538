import React, { useCallback, useEffect, useState } from 'react'
import { But<PERSON>, DatePicker, Toast, DatePickerProps, Picker, Form } from 'antd-mobile'
import { styled } from '@mui/system'
import { PickerColumn, PickerValue } from 'antd-mobile/es/components/picker-view'

const StyledButton = styled(Button)<any>(({ theme }) => ({
  color: theme.palette.primary.main,
  fontSize: 14,
  border: 'none',
  backgroundColor: '#f4f4f4',
  width: '120px'
}))
const StyledDiv = styled('div')<any>(({ theme }) => ({
  '& .adm-form-item-horizontal': {
    margin: '50px 20px'
  },
  '& .adm-form-item-label': {
    display: 'flex',
    alignItems: 'center',
    fontWeight: 'bold',
    color: '#000'
  },
  '& .textbold': {
    fontWeight: 'bold'
  }
}))
const StyledHeader = styled('div')<any>(({ theme }) => ({}))

interface IProps {
  startTitle?: string
  endTitle?: string
  onChange?: (v: string[]) => void
  value?: string[]
  sureFun: (v?: any[]) => void
  visible: boolean
  data: any[]
}

type TProps = IProps & Pick<DatePickerProps, 'precision'>
export default React.forwardRef<HTMLDivElement, TProps>(
  (
    { startTitle = '选择时分', endTitle = '选择时分', onChange, value, data, visible, precision = 'minute', sureFun },
    ref
  ) => {
    const [val, setVal] = useState<(string | null)[]>(['M'])
    return (
      <StyledDiv>
        {' '}
        <Picker
          columns={data}
          visible={visible}
          onClose={() => {
            sureFun()
            // setVisible(false)
          }}
          value={value}
          onConfirm={(v: any) => {
            console.log(v, 'vvv')
            sureFun(v)
            setVal(v)
          }}
        />
      </StyledDiv>
    )
  }
)
