import React from 'react'
import { Box, styled, keyframes } from '@mui/system'
import { Image } from 'antd-mobile'
import { Typography } from '@/components'
import produce from 'immer'
import { useMemoizedFn } from 'ahooks'

interface IProps {
  message?: any
  onClose?: (id: string) => void
}

const appear = keyframes`
    0% {
        transform: translateY(-100%)
    }
    100% {
        transform: translateY(12px)
    }
`
const fadeout = keyframes`
    0% {
        opacity:1
    }
    100% {
        opacity:0
    }
`

const NoticeListWrapper = styled(Box)<any>(({ show }) => ({
  position: 'fixed',
  zIndex: 9999,
  top: 0,
  left: 12,
  right: 12,
  display: 'flex',

  flexDirection: 'column-reverse',
  '&>*': {
    marginBottom: 12
  }
}))
const NoticeWrapper = styled(Box)({
  background: 'rgba(255,255,255,0.95)',
  borderRadius: 8,
  padding: '8px 16px',
  animation: `${appear} 0.45s ease-in-out,${fadeout} 0.35s ease-in-out 4s`,
  animationFillMode: 'forwards'
})

const Notice = ({ id, content = '未知消息', onClose }: any) => {
  const handleClose = useMemoizedFn(() => {
    if (typeof onClose === 'function') {
      onClose(id)
    }
  })
  React.useEffect(() => {
    setTimeout(handleClose, 6000)
  }, [handleClose])
  return (
    <NoticeWrapper>
      <Box className='flex' sx={{ py: 0.5 }}>
        <Image src='./logo.svg' width={16} height={16} fit='contain' />
        <Typography sx={{ ml: 1 }} variant='body2'>
          {content}
        </Typography>
      </Box>
    </NoticeWrapper>
  )
}

export default React.forwardRef<HTMLDivElement, IProps>(({ message, onClose }, ref) => {
  const [notices, setNotices] = React.useState<any>([])
  const handleClose = (id: string) => {
    setNotices((prev: any) =>
      produce(prev, (draft: any) => {
        const _i = prev.findIndex((n: any) => n.id === id)
        draft.splice(_i, 1)
      })
    )
    if (typeof onClose === 'function') {
      onClose(id)
    }
  }
  React.useEffect(() => {
    if (message) {
      setNotices((prev: any[]) => [...prev, message])
    }
  }, [message])
  return (
    <NoticeListWrapper>
      {notices.map(({ id, content }: any) => (
        <Notice onClose={handleClose} key={id} content={content} />
      ))}
    </NoticeListWrapper>
  )
})
