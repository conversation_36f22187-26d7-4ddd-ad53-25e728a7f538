import React from 'react'
import { Box, styled } from '@mui/system'
import { <PERSON><PERSON>, <PERSON>bs, DotLoading, InfiniteScroll, Avatar } from 'antd-mobile'
import List from '../../../List'
import { Collapse, TabPanel, Typography } from '@/components'
import { useEasyFormRequest } from '@/hooks'
import { loadPosition } from '../../../api'
import { IconPosition } from '../../../icon'

import { InfiniteScrollContent } from '../index'

interface IListProps {
  orgCode: string
  orgName?: string
  onCheck?: (type: string, itemData: any, checked?: boolean) => void
  selected?: any
}

export default function ({ orgCode, onCheck, selected, orgName }: IListProps) {
  const [{ pageNo, pageSize }, setPagination] = React.useState({ pageNo: 1, pageSize: 20 })
  const [hasMore, setHasMore] = React.useState(true)
  const [list, setList] = React.useState<any>([])
  const { runAsync: request, loading } = useEasyFormRequest(loadPosition, {
    manual: true,
    loadingDelay: 200,
    throttleWait: 300
  })
  const loadMore = async () => {
    const _data = await request(orgCode, { pageNo, pageSize })
    const _hasMore = _data.total > pageSize * pageNo
    setHasMore(_hasMore)
    setList([...list, ..._data.data])
    if (_hasMore) {
      setPagination((prev) => ({ ...prev, pageNo: prev.pageNo + 1 }))
    }
  }
  const handleCheck = (itemData: any, checked?: boolean) => {
    if (typeof onCheck === 'function') {
      onCheck('pos', itemData, checked)
    }
  }
  return (
    <Box>
      {loading ? (
        <Box className='flex justify-center' sx={{ p: 2 }}>
          <DotLoading />
        </Box>
      ) : (
        <List>
          {list.length === 0 ? (
            <Typography sx={{ mt: 2 }} align='center' variant='subtitle2'>
              当前机构暂无岗位数据
            </Typography>
          ) : (
            <>
              {list.map((l: any) => (
                <List.Item
                  key={l.code}
                  checked={!!selected?.pos?.find((n: any) => n.code === l.code)}
                  onCheck={(c) =>
                    handleCheck({ code: l.code, name: `${l.name}${orgName ? '(' + orgName + ')' : ''}` }, c)
                  }
                  prefix={<IconPosition style={{ color: '#999' }} width='16px' height='16px' />}
                  checkable
                  title={l.name}
                />
              ))}
            </>
          )}
          <InfiniteScroll hasMore={hasMore} loadMore={loadMore}>
            <InfiniteScrollContent hasMore={hasMore} />
          </InfiniteScroll>
        </List>
      )}
    </Box>
  )
}
