import React from 'react'
import { Box, styled } from '@mui/system'
import { Button, Tabs, DotLoading, InfiniteScroll, Avatar, SearchBar } from 'antd-mobile'
import List from '../../../List'
import { Collapse, TabPanel, Typography } from '@/components'
import { useEasyFormRequest } from '@/hooks'
import { loadOrg, loadUser } from '../../../api'
import { IconOrg, IconLevel } from '../../../icon'

import { InfiniteScrollContent } from '../index'

const StyleIconLevel = styled(IconLevel)(({ theme }) => ({
  color: theme.palette.primary.main,
  marginRight: 4
}))

const StyledButton = styled(Button)({
  '& span': {
    display: 'flex',
    alignItems: 'center',
    fontSize: 12
  }
})

export const BtnEnter = ({ onClick, text = '进入下级', ...other }: any) => {
  return (
    <StyledButton
      size='small'
      className='flex items-center shrink-0'
      onClick={onClick}
      fill='none'
      color='primary'
      {...other}
    >
      <StyleIconLevel width='14px' height='14px' />
      {text}
    </StyledButton>
  )
}

interface IOrgListProps {
  orgCode: string
  onEnterView: (
    orgCode: string,
    orgName?: string,
    viewType?: string,
    positionsCount?: number,
    clazzsCount?: number
  ) => void
  onCheck?: (type: string, itemData: any, checked?: boolean) => void
  selected?: any
  disabledPermission?: boolean
}

export default function ({ orgCode, onEnterView, onCheck, selected, disabledPermission }: IOrgListProps) {
  const [{ pageNo, pageSize }, setPagination] = React.useState({ pageNo: 1, pageSize: 20 })
  const [hasMore, setHasMore] = React.useState(true)
  const [list, setList] = React.useState<any>([])
  const [userList, setuserList] = React.useState<any>([])
  const [keywords, setKeywords] = React.useState<string>()

  const handleCheck = (type: string, itemData: any, checked?: boolean) => {
    if (typeof onCheck === 'function') {
      onCheck(type, itemData, checked)
    }
  }

  const { data, loading } = useEasyFormRequest(() => loadOrg(orgCode, disabledPermission), {
    manual: false,
    loadingDelay: 200
  })
  const { runAsync: requestUser } = useEasyFormRequest(loadUser, {
    manual: true
  })
  const loadMoreUser = async () => {
    const _userData = await requestUser({ pageNo, pageSize, keywords, orgCode }, disabledPermission)
    const _hasMore = _userData.total > pageSize * pageNo
    setHasMore(_hasMore)
    setuserList([...userList, ..._userData.data])
    if (_hasMore) {
      setPagination((prev) => ({ ...prev, pageNo: prev.pageNo + 1 }))
    }
  }
  const handleChangeView = (code: string, name?: string, positionsCount?: number, clazzsCount?: number) => {
    if (typeof onEnterView === 'function') {
      onEnterView(code, name, 'org', positionsCount, clazzsCount)
    }
  }

  const handleSearch = async (sv: string) => {
    setKeywords(sv)
    const _userData = await requestUser({ pageNo: 1, pageSize: 20, keywords: sv, orgCode: orgCode })
    const _hasMore = _userData.total > pageSize * pageNo
    setHasMore(_hasMore)
    setuserList([..._userData.data])
    setPagination((prev) => ({ ...prev, pageNo: _hasMore ? 1 + 1 : 1 }))
  }
  const handleClearSearch = async () => {
    setKeywords(void 0)
    const _userData = await requestUser({ pageNo: 1, pageSize: 20, keywords: void 0, orgCode: orgCode })
    const _hasMore = _userData.total > pageSize * pageNo
    setHasMore(_hasMore)
    setuserList([..._userData.data])
    setPagination((prev) => ({ ...prev, pageNo: _hasMore ? 1 + 1 : 1 }))
  }

  const filterOrgList = React.useMemo(() => list.filter((i: any) => i.name.includes(keywords || '')), [keywords, list])

  React.useEffect(() => {
    if (data) {
      setList(data.data.orgs)
    }
  }, [data])
  return (
    <Box>
      <Box>
        <SearchBar onSearch={handleSearch} onClear={handleClearSearch} placeholder='请输入关键字搜索' />
      </Box>
      {loading ? (
        <Box className='flex justify-center' sx={{ p: 2 }}>
          <DotLoading />
        </Box>
      ) : (
        <List>
          {filterOrgList.length === 0 && userList.length === 0 ? (
            <Typography sx={{ mt: 2 }} align='center' variant='subtitle2'>
              暂无数据
            </Typography>
          ) : (
            <>
              {filterOrgList.map((l: any) => (
                <List.Item
                  checked={!!selected?.org?.find((n: any) => n.code === l.code)}
                  onCheck={(c) => {
                    handleCheck('org', { code: l.code, name: l.name }, c)
                  }}
                  key={l.code}
                  prefix={<IconOrg style={{ color: '#999' }} width='16px' height='16px' />}
                  checkable
                  title={l.name}
                  arrow={
                    <BtnEnter
                      disabled={!!selected?.org?.find((n: any) => n.code === l.code)}
                      onClick={() => handleChangeView(l.code, l.name, l.positions?.total, l.clazzs?.total)}
                    />
                  }
                />
              ))}
              {userList.map((l: any) => (
                <List.Item
                  checked={!!selected?.user?.find((n: any) => n.code === l.code)}
                  onCheck={(c) => {
                    handleCheck('user', { code: l.code, name: l.name }, c)
                  }}
                  prefix={<Avatar src='' style={{ '--size': '24px', '--border-radius': '50%' }} />}
                  key={l.code}
                  checkable
                  title={`${l.name} (${l.code})`}
                />
              ))}
            </>
          )}
          <InfiniteScroll hasMore={hasMore} loadMore={loadMoreUser}>
            <InfiniteScrollContent hasMore={hasMore} />
          </InfiniteScroll>
        </List>
      )}
    </Box>
  )
}
