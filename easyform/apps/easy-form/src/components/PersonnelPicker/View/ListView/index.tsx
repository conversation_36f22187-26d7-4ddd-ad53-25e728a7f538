import React from 'react'
import { Box, styled } from '@mui/system'
import { Button, Tabs } from 'antd-mobile'
import List from '../../List'
import { Collapse, TabPanel, Typography } from '@/components'
import { useEasyFormRequest } from '@/hooks'
import { IViewProps } from '../index'

interface IListViewProps extends IViewProps {
  dataset?: any[]
  prefix?: React.ReactNode
}

export default function ({ dataset = [], prefix, selected, onCheck, viewType }: IListViewProps) {
  const handleCheck = (itemData: any, checked?: boolean) => {
    if (typeof onCheck === 'function') {
      onCheck(viewType, itemData, checked)
    }
  }
  return (
    <Box sx={{ p: 1.5 }}>
      <List>
        {dataset?.length > 0 ? (
          dataset.map(({ code, name }, i) => (
            <List.Item
              onCheck={(c) => handleCheck({ code, name }, c)}
              checked={selected && !!selected[viewType]?.find((n: any) => n.code === code)}
              prefix={prefix}
              checkable
              title={name}
              key={code}
            />
          ))
        ) : (
          <Typography align='center' sx={{ my: 2 }} variant='subtitle1'>
            暂无数据
          </Typography>
        )}
      </List>
    </Box>
  )
}
