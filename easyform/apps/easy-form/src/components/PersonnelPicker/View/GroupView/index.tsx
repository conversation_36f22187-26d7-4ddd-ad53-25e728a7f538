import React from 'react'
import { Box, styled } from '@mui/system'
import { <PERSON>ton, <PERSON>bs, DotLoading, InfiniteScroll, Avatar, SearchBar } from 'antd-mobile'
import List from '../../List'
import { Collapse, TabPanel, Typography } from '@/components'
import { useEasyFormRequest } from '@/hooks'
import { loadGroup } from '../../api'
import { IconRole } from '../../icon'
import { InfiniteScrollContent } from '../OrgView'

interface IListProps {
  orgName?: string
  onCheck?: (type: string, itemData: any, checked?: boolean) => void
  selected?: any
}

export default function ({ onCheck, selected }: IListProps) {
  const [{ pageNo, pageSize }, setPagination] = React.useState({ pageNo: 1, pageSize: 20 })
  const [hasMore, setHasMore] = React.useState(true)
  const [list, setList] = React.useState<any>([])
  const { runAsync: request, loading } = useEasyFormRequest(loadGroup, {
    manual: true,
    throttleWait: 200,
    loadingDelay: 200
  })
  const [keywords, setKeywords] = React.useState<string>()

  const loadMore = async () => {
    const _data = await request({ pageNo, pageSize, name: keywords })
    const _hasMore = _data.total > pageSize * pageNo
    setHasMore(_hasMore)
    setList([...list, ..._data.data])
    if (_hasMore) {
      setPagination((prev) => ({ ...prev, pageNo: prev.pageNo + 1 }))
    }
  }
  const handleCheck = (itemData: any, checked?: boolean) => {
    if (typeof onCheck === 'function') {
      onCheck('group', itemData, checked)
    }
  }

  const handleSearch = async (sv: string) => {
    setKeywords(sv)
    const _data = await request({ pageNo: 1, pageSize: 20, name: sv })
    const _hasMore = _data.total > pageSize * pageNo
    setHasMore(_hasMore)
    setList([..._data.data])
    setPagination((prev) => ({ ...prev, pageNo: _hasMore ? 1 + 1 : 1 }))
  }
  const handleClearSearch = async () => {
    setKeywords(void 0)
    const _data = await request({ pageNo: 1, pageSize: 20, name: void 0 })
    const _hasMore = _data.total > pageSize * pageNo
    setHasMore(_hasMore)
    setList([..._data.data])
    setPagination((prev) => ({ ...prev, pageNo: _hasMore ? 1 + 1 : 1 }))
  }

  return (
    <Box sx={{ p: 1.5 }}>
      <Box>
        <SearchBar onSearch={handleSearch} onClear={handleClearSearch} placeholder='请输入关键字搜索' />
      </Box>
      <List>
        {list.length === 0 ? (
          <Typography sx={{ mt: 2 }} align='center' variant='subtitle2'>
            暂无数据
          </Typography>
        ) : (
          <>
            {list.map((l: any) => (
              <List.Item
                checked={!!selected?.group?.find((n: any) => n.code === l.code)}
                onCheck={(c) => handleCheck({ code: l.code, name: `${l.name}` }, c)}
                key={l.code}
                prefix={<IconRole style={{ color: '#999' }} width='16px' height='16px' />}
                checkable
                title={l.name}
              />
            ))}
          </>
        )}
        <InfiniteScroll hasMore={hasMore} loadMore={loadMore}>
          <InfiniteScrollContent hasMore={hasMore} />
        </InfiniteScroll>
      </List>
    </Box>
  )
}
