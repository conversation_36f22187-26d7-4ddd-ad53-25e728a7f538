import React from 'react'
import { Box, styled } from '@mui/system'
import { <PERSON>up, <PERSON><PERSON>, <PERSON>wiper, DotLoading, Avatar, Ellipsis } from 'antd-mobile'
import { LeftOutline as IconBack, CloseOutline as IconClose } from 'antd-mobile-icons'
import { useBoolean, useMemoizedFn } from 'ahooks'
import produce from 'immer'

import { useEasyFormRequest } from '@/hooks'
import { Typography, Collapse } from '@/components'
import List from './List'
import {
  OrgView as MyOrgView,
  ListView,
  GroupView,
  PositionsetView,
  ClazzUserView,
  ClazzView,
  breadcrumbsDataItem
} from './View'
import * as Api from './api'
import { IconOrg, IconPosition, IconClazz, IconRole } from './icon'

const OrgView = React.memo(MyOrgView)

const RootCategory = [
  {
    key: 'org',
    name: '按架构选'
  },
  {
    key: 'org',
    name: '选岗位集'
  },
  {
    key: 'clazz',
    name: '选班级'
  }
]

const RoundRect = styled(Box)(({ theme }) => ({
  display: 'flex',
  justifyContent: 'center',
  alignItems: 'center',
  borderRadius: 8,
  width: 36,
  height: 36,
  backgroundColor: theme.palette.divider
}))
const StyledButton = styled(Button, { shouldForwardProp: (prop) => prop !== 'hasSelected' })<any>(
  ({ theme, hasSelected }) => ({
    fontSize: '14px',
    color: theme.palette.text.secondary
  })
)
const MiniButton = styled(Button)({
  '--adm-font-size-main': '12px',
  padding: '2px 4px'
})

export type CategoryItem = {
  name: string
  code: string
}
interface IPersonalPicker {
  value?: any
  onChange?: (value: any, desc?: string) => void
  text?: string
  renderValue?: (value: any) => any
  categories?: CategoryItem[]
  advanced?: boolean
  disabledPermission?: boolean
  extra?: any[]
}

const Views: any = {
  org: OrgView,
  posset: PositionsetView,
  group: GroupView,
  clazzUser: ClazzUserView,
  clazz: ClazzView
}

interface IHeaderProps {
  closeable?: boolean
  title?: string
  onBack?: () => void
  onClose?: () => void
}
const Header = ({ closeable, title = '', onBack, onClose }: IHeaderProps) => {
  return (
    <Box sx={{ height: '40px', bgcolor: '#FFF', px: 1.5 }} className='flex items-center justify-between'>
      <Box sx={{ width: 48 }}>
        <IconBack onClick={onBack} />
        {closeable && <IconClose onClick={onClose} style={{ marginLeft: 16 }} />}
      </Box>
      <Typography variant='subtitle1'>{title}</Typography>
      <div />
    </Box>
  )
}

const ContentWrapper = styled(Box)({
  height: 'calc(100vh - 100px)',
  overflow: 'auto'
})

const FooterWrapper = styled(Box)(({ theme }) => ({
  display: 'flex',
  alignItems: 'center',
  justifyContent: 'space-between',
  height: '60px',
  width: '100%',
  // borderTop: `1px solid ${theme.palette.divider}`,
  backgroundColor: theme.palette.background.default,
  padding: '0 12px'
}))

const IconType: any = {
  org: <IconOrg width='16px' height='16px' style={{ color: '#999' }} />,
  posset: <IconPosition width='16px' height='16px' style={{ color: '#999' }} />,
  clazz: <IconClazz width='16px' height='16px' style={{ color: '#999' }} />,
  pos: <IconPosition width='16px' height='16px' style={{ color: '#999' }} />,
  cat: <IconRole width='16px' height='16px' style={{ color: '#999' }} />,
  user: <Avatar src='' style={{ '--size': '24px', '--border-radius': '50%' }} />
}
const TextType: any = {
  org: '机构',
  posset: '岗位集',
  clazz: '班级',
  user: '用户',
  pos: '岗位',
  cat: '自动筛选',
  group: '用户组',
  extra: '分类筛选'
}
const getFrindlyInfo = (selected: any) => {
  const res: any = []
  Object.entries(selected).forEach(([k, v]: any) => {
    if (v.length > 0) {
      res.push(`${v.length}个${TextType[k]} `)
    }
  })
  return res.join(',')
}

export default React.forwardRef<any, IPersonalPicker>(
  ({ text = '选择参与者范围', value, onChange, renderValue, categories, advanced, disabledPermission, extra }, ref) => {
    const [open, { toggle: toggleOpen }] = useBoolean(false)
    const [openSelected, { toggle: toggleSelected }] = useBoolean(false)
    const swiperRef = React.useRef<any>(null)
    const [extraViews, setExtraViews] = React.useState<any>([])
    const [currentIndex, setCurrentIndex] = React.useState(0)
    /**
     *{
       org:[],
       user:[],
       posset:[],
       clazz:[]
       pos:[]
       cat:[]
     }
     */
    const [selected, setSelected] = React.useState<any>({})

    /**
     * 组织机构面包屑数据
     */
    const [breadcrumbs, setBreadcrumbs] = React.useState<breadcrumbsDataItem[]>([])

    const handleSelected = useMemoizedFn((type: string, itemData: any) => {
      console.log('handleselected:', type, itemData)
      setSelected(
        produce(selected, (draft: any) => {
          if (!draft[type]) {
            draft[type] = [itemData]
          } else {
            const _existIndex = draft[type].findIndex((n: any) => n.code === itemData.code)
            if (_existIndex !== -1) {
              draft[type].splice(_existIndex, 1)
            } else {
              draft[type].push(itemData)
            }
          }
        })
      )
    })
    /**
     * 加载初始数据
     */
    const { data, loading } = useEasyFormRequest(() => Api.loadOrg(void 0, disabledPermission), {
      ready: open,
      loadingDelay: 200
    })

    const handleEnterView = useMemoizedFn((viewType: string, extra: any) => {
      // console.log('enter view:', viewType, extra)
      setExtraViews((prev: any) =>
        produce(prev, (draft: any) => {
          draft.push({ viewType: viewType, extra: extra })
        })
      )
      setTimeout(() => {
        swiperRef.current.swipeNext()
      }, 100)
    })
    const handleDestroyView = useMemoizedFn((destroyAll?: boolean, targetIndex?: number) => {
      if (destroyAll) {
        swiperRef.current.swipeTo(0)
        setBreadcrumbs((prev) =>
          produce(prev, (draft) => {
            draft.splice(1, prev.length - 1)
          })
        )
        setTimeout(() => {
          setExtraViews([])
        }, 500)
      } else {
        if (targetIndex === void 0) {
          const lastIndex = extraViews.length - 1
          if (extraViews[lastIndex].viewType === 'org') {
            setBreadcrumbs((prev) =>
              produce(prev, (draft) => {
                draft.splice(prev.length - 1, 1)
              })
            )
          }
          swiperRef.current.swipePrev()
          setTimeout(() => {
            setExtraViews((prev: any) =>
              produce(prev, (draft: any) => {
                draft.splice(lastIndex, 1)
              })
            )
          }, 500)
        } else {
          const len = breadcrumbs.length - 1 - targetIndex
          setBreadcrumbs((prev) =>
            produce(prev, (draft) => {
              draft.splice(targetIndex + 1, len)
            })
          )
          swiperRef.current.swipeTo(targetIndex)
          setTimeout(() => {
            setExtraViews((prev: any) =>
              produce(prev, (draft: any) => {
                /**
                 * extara views 额外的view
                 */
                draft.splice(targetIndex, len)
              })
            )
          }, 500)
        }
      }
    })
    const handleIndexChange = useMemoizedFn((index: number) => {
      console.log('===change:', index)
      setCurrentIndex(index)
    })
    const handleBack = useMemoizedFn(() => {
      if (currentIndex === 0) {
        toggleOpen()
      } else {
        handleDestroyView()
      }
    })

    const handleOk = useMemoizedFn((e: React.MouseEvent) => {
      e.stopPropagation()
      console.log(selected)
      if (typeof onChange === 'function') {
        onChange(selected, selectedText)
      }
      toggleOpen()
    })

    const selectedText = React.useMemo(() => getFrindlyInfo(selected), [selected])

    const selectedList = React.useMemo(() => {
      return Object.entries(selected).reduce((p: any, [k, v]: any) => {
        return [...p, ...v.map((i: any) => ({ ...i, type: k }))]
      }, [])
    }, [selected])

    React.useEffect(() => {
      if (value) {
        setSelected(value)
      }
    }, [value])

    React.useImperativeHandle(ref, () => ({
      toggle: () => toggleOpen()
    }))

    const handleEnter = useMemoizedFn((target, viewType = 'org') => {
      handleEnterView(viewType, target)
      if (viewType === 'org') {
        setTimeout(() => {
          setBreadcrumbs(
            produce(breadcrumbs, (draft) => {
              draft.push({ id: target.code, name: target.name })
            })
          )
        }, 300)
      }
    })

    const handleBreadClick = useMemoizedFn((index: number) => {
      handleDestroyView(false, index)
    })

    const [initOrg, initManageClass] = React.useMemo(
      () => [data?.data?.orgs, data?.data?.manageClasses],
      [data?.data?.manageClasses, data?.data?.orgs]
    )

    React.useEffect(() => {
      if (initOrg) {
        setBreadcrumbs([{ id: initOrg[0].code, name: initOrg[0].name }])
      }
    }, [initOrg])

    return (
      <>
        {renderValue ? (
          renderValue(selected)
        ) : (
          <Box>
            <StyledButton onClick={toggleOpen}>
              <Ellipsis content={selectedText || text} />
            </StyledButton>
          </Box>
        )}
        <Popup visible={open}>
          <Box className='h-screen relative'>
            <Header
              title={text}
              onClose={() => handleDestroyView(true)}
              closeable={currentIndex > 1}
              onBack={handleBack}
            />
            <ContentWrapper>
              {loading ? (
                <Box sx={{ p: 2 }} className='flex justify-center'>
                  <DotLoading />
                </Box>
              ) : !initOrg && !initManageClass ? (
                <Typography variant='subtitle2' sx={{ mb: 2 }} align='center'>
                  暂无数据
                </Typography>
              ) : (
                <Swiper
                  style={{ height: '100%' }}
                  onIndexChange={handleIndexChange}
                  ref={swiperRef}
                  loop={false}
                  allowTouchMove={false}
                  indicator={() => null}
                >
                  <Swiper.Item>
                    <Box className='h-full overflow-auto'>
                      <Box sx={{ pb: 1, borderBottom: 10, borderColor: 'divider' }} className='flex justify-center'>
                        {/* <Box sx={{ mx: 2 }} className='flex flex-col items-center'>
                          <RoundRect sx={{ mb: 0.5 }}>
                            <IconOrg />
                          </RoundRect>
                          <Typography variant='subtitle2'>按架构选</Typography>
                        </Box> */}
                        {advanced && (
                          <>
                            <Box
                              onClick={() =>
                                handleEnterView('posset', {
                                  // dataset: data?.data?.positionSets,
                                  // prefix: <IconPosition width='16px' height='16px' style={{ color: '#999' }} />
                                })
                              }
                              sx={{ mx: 2 }}
                              className='flex flex-col items-center'
                            >
                              <RoundRect sx={{ mb: 0.5 }}>
                                <IconPosition />
                              </RoundRect>
                              <Typography variant='subtitle2'>按岗位集选</Typography>
                            </Box>
                            <Box
                              onClick={() =>
                                handleEnterView('group', {
                                  // dataset: data?.data?.manageClasses,
                                  // prefix: <IconRole width='16px' height='16px' style={{ color: '#999' }} />
                                })
                              }
                              sx={{ mx: 2 }}
                              className='flex flex-col items-center'
                            >
                              <RoundRect sx={{ mb: 0.5 }}>
                                <IconRole />
                              </RoundRect>
                              <Typography variant='subtitle2'>按用户组选</Typography>
                            </Box>
                          </>
                        )}
                        <Box
                          onClick={() =>
                            handleEnterView('clazz', {
                              dataset: initManageClass || [],
                              prefix: <IconClazz width='16px' height='16px' style={{ color: '#999' }} />
                            })
                          }
                          sx={{ mx: 2 }}
                          className='flex flex-col items-center'
                        >
                          <RoundRect sx={{ mb: 0.5 }}>
                            <IconClazz />
                          </RoundRect>
                          <Typography variant='subtitle2'>选所管班级</Typography>
                        </Box>
                      </Box>

                      <Box>
                        {initOrg?.length > 0 ? (
                          <OrgView
                            breadcrumbs={breadcrumbs}
                            viewType='org'
                            selected={selected}
                            onCheck={handleSelected}
                            categories={categories}
                            clazzsCount={data?.data?.orgs[0]?.clazzs?.total}
                            postionsCount={data?.data?.orgs[0]?.positions?.total}
                            onEnterView={handleEnter}
                            code={data?.data?.orgs[0]?.code}
                            name={data?.data?.orgs[0]?.name}
                            onBreadClick={handleBreadClick}
                            disabledPermission={disabledPermission}
                          />
                        ) : (
                          <ClazzView
                            extra={extra}
                            selected={selected}
                            dataset={initManageClass}
                            onEnterView={handleEnter}
                            onCheck={handleSelected}
                          />
                        )}
                      </Box>
                    </Box>
                  </Swiper.Item>
                  {extraViews.map(({ viewType, extra }: any, i: number) => (
                    <Swiper.Item key={i}>
                      <Box className='h-full overflow-auto'>
                        {React.createElement(Views[viewType], {
                          ...extra,
                          onEnterView: handleEnter,
                          selected: selected,
                          viewType: viewType,
                          categories: categories,
                          onCheck: handleSelected,
                          breadcrumbs: breadcrumbs,
                          onBreadClick: handleBreadClick,
                          disabledPermission: disabledPermission
                        })}
                      </Box>
                    </Swiper.Item>
                  ))}
                </Swiper>
              )}
            </ContentWrapper>
            <FooterWrapper
              onClick={() => {
                selectedList.length > 0 && toggleSelected()
              }}
            >
              <Typography variant='subtitle2'>{selectedText ? `已选择：${selectedText}` : '请选择'}</Typography>
              <Button onClick={handleOk} color='primary' size='small'>
                确定
              </Button>
              {/* <Button onClick={handleOk} color='primary' size='small' disabled={!selectedText}>
                确定
              </Button> */}
            </FooterWrapper>
          </Box>
        </Popup>
        <Popup
          onMaskClick={toggleSelected}
          bodyStyle={{ height: '80vh', borderTopLeftRadius: '8px', borderTopRightRadius: '8px' }}
          visible={openSelected}
        >
          <Box className='flex items-center justify-between' sx={{ height: 32, px: 1.5 }}>
            <div />
            <Typography variant='subtitle1'>已选择</Typography>
            <IconClose onClick={toggleSelected} />
          </Box>
          <Box className='overflow-auto' sx={{ height: 'calc(100% - 32px)', px: 1.5 }}>
            <List>
              {selectedList.map(({ code, name, type }) => (
                <List.Item
                  key={code}
                  title={name}
                  prefix={IconType[type]}
                  arrow={
                    <MiniButton
                      onClick={() => handleSelected(type, { code, name })}
                      fill='outline'
                      color='danger'
                      size='mini'
                    >
                      移除
                    </MiniButton>
                  }
                />
              ))}
            </List>
          </Box>
        </Popup>
      </>
    )
  }
)
