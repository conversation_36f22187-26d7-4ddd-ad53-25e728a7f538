import React from 'react'
import { Box, styled } from '@mui/system'
import { Checkbox } from 'antd-mobile'
import { DownOutline as IconArrowDown, UpOutline as IconArrowUp } from 'antd-mobile-icons'
import { useBoolean } from 'ahooks'

import { Typography, Collapse } from '@/components'

interface IListItemProps {
  title?: React.ReactNode
  checkable?: boolean
  arrow?: boolean | React.ReactNode
  prefix?: React.ReactNode
  clickable?: boolean
  childData?: any[]
  onCheck?: (checked: boolean) => void
  checked?: boolean
}

const ListRoot = styled(Box)(({ theme }) => ({
  // padding: '0 12px'
}))

const ItemRoot = styled(Box)(({ theme }) => ({
  display: 'flex',
  alignItems: 'center',
  '& .list-item-content': {
    borderBottom: `1px solid ${theme.palette.divider}`
  },
  '&:last-child .list-item-content': {
    borderBottom: 'none'
  },
  '&.a:active': {
    backgroundColor: theme.palette.divider
  }
}))
const StyledCheckbox = styled(Checkbox)({
  '--icon-size': '18px'
})

const IconArrowUpOrDown = styled(IconArrowDown)<any>(({ theme, open }) => ({
  transition: 'transform 0.35s ease-in-out',
  transform: open ? 'rotate(180deg)' : 'rotate(0deg)'
}))

export function ListItem({ title, checkable, arrow, prefix, clickable, childData, onCheck, checked }: IListItemProps) {
  const hasChildren = React.useMemo(() => Array.isArray(childData) && childData.length > 0, [childData])
  const [open, { toggle }] = useBoolean(false)
  return (
    <>
      <ItemRoot onClick={toggle} component={clickable ? 'a' : 'div'}>
        {checkable && <StyledCheckbox checked={checked} onChange={onCheck} style={{ marginRight: 8 }} />}
        <Box className='flex-1 flex items-center list-item-content' sx={{ py: 1.5 }}>
          {prefix && (
            <Box className='flex items-center' sx={{ mr: 1 }}>
              {prefix}
            </Box>
          )}
          <Typography variant='body2' className='flex-grow'>
            {title}
          </Typography>
          {hasChildren ? <IconArrowUpOrDown open={open} /> : arrow ? arrow : null}
        </Box>
      </ItemRoot>
      {hasChildren && (
        <Collapse in={open}>
          <Box sx={{ pl: 2 }}>
            {childData?.map((child, i) => (
              <ListItem key={i} {...child} />
            ))}
          </Box>
        </Collapse>
      )}
    </>
  )
}

interface IListProps {
  children?: React.ReactNode
  sx?: any
}
function List({ children, sx }: IListProps) {
  return <ListRoot>{children}</ListRoot>
}

List.Item = ListItem

export default List
