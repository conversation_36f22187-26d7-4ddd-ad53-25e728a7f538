import React, { useCallback, useEffect, useState } from 'react'
import { <PERSON><PERSON>, Date<PERSON>icker, Selector, DatePickerProps, Picker, Form } from 'antd-mobile'
import { styled } from '@mui/system'
import { PickerColumn, PickerValue } from 'antd-mobile/es/components/picker-view'

const StyledButton = styled(Button)<any>(({ theme }) => ({
  color: theme.palette.primary.main,
  fontSize: 14,
  border: 'none',
  backgroundColor: '#f4f4f4',
  width: '120px'
}))
const StyledSelector = styled(Selector)(({ theme }) => {
  console.log(theme, 'them')

  return {
    // '--border-radius': '100px',
    '--checked-color': theme.palette.primary,
    // '--border': '1px solid red',
    // '--checked-border': 'solid red 1px',
    '.adm-selector .adm-space-horizontal': {
      padding: '20px 0px 10px 16px'
    },
    '& .adm-selector-item': {
      width: '77px',
      borderRadius: '2px'
    }
    // '& .adm-selector-item-active': {
    //   border: '1px solid red'
    // }
  }
})
const StyledDiv = styled('div')<any>(({ theme }) => ({
  '& .adm-form-item-horizontal': {
    margin: '50px 20px'
  },
  '& .adm-form-item-label': {
    display: 'flex',
    alignItems: 'center',
    fontWeight: 'bold',
    color: '#000'
  },
  '& .textbold': {
    fontWeight: 'bold'
  }
}))
const StyledHeader = styled('div')<any>(({ theme }) => ({}))

interface IProps {
  startTitle?: string
  endTitle?: string
  onChange?: (v: string[]) => void
  value?: string[]
  sureFun: (v?: any[]) => void
  visible: boolean
}
const basicColumnsWeek: any[] = [
  { label: '周一', value: 'MON' },
  { label: '周二', value: 'TUES' },
  { label: '周三', value: 'WED' },
  { label: '周四', value: 'THUR' },
  { label: '周五', value: 'FRI' },
  { label: '周六', value: 'SAT' },
  { label: '周日', value: 'SUN' }
]

type TProps = IProps & Pick<DatePickerProps, 'precision'>
export default React.forwardRef<HTMLDivElement, TProps>(
  (
    { startTitle = '选择时分', endTitle = '选择时分', onChange, value, precision = 'minute', visible, sureFun },
    ref
  ) => {
    const [openStart, setOpenStart] = React.useState(false)
    const [openEnd, setOpenEnd] = React.useState(false)
    const [startDate, setStartDate] = React.useState<any[]>()
    const [endDate, setEndDate] = React.useState<any>()
    const [innerValue, setInnerValue] = React.useState<string[]>([])
    const [val, setVal] = useState<any>([])

    // React.useEffect(() => {
    //   if (Array.isArray(val)) {
    //     if (val[0]) {
    //       setStartDate(val[0]?.split(':'))
    //     }
    //     if (val[1]) {
    //       setEndDate(val[1]?.split(':'))
    //     }
    //   }
    // }, [val])

    useEffect(() => {
      console.log(value, val, 'value每周')
      setVal(value || [])
    }, [value, visible])
    return (
      <StyledDiv>
        {/* <div className='flex items-center justify-between' ref={ref}> */}
        <StyledHeader className='adm-picker-header'>
          <a className='adm-picker-header-button' role='button' onClick={() => sureFun()}>
            取消
          </a>
          <span className='textbold'>请设置</span>
          <a
            className='adm-picker-header-button'
            role='button'
            onClick={() => {
              console.log(val, 'val')
              sureFun(val)
              // console.log(ST)
            }}
          >
            确定
          </a>
        </StyledHeader>
        <StyledSelector
          options={basicColumnsWeek}
          value={val}
          multiple={true}
          // defaultValue={['1']}
          onChange={(arr, extend) => {
            console.log(arr, 'arr')
            setVal(arr)
            //@ts-ignore
            // extend.items[0] && extend.items[0]?.defaultOpt && setCurrentSelect(extend.items[0]?.defaultOpt)
          }}
          // showCheckMark={false}
        />
      </StyledDiv>
    )
  }
)
