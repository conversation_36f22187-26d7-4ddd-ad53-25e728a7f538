import React, { useState, useMemo, useEffect } from 'react'
import { useTheme, styled, Box } from '@mui/system'
import { Switch, Form, CheckList, Popup, List, ActionSheet } from 'antd-mobile'
import SelfData from './SelfData'
import { useEasyFormRequest } from '@/hooks'
import { mobilePublish } from '@/api/easyform'
import { request } from '@sui/runtime'
const StyledDiv = styled('div')<any>(({ noBorder }) => ({
  '.adm-list-item-content-main': {
    fontSize: ' var(--adm-font-size-7)',
    color: ' var(--adm-color-text-secondary)'
  },
  '& .adm-list-item-content-main': {
    paddingBottom: 0
  }
}))
const StyledActionSheet = styled(ActionSheet)<any>(({ noBorder }) => ({
  '.adm-action-sheet-button-item-wrapper:last-child': {
    borderTop: '11px solid var(--adm-color-border)'
  }
}))

const items = [
  { label: '每天', value: 'day' },
  { label: '每周', value: 'week' },
  { label: '自定义周几打卡', value: 'customWeek' },
  { label: '每月', value: 'month' }
]
const actions: any[] = [
  { text: '每天', key: 'day' },
  { text: '每周', key: 'week' },
  { text: '每月', key: 'month' },
  { text: '自定义周几打卡', key: 'customWeek' },
  { text: '取消', key: 'cancel' }
]
const basicColumnsArr: any[] = [
  { label: '一', value: 'MON', sort: '1' },
  { label: '二', value: 'TUES', sort: '2' },
  { label: '三', value: 'WED', sort: '3' },
  { label: '四', value: 'THUR', sort: '4' },
  { label: '五', value: 'FRI', sort: '5' },
  { label: '六', value: 'SAT', sort: '6' },
  { label: '日', value: 'SUN', sort: '7' }
]

const itemObj: any = { day: '每天', week: '每周', month: '每月', customWeek: '自定义周几打卡' }
export default React.forwardRef<any, any>(function CycleClockCom({ value, onChange, appAssets, ...other }: any, ref) {
  const [visible, setVisible] = useState(false)
  const [visibleSelf, setVisibleSelf] = useState(false)

  const [selected, setSelected] = useState<string>('')
  const [searchText, setSearchText] = useState('')
  const [text, setText] = useState('')
  const filteredItems: any[] = useMemo(() => {
    if (searchText) {
      return actions.filter((item) => item?.text?.includes(searchText))
    } else {
      return actions
    }
  }, [actions, searchText])

  const sureFun = (data?: any[]) => {
    console.log(data, 'data')
    if (data) onChange(data)
    closeVis()
  }
  const closeVis = () => {
    setVisibleSelf(false)
  }
  function opSet(arr: any, sortarr: any, diffIndex?: any) {
    if (diffIndex) {
      const num = arr
        .filter((item: any, index: number) => sortarr.includes(item.value))
        .map((item: any, i: number) => item?.sort)
        .join('')
      console.log(num, 'num')
      let ncontinuity = 0 //用于连续个数的统计
      for (let i = 1; i < num.length; i++) {
        if (num[i] - num[i - 1] == 1 || num[i] - num[i - 1] == -1) {
          //等于1代表升序连贯   等于-1代表降序连贯
          ncontinuity += 1 //存在连贯：计数+1
        }
      }
      if (ncontinuity > num.length - 2) {
        return true
      } else {
        return false
      }
    } else {
      return arr.filter((item: any) => sortarr.includes(item.value)).map((item: any) => item)
    }
  }
  useEffect(() => {
    console.log(value, 'value', '打卡频率')
    let text = ''
    if (Array.isArray(value)) {
      if (value.length == 7) {
        onChange('day')
        return
      }
      console.log(opSet(basicColumnsArr, value, true), 'opSet(basicColumnsArr, value)')
      const isCoherent = opSet(basicColumnsArr, value, true)
      const result = opSet(basicColumnsArr, value)
      text = text + '每周'
      result?.map((item: any, i: number) => {
        if (isCoherent) {
          console.log(isCoherent, text, 'isCoherent')
          if (i == 0) text = text + item.label + '至'
          if (i == result.length - 1) text = text + '周' + item.label
        } else {
          text = text + item.label + '、'
        }
      })
      if (!isCoherent) text = text.slice(0, text.length - 1)
      console.log(text, 'text')
    } else {
      text = itemObj[value]
    }
    setText(text)
  }, [value])
  return (
    <StyledDiv>
      <div>
        <List mode='card'>
          <List.Item
            extra={text}
            onClick={() => {
              setVisible(true)
            }}
          >
            打卡频率
          </List.Item>
        </List>
        {/* <Input
          onClick={() => {
            setVisible(true)
          }}
          value={itemObj[value]}
        /> */}
      </div>
      {/* <Popup
        visible={visible}
        onMaskClick={() => {
          setVisible(false)
        }}
        destroyOnClose
      > */}
      <StyledActionSheet
        visible={visible}
        actions={filteredItems}
        closeOnAction={true}
        onClose={() => setVisible(false)}
        onAction={(action: any, index: number) => {
          console.log(action, index)
          if (action?.key == 'customWeek') {
            setVisibleSelf(true)
            if (!Array.isArray(value)) onChange(['MON', 'TUES', 'WED', 'THUR', 'FRI'])
            return
          } else {
            if (action?.key !== 'cancel') onChange(action?.key)
          }
        }}
      />
      {/* <div>
          <CheckList
            // defaultValue={selected ? [selected] : []}
            activeIcon={null}
            value={value}
            onChange={(val: any) => {
              onChange(val[0])
              setSelected(val[0])
              setVisible(false)
              console.log(val, 'val', value, onChange)
            }}
          >
            {filteredItems.map((item) => (
              <CheckList.Item key={item?.value} value={item.value}>
                {item?.label}
              </CheckList.Item>
            ))}
          </CheckList>
        </div> */}
      {/* </Popup> */}
      <Popup
        visible={visibleSelf}
        onMaskClick={() => {
          setVisibleSelf(false)
        }}
        bodyStyle={{ height: '40vh' }}
      >
        <SelfData sureFun={sureFun} value={value} visible={visibleSelf} />
      </Popup>
    </StyledDiv>
  )
})
