import { Badge, Avatar } from 'antd-mobile'
import { styled } from '@mui/system'

export type AvatarProps = {
  src: string
  size?: number
  color?: string
  round?: boolean
  content?: any
  badgeColror?: string
}

const StyledBadge = styled(Badge)({
  minWidth: 6,
  width: 6,
  height: 6,
  borderRadius: 3
})

export default function ({ src, size = 44, color, round, content, badgeColror }: AvatarProps) {
  return content ? (
    <Badge color={badgeColror} content={content}>
      <Avatar style={{ '--size': `${size}px`, '--border-radius': round ? '50%' : void 0 }} src={src} />
    </Badge>
  ) : (
    <StyledBadge
      style={{ '--top': 'calc(100% - 10px)', '--right': '0px' }}
      color={color}
      content={color ? Badge.dot : void 0}
    >
      <Avatar style={{ '--size': `${size}px`, '--border-radius': round ? '50%' : void 0 }} src={src} />
    </StyledBadge>
  )
}
