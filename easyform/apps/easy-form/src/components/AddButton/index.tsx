import React from 'react'
import { Box, styled } from '@yiban/system'
import { Popover } from 'antd-mobile'
import { Action } from 'antd-mobile/es/components/popover'
import { AddOutline as IconAdd } from 'antd-mobile-icons'

export type AddButtonProps = {
  actions?: Action[]
  sx?: any
  onClick?: () => void
  onAction?: (node: Action) => void
  size?: number
}

const Root = styled(Box)<Pick<AddButtonProps, 'size'>>(({ theme, size = 42 }) => ({
  display: 'flex',
  alignItems: 'center',
  justifyContent: 'center',
  backgroundColor: theme.palette.primary.main,
  color: '#FFF',
  width: size,
  height: size,
  borderRadius: '50%',
  transition: 'backgroundColor .35s ease-in-out',
  '&:hover': {
    backgroundColor: theme.palette.primary.light
  },
  fontSize: 24,
  cursor: 'pointer'
}))

export default function ({ actions, sx, onClick, onAction, size }: AddButtonProps) {
  return actions ? (
    <Popover.Menu actions={actions} placement='top' onAction={onAction} trigger='click'>
      <Root sx={sx}>
        <IconAdd />
      </Root>
    </Popover.Menu>
  ) : (
    <Root sx={sx} onClick={onClick}>
      <IconAdd />
    </Root>
  )
}
