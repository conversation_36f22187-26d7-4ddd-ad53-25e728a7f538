/* eslint-disable */
// @ts-nocheck
export interface INode {
  code: string
  name: string
  type?: string | number
  _ref?: INode
  positions?: INode[]
  leader?: INode[]
  persons?: INode[]
  children?: INode[]
  disabled?: boolean
  parentNode?: INode
  topNode?: INode
  checked?: boolean
  checkable?: boolean
  isLeaf?: boolean
}

export function parsePositionSet(
  nodes: INode[],
  disabledKeys?: string[],
  options?: { isLeaf?: boolean; parent?: INode; top?: INode; childrenAttrs?: string[] | string; checkable?: boolean }
) {
  const { parent, top, childrenAttrs, checkable, isLeaf } = options || {}
  disabledKeys = disabledKeys || []
  let arr = (nodes || []).map((node: INode) => {
    const disabled = disabledKeys && disabledKeys.indexOf(node.code) > -1 ? true : false
    const _node: INode = {
      code: node.code,
      name: node.name,
      isLeaf: typeof isLeaf === 'boolean' ? isLeaf : true,
      disabled,
      checked: disabled,
      parentNode: parent,
      topNode: top,
      checkable: checkable !== false,
      _ref: node
    }

    if (node.positions && (!childrenAttrs || childrenAttrs.indexOf('positions') > -1)) {
      _node.children = parsePositionSet(node.positions, disabledKeys, {
        childrenAttrs,
        checkable,
        isLeaf,
        parent: _node,
        top: _node.topNode || _node.parentNode || _node
      })
    }
    if (node.persons && (!childrenAttrs || childrenAttrs.indexOf('persons') > -1)) {
      _node.children = parsePositionSet(node.persons, disabledKeys, {
        childrenAttrs,
        checkable,
        isLeaf,
        parent: _node,
        top: _node.topNode || _node.parentNode || _node
      })
    }
    if (_node.children && _node.children.length) {
      _node.isLeaf = false
      _node.checkable = false
    }
    return _node
  })
  return arr
}

/**
 * 获取院系下的辅导员或班主任
 * @param nodes
 * @param res
 * @returns
 */
export function parseClassLeaderSet(
  nodes: any,
  disabledKeys?: string[],
  options?: {
    checkable?: boolean
    isLeaf?: boolean
    childrenAttrs?: string[] | string
    types?: Array<string | number>
    res?: any[]
    topNode?: any
  }
) {
  let { types, res, topNode, checkable, childrenAttrs, isLeaf } = options || {}
  types = types || [2]
  res = res || []
  disabledKeys = disabledKeys || []
  ;(nodes || []).map((node: any) => {
    if (
      (!types || !types.length || types.indexOf(node.type) > -1) &&
      res.findIndex((item) => item.id == node.id) == -1
    ) {
      const disabled = disabledKeys && disabledKeys.indexOf(node.code) > -1 ? true : false
      res.push({
        ...node,
        _ref: node,
        isLeaf: isLeaf !== false,
        disabled,
        checked: disabled,
        checkable: checkable !== false,
        topNode: topNode
      })
    }
    if (node.leader && (!childrenAttrs || childrenAttrs.indexOf('leader') > -1)) {
      parseClassLeaderSet(node.leader, disabledKeys, {
        types,
        res,
        topNode,
        childrenAttrs,
        checkable
      })
    }
  })
  return res
}
