const SvgComponent = ({ width = 24, height = 24, ...other }: any) => (
  <svg xmlns='http://www.w3.org/2000/svg' width={width} height={height} viewBox='0 0 1300 1024' {...other}>
    <path
      fill='#F45151'
      d='m784.015 99.205 10.903 18.428-21.295-2.56-14.18 16.125-4.094-21.09-19.708-8.497 18.735-10.443 2.048-21.346 15.715 14.59 20.936-4.66-9.06 19.453zm283.128 543.169-18.429 10.903 2.56-21.294-16.074-14.18 21.04-4.146 8.497-19.657 10.442 18.735 21.346 1.997-14.589 15.715 4.658 20.936-19.451-9.009z'
    />
    <path
      fill='#F45151'
      d='m1067.143 642.374-18.429 10.903 2.56-21.294-16.074-14.18 21.04-4.146 8.497-19.657 10.442 18.735 21.346 1.997-14.589 15.715 4.658 20.936-19.451-9.009zm-495.257-542.35-17.404-12.542 20.527-6.193 6.501-20.425 12.234 17.61 21.449-.154-12.951 17.097 6.757 20.373-20.271-7.013-17.251 12.695.41-21.448zm419.496 684.349-21.192 3.48 10.136-18.888-9.829-19.094 21.09 3.788 15.101-15.254 2.918 21.244 19.145 9.623-19.299 9.368-3.225 21.192-14.845-15.459zm-563.389-627.58-20.782-5.324 16.892-13.258-1.33-21.397 17.762 11.927 19.964-7.883-5.887 20.63 13.667 16.533-21.397.82-11.517 18.07-7.372-20.118zm426.202 739.633-20.731-5.323 16.84-13.258-1.33-21.397 17.814 11.978 19.912-7.935-5.835 20.63 13.667 16.534-21.448.819-11.518 18.07-7.37-20.118zm-291.779 27.182 10.904 18.428-21.295-2.56-14.18 16.074-4.146-21.039-19.656-8.497 18.735-10.443 1.996-21.346 15.715 14.59 20.937-4.66-9.01 19.453zM242.791 420.725l-18.48 10.954 2.56-21.295-16.074-14.23 21.04-4.095 8.497-19.708 10.442 18.735 21.346 2.048-14.589 15.715 4.71 20.936-19.452-9.06z'
    />
    <path
      fill='#F45151'
      d='m242.79 420.725-18.479 10.954 2.56-21.295-16.074-14.23 21.04-4.095 8.497-19.708 10.442 18.735 21.346 2.048-14.589 15.715 4.71 20.936-19.452-9.06zm457.991 523.154-17.404-12.49 20.475-6.245 6.553-20.425 12.234 17.61 21.448-.103-12.95 17.097 6.756 20.322-20.27-7.013-17.303 12.695.461-21.448zM303.552 278.828l-21.192 3.43 10.135-18.89-9.828-19.042 21.09 3.788 15.1-15.254 2.919 21.243 19.144 9.624-19.298 9.317-3.225 21.243-14.845-15.46z'
    />
    <path
      fill='#F45151'
      d='M407.62 90.605a486.861 486.861 0 0 1 504.521 11.774l25.032-14.436A511.893 511.893 0 0 0 139.644 547.52l25.032-14.384A486.759 486.759 0 0 1 407.62 90.605zm485.581 842.729a486.861 486.861 0 0 1-504.52-11.62L363.7 936.098a511.893 511.893 0 0 0 797.477-459.526l-24.775 14.384a486.912 486.912 0 0 1-243.2 442.378z'
    />
    <path
      fill='#F45151'
      d='M813.91 795.84a326.587 326.587 0 0 1-258.148 29.536l-29.792 17.2a353.206 353.206 0 0 0 472.732-272.43l-29.792 17.148a326.843 326.843 0 0 1-155 208.545zM486.86 228.1a326.587 326.587 0 0 1 258.147-29.537l29.793-17.2a353.206 353.206 0 0 0-472.784 272.43l29.843-17.149A326.485 326.485 0 0 1 486.861 228.1zm801.42 146.605a53.442 53.442 0 0 1-14.385 11.979L229.635 988.618a53.8 53.8 0 0 1-73.405-19.759L7.218 710.661a53.698 53.698 0 0 1 19.708-73.406L1071.442 35.321a53.8 53.8 0 0 1 73.406 19.759l148.807 258.198a53.953 53.953 0 0 1-5.375 61.427zM32.197 665.716a28.103 28.103 0 0 0-2.815 32.147L178.19 956.062a28.154 28.154 0 0 0 38.443 10.238L1261.15 364.519a28.154 28.154 0 0 0 10.238-38.443l-148.756-258.25a28.103 28.103 0 0 0-38.392-10.238L39.723 659.42a27.13 27.13 0 0 0-7.525 6.296z'
    />
    <path
      fill='#F45151'
      d='M280.978 604.033 469.61 495.205l9.06 15.766-72.278 41.873a252.107 252.107 0 0 1 1.433 34.348l87.022 150.19-16.432 9.52-73.406-126.795a353.206 353.206 0 0 1-41.924 116.506l-18.479-7.985q50.32-87.022 43.972-165.649l-98.488 56.872zm264.034 7.883-5.119 19.504a487.782 487.782 0 0 0-99.358-25.595l5.119-17.507a524.639 524.639 0 0 1 99.358 23.7zm207.47-29.433q-11.364 6.5-39.979 22.472a173.583 173.583 0 0 1-37.368 16.227 58.202 58.202 0 0 1-33.12-2.457 14.18 14.18 0 0 0-11.824-.563q-7.986 4.607-8.753 46.07l-18.275-3.942q1.587-38.238 11.722-52.008l-38.955-67.467-31.481 18.172-8.497-14.64 46.377-26.772 47.913 82.977a52.98 52.98 0 0 1 8.498 1.894 59.635 59.635 0 0 0 25.594 3.993 98.181 98.181 0 0 0 33.478-12.234q23.752-13.156 44.176-24.878c6.655-3.84 17.149-10.238 31.482-18.787q17.762-10.852 24.11-15.356l5.477 18.172zM568.201 473.808l-5.12 17.609a284.203 284.203 0 0 0-58.355-13.207l5.119-16.739a306.777 306.777 0 0 1 58.356 12.337zm78.934-40.952a266.184 266.184 0 0 0 20.475-38.648l-98.744 56.718-7.78-13.565 120.038-69.259 6.655 11.518a280.568 280.568 0 0 1-25.953 54.926l5.119.819 43.92-25.595 57.281 99.205c6.603 11.364 3.634 20.476-8.753 27.796l-18.428 10.647-11.364-11.466 17.916-9.47a7.883 7.883 0 0 0 3.634-12.132l-9.623-16.688-37.266 21.55 22.011 38.188-14.845 8.548-22.062-38.136-37.01 21.398 22.42 38.801-15.356 8.753-67.877-117.837 55.49-31.994a314.763 314.763 0 0 0-39.314-.972l4.249-15.357q20.373.563 35.115 2.099zm15.868 49.45-11.568-20.016-37.164 21.244 11.569 19.964zm-29.434 34.706 11.262 19.554 37.061-21.397-11.313-19.708zm81.494-65.01-11.518-19.965-37.266 21.551 11.518 19.964zm-29.588 34.86 11.262 19.502 37.317-21.5-11.313-19.554zm293.366-35.066c-5.63 3.277-12.234 6.962-19.861 11.16l-10.648 6.142-11.005 6.092a184.793 184.793 0 0 1-38.7 16.995 60.864 60.864 0 0 1-33.99-1.945 14.487 14.487 0 0 0-12.285-.308q-8.446 4.863-8.907 46.583l-18.172-3.788q1.28-39.16 11.774-52.93l-38.802-67.212-31.993 18.429-8.65-15.05 47.247-27.335 48.066 83.234 4.147.563a27.181 27.181 0 0 1 4.556 1.228 72.689 72.689 0 0 0 26.26 3.481 111.746 111.746 0 0 0 34.348-13.36q24.673-13.617 46.07-25.902l14.487-8.702 15.1-8.702q16.586-10.238 22.575-14.18l5.221 18.582zM788.724 346.45l-4.71 18.428a296.13 296.13 0 0 0-58.56-14.742l5.12-16.79a305.702 305.702 0 0 1 58.15 13.104zm134.781 97.669L911.17 430.86c11.825-5.887 21.858-11.16 30.15-15.971a9.521 9.521 0 0 0 4.3-14.589l-54.516-94.29-83.848 48.424-9.112-15.766 83.9-48.425-20.784-35.832 16.176-9.368 20.783 35.832 32.863-18.94 9.061 15.716-32.812 19.247 56.615 98.078c7.525 13.002 4.505 23.394-8.907 31.175zm-23.086-59.482-7.73 16.33A411.51 411.51 0 0 0 835 375.37l7.832-15.357a380.131 380.131 0 0 1 57.588 24.622z'
    />
  </svg>
)
export default SvgComponent
