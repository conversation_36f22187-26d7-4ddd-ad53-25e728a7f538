const SvgComponent = ({ width = 24, height = 24, ...other }: any) => (
  <svg
    xmlns='http://www.w3.org/2000/svg'
    width={width}
    height={height}
    className='icon'
    viewBox='0 0 1024 1024'
    {...other}
  >
    <path
      fill='#4caf50'
      d='m513.86 715.33-19.48 3.1 14.17 13.28-3.55 19.48 17.72-9.29 17.71 9.29-3.55-19.48 14.17-13.28-19.48-3.1-8.85-17.27zm208.55-90.79 15.04 12.86.89-19.93 16.38-10.62-18.16-6.65-4.85-19.04-11.96 15.49-19.48-1.33 10.63 16.39-7.53 18.6zm-92.54 82.36 6.64 18.17 10.63-16.39 19.92-.88-12.84-15.06 5.76-19.03-18.6 7.52-16.4-10.63 1.33 19.49-15.48 11.95zm39.58-126.94a8.659 8.659 0 0 0-10.79-1.9c-1.12.78-2.17 1.67-3.11 2.66a160.127 160.127 0 0 1-53.13 50.03 162.33 162.33 0 0 1-69.96 21.24c-1.43.03-2.82.49-3.98 1.33v.01a8.846 8.846 0 0 0-3.99 9.62c.9 3.73 4.1 6.47 7.93 6.76h.95a177.236 177.236 0 0 0 136.38-78.82 8.637 8.637 0 0 0-.3-10.93zm94.94-360.06a28.294 28.294 0 0 0-21.61 3.06L178.66 548.84a27.834 27.834 0 0 0-13.18 17.12 27.898 27.898 0 0 0 3.01 21.4l95.65 165.6h-.01a28.028 28.028 0 0 0 24.31 14.14c5.01 0 9.93-1.37 14.21-3.95l564.56-325.89a27.898 27.898 0 0 0 10.17-38.53l-95.64-165.6a28.284 28.284 0 0 0-17.35-13.23zm84.21 207.62L302.65 742.78c-10.29 5.72-23.26 2.18-29.22-7.97l-85.01-147.45c-5.72-10.29-2.18-23.26 7.97-29.22l545.96-315.26h-.01a21.1 21.1 0 0 1 10.43-2.76 22.07 22.07 0 0 1 18.79 10.73l85.01 147.45c5.7 10.29 2.17 23.25-7.97 29.22z'
    />
    <path
      fill='#4caf50'
      d='M522.72 50.27c-240.99 0-437 192.51-442.65 432.14v21.11C85.7 743.19 281.7 935.75 522.72 935.83c244.54 0 442.78-198.24 442.78-442.78 0-244.54-198.24-442.78-442.78-442.78zm0 867.85c-234.76 0-425.07-190.31-425.07-425.07.06-234.73 190.33-425 425.07-425.07 234.76 0 425.07 190.31 425.07 425.07S757.47 918.12 522.72 918.12z'
    />
    <path
      fill='#4caf50'
      d='m531.57 252.6-8.85-17.71-8.86 17.71-19.48 2.66 14.17 13.75-3.55 19.04 17.72-8.86 17.71 8.86-3.55-19.04 14.17-13.75zm287.85 238.02a9.05 9.05 0 0 0-8.9-.23 9.154 9.154 0 0 0-4.43 7.53c-1.8 99.42-55.54 190.64-141.64 240.39s-191.96 50.77-279 2.7c-2.8-1.33-6.06-1.33-8.85 0a9.11 9.11 0 0 0-4.31 7.75c0 3.15 1.63 6.08 4.31 7.75 92.46 51.34 205.08 50.4 296.68-2.47 91.59-52.87 148.74-149.93 150.53-255.67 0-3.18-1.67-6.12-4.39-7.75zM398.3 306.2l6.63 18.16 10.63-16.38 19.93-.88-12.42-15.06 5.32-19.04-18.14 7.52-16.4-11.07.89 19.48-15.5 12.4zm-76.61 87.21 15.04 12.41.89-19.92L354 375.27l-18.14-6.64-4.87-19.06-12.4 15.5-19.48-.88 11.07 16.39-7.53 18.14z'
    />
    <path
      fill='#4caf50'
      d='M230.69 496.76c1.47 0 2.92-.36 4.22-1.06a9.12 9.12 0 0 0 4.43-7.51c1.8-99.42 55.54-190.64 141.64-240.39 86.1-49.76 191.96-50.78 279-2.7 2.8 1.33 6.06 1.33 8.86 0a9.098 9.098 0 0 0 4.32-7.75 9.11 9.11 0 0 0-4.32-7.75c-92.48-51.23-205.04-50.24-296.6 2.61-91.56 52.85-148.72 149.82-150.61 255.53 0 2.4.95 4.71 2.65 6.4a8.997 8.997 0 0 0 6.41 2.62z'
    />
    <path
      fill='#4caf50'
      d='M375.71 395.21a8.606 8.606 0 0 0 .22 11 8.616 8.616 0 0 0 10.85 1.82c1.13-.77 2.17-1.66 3.1-2.66a160.198 160.198 0 0 1 53.13-50.02 162.343 162.343 0 0 1 69.96-21.25c1.43-.04 2.82-.5 3.98-1.33a8.786 8.786 0 0 0 4.13-10.15 8.76 8.76 0 0 0-8.99-6.25c-55.28 3.53-105.73 32.68-136.38 78.82v.02zm72.2 171.59c9.48 21.88 9.29 26.86-5.4 35.12l-46.62 26.21c-15.68 8.82-18.97 8.98-23.94.14l-17.07-30.37 64.73-36.39 3.77 6.7 17.68-9.94-39.68-70.57-105.65 59.39 9.46 16.82 87.97-49.46 17.07 30.37-64.73 36.39-10.9-19.39-17.54 9.86 37.35 66.44c12.1 21.53 23.56 22.22 51.08 6.75l47.19-26.53c25.95-14.59 27.81-25.96 16.02-54.6-5.55 1.8-14.85 3.28-20.79 3.06zm131.23-119.01-27.65-49.19-70.14 39.44L509 487.23l70.14-39.44zm-27.07-10.1 4.09 7.27-38.35 21.56-4.09-7.27 38.35-21.56zm-48.53 3.45 38.35-21.56 4.01 7.13-38.35 21.56-4.01-7.13zm-2.02 85.19 12.55-13.06-10.86-13.97-10.91 10.63-11.86-21.1 14.26-8.02-8.82-15.68-14.26 8.02-15.15-26.95-15.4 8.66 15.15 26.95-15.4 8.66 8.82 15.68 15.4-8.66 14.19 25.23c-5.53 5.18-10.36 9.95-14.64 13.67l13.07 14.23c3.49-3.47 7.05-7.15 11.03-11.08l16.03 28.51c1.04 1.85.65 2.83-1.06 3.79-1.63 1.11-6.48 3.83-11.83 6.65 4.62 3.22 10.4 9.16 13.16 13.06 9.21-4.99 15.16-9.09 17.83-14.34 2.84-4.97 1.79-9.83-2.62-17.67l-18.68-33.21z'
    />
    <path
      fill='#4caf50'
      d='M596.81 520.61c-3.56 2-6.78 3.62-10.22 5.18l-9.22-16.4 27.23-15.31-7.46-13.26-27.23 15.31-7.05-12.55 34.79-19.56-7.7-13.69-84.26 47.37 7.7 13.69 33.5-18.84 20.92 37.21c-6.97-.39-14-3.2-21.78-9.71-1.34-5.06-3.21-10.39-5.39-15.92l-16.44 6.8c8.76 20.59 11.81 39.7 5.27 55.77 4.62.21 14.01 1.88 18.09 3.15 2.99-9.37 3.73-19.73 2.39-30.8 19.39 11.8 35.72 7.12 54.39-3.38l24.81-13.95c-1.84-4.6-3.85-12.85-3.77-17.39-6.04 3.61-23.72 13.55-28.57 16.28zm-5.6-104.74 125.04-70.3-9.3-16.54-50.47 28.38.35-.38c-4.9-4.38-14.17-9.86-21.47-13.82l-13.05 14.84c4.97 2.83 10.57 6.44 15.21 10.03l-55.6 31.26 9.29 16.53zm35.33 33.8c8.36-14.83 16.46-36.46 19.11-54.46l-19.92 2.94c-2.5 14.91-10.48 33.1-19.56 46.65 5.45.69 15.31 3.21 20.37 4.87zm46.42-66.63c17.9 2.13 41.26 6.64 53.21 11.55l8.37-19.15c-12.97-4.72-36.56-7.96-53.55-9.48l-8.03 17.08zm30.25 54.48c4.14-14.33 5.44-29.7 3.69-46.17l-19.91 4.63c2.12 13.45 1.05 26.24-2.74 38.51-12.17-3.29-23.51-9.11-33.64-17.11L638 430.66c12.55 9.64 25.71 16.69 39.25 21.09-8.76 17.12-22.85 33.11-41.28 48.73 5.36 1.86 14.6 6.62 18.76 9.34 18.07-16.92 32.38-34.53 41.48-53.72 20.49 2.74 42.4-.01 67.33-8.4-.43-5.76.07-15.23 1.69-21.03-22.73 8.65-43.28 12.14-62.02 10.85z'
    />
  </svg>
)
export default SvgComponent
