const SvgComponent = ({ width = 24, height = 24, ...other }: any) => (
  <svg xmlns='http://www.w3.org/2000/svg' width={width} height={height} viewBox='0 0 1024 1024' {...other}>
    <path fill='none' d='M0 0h1024v1024H0z' />
    <path
      fill='#FF8534'
      d='M347.136 139.264 326.656 128l-1.024 23.552-18.432 15.36 22.528 8.192 9.216 21.504 15.36-18.432 23.552-2.048-13.312-19.456 6.144-22.528zM481.28 107.52l-15.36-18.432-8.192 21.504-22.528 9.216 18.432 15.36 1.024 22.528 20.48-11.264 23.552 5.12-6.144-22.528 13.312-19.456zM229.376 232.448l-23.552-1.024 8.192 21.504-9.216 21.504 23.552-2.048 17.408 15.36 6.144-22.528 20.48-12.288-19.456-12.288-5.12-23.552z'
    />
    <path
      fill='#FF8534'
      d='M512 998.4C243.712 998.4 25.6 780.288 25.6 512S243.712 25.6 512 25.6 998.4 243.712 998.4 512 780.288 998.4 512 998.4M512 0C229.376 0 0 229.376 0 512s229.376 512 512 512 512-229.376 512-512S794.624 0 512 0'
    />
    <path
      fill='#FF8534'
      d='M510.976 60.416c247.808 0 450.56 201.728 450.56 450.56 0 247.808-201.728 450.56-450.56 450.56s-450.56-201.728-450.56-450.56c0-247.808 202.752-450.56 450.56-450.56zm0 13.312c-240.64 0-437.248 196.608-437.248 437.248 0 241.664 196.608 437.248 437.248 437.248s437.248-196.608 437.248-437.248S752.64 73.728 510.976 73.728z'
    />
    <path
      fill='#FF8534'
      d='m233.472 675.84 45.056-25.6 12.288 20.48-45.056 25.6-12.288-20.48zm67.584 14.336 25.6 44.032 20.48-12.288-25.6-44.032 45.056-26.624 6.144 11.264 20.48-12.288-63.488-109.568-65.536 37.888-11.264-19.456-20.48 12.288 11.264 19.456-65.536 37.888 63.488 110.592 20.48-12.288-7.168-12.288 46.08-24.576zm10.24-31.744-12.288-20.48 45.056-25.6 12.288 20.48-45.056 25.6zm22.528-65.536-45.056 25.6-12.288-20.48 45.056-25.6 12.288 20.48zM256 610.304l12.288 20.48-45.056 25.6-12.288-20.48 45.056-25.6zm64.512-126.976-83.968 49.152c-5.12-5.12-10.24-10.24-15.36-14.336l-20.48 16.384c5.12 3.072 9.216 6.144 14.336 10.24l-82.944 48.128 24.576 43.008 20.48-11.264-13.312-23.552 148.48-84.992 13.312 22.528 20.48-12.288-25.6-43.008zm138.24 26.624c-5.12 6.144-9.216 12.288-14.336 18.432l-24.576-43.008 20.48-11.264-11.264-20.48-20.48 11.264-22.528-38.912-20.48 12.288 22.528 38.912-23.552 13.312 11.264 20.48 23.552-13.312 28.672 49.152c-7.168 7.168-14.336 14.336-22.528 21.504l16.384 17.408c6.144-6.144 12.288-12.288 17.408-18.432l26.624 46.08c3.072 5.12 2.048 9.216-3.072 12.288-5.12 3.072-11.264 6.144-17.408 8.192l16.384 18.432 19.456-11.264c13.312-8.192 16.384-19.456 8.192-33.792l-33.792-58.368 10.24-12.288c2.048-2.048 3.072-4.096 5.12-6.144l-12.288-20.48zm23.552-26.624 28.672-16.384-12.288-20.48-27.648 16.384-36.864-63.488-20.48 12.288 97.28 168.96c3.072 6.144 3.072 12.288-2.048 17.408l16.384 16.384c17.408-16.384 32.768-32.768 45.056-48.128l-15.36-17.408c-6.144 8.192-12.288 16.384-20.48 24.576l-52.224-90.112zm145.408 71.68c8.192-5.12 13.312-11.264 15.36-17.408 2.048-7.168-4.096-24.576-17.408-51.2l-22.528 5.12 3.072 6.144c8.192 15.36 12.288 25.6 12.288 29.696s-2.048 8.192-6.144 10.24l-8.192 5.12c-5.12 3.072-9.216 2.048-12.288-4.096l-52.224-90.112c12.288-13.312 23.552-29.696 34.816-48.128l-22.528-10.24c-7.168 13.312-15.36 25.6-24.576 36.864l-35.84-61.44-20.48 12.288 102.4 178.176c9.216 15.36 20.48 19.456 34.816 10.24l19.456-11.264zm22.528-282.624 22.528 37.888-78.848 45.056 61.44 107.52 20.48-12.288-7.168-12.288 57.344-32.768 43.008 74.752 21.504-12.288-43.008-74.752 58.368-33.792 7.168 12.288 21.504-12.288-61.44-107.52-78.848 46.08-22.528-37.888-21.504 12.288zm7.168 145.408-30.72-53.248 57.344-32.768 30.72 53.248-57.344 32.768zm78.848-46.08-30.72-53.248 58.368-33.792 30.72 53.248-58.368 33.792zM514.048 166.4c-166.912 0-310.272 123.904-334.848 289.792l25.6 4.096C227.328 306.688 359.424 192 514.048 192c58.368 0 114.688 16.384 163.84 46.08l13.312-21.504C637.952 183.808 576.512 166.4 514.048 166.4M834.56 524.8c-9.216 165.888-146.432 294.912-312.32 294.912-67.584 0-133.12-21.504-187.392-62.464l-15.36 20.48c58.368 44.032 129.024 67.584 202.752 67.584 179.2 0 327.68-140.288 337.92-319.488l-25.6-1.024z'
    />
  </svg>
)
export default SvgComponent
