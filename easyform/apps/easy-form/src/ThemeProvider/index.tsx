import React from 'react'
import { ConfigProvider } from 'antd'
import { ThemeProvider as MuiThemeProvider, GlobalStyles } from '@mui/system'
import zhCN from 'antd/locale/zh_CN'

export default function ThemeProvider({ children, theme }: any) {
  const styles = React.useMemo(
    () =>
      theme
        ? {
            ':root:root': {
              '--adm-color-primary': theme.palette.primary.main,
              '--adm-color-success': theme.palette.success.main,
              '--adm-color-warning': theme.palette.warning.main,
              '--adm-color-danger': theme.palette.error.main,
              '--adm-font-size-main': theme.typography.fontSize,
              '--adm-color-text': theme.palette.text.primary,
              '--adm-color-text-secondary': theme.palette.text.secondary,
              '--adm-color-weak': theme.palette.text.hint
            }
          }
        : {},
    [theme]
  )
  return (
    <MuiThemeProvider theme={theme}>
      <GlobalStyles styles={styles} />
      <ConfigProvider
        locale={zhCN}
        theme={{
          token: {
            colorPrimary: theme.palette.primary.main
          }
        }}
      >
        {children}
      </ConfigProvider>
    </MuiThemeProvider>
  )
}
