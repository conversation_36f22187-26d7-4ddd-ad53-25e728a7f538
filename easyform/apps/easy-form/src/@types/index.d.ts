declare const __webpack_init_sharing__: any
declare const __webpack_share_scopes__: any

interface Window {
  PUBLIC_URL: string
  APP_CONFIG: any
}

declare module '@yiban/participator-picker' {
  const ParticipatorPicker: any
  export default ParticipatorPicker
}

declare module '@yiban/common-mobile' {
  const Form: any
  const Input: any
  const Select: any
  const ImageUploader: any
  const Radio: any
  const Checkbox: any
  const Switch: any
  const ImageList: any
  const FormItem: any
  const FormItemWrapper: any
  const TextArea: any
  const Slider: any
  const ColorPicker: any
  const Upload: any
  const UploadList: any
  type ImageListProps = {
    [k: string]: any
  }
  type ImageUploaderProps = {
    [k: string]: any
  }
  type SelectProps = {
    [k: string]: any
  }
  type InputProps = {
    [k: string]: any
  }
  type FormItemProps = {
    [k: string]: any
  }
  type FormItemWrapperProps = {
    [k: string]: any
  }
  type UploadProps = {
    [k: string]: any
  }
  type UploadListProps = {
    [k: string]: any
  }
  export {
    Form,
    Input,
    Select,
    SelectProps,
    Switch,
    Slider,
    ImageUploader,
    Checkbox,
    Switch,
    ImageList,
    ColorPicker,
    ImageListProps,
    ImageUploaderProps,
    InputProps,
    FormItem,
    FormItemProps,
    TextArea,
    FormItemWrapper,
    FormItemWrapperProps,
    Upload,
    UploadProps,
    UploadList,
    UploadListProps
  }
}
