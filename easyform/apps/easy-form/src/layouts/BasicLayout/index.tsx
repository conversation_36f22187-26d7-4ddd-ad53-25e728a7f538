import React from 'react'
import { Outlet, useNavigate } from 'react-router-dom'
import { Box } from '@mui/system'

export default function BasicLayout() {
  // const messageClient = useMessageClient()
  // const [message, setMessage] = React.useState<any>()
  // const handleMessages = React.useCallback((e) => {
  //   try {
  //     const obj = JSON.parse(e.data)
  //     setMessage({ id: obj.meta.messageId, content: obj.msgdata.content || '收到未知消息' })
  //   } catch (e) {
  //     console.log('===error notice parse:')
  //   }
  // }, [])

  // React.useEffect(() => {
  //   messageClient.addEventListener('notify', handleMessages)
  //   return () => {
  //     messageClient.removeEventListener('notify', handleMessages)
  //   }
  // }, [handleMessages, messageClient])
  // React.useEffect(() => {
  //   setTimeout(() => {
  //     setMessage({ id: 1, content: 'welcome to <PERSON><PERSON><PERSON>' })
  //   }, 2000)
  // }, [])
  const navigate = useNavigate()
  React.useEffect(() => {
    ;(window as any).EASY_FORM = {
      back: () => {
        if (!location.href.includes('/creation')) {
          navigate(-1)
        }
      }
    }
  }, [navigate])
  React.useEffect(() => {
    document.title = '万能表单'
  }, [])
  return (
    <Box className='h-screen'>
      <Outlet />
    </Box>
  )
}
