import React, { useState } from 'react'
import { Outlet, useNavigate, useLocation } from 'react-router-dom'
import { Box, styled } from '@mui/system'
import themes from '@/themes'
import ThemeProvider from '@/ThemeProvider'
import ProLayout from '@ant-design/pro-layout'
import { UnorderedListOutlined } from '@ant-design/icons'
import { Logo } from './icon/logo'

const LayoutBox = styled(Box)(() => ({
  '.ezf-pro-top-nav-header-main-left': {
    width: '324px',
    paddingLeft: '50px'
  },
  '.ezf-pro-top-nav-header-logo >*:first-child>h1': {
    fontSize: '20px',
    marginLeft: '16px'
  }
}))

export default function BaseLayout() {
  const navigate = useNavigate()
  const location = useLocation()
  const menuItemRender = (item: any) => {
    const handleClick = () => {
      navigate(item.path || '/')
    }
    return (
      <Box onClick={handleClick} style={{ cursor: 'pointer' }}>
        {item.name}
      </Box>
    )
  }

  const menuDataRender = () => [
    {
      path: '/ezform/pc',
      name: '万能表单'
    },
    {
      path: '/ezform/joined',
      name: '我参与的'
    },
    {
      path: '/ezform/activity',
      name: '表单管理'
    },
    {
      path: '/ezform/template',
      name: '模板管理'
    }
  ]
  return (
    <ThemeProvider theme={themes.pc}>
      <Box className='h-screen flex flex-col'>
        <LayoutBox className='shrink-0'>
          <ProLayout
            token={{
              colorBgAppListIconHover: 'rgba(0,0,0,0.06)',
              colorTextAppListIconHover: 'rgba(255,255,255,0.95)',
              colorTextAppListIcon: 'rgba(255,255,255,0.85)',
              header: {
                colorBgHeader: '#5353ec',
                colorBgRightActionsItemHover: 'rgba(0,0,0,0.06)',
                colorTextRightActionsItem: 'rgba(255,255,255,0.65)',
                colorHeaderTitle: '#fff',
                colorBgMenuItemHover: 'rgba(0,0,0,0.06)',
                colorBgMenuItemSelected: 'rgba(0,0,0,0.15)',
                colorTextMenuSelected: '#fff',
                colorTextMenu: 'rgba(255,255,255,0.75)',
                colorTextMenuSecondary: 'rgba(255,255,255,0.65)',
                colorTextMenuActive: 'rgba(255,255,255,0.95)'
              }
            }}
            title='万能表单'
            logo={Logo}
            layout='top'
            route={{ routes: menuDataRender() }}
            location={location}
            menuItemRender={menuItemRender}
            contentStyle={{ padding: 0, height: 'calc(100vh - 55px)' }}
          >
            <Box className='content flex-grow overflow-auto'>
              <React.Suspense fallback={<div>loading...</div>}>
                <Outlet />
              </React.Suspense>
            </Box>
          </ProLayout>
        </LayoutBox>
      </Box>
    </ThemeProvider>
  )
}
