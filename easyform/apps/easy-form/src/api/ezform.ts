import { IEzFormResultData } from '@/interfaces/service'
import { IRequestOptions, createRequest } from '@sui/runtime'
import { message } from 'antd'

export const EZFORM_API_PREFIX = '/easyform-api'

const suiRequest = createRequest<IEzFormResultData>({
  prefix: EZFORM_API_PREFIX
})

const request = (options: IRequestOptions) => {
  return new Promise((resolve, reject) => {
    suiRequest(options)
      .then((res) => {
        if (res.code === 'SUCCESS') {
          resolve(res.result)
          return
        }
        message.error({
          content: res.msg
        })
        reject(res.msg)
      })
      .catch((err) => {
        reject(err)
      })
  })
}

/**
 * 应用接口定义
 */
export const API = {
  activityList: '/activity/page',
  activityFormList: '/activity/form/page',
  getActivityDefine: '/activity/define',
  deleteActivity: '/activity/del',
  toggleActivity: '/activity/toggleEnable',
  deleteInstanceForm: '/activity/form/del',
  // 模版
  templateList: '/template/page',
  template: '/template',
  toggleTemplate: '/template/toggleEnable',
  //我参与的活动
  joinedActivity: '/activity/participate/page'
}

/**
 * 获取活动定义
 * @param params
 * @returns
 */
export function getActivityDefine(activityId: string) {
  // return request.get(`${API.getActivityDefine}/${activityId}`)
  return request({
    url: `${API.getActivityDefine}/${activityId}`,
    method: 'GET'
  })
}
/**
 * 删除活动
 */
export function deleteActivity(activityIds: string[]) {
  return request({
    url: API.deleteActivity,
    method: 'POST',
    data: {
      ids: activityIds
    }
  })
}
/**
 * 启用/关闭活动
 */
export function toggleActivity(activityId: string) {
  return request({
    url: API.toggleActivity + '/' + activityId,
    method: 'POST'
  })
}
/**
 * 导出活动实例表单excel
 */

export function exportExcelForInstanceForm(instanceId: string, params?: any, activityName = '') {
  suiRequest({
    url: API.activityFormList,
    method: 'GET',
    responseType: 'blob',
    headers: {
      isExport: 'true'
    },
    params: { ...params, page: 1, pageSize: 999999999, activityInstId: instanceId }
  }).then((res: any) => {
    const blob = new Blob([res])
    const url = window.URL.createObjectURL(blob)
    const link = document.createElement('a')
    link.href = url
    link.setAttribute('download', `万能表单-${activityName}${(new Date() as any).format('MMDDHHmm')}.xls`)
    document.body.appendChild(link)
    link.click()
  })
}

/**
 * 删除实例表单数据
 */
export function deleteInstanceForm(instanceId: string, formIds: string[]) {
  return request({
    url: API.deleteInstanceForm + '/' + instanceId,
    method: 'POST',
    data: {
      formIds: formIds
    }
  })
}

/**
 * 模版
 */
export function saveTemplate(parameter: any) {
  const { id, ...params } = parameter || {}
  return request({
    url: `${API.template}${id ? '/' + id : ''}`,
    method: 'POST',
    data: params
  })
}

export function deleteTemplate(id: string) {
  return request({
    url: `${API.template}/del/${id}`,
    method: 'POST'
  })
}

export function toggleTemplate(id: string) {
  return request({
    url: `${API.toggleTemplate}/${id}`,
    method: 'POST'
  })
}
