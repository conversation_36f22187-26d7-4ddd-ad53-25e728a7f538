const api = {
  createTask: '/api/v1/ybtask/task',
  updateTask: '/api/v1/ybtask/task/{id}/update',
  getTask: '/ybtask/task'
}

export function createFormTask(payload: any) {
  return {
    url: api.createTask,
    method: 'POST',
    data: payload
  }
}

export function updateFormTask(id: any, payload: any) {
  return {
    url: api.updateTask,
    method: 'POST',
    data: payload
  }
}
export function getTaskInfo(id: any) {
  return {
    url: api.getTask + '/' + id,
    method: 'GET'
  }
}
