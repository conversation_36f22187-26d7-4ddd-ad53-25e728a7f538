import { IRequestOptions, createRequest } from '@sui/runtime'
import { message } from 'antd'

export const EZFORM_API_PREFIX = '/easyform-api'

const suiRequest = createRequest({
  prefix: EZFORM_API_PREFIX
})
const api = {
  userInfo: '/userInfo',
  getDefine: '/activity/define',
  submit: '/activity/participate',
  read: '/event/read',
  loadFormData: '/activity/form/my',
  getList: '/activity/form/page',
  acitvity: '/build',
  getAcitivity: '/activity/define',
  getActivityList: '/activity/page',
  getMyActivity: '/activity/participate/page',
  toggleActivityEnable: '/activity/toggleEnable',
  deleteActivity: '/activity/del',
  checkAllowJoin: '/activity/participate',
  processContext: '/wk/runContext',
  approve: '/wk/complete',
  templateList: '/template/page',
  getTemplateDefine: '/template/define',
  getMyInstFormId: '/activity/participate/inst/page',
  processHistory: '/wk/his',
  myFormData: '/activity/participate/form',
  updateFormStatus: '/activity/form/status',
  loadFormDataByInstFormId: '/activity/form/load',
  deleteForm: '/activity/form/del',
  getInsId: '/activity/inst/page',
  updateTemplate: '/template/',
  groupAppSubmit: '/groupapp/my/participate',
  groupAppUpdateForm: '/groupapp/my/participate/update',
  queryReadState: '/groupapp/participate',
  loadGroupAppFormData: '/groupapp/participate',
  delGroupAppForm: '/groupapp/my/participate/cancel'
}

export function loadFormDataByInstFormId(instFormId: string) {
  return {
    url: api.loadFormDataByInstFormId,
    method: 'GET',
    params: {
      instFormId: instFormId
    }
  }
}

/**
 * 导出活动实例表单excel
 */

export function exportExcelForInstanceForm(activityId: string, params?: any, activityName = '') {
  suiRequest({
    url: '/activity/form/page',
    method: 'GET',
    responseType: 'blob',
    headers: {
      isExport: 'true'
    },
    params: { ...params, page: 1, pageSize: 999999999, activityId: activityId }
  }).then((res: any) => {
    const blob = new Blob([res])
    const url = window.URL.createObjectURL(blob)
    const link = document.createElement('a')
    link.href = url
    link.setAttribute('download', `万能表单-${activityName}${(new Date() as any).format('MMDDHHmm')}.xls`)
    document.body.appendChild(link)
    link.click()
  })
}
export function getMyFormData(activityId?: string, activityInstanceId?: string) {
  return {
    url: api.myFormData,
    method: 'GET',
    params: {
      activityId: activityId,
      activityInstId: activityInstanceId
    }
  }
}
export function getInstanceId(id: any) {
  return {
    url: api.getInsId + '/' + id,
    method: 'GET'
  }
}
export function updateTemp(id: any, data: any) {
  return {
    url: api.updateTemplate + id,
    method: 'POST',
    data
  }
}
export function getTempInfo(id: any) {
  return {
    url: api.updateTemplate + id,
    method: 'GET'
  }
}
export function getProcessHistory(instFormId: string) {
  return {
    url: api.processHistory + '/' + instFormId,
    method: 'GET'
  }
}
export function changeFormStatus(instanceId: string, data: any) {
  return {
    url: api.updateFormStatus + '/' + instanceId,
    method: 'POST',
    data
  }
}
export function getMyInstFormId(activityId: string) {
  return {
    url: api.getMyInstFormId + '/' + activityId,
    method: 'GET'
  }
}
export function getTemplateDefine(id: string) {
  return {
    url: api.getTemplateDefine + '/' + id,
    method: 'GET'
  }
}

export function templateList(params: any) {
  if (params?.category === '') {
    delete params.category
  }
  return {
    url: api.templateList,
    method: 'GET',
    params: params
  }
}

export function approveProcess(instFormId: string, parameters: any = {}) {
  return {
    url: `${api.approve}/${instFormId}`,
    method: 'POST',
    data: parameters
  }
}

export function getProcessContext(parameters: any) {
  return {
    url: `${api.processContext}`,
    method: 'GET',
    params: parameters
  }
}

export function checkAllowJoin(activityId: string, instanceId?: string) {
  return {
    url: `${api.checkAllowJoin}/${activityId}`,
    method: 'GET',
    params: {
      instanceId: instanceId
    }
  }
}

export function deleteActivity(id: string) {
  return {
    url: api.deleteActivity,
    method: 'POST',
    data: { ids: [id] }
  }
}

export function deleteFormIns(id: string, data?: any) {
  return {
    url: `${api.deleteForm}/${id}`,
    method: 'POST',
    data
  }
}

export function toggleActivityEnable(id: string) {
  return {
    url: `${api.toggleActivityEnable}/${id}`,
    method: 'post'
  }
}

export function getMyActivity(params: any) {
  return {
    url: api.getMyActivity,
    method: 'GET',
    params: params
  }
}

export function getActivityList(params: any) {
  return {
    url: `${api.getActivityList}`,
    method: 'GET',
    params: params
  }
}

export function getActivity(id: string) {
  return {
    url: `${api.getAcitivity}/${id}`,
    method: 'GET'
  }
}

export function saveActivity(params: any, id?: string) {
  return {
    url: `${api.acitvity}${id ? '/' + id : ''}`,
    method: 'POST',
    data: params
  }
}

export function loadFormData(instanceId: string) {
  return {
    url: `${api.loadFormData}/${instanceId}`,
    method: 'GET'
  }
}

export function getList(activityInstId?: string, activityId?: string, params?: any) {
  return {
    url: api.getList,
    method: 'GET',
    params: {
      activityId: activityId,
      activityInstId: activityInstId,
      ...params
    }
  }
}
export function getUserInfo() {
  return {
    url: api.userInfo,
    method: 'GET'
  }
}

export function getActivityDefine(id: string, querystr = '') {
  return {
    url: `${api.getDefine}/${id}${querystr}`,
    method: 'GET'
  }
}

export function read(acitvityId: string, instanceId: string) {
  return {
    url: `${api.read}/${acitvityId}/${instanceId}`,
    method: 'POST',
    data: {}
  }
}

export function saveActivityInstance(activityId: string, parametter: any = {}, instanceId?: string, groupId?: string) {
  return {
    url: `${groupId ? api.groupAppSubmit : api.submit}/${activityId}`,
    method: 'POST',
    params: {
      activityInstId: instanceId
    },
    data: parametter,
    headers: {
      'x-sudy-group-id': groupId
    }
  }
}

export function submitGroupFormData(activityId: string, parametter: any = {}, groupId?: string, formId?: string) {
  return {
    url: formId ? `${api.groupAppUpdateForm}/${activityId}/${formId}` : `${api.groupAppSubmit}/${activityId}`,
    method: 'POST',
    data: parametter,
    headers: {
      'x-sudy-group-id': groupId
    }
  }
}

export function deleteActivityInstance(pageId: string, dataId: string, parametter: any) {
  return {
    url: `${api.submit}/${pageId}/${dataId}`,
    method: 'DELETE',
    data: parametter
  }
}
// 获取活动定义
export function activityPage(parametter: any) {
  console.log(parametter, 'parametter')
  return {
    url: '/activity/page',
    method: 'GET',
    params: parametter
  }
}
//上传接口
export function mobilePublish(parameters: any, id = 0) {
  console.log(parameters?.id, id)
  return {
    url: '/attachment/upload/yibanng',
    method: 'POST',
    data: parameters
  }
}

export function getStatistic(activityId: string, instanceId?: string, parameters?: any) {
  return {
    url: '/statistic/field',
    method: 'GET',
    params: { activityId, activityInstId: instanceId, ...parameters }
  }
}

export function getStatisticParticipate(activityId: string, instanceId?: string, parameters?: any) {
  return {
    url: '/statistic/participateCount',
    method: 'GET',
    params: { activityId, activityInstId: instanceId, ...parameters }
  }
}

export function activityInst(parameters: any, id = 0) {
  console.log(parameters, id)
  return {
    url: `/activity/inst/page/${parameters?.instanceId}`,
    method: 'GET',
    params: parameters
  }
}

/**
 * 群组app,查询活动所有实例我参与的表单(eg:群日志)
 */
export function getMyAllInstFormList(activityId = '', params: any = {}, groupId?: string) {
  return {
    url: `/groupapp/participate/${activityId}`,
    method: 'GET',
    params: params,
    headers: {
      'x-sudy-group-id': groupId
    }
  }
}

export function readState(groupId: string, activityId: string, formId: string, params?: any) {
  return {
    url: `${api.queryReadState}/${activityId}/messageRead/${formId}`,
    method: 'GET',
    params: params,
    headers: {
      'x-sudy-group-id': groupId
    }
  }
}
export function loadGroupAppFormData(groupId: string, activityId: string, formId: string) {
  return {
    url: `${api.loadGroupAppFormData}/${activityId}/form`,
    method: 'GET',
    params: { formId: formId },
    headers: {
      'x-sudy-group-id': groupId
    }
  }
}

export function delGroupForm(activityId = '', instanceId: string, formId: string, groupId?: string) {
  return {
    url: `${api.delGroupAppForm}/${activityId}`,
    method: 'POST',
    params: {
      activityInstId: instanceId
    },
    data: {
      formIds: [formId]
    },
    headers: {
      'x-sudy-group-id': groupId
    }
  }
}
