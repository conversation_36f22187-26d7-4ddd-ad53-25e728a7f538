import { IIdentityList, ISubjectMembers, SubjectType } from '@/interfaces/user'
import { IResultData } from '@/interfaces/service'

import { IRequestOptions, createRequest } from '@sui/runtime'

export const CUC_API_PREFIX = '/easyform-api/cuc2'
const request = createRequest({
  prefix: CUC_API_PREFIX
})

export const API_CUC = {
  /**
   * 获取CUC的人员数据
   */
  getCucUserInfo: '/user/:id',
  /**
   * 获取人员管理的班级
   */
  getLeaderClasses: '/classleader/:id/classes',

  /**
   * 获取学院下的辅导员
   */
  getAcademyClassLeaders: '/academy/:id/classleader',

  /**
   * 获取学工岗位
   */
  getPositionsetXgbzr: '/positionset/poss_xgbzr/person',

  /**
   * 获取班级下的学生
   */
  getClassStudents: '/class/:id/students',

  /**
   * 获取主体下的成员
   */
  getSubjectMembers: '/subject/members',

  /**
   * 获取身份列表
   */
  getIdentityList: '/identity/list'
}

export interface ISubjectMembersServiceQuery {
  subjectType: SubjectType
  subjectCode: string
  length?: number
}

/**
 * 查询主体下的成员
 * @param params
 */
export function getSubjectMembers(
  params?: IRequestOptions<IResultData<ISubjectMembers>, any, any, ISubjectMembersServiceQuery>
) {
  return request<IResultData<ISubjectMembers>>(API_CUC.getSubjectMembers, params)
}
