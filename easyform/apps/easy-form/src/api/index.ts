const api = {
  msgList: '/message/',
  userInfo: '/user/getinfo',
  ybActivity: '/ybactivity/activity'
}

export function saveActivity(params: any, id?: string) {
  return {
    url: id ? `${api.ybActivity}/${id}/update` : api.ybActivity,
    method: 'POST',
    data: params
  }
}
export function deleteActivity(id: string) {
  return {
    url: `${api.ybActivity}/${id}/delete`,
    method: 'POST'
    // data: { ids: [id] }
  }
}
export function getUserInfo(parameter: any = {}) {
  return {
    url: api.userInfo,
    method: 'GET',
    params: parameter
  }
}

export function getMsgList(parameter: any = {}) {
  return {
    url: `${api.msgList}u/query`,
    method: 'GET',
    params: parameter
  }
}
