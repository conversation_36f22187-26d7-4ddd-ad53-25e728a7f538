import { createRequest, IRequestOptions } from '@sui/runtime'

export const CUC_PRE = '/easyform-api/cuc2'

const cucR = createRequest({
  prefix: CUC_PRE
})

const cucRequest = (options: IRequestOptions) => {
  return new Promise((resolve, reject) => {
    cucR(options)
      .then((res) => {
        resolve(res)
      })
      .catch((err) => {
        reject(err)
      })
  })
}

//查询机构树
export function getOrgTree(data: any) {
  return cucRequest({
    url: '/orgTree',
    method: 'GET',
    params: data
  })
}

//查询班级
export function getClazz(data: any) {
  return cucRequest({
    url: `/clazz/${data.orgCode}`,
    method: 'GET',
    params: { ...data }
  })
}

//查询组
export function getGroup(data: any) {
  return cucRequest({
    url: '/group',
    method: 'GET',
    params: data
  })
}

//查询岗位
export function getPosition(data: any) {
  return cucRequest({
    url: `/position/${data.orgCode}`,
    method: 'GET',
    params: data
  })
}

//查询岗位集（职称）
export function getPositionSet(data: any) {
  return cucRequest({
    url: '/positionSet',
    method: 'GET',
    params: data
  })
}

//查询人员
export function getUser(data: any) {
  return cucRequest({
    url: '/user',
    method: 'GET',
    params: data
  })
}
