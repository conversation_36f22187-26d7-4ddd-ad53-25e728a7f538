const { readFileSync } = require('fs')
const path = require('path')
const { getDevConfig } = require('./util')
const getCookie = () => readFileSync(path.join(__dirname, '../', 'cookie.txt')).toString().trim()

const { prefixUrl = '', serverHost = '' } = getDevConfig()

module.exports = {
  historyApiFallback: {
    index: '/easyform/'
  },
  allowedHosts: 'all',
  compress: false,
  proxy: {
    [`${prefixUrl}/api`]: {
      target: serverHost,
      changeOrigin: true,
      onProxyReq(proxyReq) {
        proxyReq.setHeader('<PERSON>ie', getCookie())
      }
    },
    '/easyform-api/': {
      // target: 'https://xhyb.xhu.edu.cn',
      target: serverHost,
      changeOrigin: true,
      // pathRewrite: {
      //   '/coreplus/api': '/api'
      // },
      onProxyReq: function (proxyReq, res, req) {
        proxyReq.setHeader('<PERSON><PERSON>', getCookie())
      }
    },
    '/ticket-api/': {
      target: serverHost,
      changeOrigin: true,
      onProxyReq: function (proxyReq, res, req) {
        proxyReq.setHeader('Cookie', getCookie())
      }
    },
    '/api/v1': {
      target: 'https://yibanng.sudytech.cn',
      changeOrigin: true,
      onProxyReq(proxyReq) {
        proxyReq.setHeader('Cookie', readFileSync('./cookie.txt').toString().trim())
      }
    },
    '/easyform-api/cuc': {
      target: 'https://edu.sudytech.cn',
      changeOrigin: true,
      // pathRewrite: {
      //   '/coreplus/api': '/api'
      // },
      onProxyReq(proxyReq) {
        proxyReq.setHeader('Cookie', readFileSync('./cookie.txt').toString().trim())
      }
    },
    '/sui/': {
      target: serverHost,
      changeOrigin: true
    }
  }
}
