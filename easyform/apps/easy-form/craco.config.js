const path = require('path')
const resolve = (dir) => path.resolve(__dirname, dir)
const webpack = require('webpack')
const { ModuleFederationPlugin } = webpack.container
const HtmlWebpackPlugin = require('html-webpack-plugin')
const devServer = require('./config/devServer')

module.exports = {
  babel: {
    plugins: [
      [
        '@emotion',
        {
          importMap: {
            '@mui/system': {
              styled: {
                canonicalImport: ['@emotion/styled', 'default'],
                styledBaseImport: ['@mui/system', 'styled']
              }
            }
          }
        }
      ]
    ]
  },
  devServer: devServer,
  webpack: {
    alias: {
      '@': resolve('src'),
      '@@': resolve('src/.sui'),
      'process': 'process/browser'
    },
    plugins: {
      add: [
        new ModuleFederationPlugin({
          name: 'coreplus_mobile',
          shared: {
            react: { eager: true, singleton: true },
            'react-dom': { eager: true, singleton: true },
            'react-router': { eager: true, singleton: true },
            'react-router-dom': { eager: true, singleton: true },
            '@mui/system': { singleton: true, requiredVersion: false },
            'antd-mobile': { singleton: true, requiredVersion: false }
          }
        })
      ]
    },
    configure: (webpackConfig, { env, paths }) => {
      webpackConfig.output.publicPath = '/easyform/'

      // 禁用 fork-ts-checker-webpack-plugin 以避免 RpcIpcMessagePortClosedError
      webpackConfig.plugins = webpackConfig.plugins.filter(
        plugin => plugin.constructor.name !== 'ForkTsCheckerWebpackPlugin'
      )

      return webpackConfig
    }
  },
  jest: {
    babel: {
      addPresets: true,
      addPlugins: true
    }
  }
}
