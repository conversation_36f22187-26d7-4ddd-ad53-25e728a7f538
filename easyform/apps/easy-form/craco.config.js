const path = require('path')
const resolve = (dir) => path.resolve(__dirname, dir)
const webpack = require('webpack')
const { ModuleFederationPlugin } = webpack.container
const HtmlWebpackPlugin = require('html-webpack-plugin')
const devServer = require('./config/devServer')

module.exports = {
  babel: {
    plugins: [
      [
        '@emotion',
        {
          importMap: {
            '@mui/system': {
              styled: {
                canonicalImport: ['@emotion/styled', 'default'],
                styledBaseImport: ['@mui/system', 'styled']
              }
            }
          }
        }
      ]
    ]
  },
  devServer: devServer,
  webpack: {
    alias: {
      '@': resolve('src'),
      process: 'process/browser'
    },
    plugins: {
      add: [
        new ModuleFederationPlugin({
          name: 'coreplus_mobile',
          shared: {
            react: { eager: true, singleton: true },
            'react-dom': { eager: true, singleton: true },
            'react-router': { eager: true, singleton: true },
            'react-router-dom': { eager: true, singleton: true },
            '@mui/system': { singleton: true, requiredVersion: false },
            'antd-mobile': { singleton: true, requiredVersion: false }
          }
        })
      ]
    },
    configure: (webpackConfig, { env, paths }) => {
      webpackConfig.output.publicPath = '/easyform/'
      return webpackConfig
    }
  },
  jest: {
    babel: {
      addPresets: true,
      addPlugins: true
    }
  }
}
