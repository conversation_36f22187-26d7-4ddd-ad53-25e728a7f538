const defaultTheme = {
  palette: {
    type: 'light',
    primary: {
      light: '#9E92F5',
      main: '#6F6DEA'
    },
    secondary: {
      main: '#FB8077'
    },
    error: {
      main: '#f44336'
    },
    warning: {
      main: '#EE8152'
    },
    info: {
      main: '#583784'
    },
    success: {
      main: '#4caf50'
    },
    divider: 'rgba(0,0,0,0.05)',
    text: {
      primary: '#333',
      secondary: '#84848E',
      disabled: 'rgba(0,0,0,0.38)',
      hint: 'rgba(0,0,0,0.3)',
      dark: '#393965'
    },
    background: {
      light: '#DEF2F8',
      default: '#f8f8f8',
      paper: '#ffffff'
    }
  },
  shape: {
    borderRadius: 4
  },
  typography: {
    fontSize: 16,
    body1: {
      fontSize: 16
    },
    body2: {
      fontSize: 14
    },
    subtitle1: {
      fontSize: 13,
      color: '#84848E'
    },
    subtitle2: {
      fontSize: 12,
      color: 'rgba(0,0,0,0.3)'
    },
    h1: {
      fontSize: 24,
      fontWeight: 500,
      letterSpacing: 1.5
    },
    h2: {
      fontSize: 22,
      fontWeight: 500,
      letterSpacing: 1.5
    },
    h3: {
      fontSize: 20,
      fontWeight: 500,
      letterSpacing: 1.5
    },
    h4: {
      fontSize: 18,
      fontWeight: 500,
      letterSpacing: 1.5
    },
    h5: {
      fontSize: 16,
      fontWeight: 500,
      letterSpacing: 1
    },
    h6: {
      fontSize: 14,
      fontWeight: 500,
      letterSpacing: 1
    }
  },
  spacing: 8
}

export default defaultTheme
export type DefaultTheme = typeof defaultTheme
