{"singleQuote": true, "jsxBracketSameLine": false, "useTabs": false, "eslintIntegration": false, "tslintIntegration": true, "requireConfig": false, "stylelintIntegration": false, "arrowParens": "always", "bracketSpacing": true, "embeddedLanguageFormatting": "auto", "htmlWhitespaceSensitivity": "css", "insertPragma": false, "jsxSingleQuote": true, "tsxSingleQuote": true, "endOfLine": "lf", "printWidth": 120, "proseWrap": "preserve", "quoteProps": "as-needed", "requirePragma": false, "semi": false, "tabWidth": 2, "trailingComma": "none"}