# See https://help.github.com/articles/ignoring-files/ for more about ignoring files.

# dependencies
node_modules
.pnp
.pnp.js

# testing
coverage

# cra
out
build
admin-build

# other
dist

# misc
.DS_Store
*.pem

# debug
npm-debug.log*
yarn-debug.log*
yarn-error.log*
.pnpm-debug.log*

# local env files
.env.local
.env.development.local
.env.test.local
.env.production.local

# turbo
.turbo
Footer

#sui
#es
#lib
.sui
sui.dev.log
*.log
package-lock.json
cookie.txt
lib/
es/
!system/lib/
!system/es/
.idea
sui2/