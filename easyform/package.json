{"private": true, "workspaces": ["apps/*", "packages/*", "config/*"], "scripts": {"build": "turbo run build", "clean": "turbo run clean", "dev": "turbo run dev --no-cache --parallel --continue", "format": "prettier --write \"**/*.{ts,tsx,md}\"", "lint": "turbo run lint", "test": "turbo run test"}, "devDependencies": {"eslint": "^8.4.1", "prettier": "^2.5.1", "turbo": "1.5.4"}, "resolutions": {"antd": "^5.6.2", "@mui/styled-engine": "5.10.16"}}