# Turborepo Project 说明

This is an Turborepo Project.

## What's inside?

This Turborepo includes the following packages/apps:

### Apps and Packages

- `apps/yiban-mobile`: 易班应用 [移动端](http://yibanng.sudytech.cn) app
- `packages/message-template`: 消息模板组件仓库
- `eslint-config-custom`: `eslint` configurations (includes `eslint-plugin-react` and `eslint-config-prettier`)
- `tsconfig`: `tsconfig.json`s used throughout the monorepo

### Utilities

- [TypeScript](https://www.typescriptlang.org/) for static type checking
- [ESLint](https://eslint.org/) for code linting
- [Jest](https://jestjs.io) test runner for all things JavaScript
- [Prettier](https://prettier.io) for code formatting

### Command

- 启动: `yarn dev`
- 打包: `yarn build`
- 指定包安装依赖: `yarn workspace @yiban/app add d3-scale`
- 应用启动代理需在根目录新建 cookie.txt 文件
