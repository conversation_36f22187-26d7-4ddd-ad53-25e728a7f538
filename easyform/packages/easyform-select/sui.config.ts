import { readFileSync } from 'fs'
import { ISuiConfig } from '@sui/sui'
export default {
  // default sui config see http://170.18.10.145:7002/package/@sui/sui
  moduleFederation: {
    shared: {
      react: { singleton: true },
      'react-dom': { singleton: true },
      '@yiban/system': { singleton: true, requiredVersion: false },
      '@mui/system': { singleton: true, requiredVersion: false },
      'antd-mobile': { singleton: true, requiredVersion: false },
      '@sui/provider': {
        singleton: true,
        requiredVersion: false
      }
    }
  },
  devServer: {
    proxy: {
      '/easyform-api/cuc2': {
        target: 'https://edu.sudytech.cn',
        changeOrigin: true,
        onProxyReq(proxyReq: any) {
          proxyReq.setHeader('Cookie', readFileSync('./cookie.txt').toString().trim())
        }
      },
      '/easyform-api/cuc': {
        target: 'https://edu.sudytech.cn',
        changeOrigin: true,
        onProxyReq(proxyReq: any) {
          proxyReq.setHeader('<PERSON>ie', readFileSync('./cookie.txt').toString().trim())
        }
      },
      '/api/v1': {
        target: 'https://edu.sudytech.cn/edu',
        changeOrigin: true,
        onProxyReq(proxyReq: any) {
          proxyReq.setHeader('Cookie', readFileSync('./cookie.txt').toString().trim())
        }
      }
    }
  },
  removeConsole: false
} as ISuiConfig
