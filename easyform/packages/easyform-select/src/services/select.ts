import { createRequest, IRequestOptions } from '@sui/runtime'

export const SCHE_PRE = `${''}/easyform-api`

const scheR = createRequest({
  prefix: SCHE_PRE
})
console.log('222aaa', SCHE_PRE)

const scheRequest = (options: IRequestOptions) => {
  return new Promise((resolve, reject) => {
    scheR(options)
      .then((res) => {
        resolve(res)
      })
      .catch((err) => {
        reject(err)
      })
  })
}

//查询组织机构
export function getStudentList(category: any, data: any) {
  return scheRequest({
    url: `/cuc/selector/org/${category}`,
    method: 'GET',
    params: data
  })
}

//查询字典表数据
export function getGroupList(category: any) {
  return scheRequest({
    url: `/cuc/selector/dic/${category}`,
    method: 'GET'
  })
}

//查询成员数据
export function getMember(category: any, data: any) {
  return scheRequest({
    url: `/cuc/selector/member/${category}`,
    method: 'GET',
    params: data
  })
}

//查询机构树
export function getOrgTree(data: any) {
  return scheRequest({
    url: '/cuc',
    method: 'GET',
    data
  })
}

//查询组
export function getGroup(data: any) {
  return scheRequest({
    url: '/cuc/group',
    method: 'GET',
    data
  })
}

//查询人
export function getUser(data: any) {
  return scheRequest({
    url: `/cuc/user`,
    method: 'GET',
    data
  })
}

//查询岗位集
export function getPositionSet(data: any) {
  return scheRequest({
    url: '/cuc/positionSet',
    method: 'GET',
    data
  })
}
