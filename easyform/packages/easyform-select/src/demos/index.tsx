import { Selection } from '@/apps'
import { But<PERSON>, Modal } from 'antd'
import { useState } from 'react'

export default function () {
  const extend = {
    categories: [{ key: 'expr', label: '自动筛选' }],
    data: {
      expr: [
        { code: 'postset:poss_xgbzr', name: '部门学工办主任' },
        {
          code: 'postset:poss_xgbzr2',
          name: '部门学工办主任2'
        }
      ]
    }
  }
  const value = {
    clazz: [
      {
        code: '1205201702',
        name: '保险学17-2'
      }
    ],
    org: [
      {
        code: '1512202101',
        name: '财务(高水平)21-1'
      },
      {
        code: '40003',
        name: '马克思主义学院'
      },
      {
        code: '3120171701133',
        name: '胡曦'
      },
      {
        code: 'dhu',
        name: '东华大学'
      },
      {
        code: '20009',
        name: '校工会'
      },
      {
        code: '0120220072',
        name: '李翠平'
      }
    ],
    group: [
      {
        code: 'gro_shuttleAppointment',
        name: '班车预约管理员'
      }
    ],
    pos: [
      {
        code: 'mkszyxy_xgbzr',
        name: '学工办主任'
      }
    ],
    posset: [
      {
        code: 'poss_test1',
        name: 'test1'
      }
    ],
    student: {
      user: [
        {
          code: 'poss_test2',
          name: 'test2'
        }
      ]
    }
  }
  function splitObjects(data: any) {
    const userObjects: any[] = []
    const otherObjects: any[] = []
    for (const key in data) {
      if (key === 'student' || key === 'jzg') {
        const value = data[key]
        Object.keys(value).forEach((v: any) => {
          if (v !== 'user') {
            otherObjects.push(...value[v])
          }
        })
        const user = value['user']
        if (user) {
          user.map((u: any) => {
            return userObjects.push({ code: u.code, name: u.name })
          })
        }
      } else {
        const value = data[key]
        value.map((v: any) => {
          return otherObjects.push({ code: v.code, name: v.name })
        })
      }
    }
    return { userObjects, otherObjects }
  }
  function mergeArrays(arr1: any[], arr2: any[]) {
    const mergedArray = []

    for (const item of arr1) {
      mergedArray.push({
        value: item.code,
        label: item.name
      })
    }

    for (const item of arr2) {
      mergedArray.push({
        value: item.code,
        label: item.name
      })
    }

    return mergedArray
  }
  function splitValue(data: any) {
    const userObjects: any[] = []
    const otherObjects: any[] = []
    for (const key in data) {
      if (key === 'student' || key === 'jzg') {
        const value = data[key]
        Object.keys(value).forEach((v: any) => {
          if (v !== 'user') {
            otherObjects.push(...value[v])
          }
        })
        const user = value['user']
        if (user) {
          user.map((u: any) => {
            return userObjects.push({ code: u.code, name: u.name })
          })
        }
      } else {
        const value = data[key]
        value.map((v: any) => {
          return otherObjects.push({ code: v.code, name: v.name })
        })
      }
    }
    return { userObjects, otherObjects }
  }
  //将object转换为带有type的数组
  function transformType(inputObject: any) {
    const transformedData: { code: any; name: any; type: any; nodetype?: any }[] = []
    function processNode(node: any, type: any) {
      if (Array.isArray(node)) {
        node?.map((n: any) => {
          const transformedItem: any = {
            code: n.code,
            name: n.name,
            type: type
          }
          transformedData.push(transformedItem)
        })
      } else {
        for (const key in node) {
          if (Array.isArray(node[key])) {
            node[key].forEach((item: any) => {
              const transformedItem: any = {
                code: item.code,
                name: item.name,
                type: type,
                nodeType: key !== '0' ? key : ''
              }
              transformedData.push(transformedItem)
            })
          }
        }
      }
    }
    for (const Type in inputObject) {
      processNode(inputObject[Type], Type)
    }
    console.log('transformedData--', transformedData)
    return transformedData
  }

  //将selectedOrg和selectPersons转化为participantDetail的格式
  const NodeTypeMap: Record<string, any> = {
    clazz: 'clazz'
  }
  const returnData = (data: any) => {
    const obj: Record<string, any> = {}
    data.selectedOrg?.forEach((o: any) => {
      switch (o?.type) {
        case 'student': {
          if (!obj.student) {
            obj.student = {}
          }
          const _key = NodeTypeMap[o.nodeType] || 'org'
          if (!obj.student[_key]) {
            obj.student[_key] = []
          }
          obj.student[_key].push({ code: o.code, name: o.name })
          // obj.student[_key].push({ code: o.key || o.code, name: o.name })
          break
        }
        case 'jzg': {
          if (!obj.jzg) {
            obj.jzg = {}
          }
          const _key = NodeTypeMap[o.nodeType] || 'org'
          if (!obj.jzg[_key]) {
            obj.jzg[_key] = []
          }
          // obj.jzg[_key].push({ code: o.key || o.code, name: o.name })
          obj.jzg[_key].push({ code: o.code, name: o.name })

          break
        }
        case 'group': {
          if (!obj.group) {
            obj.group = []
          }
          obj.group.push({ code: o.code, name: o.name })
          // obj.group.push({ code: o.key || o.code, name: o.title || o.name })
          break
        }
        case 'pos': {
          if (!obj.pos) {
            obj.pos = []
          }
          obj.pos.push({ code: o.code, name: o.name })
          // obj.pos.push({ code: o.key || o.code, name: o.title || o.name })
          break
        }
        case 'posset': {
          if (!obj.posset) {
            obj.posset = []
          }
          obj.posset.push({ code: o.key || o.code, name: o.title || o.name })
          // obj.posset.push({ code: o.key || o.code, name: o.title || o.name })
          break
        }
        case 'org': {
          if (!obj.org) {
            obj.org = []
          }
          obj.org.push({ code: o.code, name: o.name })
          break
        }
        case 'clazz': {
          if (!obj.clazz) {
            obj.clazz = []
          }
          obj.clazz.push({ code: o.code, name: o.name })
          break
        }
        case 'user': {
          if (!obj.user) {
            obj.user = []
          }
          obj.user.push({ code: o.code, name: o.name })
          break
        }
        case 'cat': {
          if (!obj.cat) {
            obj.cat = []
          }
          obj.cat.push({ code: o.code, name: o.name })
          break
        }
        case 'extra': {
          if (!obj.extra) {
            obj.extra = []
          }
          obj.extra.push({ code: o.code, name: o.name })
          break
        }
      }
    })
    data.selectPersons.forEach((o: any) => {
      switch (o.type) {
        case 'student': {
          if (!obj.student) {
            obj.student = {}
          }
          const _key = 'user'
          if (!obj.student[_key]) {
            obj.student[_key] = []
          }
          obj.student[_key].push({ code: o.code || o.key, name: o.name })
          break
        }
        case 'jzg': {
          if (!obj.jzg) {
            obj.jzg = {}
          }
          const _key = 'user'
          if (!obj.jzg[_key]) {
            obj.jzg[_key] = []
          }
          obj.jzg[_key].push({ code: o.code || o.key, name: o.name })
          break
        }
      }
    })
    return obj
  }

  //将participantDetail转换成slect需要的格式
  const name = {
    student: '学生',
    jzg: '教职工',
    group: '组',
    pos: '岗位',
    posset: '职称',
    clazz: '班级',
    user: '用户',
    org: '机构'
  }
  const orgName = {
    org: '机构',
    clazz: '班级',
    user: '用户'
  }
  function transformDefaultValue(obj: any) {
    const result: any[] = []
    for (const key in obj) {
      const Key = key as keyof typeof name
      const orgArray = obj[key]?.org
      if (Key === 'student' || Key === 'jzg') {
        const value = obj[Key]
        const newData: any[] = []
        // 遍历原始对象
        for (const subkey in value) {
          const type = subkey as keyof typeof orgName // 获取类型名称
          const elements = value[subkey] // 获取类型的元素数组
          elements.map((e: any) => {
            newData.push({
              value: e.code,
              label: `${name[Key]}:[${orgName[type]}:${e.name}]`
            })
          })
        }
        result.push(...newData)
      } else {
        Array.isArray(obj[Key]) &&
          obj[Key]?.map((o: any) => {
            result.push({
              value: o.code,
              label: `${name[Key]}:${o.name}`
            })
          })
      }
    }
    return result
  }

  function extendDetail(data: any, code: any) {
    let foundData: any
    let foundCategory: any
    const result: { [key: string]: any[] } = {}
    if (data) {
      const findData = null
      Object.keys(data.data).some((key) => {
        foundData = data.data[key].find((item: any) => item.code === code)
        // 如果找到匹配的数据，结束循环
        if (foundData) {
          foundCategory = key
          return true
        }
      })
      if (foundData) {
        result[foundCategory] = [foundData]
        console.log('result---', result)
        return result
      }
    }
  }

  console.log('bbbbbb', extendDetail(extend, 'postset:poss_xgbzr'))

  // const { userObjects, otherObjects } = splitObjects(value)
  // const aa = mergeArrays(userObjects, otherObjects)
  const obj = transformType(value)
  const defaultValue = transformDefaultValue(value)
  // console.log('value==', value)
  // console.log('userObjects===', userObjects)
  // console.log('otherObjects===', otherObjects)
  // console.log('obj===', obj)
  // console.log('defaultValue==', defaultValue)
  return (
    <div>
      <Selection
        // onChange={(...args: any) => console.log('aaaa', args)}
        // isModal={true}
        // MenuItem={[
        //   {
        //     label: '学生',
        //     key: '学生'
        //   },
        //   {
        //     label: '教职工',
        //     key: '教职工'
        //   }
        // ]}
        extend={extend}
        // value={{
        //   expr: [
        //     {
        //       code: 'postset:poss_xgbzr',
        //       name: '部门学工办主任'
        //     }
        //   ],
        //   pos: [
        //     {
        //       code: 'jwc_xgbzr',
        //       name: '学工办主任(教务处（教师教学发展中心、创新创业学院、西华学院）)'
        //     },
        //     {
        //       code: 'mkszyxy_xgbzr',
        //       name: '学工办主任(马克思主义学院)'
        //     },
        //     {
        //       code: 'glxy_xgbzr',
        //       name: '学工办主任(管理学院)'
        //     }
        //   ]
        // }}
        value={{
          jzg: {
            org: [
              {
                code: '10000',
                name: '西华大学'
              },
              {
                code: '20001',
                name: '党政办公室（保密办公室）'
              }
            ],
            clazz: [
              {
                code: '1805201901',
                name: '西华制造(车辆)19-1'
              }
            ]
          },
          student: {
            clazz: [
              {
                code: '18052019012',
                name: '西华制造(车辆)19-1'
              }
            ]
          }
        }}
        orgCode={40002}
        // disabled={true}
      />
    </div>
  )
}
