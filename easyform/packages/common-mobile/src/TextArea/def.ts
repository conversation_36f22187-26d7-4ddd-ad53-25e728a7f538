import { properties } from '@/FormItemWrapper/def'
export const DefaultInitValue = [
  { label: '无', value: '' },
  { label: '登录人姓名', value: '{{userName}}' },
  { label: '登录人学工号', value: '{{code}}' },
  { label: '登录人手机号', value: '{{phone}}' },
  { label: '登录人机构', value: '{{org}}' },
  { label: '登录人专业', value: '{{speciality}}' },
  { label: '登录人班级', value: '{{class}}' }
]
export default {
  properties: {
    ...properties,
    placeholder: {
      label: '输入提示',
      component: 'Input'
    },
    readOnly: {
      label: '只读',
      component: 'Switch'
    },
    disabled: {
      label: '禁用',
      component: 'Switch'
    },
    maxLength: {
      label: '最大输入长度',
      component: 'Input',
      props: {
        type: 'number'
      }
    },
    showCount: {
      label: '是否展示字数',
      component: 'Switch'
    },
    defaultValue: {
      label: '输入框默认内容',
      component: 'Input'
    },
    maxRows: {
      label: '最大行数',
      component: 'Input',
      props: {
        type: 'number'
      }
    },
    minRows: {
      label: '最小行数',
      component: 'Input',
      props: {
        type: 'number'
      }
    },
    initialValue: {
      label: '初始值',
      component: 'Select',
      props: {
        options: DefaultInitValue
      }
    }
  }
}
