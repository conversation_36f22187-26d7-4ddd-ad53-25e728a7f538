import React from 'react'
import { TextArea, TextAreaProps as AMTextAreaProps } from 'antd-mobile'
import Form<PERSON>temWrapper, { FormItemProps } from '../FormItemWrapper'
import { Input } from 'antd'

const { TextArea: PCTextArea } = Input

export type TextAreaProps = { isPC?: boolean; minRows?: number; maxRows?: number } & FormItemProps & AMTextAreaProps
export default React.forwardRef<HTMLInputElement, TextAreaProps>(({ isPC, minRows = 2, maxRows, ...other }, ref) => {
  return (
    <>
      {isPC ? (
        <FormItemWrapper {...other}>
          <PCTextArea autoSize={{ minRows: minRows, maxRows: maxRows }} allowClear />
        </FormItemWrapper>
      ) : (
        <FormItemWrapper {...other}>
          <TextArea autoSize={{ minRows: minRows, maxRows: maxRows }} />
        </FormItemWrapper>
      )}
    </>
  )
})
export { default as def } from './def'
