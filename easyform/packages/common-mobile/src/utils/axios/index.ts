import easyformAxios from './easyform'
import yibanAxios from './yiban'
import { Toast } from 'antd-mobile'

export function errorHandler(error: any) {
  if (error.code === 'ERR_NETWORK') {
    Toast.show({
      content: 'Network Error',
      icon: 'fail'
    })
  } else if (error.response) {
    switch (error.response.status) {
      case 401: {
        Toast.show({
          content: '身份信息已过期',
          icon: 'fail'
        })
        if (process.env.NODE_ENV !== 'development') {
          setTimeout(() => {
            window.location.reload()
          }, 1000)
        }
        break
      }
      case 404:
        Toast.show({
          content: '访问的接口不存在',
          icon: 'fail'
        })
        break
      case 400:
        Toast.show({
          content: '很抱歉，错误的错误',
          icon: 'fail'
        })
        break
      case 405:
        Toast.show({
          content: '很抱歉，不合法的请求',
          icon: 'fail'
        })
        break
      case 403:
        Toast.show({
          content: '您的请求无权限访问',
          icon: 'fail'
        })
        break
      case 500:
        Toast.show({
          content: error?.response?.data?.msg || '请求失败',
          icon: 'fail'
        })
        break
      default:
        break
    }
  }
  return Promise.reject(error)
}

export { easyformAxios, yibanAxios }
