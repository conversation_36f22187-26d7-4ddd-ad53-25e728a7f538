import React from 'react'
import FormItemWrapper, { FormItemProps } from '../FormItemWrapper'
import { Radio as AMRadio, RadioGroupProps as AMRadioGroupProps, Image, Input } from 'antd-mobile'
import { Box, styled } from '@mui/system'

type Option = {
  label: string
  value: string | number
  image?: string
  inputAble?: boolean // 支持输入;value: 其他:我不
}

export type RadioProps = {
  options?: Option[]
  placeholder?: string
  imageUrlPrefix?: string
  defaultCheckFirst?: boolean //默认选中第一个选项
} & AMRadioGroupProps &
  FormItemProps

const OptionWrapper = styled(Box)({
  display: 'flex',
  flexDirection: 'column',
  margin: '4px auto'
})

export const Radio = ({
  options,
  defaultCheckFirst,
  imageUrlPrefix = '',
  onChange,
  value,
  disabled,
  placeholder,
  ...other
}: RadioProps) => {
  const [inputValue, setInputValue] = React.useState<string>()
  const handleChange = (v: string | number) => {
    if (typeof onChange === 'function') {
      onChange(v)
    }
  }
  const optionValue = React.useMemo(() => {
    if (value && options?.findIndex((o) => o.value === value) === -1) {
      const [ov, ...rest] = value.toString().split(':')
      setInputValue(rest.join(':'))
      return options.find((o) => o.value.toString() === ov)?.value
    }
    return value || ''
  }, [options, value])
  return (
    <AMRadio.Group
      disabled={disabled}
      value={optionValue}
      onChange={handleChange}
      defaultValue={defaultCheckFirst ? (options ? options[0]?.value : void 0) : void 0}
      {...other}
    >
      {options?.map((o) => (
        <OptionWrapper key={o.value}>
          <AMRadio value={o.value}>{o.label}</AMRadio>
          {o.image && <Image style={{ margin: '4px auto' }} src={`${imageUrlPrefix}${o.image}`} />}
          {o.inputAble && optionValue === o.value && (
            <Input
              disabled={disabled}
              placeholder={placeholder}
              style={{ marginTop: 12 }}
              value={inputValue}
              onChange={(v) => {
                setInputValue(v)
                handleChange(`${o.value}:${v}`)
              }}
            />
          )}
        </OptionWrapper>
      ))}
    </AMRadio.Group>
  )
}

export default React.forwardRef<HTMLDivElement, RadioProps>((props, ref) => {
  return (
    <FormItemWrapper {...props}>
      <Radio />
    </FormItemWrapper>
  )
})
export { default as def } from './def'
