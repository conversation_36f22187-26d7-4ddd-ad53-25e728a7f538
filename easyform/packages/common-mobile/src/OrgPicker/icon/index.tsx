export function IconOrg({ width = 24, height = 24, ...other }: any) {
  return (
    <svg
      {...other}
      viewBox='0 0 1024 1024'
      version='1.1'
      xmlns='http://www.w3.org/2000/svg'
      width={width}
      height={height}
    >
      <path
        d='M235.3 754.2v121.3H114V754.2h121.3m50-50H64v221.3h221.3V704.2zM573.7 754.2v121.3H452.3V754.2h121.4m50-50H402.3v221.3h221.3V704.2h0.1zM910.4 754.2v121.3H789V754.2h121.4m50-50H739v221.3h221.3V704.2h0.1zM654.1 148.2V304h-284V148.2h284m50-50h-384V354h384.1V98.2h-0.1z'
        fill='currentColor'
        p-id='2796'
      ></path>
      <path d='M488 342h50v382h-50z' fill='currentColor' p-id='2797'></path>
      <path d='M143 487.1h737.5v50H143z' fill='currentColor' p-id='2798'></path>
      <path d='M830.5 513.2h50V720h-50zM143 513.2h50V716h-50z' fill='currentColor' p-id='2799'></path>
    </svg>
  )
}
export function IconPosition({ width = 24, height = 24, ...other }: any) {
  return (
    <svg
      {...other}
      viewBox='0 0 1024 1024'
      version='1.1'
      xmlns='http://www.w3.org/2000/svg'
      width={width}
      height={height}
    >
      <path
        fill='currentColor'
        d='M920 147H104c-35.8 0-65 29.2-65 65v600c0 35.8 29.2 65 65 65h382.9c13.8 0 25-11.2 25-25s-11.2-25-25-25H104c-8.3 0-15-6.7-15-15V212c0-8.3 6.7-15 15-15h816c8.3 0 15 6.7 15 15v591c0 13.3-10.8 24-24 24H783.7c-13.8 0-25 11.2-25 25s11.2 25 25 25H911c40.8 0 74-33.2 74-74V212c0-35.8-29.2-65-65-65z'
        p-id='3989'
      ></path>
      <path
        fill='currentColor'
        d='M463 386.6c0-67.8-55.2-123-123-123s-123 55.2-123 123 55.2 123 123 123 123-55.1 123-123z m-196 0c0-40.3 32.7-73 73-73s73 32.7 73 73-32.7 73-73 73-73-32.7-73-73z m73 152c-108.6 0-196.9 88.3-196.9 196.9 0 13.8 11.2 25 25 25s25-11.2 25-25c0-81 65.9-146.9 146.9-146.9s146.9 65.9 146.9 146.9c0 13.8 11.2 25 25 25s25-11.2 25-25c0-108.5-88.3-196.9-196.9-196.9z m302.9-143.2h130c13.8 0 25-11.2 25-25s-11.2-25-25-25h-130c-13.8 0-25 11.2-25 25 0 13.7 11.3 25 25 25z m-25 159.1c0 13.7 11.2 25 25 25h220c13.8 0 25-11.2 25-25s-11.2-25-25-25h-220c-13.7 0-25 11.3-25 25z m270 110c0-13.8-11.2-25-25-25h-220c-13.8 0-25 11.2-25 25 0 13.7 11.2 25 25 25h220c13.8 0 25-11.2 25-25z'
        p-id='3990'
      ></path>
    </svg>
  )
}

export function IconRole({ width = 24, height = 24, ...other }: any) {
  return (
    <svg
      {...other}
      viewBox='0 0 1333 1024'
      version='1.1'
      xmlns='http://www.w3.org/2000/svg'
      p-id='4987'
      width={width}
      height={height}
    >
      <path
        d='M 1101.48 342.52 a 155.844 155.844 0 1 0 -226.753 0 a 244.675 244.675 0 0 0 -53.7662 38.1818 A 197.922 197.922 0 0 0 484.34 373.688 a 243.896 243.896 0 0 0 -63.1169 -48.3117 a 155.844 155.844 0 1 0 -226.753 0 A 245.454 245.454 0 0 0 62.003 544.337 a 38.961 38.961 0 0 0 77.9221 0 a 168.311 168.311 0 0 1 298.441 -105.974 a 38.1818 38.1818 0 0 0 17.1428 13.2468 a 190.909 190.909 0 0 0 66.2337 180.78 a 319.48 319.48 0 0 0 -189.351 290.649 a 38.961 38.961 0 0 0 77.9221 0 a 241.559 241.559 0 0 1 482.337 0 a 38.961 38.961 0 0 0 77.9221 0 a 319.48 319.48 0 0 0 -189.351 -290.649 a 197.922 197.922 0 0 0 69.3506 -149.61 a 196.363 196.363 0 0 0 0 -28.052 a 37.4026 37.4026 0 0 0 17.922 -10.1299 a 166.753 166.753 0 0 1 123.117 -52.9869 A 168.311 168.311 0 0 1 1152.9 559.143 a 38.961 38.961 0 0 0 77.9221 0 a 245.454 245.454 0 0 0 -129.351 -216.623 Z M 308.236 295.767 a 77.9221 77.9221 0 1 1 77.9221 -77.9221 a 77.9221 77.9221 0 0 1 -77.9221 77.9221 Z m 342.857 311.688 a 120.779 120.779 0 1 1 121.558 -124.675 A 121.558 121.558 0 0 1 651.093 607.454 Z m 336.623 -290.649 a 77.9221 77.9221 0 1 1 77.9221 -77.9221 a 77.9221 77.9221 0 0 1 -77.9221 74.8052 Z'
        fill='currentColor'
        p-id='4988'
      ></path>
    </svg>
  )
}
export function IconLevel({ width = 24, height = 24, ...other }: any) {
  return (
    <svg
      {...other}
      viewBox='0 0 1024 1024'
      version='1.1'
      xmlns='http://www.w3.org/2000/svg'
      p-id='5574'
      width={width}
      height={height}
    >
      <path
        d='M384 725.333333c0 21.333333 17.066667 38.4 38.4 42.666667H640v-42.666667c0-21.333333 17.066667-38.4 38.4-42.666666H853.333333c25.6 0 42.666667 17.066667 42.666667 42.666666v170.666667c0 25.6-17.066667 42.666667-42.666667 42.666667h-170.666666c-25.6 0-42.666667-17.066667-42.666667-42.666667v-42.666667h-213.333333c-68.266667 0-123.733333-51.2-128-119.466666V341.333333H170.666667c-21.333333 0-38.4-17.066667-42.666667-38.4V128c0-25.6 17.066667-42.666667 42.666667-42.666667h341.333333c25.6 0 42.666667 17.066667 42.666667 42.666667v170.666667c0 25.6-17.066667 42.666667-42.666667 42.666666H384v128h256v-42.666666c0-21.333333 17.066667-38.4 38.4-42.666667H853.333333c25.6 0 42.666667 17.066667 42.666667 42.666667v170.666666c0 25.6-17.066667 42.666667-42.666667 42.666667h-170.666666c-25.6 0-42.666667-17.066667-42.666667-42.666667v-42.666666H384v170.666666z m426.666667 42.666667h-85.333334v85.333333h85.333334v-85.333333z m0-298.666667h-85.333334v85.333334h85.333334v-85.333334z m-341.333334-298.666666H213.333333v85.333333h256V170.666667z'
        fill='currentColor'
        p-id='5575'
      ></path>
    </svg>
  )
}

export function IconClazz({ width = 24, height = 24, ...other }: any) {
  return (
    <svg
      {...other}
      viewBox='0 0 1024 1024'
      version='1.1'
      xmlns='http://www.w3.org/2000/svg'
      p-id='1499'
      width={width}
      height={height}
    >
      <path
        d='M297.6 387.9c-65.6 0-119-52-119-115.9s53.4-115.9 119-115.9 119 52 119 115.9-53.4 115.9-119 115.9z m0-191.8c-43.6 0-79 34-79 75.9 0 41.8 35.5 75.9 79 75.9s79-34 79-75.9c0-41.8-35.5-75.9-79-75.9z'
        fill='currentColor'
        p-id='1500'
      ></path>
      <path
        d='M485.5 568.3h-40V489c0-53.7-43.7-97.4-97.4-97.4H246.9c-53.7 0-97.4 43.7-97.4 97.4v79.3h-40V489c0-75.7 61.6-137.4 137.4-137.4h101.3c75.7 0 137.4 61.6 137.4 137.4v79.3zM740.8 387.9c-65.6 0-119-52-119-115.9s53.4-115.9 119-115.9 119 52 119 115.9-53.4 115.9-119 115.9z m0-191.8c-43.6 0-79 34-79 75.9 0 41.8 35.5 75.9 79 75.9s79-34 79-75.9c0-41.8-35.5-75.9-79-75.9z'
        fill='currentColor'
        p-id='1501'
      ></path>
      <path
        d='M928.8 568.3h-40V489c0-53.7-43.7-97.4-97.4-97.4H690.1c-53.7 0-97.4 43.7-97.4 97.4v79.3h-40V489c0-75.7 61.6-137.4 137.4-137.4h101.3c75.7 0 137.4 61.6 137.4 137.4v79.3z'
        fill='currentColor'
        p-id='1502'
      ></path>
      <path
        d='M996.5 722.1H33.6c-11 0-20-9-20-20V584.5c0-11 9-20 20-20h962.9c11 0 20 9 20 20v117.7c0 11-9 19.9-20 19.9z m-942.9-40h922.9v-77.7H53.6v77.7z'
        fill='currentColor'
        p-id='1503'
      ></path>
      <path
        d='M125.4 926.3c-11 0-20-9-20-20V720.1c0-11 9-20 20-20s20 9 20 20v186.2c0 11.1-8.9 20-20 20zM904.6 926.3c-11 0-20-9-20-20V702.1c0-11 9-20 20-20s20 9 20 20v204.2c0 11.1-9 20-20 20z'
        fill='currentColor'
        p-id='1504'
      ></path>
    </svg>
  )
}
