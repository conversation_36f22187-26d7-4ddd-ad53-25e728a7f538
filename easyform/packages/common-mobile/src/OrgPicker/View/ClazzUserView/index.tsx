import React from 'react'
import { Box, styled } from '@mui/system'
import { <PERSON>ton, <PERSON>bs, DotLoading, InfiniteScroll, Avatar, SearchBar } from 'antd-mobile'
import List from '../../List'
import { Collapse, TabPanel, Typography } from '@/components'
import { useEasyFormRequest } from '@/hooks'
import { loadOrg, loadUser } from '../../api'

import { InfiniteScrollContent } from '../OrgView'

const StyledButton = styled(Button)({
  '& span': {
    display: 'flex',
    alignItems: 'center',
    fontSize: 12
  }
})

interface IListProps {
  code: string
  onCheck?: (type: string, itemData: any, checked?: boolean) => void
  selected?: any
}

export default function ({ code, onCheck, selected }: IListProps) {
  const [{ pageNo, pageSize }, setPagination] = React.useState({ pageNo: 1, pageSize: 20 })
  const [hasMore, setHasMore] = React.useState(true)
  const [userList, setuserList] = React.useState<any>([])
  const [keywords, setKeywords] = React.useState<string>()

  const handleCheck = (type: string, itemData: any, checked?: boolean) => {
    if (typeof onCheck === 'function') {
      onCheck(type, itemData, checked)
    }
  }

  const { runAsync: requestUser, loading } = useEasyFormRequest(loadUser, {
    manual: true
  })
  const loadMoreUser = async () => {
    const _userData = await requestUser({ pageNo, pageSize, keywords, clazzCode: code })
    const _hasMore = _userData.total > pageSize * pageNo
    setHasMore(_hasMore)
    setuserList([...userList, ..._userData.data])
    if (_hasMore) {
      setPagination((prev) => ({ ...prev, pageNo: prev.pageNo + 1 }))
    }
  }

  const handleSearch = async (sv: string) => {
    setKeywords(sv)
    const _userData = await requestUser({ pageNo: 1, pageSize: 20, keywords: sv, clazzCode: code })
    const _hasMore = _userData.total > pageSize * pageNo
    setHasMore(_hasMore)
    setuserList([..._userData.data])
    setPagination((prev) => ({ ...prev, pageNo: _hasMore ? 1 + 1 : 1 }))
  }
  const handleClearSearch = async () => {
    setKeywords(void 0)
    const _userData = await requestUser({ pageNo: 1, pageSize: 20, keywords: void 0, clazzCode: code })
    const _hasMore = _userData.total > pageSize * pageNo
    setHasMore(_hasMore)
    setuserList([..._userData.data])
    setPagination((prev) => ({ ...prev, pageNo: _hasMore ? 1 + 1 : 1 }))
  }

  return (
    <Box sx={{ p: 1.5 }}>
      <Box>
        <SearchBar onSearch={handleSearch} onClear={handleClearSearch} placeholder='请输入关键字搜索' />
      </Box>
      <List>
        {userList.length === 0 ? (
          <Typography sx={{ mt: 2 }} align='center' variant='subtitle2'>
            暂无数据
          </Typography>
        ) : (
          <>
            {userList.map((l: any) => (
              <List.Item
                checked={!!selected?.user?.find((n: any) => n.code === l.code)}
                onCheck={(c) => {
                  handleCheck('user', { code: l.code, name: l.name }, c)
                }}
                prefix={<Avatar src='' style={{ '--size': '24px', '--border-radius': '50%' }} />}
                key={l.code}
                checkable
                title={`${l.name} (${l.code})`}
              />
            ))}
          </>
        )}
        <InfiniteScroll hasMore={hasMore} loadMore={loadMoreUser}>
          <InfiniteScrollContent hasMore={hasMore} />
        </InfiniteScroll>
      </List>
    </Box>
  )
}
