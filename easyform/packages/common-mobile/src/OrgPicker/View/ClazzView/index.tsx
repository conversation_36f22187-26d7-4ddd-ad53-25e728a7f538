import React from 'react'
import { Box, styled } from '@mui/system'
import { Button, <PERSON>bs, DotLoading, InfiniteScroll, Avatar } from 'antd-mobile'
import List from '../../List'
import { Collapse, TabPanel, Typography } from '@/components'
import { useEasyFormRequest } from '@/hooks'
import { loadClazz } from '../../api'
import { IconClazz } from '../../icon'
import { BtnEnter } from '../OrgView/OrgList'
import { useMemoizedFn } from 'ahooks'

interface IListProps {
  onCheck?: (type: string, itemData: any, checked?: boolean) => void
  selected?: any
  onEnterView: (target: { code: string; name?: string }, viewType?: string) => void
  dataset?: any[]
}

export default function ({ onCheck, selected, onEnterView, dataset = [] }: IListProps) {
  const handleCheck = useMemoizedFn((itemData: any, checked?: boolean) => {
    if (typeof onCheck === 'function') {
      onCheck('clazz', itemData, checked)
    }
  })
  const handleChangeView = useMemoizedFn((code: string, name?: string) => {
    if (typeof onEnterView === 'function') {
      onEnterView({ code, name }, 'clazzUser')
    }
  })

  return (
    <Box sx={{ p: 1.5 }}>
      <List>
        {dataset.length === 0 ? (
          <Typography sx={{ mt: 2 }} align='center' variant='subtitle2'>
            暂无数据
          </Typography>
        ) : (
          <>
            {dataset.map((l: any) => (
              <List.Item
                checked={!!selected?.clazz?.find((n: any) => n.code === l.code)}
                onCheck={(c) => handleCheck({ code: l.code, name: `${l.name}` }, c)}
                key={l.code}
                prefix={<IconClazz style={{ color: '#999' }} width='16px' height='16px' />}
                checkable
                arrow={
                  <BtnEnter
                    text='进入班级'
                    disabled={!!selected?.clazz?.find((n: any) => n.code === l.code)}
                    onClick={() => handleChangeView(l.code, l.name)}
                  />
                }
                title={l.name}
              />
            ))}
          </>
        )}
      </List>
    </Box>
  )
}
