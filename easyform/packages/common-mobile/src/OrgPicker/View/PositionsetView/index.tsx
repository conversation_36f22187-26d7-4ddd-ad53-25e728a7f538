import React from 'react'
import { Box, styled } from '@mui/system'
import { <PERSON>ton, <PERSON>bs, DotLoading, InfiniteScroll, Avatar } from 'antd-mobile'
import List from '../../List'
import { Collapse, TabPanel, Typography } from '@/components'
import { useEasyFormRequest } from '@/hooks'
import { loadPositionSet } from '../../api'
import { IconRole } from '../../icon'
import { InfiniteScrollContent } from '../OrgView'

interface IListProps {
  orgName?: string
  onCheck?: (type: string, itemData: any, checked?: boolean) => void
  selected?: any
}

export default function ({ onCheck, selected }: IListProps) {
  const [{ pageNo, pageSize }, setPagination] = React.useState({ pageNo: 1, pageSize: 20 })
  const [hasMore, setHasMore] = React.useState(true)
  const [list, setList] = React.useState<any>([])
  const { runAsync: request, loading } = useEasyFormRequest(loadPositionSet, {
    manual: true
  })
  const loadMore = async () => {
    const _data = await request({ pageNo, pageSize })
    const _hasMore = _data.total > pageSize * pageNo
    setHasMore(_hasMore)
    setList([...list, ..._data.data])
    if (_hasMore) {
      setPagination((prev) => ({ ...prev, pageNo: prev.pageNo + 1 }))
    }
  }
  const handleCheck = (itemData: any, checked?: boolean) => {
    if (typeof onCheck === 'function') {
      onCheck('group', itemData, checked)
    }
  }

  return (
    <Box sx={{ p: 1.5 }}>
      {loading ? (
        <Box className='flex justify-center' sx={{ p: 2 }}>
          <DotLoading />
        </Box>
      ) : (
        <List>
          {list.length === 0 ? (
            <Typography sx={{ mt: 2 }} align='center' variant='subtitle2'>
              暂无数据
            </Typography>
          ) : (
            <>
              {list.map((l: any) => (
                <List.Item
                  checked={!!selected?.group?.find((n: any) => n.code === l.code)}
                  onCheck={(c) => handleCheck({ code: l.code, name: `${l.name}` }, c)}
                  key={l.code}
                  prefix={<IconRole style={{ color: '#999' }} width='16px' height='16px' />}
                  checkable
                  title={l.name}
                />
              ))}
            </>
          )}
          <InfiniteScroll hasMore={hasMore} loadMore={loadMore}>
            <InfiniteScrollContent hasMore={hasMore} />
          </InfiniteScroll>
        </List>
      )}
    </Box>
  )
}
