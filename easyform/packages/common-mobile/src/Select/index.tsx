import React from 'react'
import <PERSON><PERSON>tem<PERSON>rapper, { FormItemProps } from '../FormItemWrapper'
import { Box, styled } from '@mui/system'
import { Select as PcSelect } from 'antd'
import { Input, Picker } from 'antd-mobile'
import { DownOutline as IconDown } from 'antd-mobile-icons'
import { PickerColumn, PickerValue, PickerValueExtend } from 'antd-mobile/es/components/picker-view'

const Root = styled(Box)({
  display: 'flex',
  alignItems: 'center'
})

const PcRoot = styled(Box)({})

const StyledInput = styled(Input)({
  flexGrow: 1,
  marginRight: 8
})

export type Option = {
  label?: string
  text?: string
  value: string
}

export type SelectProps = {
  options?: Option[] | string[]
  placeholder?: string
  value?: string
  isPC?: boolean
  readOnly?: boolean
  onChange?: (value: string | null) => void
} & FormItemProps

const StyledIconDown = styled(IconDown, { shouldForwardProp: (prop) => prop !== 'open' })<{ open?: boolean }>(
  ({ open }) => ({
    transition: 'transform .3s ease-in-out',
    transform: open ? 'rotate(180deg)' : void 0
  })
)

const Select = ({ value, onChange, placeholder = '请选择', readOnly, isPC, options }: SelectProps) => {
  const [open, setOpen] = React.useState(false)
  const columns = React.useMemo<PickerColumn[]>(
    () =>
      options
        ? [
            options.map((o) =>
              typeof o === 'string'
                ? o
                : {
                    label: o.label || o.text,
                    value: o.value,
                    key: o.value
                  }
            )
          ]
        : [],
    [options]
  )
  const pcOptions = React.useMemo<PickerColumn[]>(
    () =>
      options
        ? (options as any).map((o: string | Option) =>
            typeof o === 'string'
              ? {
                  label: o,
                  value: o
                }
              : {
                  label: o.label || o.text,
                  value: o.value
                  //key: o.value
                }
          )
        : [],
    [options]
  )
  const handleSelect = (value: PickerValue[], extend: PickerValueExtend) => {
    if (typeof onChange === 'function') {
      console.log('==change:', value[0])
      onChange(value[0] as any)
    }
  }
  const handleChange = (value: PickerValue) => {
    if (typeof onChange === 'function') {
      console.log('==selectchange:', value)
      onChange(value as any)
    }
  }
  const optionValueMap = React.useMemo<{ [k: string]: string }>(() => {
    return options
      ? (options as any[]).reduce((p, c, i) => {
          return { ...p, [typeof c === 'string' ? c : c.value]: typeof c === 'string' ? c : c.label || c.text }
        }, {})
      : {}
  }, [options])

  const handleClick = () => {
    if (!readOnly) {
      setOpen((prev) => !prev)
    }
  }

  const showValue = React.useMemo(() => (value !== void 0 ? optionValueMap[value] : void 0), [optionValueMap, value])
  return (
    <>
      {isPC ? (
        <PcRoot>
          <PcSelect value={value} onChange={handleChange} style={{ width: '100%' }} options={pcOptions}></PcSelect>
        </PcRoot>
      ) : (
        <>
          <Root onClick={handleClick}>
            <StyledInput value={showValue} readOnly />
            {!readOnly && <StyledIconDown open={open} />}
          </Root>
          <Picker
            columns={columns}
            visible={open}
            onConfirm={handleSelect}
            onClose={() => setOpen(false)}
            value={[value || null]}
          />
        </>
      )}
    </>
  )
}

export default React.forwardRef<HTMLDivElement, SelectProps>((props, ref) => {
  return (
    <FormItemWrapper {...props}>
      <Select />
    </FormItemWrapper>
  )
})
export { default as def } from './def'
