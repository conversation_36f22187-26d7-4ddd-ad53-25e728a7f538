import React from 'react'
import { Input, InputProps as AMInputProps } from 'antd-mobile'
import FormItemWrapper, { FormItemProps } from '../FormItemWrapper'
import Rules, { TRuleType } from './rules'
import { Input as PcInput } from 'antd'
import { Rule } from 'rc-field-form/lib/interface'

export type InputProps = FormItemProps &
  AMInputProps & {
    isPC?: boolean
    ruleType?:
      | TRuleType
      | RegExp
      | string
      | {
          reg: string | RegExp
          message?: string
        }
    readOnly?: boolean
  }
export default React.forwardRef<HTMLInputElement, InputProps>(({ ruleType, isPC, ...other }, ref) => {
  const rules = React.useMemo<Rule[] | undefined>(() => {
    if (typeof ruleType === 'string') {
      const _targetRule = (Rules as any)[ruleType]
      return _targetRule
        ? [{ pattern: _targetRule.reg, message: _targetRule.message }]
        : [{ pattern: new RegExp(ruleType), message: '输入不合法' }]
    }
    /**
     * RegExp或对象{reg:'',message:''}
     */
    if (typeof ruleType === 'object') {
      return [
        {
          pattern: new RegExp((ruleType as any)?.reg || ruleType),
          message: (ruleType as any)?.message || '输入不合法'
        }
      ]
    }
    return void 0
  }, [ruleType])
  return (
    <>
      {isPC ? (
        <FormItemWrapper rules={rules} {...other}>
          <PcInput allowClear />
        </FormItemWrapper>
      ) : (
        <FormItemWrapper rules={rules} {...other}>
          <Input clearable />
        </FormItemWrapper>
      )}
    </>
  )
})

export { default as def } from './def'
