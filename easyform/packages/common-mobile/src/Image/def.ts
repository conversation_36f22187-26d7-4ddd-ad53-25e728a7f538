export default {
  properties: {
    src: {
      label: '上传图片',
      component: 'ImageUploader',
      props: {
        multiple: false,
        maxCount: 1
      }
    },
    textAlign: {
      label: '图片对齐方式',
      component: 'Select',
      props: {
        value: 'left',
        options: [
          {
            label: '居中',
            value: 'center'
          },
          {
            label: '左对齐',
            value: 'left'
          },
          {
            label: '右对齐',
            value: 'right'
          }
        ]
      }
    },
    objectFit: {
      label: '图片填充方式',
      component: 'Select',
      props: {
        value: 'none',
        options: [
          {
            label: '无',
            value: 'none'
          },
          {
            label: '缩放',
            value: 'contain'
          },
          {
            label: '裁剪',
            value: 'cover'
          },
          {
            label: '填充',
            value: 'fill'
          }
        ]
      }
    },
    borderRadius: {
      label: '图片圆角',
      component: 'Slider',
      props: {
        unitOptions: ['%'],
        unitRanges: [{ min: 0, max: 100 }]
      }
    },
    width: {
      label: '图片宽度',
      component: 'Slider',
      props: {
        unitOptions: ['%', 'px'],
        unitRanges: [
          { min: 0, max: 100 },
          { min: 0, max: 1000 }
        ]
        //disableSliderUnit: ['px']
      }
    },
    height: {
      label: '图片高度',
      component: 'Slider',
      props: {
        unitOptions: ['px', '%'],
        unitRanges: [
          { min: 0, max: 1000 },
          { min: 0, max: 100 }
        ]
        //disableSliderUnit: ['px']
      }
    },
    preview: {
      label: '预览模式',
      component: 'Switch',
      props: {
        initialValue: true
      }
    }
  }
}
