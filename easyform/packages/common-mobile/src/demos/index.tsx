import { <PERSON>, Button } from 'antd-mobile'
import { PictureOutline } from 'antd-mobile-icons'
import {
  Input,
  TextArea,
  Slider,
  Select,
  ImageUploader,
  HyperText,
  Radio,
  OrgPicker,
  Checkbox,
  ColorPicker,
  Label,
  Image,
  Upload,
} from '@/index'
import UploadList from '@/components/UploadList'
import ImageList from '@/components/ImageList'

const data = [
  {
    title: '辅导员审批',
    img: 'https://gimg2.baidu.com/image_search/src=http%3A%2F%2Fc-ssl.duitang.com%2Fuploads%2Fitem%2F201912%2F25%2F20191225224833_zloky.thumb.1000_0.jpg&refer=http%3A%2F%2Fc-ssl.duitang.com&app=2002&size=f9999,10000&q=a80&n=0&g=0n&fmt=auto?sec=1683789869&t=64706f7bdd004c6ddb22ed62c9f7f924',
    name: '华',
    isPass: true,
    approvalTime: '2023-04-11 14:58'
  },
  {
    title: '辅导员审批',
    img: 'https://gimg2.baidu.com/image_search/src=http%3A%2F%2Fc-ssl.duitang.com%2Fuploads%2Fitem%2F201912%2F25%2F20191225224833_zloky.thumb.1000_0.jpg&refer=http%3A%2F%2Fc-ssl.duitang.com&app=2002&size=f9999,10000&q=a80&n=0&g=0n&fmt=auto?sec=1683789869&t=64706f7bdd004c6ddb22ed62c9f7f924',
    name: '江',
    isPass: false,
    approvalTime: '2023-04-11 14:58'
  },
  {
    title: '辅导员审批',
    img: 'https://gimg2.baidu.com/image_search/src=http%3A%2F%2Fc-ssl.duitang.com%2Fuploads%2Fitem%2F201912%2F25%2F20191225224833_zloky.thumb.1000_0.jpg&refer=http%3A%2F%2Fc-ssl.duitang.com&app=2002&size=f9999,10000&q=a80&n=0&g=0n&fmt=auto?sec=1683789869&t=64706f7bdd004c6ddb22ed62c9f7f924',
    name: '江华',
    approvalTime: '2023-04-11 14:58'
  }
]

export default function () {
  const [form] = Form.useForm()

  return (
    <div>
      <HyperText text='文件地址' href='http://www.baidu.com'></HyperText>
      <Label
        title={''}
        text={'和法规和换个风格风格化风格化规范化风格和规范风格化法国风格和花粉管花粉管花粉管风格化风格化'}
      ></Label>
      <Image></Image>
      <Form
        form={form}
        initialValues={{
          select: 'email',
          // checkbox: 'blue:1234',
          // upload: [
          //   '50af1ffb9bce4eb0a9e6127f17a94201.docx',
          //   'f76cdeba554a4c3a9bfcd73b2c113fed.png',
          //   'f0788dd00d0d4b7d84b3a4be888786a7.xlsx'
          // ]
          upload:'50af1ffb9bce4eb0a9e6127f17a94201.docx'
        }}
        onFinish={(values) => {
          console.log('values=', values)
        }}
        footer={
          <Button type='submit' block color='primary'>
            提交
          </Button>
        }
      >
        <Input label='PC输入框' name='input' value='123123'></Input>
         <UploadList value={['c68006a2b58e429a91a46c990b8c17ee.pdf','c68006a2b58e429a91a46c990b8c17ee.pdf','c68006a2b58e429a91a46c990b8c17ee.pdf']}></UploadList>
        <Upload
          // disabled={true}
          uploadUrl='/easyform-api/attachment/upload/easyform'
          acceptType={['image/*', '.doc']}
          label='文件上传'
          name='upload'
          onChange={(info, list, e) => {
            console.log('info===', info)
            console.log('list===', list)
          }}
        />
        <Slider
          label={'滑动选择'}
          unitOptions={['%', 'px']}
          unitRanges={[
            { min: 0, max: 100 },
            { min: 0, max: 1000 }
          ]}
          name={'Align'}
        ></Slider>
        <ColorPicker name={'colorPicker'} valueType='toRgbString'></ColorPicker>
        <Input minLength={5} label='输入框' name='input' />
        <Input isPC label='PC输入框' name='input' />
        <TextArea name='textarea' showCount maxLength={10} label='文本域' placeholder='请输入文本域内容' />
        <TextArea name='textarea' showCount isPC label='PC文本域' placeholder='请输入文本域内容' />
        <Select
          label='下拉'
          name='select'
          isPC={false}
          options={[
            { label: '无', value: '' },
            { label: '手机号', value: 'tel' },
            { label: '邮箱', value: 'email' }
          ]}
        />
        <Select
          label='PC下拉'
          name='select'
          isPC={true}
          options={[
            { label: '无', value: '' },
            { label: '手机号', value: 'tel' },
            { label: '邮箱', value: 'email' }
          ]}
        />
        <ImageUploader name='imageupload' label='图片上传' uploadUrl='easyform-api/attachment/upload/easyform'>
          <div
            style={{
              width: 80,
              height: 80,
              borderRadius: 40,
              backgroundColor: '#f5f5f5',
              display: 'flex',
              justifyContent: 'center',
              alignItems: 'center',
              color: '#999999'
            }}
          >
            <PictureOutline style={{ fontSize: 32 }} />
          </div>
        </ImageUploader>
        <Radio
          label='单选'
          name='radio'
          options={[
            {
              label: '黄色',
              value: 'yellow'
            },
            {
              label: '红色',
              value: 'red'
            },
            {
              label: 'PPT文档',
              value: '.ppt,.pptx'
            },
            { label: '蓝色2', value: 'blue', inputAble: true }
          ]}
        />
        <Checkbox
          label='多选'
          name='checkbox'
          valueType='string'
          options={[

            {
              label: '图片',
              value: 'image/*'
            },
            {
              label: '视频',
              value: 'video/*'
            },
            {
              label: '音频',
              value: 'audio/*'
            },
            {
              label: 'PDF',
              value: '.pdf'
            },
            {
              label: 'DOC文档',
              value: '.doc'
            },
            {
              label: 'PPT文档',
              value: '1',
              inputAble: true
            },
            {
              label: 'EXCEL文档',
              value: '.xls'
            }
          ]}
        />

        {/* <OrgPicker valueType='string' required label='请选择院系' name='org' /> */}
      </Form>
    </div>
  )
}
