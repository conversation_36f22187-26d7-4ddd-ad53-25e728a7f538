import { properties } from '@/FormItemWrapper/def'
export const DefaultInitValue = [
  { label: '无', value: '' },
  { label: '登录人姓名', value: '{{userName}}' },
  { label: '登录人学工号', value: '{{code}}' },
  { label: '登录人手机号', value: '{{phone}}' },
  { label: '登录人机构', value: '{{org}}' },
  { label: '登录人专业', value: '{{speciality}}' },
  { label: '登录人班级', value: '{{class}}' }
]
export default {
  properties: {
    ...properties,
    readOnly: {
      label: '只读',
      component: 'Switch'
    }
    // initialValue: {
    //   label: '初始值',
    //   component: 'Select',
    //   props: {
    //     options: DefaultInitValue
    //   }
    // }
  }
}
