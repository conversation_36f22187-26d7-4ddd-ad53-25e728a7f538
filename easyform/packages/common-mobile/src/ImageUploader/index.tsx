/**
 * todo
 * 待支持自定义接口返回格式
 */
import React from 'react'
import {
  ImageUploader as AMImageUploader,
  ImageUploaderProps as AMImageUploaderProps,
  ImageUploadItem
} from 'antd-mobile'
import FormItemWrapper, { FormItemProps } from '../FormItemWrapper'

export type ImageUploaderProps = {
  uploadUrl?: string
  imageUrlPrefix?: string
  onChange?: (items: string[] | string) => void
  value?: string[] | string
  valueType?: 'string' | 'array'
  readOnly?: boolean
  disabled?: boolean
} & Partial<Omit<AMImageUploaderProps, 'value' | 'onChange'>> &
  FormItemProps

const ImageUploader = React.forwardRef<any, ImageUploaderProps>(
  ({ uploadUrl, onChange, value, valueType = 'array', imageUrlPrefix = '', readOnly, disabled, ...other }, ref) => {
    const fileList = React.useMemo<ImageUploadItem[]>(
      () =>
        value
          ? typeof value === 'string'
            ? value.split(',').map((v) => ({
                url: `${imageUrlPrefix}${v}`
              }))
            : value?.map((v) => ({
                url: `${imageUrlPrefix}${v}`
              })) || []
          : [],
      [imageUrlPrefix, value]
    )

    const handleChange = (items: ImageUploadItem[]) => {
      if (typeof onChange === 'function') {
        if (valueType === 'string') {
          onChange(items.map((item) => item.url.substring(imageUrlPrefix.length)).join(','))
        } else {
          onChange(items.map((item) => item.url.substring(imageUrlPrefix.length)))
        }
      }
    }
    const upload = React.useCallback(
      (file: File) => {
        return new Promise<ImageUploadItem>((resolve, reject) => {
          if (!uploadUrl) {
            reject(new Error('uploadUrl未定义'))
          }
          const formData = new FormData()
          formData.append('file', file)
          // eslint-disable-next-line @typescript-eslint/no-non-null-assertion
          fetch(uploadUrl!, {
            method: 'POST',
            body: formData
          })
            .then((res) => res.json())
            .then((data) => {
              // console.log('===upload response:', data)
              if (data.Code === 1) {
                const _url = `${imageUrlPrefix}${Object.values(data.Data)[0]}`
                resolve({ url: _url })
              } else {
                reject(new Error(data.Msg))
              }
            })
            .catch((error) => {
              reject(error)
            })
        })
      },
      [imageUrlPrefix, uploadUrl]
    )

    return (
      <AMImageUploader
        disableUpload={disabled}
        showUpload={!readOnly}
        upload={upload}
        multiple
        onChange={handleChange}
        value={fileList}
        {...other}
      />
    )
  }
)

export default React.forwardRef<HTMLDivElement, ImageUploaderProps>(({ children, ...props }, ref) => {
  return (
    <FormItemWrapper {...props}>
      <ImageUploader>{children}</ImageUploader>
    </FormItemWrapper>
  )
})
export { default as def } from './def'
