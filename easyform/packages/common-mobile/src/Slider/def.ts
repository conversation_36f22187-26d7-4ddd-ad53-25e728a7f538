export default {
  properties: {
    min: {
      label: '最小值',
      component: 'Input'
    },
    max: {
      label: '最大值',
      component: 'Input'
    }
    // unitOptions: {
    //   label: '单位选项',
    //   component: 'Object',
    //   def: [
    //     {
    //       title: '名称',
    //       dataKey: 'label',
    //       component: 'Input'
    //     },
    //     {
    //       title: '值',
    //       dataKey: 'value',
    //       component: 'Input'
    //     }
    //   ]
    // }
  }
}
