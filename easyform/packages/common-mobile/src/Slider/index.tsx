import React, { useMemo } from 'react'
import { Box } from '@mui/system'
import { RedoOutline } from 'antd-mobile-icons'
import { Col, Button, InputNumber, Row, Tooltip, Slider as ASlider, Select } from 'antd'
import FormItemWrapper, { FormItemProps } from '../FormItemWrapper'
export type SliderValue = {
  value: number | undefined
  unit?: any
}
interface SliderProps {
  onChange?: (value: SliderValue | undefined) => void
  value?: SliderValue | undefined
  min?: number
  max?: number
  unitOptions?: any //转换的单位
  unitRanges?: Array<{ min: number; max: number }>
  disableSliderUnit?: Array<any> //当单位为数组中的单位时禁用Slider
}
const { Option } = Select
const Slider = ({ value, onChange, min, max, unitOptions, unitRanges, disableSliderUnit = [] }: SliderProps) => {
  const handleSliderChange = (sliderValue: any) => {
    onChange &&
      onChange(
        value
          ? { ...value, value: sliderValue }
          : { value: sliderValue, unit: unitOptions && unitOptions[0] ? unitOptions[0] : undefined }
      )
  }
  const handleInputChange = (inputValue: any) => {
    onChange &&
      onChange(
        value
          ? { ...value, value: inputValue }
          : { value: inputValue, unit: unitOptions && unitOptions[0] ? unitOptions[0] : undefined }
      )
  }
  const selectAfter =
    unitOptions && unitOptions.length > 1 ? (
      <Select
        defaultValue={value?.unit || unitOptions[0]}
        onChange={(selectValue: any) => {
          onChange && onChange(value ? { ...value, unit: selectValue } : { value: undefined, unit: selectValue })
        }}
        style={{ width: 60 }}
      >
        {unitOptions.map((item: any, index: any) => {
          return (
            <Option key={index} value={item.value ? item.value : item}>
              {item.label ? item.label : item}
            </Option>
          )
        })}
      </Select>
    ) : unitOptions && unitOptions[0] ? (
      unitOptions[0]
    ) : (
      ''
    )
  const handleReset = () => {
    onChange && onChange(undefined)
  }
  //是否禁用滑块
  const disabledSlider = useMemo(() => {
    console.log(2120, 1)
    if (!(unitOptions && unitOptions.length)) return false
    if (value && value?.unit) {
      return disableSliderUnit.includes(value.unit)
    } else {
      return disableSliderUnit.includes(unitOptions && unitOptions[0])
    }
  }, [value, unitOptions, disableSliderUnit])
  const [_min, _max] = useMemo(() => {
    if (unitRanges && unitRanges.length && unitOptions && unitOptions.length) {
      //获取到当前的单位
      const curUnit = (value && value.unit) || (unitOptions && unitOptions[0])
      //通过单位下标获取到单位范围
      const range = unitRanges[unitOptions.indexOf(curUnit)]
      return [range.min, range.max]
    } else {
      return [undefined, undefined]
    }
  }, [unitOptions, unitRanges, value])
  return (
    <>
      <Box sx={{ display: 'flex' }}>
        <Tooltip title='重置'>
          <Button type='link' icon={<RedoOutline />} onClick={handleReset}></Button>
        </Tooltip>

        <ASlider
          style={{ flexGrow: 1 }}
          min={_min ?? 0}
          disabled={Boolean(disabledSlider)}
          max={_max ?? 100}
          tooltip={{
            formatter: (number: any) => {
              return `${number}${value?.unit || ''}`
            }
          }}
          onChange={handleSliderChange}
          value={!disabledSlider && typeof value?.value === 'number' ? value.value : undefined}
        />

        <InputNumber
          min={disabledSlider ? undefined : _min ?? 0}
          max={disabledSlider ? undefined : _max ?? 100}
          style={{ width: 130, paddingLeft: 8 }}
          value={value?.value}
          onChange={handleInputChange}
          addonAfter={selectAfter}
        />
      </Box>
    </>
  )
}

export default ({ isPC, min, max, unitOptions, disableSliderUnit, unitRanges, ...other }: any) => {
  return (
    <FormItemWrapper {...other}>
      <Slider
        min={min}
        max={max}
        unitOptions={unitOptions}
        unitRanges={unitRanges}
        disableSliderUnit={disableSliderUnit}
      ></Slider>
    </FormItemWrapper>
  )
}

export { default as def } from './def'
