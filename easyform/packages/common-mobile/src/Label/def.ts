export default {
  properties: {
    text: {
      label: '文本内容',
      component: 'TextArea'
    },
    textColor: {
      label: '文本颜色',
      component: 'ColorPicker',
      props: {
        value: undefined,
        valueType: 'toRgbString'
      }
    },
    textSize: {
      label: '文本大小',
      component: 'Slider',
      props: {
        unitOptions: ['px'],
        unitRanges: [{ min: 0, max: 50 }]
        //disableSliderUnit: ['px']
      }
    },
    textAlign: {
      label: '文本对齐方式',
      component: 'Select',
      props: {
        value: 'left',
        options: [
          {
            label: '居中',
            value: 'center'
          },
          {
            label: '左对齐',
            value: 'left'
          },
          {
            label: '右对齐',
            value: 'right'
          }
        ]
      }
    },
    copyable: {
      label: '文本可复制',
      component: 'Switch'
    },
    title: {
      label: '标题内容',
      component: 'Input'
    },
    titleColor: {
      label: '标题颜色',
      component: 'ColorPicker',
      props: {
        value: undefined,
        valueType: 'toRgbString'
      }
    },
    titleSize: {
      label: '标题大小',
      component: 'Slider',
      props: {
        unitOptions: ['px'],
        unitRanges: [{ min: 0, max: 50 }]
        //disableSliderUnit: ['px']
      }
    },
    titleAlign: {
      label: '标题对齐方式',
      component: 'Select',
      props: {
        value: 'left',
        options: [
          {
            label: '居中',
            value: 'center'
          },
          {
            label: '左对齐',
            value: 'left'
          },
          {
            label: '右对齐',
            value: 'right'
          }
        ]
      }
    }
  }
}
