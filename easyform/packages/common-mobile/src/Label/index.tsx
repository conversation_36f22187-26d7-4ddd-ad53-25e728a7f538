import React, { forwardRef } from 'react'
import { Typography } from 'antd'
import { Box } from '@mui/system'
const { Title, Paragraph, Text, Link } = Typography
export interface LabelProps {
  title?: string //是否展示标题
  text?: string
  copyable?: boolean
  titleAlign?: 'center' | 'left' | 'right'
  textSize?: any
  textAlign?: 'center' | 'left' | 'right'
  textColor?: any
  titleColor?: any
  titleSize?: any
}
export default forwardRef((prop: LabelProps, ref) => {
  const {
    title = '',
    titleAlign = 'left',
    titleColor,
    textColor,
    textAlign = 'left',
    titleSize,
    textSize,
    copyable = false,
    text = '',
    ...props
  } = prop
  return (
    <>
      <Box ref={ref} className={'baseComponent-label'}>
        <Typography>
          {title ? (
            <Title style={{ textAlign: titleAlign, color: titleColor, fontSize: titleSize?.value }} level={4}>
              {title}
            </Title>
          ) : (
            ''
          )}
        </Typography>
        {text ? (
          <Paragraph style={{ textAlign: textAlign, color: textColor, fontSize: textSize?.value }} copyable={copyable}>
            {text}
          </Paragraph>
        ) : (
          <Box sx={{ color: textColor ?? 'text.hint' }}>在此输入文本</Box>
        )}
      </Box>
    </>
  )
})

export { default as def } from './def'
