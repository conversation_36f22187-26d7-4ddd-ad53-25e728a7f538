import React from 'react'
import { Box, styled } from '@mui/system'
import { Transition } from 'react-transition-group'

const Wrapper = styled(Box)({})

interface ICollapseProps {
  in?: boolean
  children?: React.ReactNode
  timeout?: 'auto' | number | { appear?: number; enter?: number; exit?: number }
}

function getAutoHeightDuration(height?: number) {
  if (!height) {
    return 0
  }
  const constant = height / 36
  return Math.round((4 + 15 * constant ** 0.25 + constant / 5) * 10)
}

export default React.forwardRef<any, ICollapseProps>(({ in: inProp, children, timeout = 'auto' }, ref) => {
  const nodeRef = React.useRef<any>(null)
  const [height, setHeight] = React.useState(0)
  const [duration, setDuration] = React.useState(0)
  const timeoutId = React.useRef<any>(null)

  React.useEffect(() => {
    if (inProp) {
      const { scrollHeight } = nodeRef.current
      setDuration(getAutoHeightDuration(scrollHeight))
    }
  }, [inProp])

  React.useEffect(() => {
    if (inProp) {
      timeoutId.current = setTimeout(() => {
        const { scrollHeight } = nodeRef.current
        setHeight(scrollHeight)
      }, 0)
    } else {
      if (timeoutId.current) {
        clearTimeout(timeoutId.current)
        timeoutId.current = null
      }
    }
  }, [inProp])
  const defaultStyle = React.useMemo(
    () => ({
      transition: `height ${duration}ms ease-in-out`,
      height: 0,
      overflow: 'hidden'
    }),
    [duration]
  )
  const transitionStyles = React.useMemo<any>(
    () => ({
      entering: { height: 0 },
      entered: { height },
      exiting: { height },
      exited: { height: 0 }
    }),
    [height]
  )
  return (
    <Transition in={inProp} timeout={duration}>
      {(state) => (
        <Box style={{ ...defaultStyle, ...transitionStyles[state] }}>
          <Wrapper ref={nodeRef}>{children}</Wrapper>
        </Box>
      )}
    </Transition>
  )
})
