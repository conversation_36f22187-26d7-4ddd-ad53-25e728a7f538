import React from 'react'
import { Box } from '@mui/system'
export interface ITabPanelProps {
  children?: React.ReactNode
  showValue: number | string
  value: number | string
  height?: number | string
  width?: number | string
  position?: 'relative' | 'absolute' | 'fixed' | 'static'
  isPersistent?: boolean
}
export default function TabPanel(props: ITabPanelProps) {
  const {
    children,
    showValue,
    value,
    height = '100%',
    width = 'auto',
    position = 'relative',
    isPersistent = true,
    ...other
  } = props
  const [rendered, setRendered] = React.useState<boolean>(showValue === value)
  React.useEffect(() => {
    if (!rendered && showValue === value) {
      setRendered(true)
    }
  }, [rendered, showValue, value])
  return (
    <Box hidden={showValue !== value} style={{ height: height, position: position, width: width }} {...other}>
      {((isPersistent && rendered) || showValue === value) && children}
    </Box>
  )
}
