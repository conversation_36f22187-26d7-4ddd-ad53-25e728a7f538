/**
 * 内置变量：1.dataset 数据集; 2:state 内部状态, 类型  排序等; 3:filters 全局筛选条件
 *
 *{{js变量}}
 */
const define = {
  /**
   * 数据集定义,后期后端人员参与配置，前端人员也要能配
   */
  dataset: {
    运维指标: {
      api: '',
      // datasetId: 'local',
      handler: 'OP_INDICATOR', //处理器 默认:default,数据看板数据集处理,
      processData: {
        method: ''
      },
      params: {}
    },
    就业率数据: {},
    人数: {
      datasetId: '111', //数据集名称
      pagination: false, //默认false,为true,返回数据{total:1,data:[]};false:直接返回数据[]
      indicators: [
        {
          name: 'id',
          aggregate: 'count', //聚合
          alias: '' //别名
        }
      ], //指标定义
      dims: ['校区'], //维度定义
      where: {
        condition: 'AND',
        rules: [
          {
            condition: 'AND',
            rules: []
          },
          {
            name: 'sxn',
            symbol: '>=',
            value: '{{filters.xnsj}}'
          },
          {
            name: 'indentity',
            symbol: '=',
            value: '{{state.indentity.value}}' // title也可能使用，所以是对象{title:'本科生',value:'bks'}
          },
          {
            name: 'yx',
            symbol: '=',
            value: '{{filter.yx}}'
          },
          {
            condition: 'OR',
            rules: [
              {
                name: 'sxn',
                symbol: '>=',
                value: '{{filters.xnsj}}'
              },
              {
                name: 'indentity',
                symbol: '=',
                value: '{{state.indentity.value}}' // title也可能使用，所以是对象{title:'本科生',value:'bks'}
              }
            ]
          }
        ]
      }, //条件定义 按接口定义规则,
      page_no: 1,
      page_size: 10000
    }
  },
  /**
   * 渲染定义
   */
  render: {
    componentName: '@yiban/charts.Card',
    props: {
      //或者 字符串 就业率,为数组则渲染为可tab切换标题,state.title 获取当前标题值
      title: [
        {
          name: '在校',
          value: '在校'
        },
        {
          name: '在籍2',
          value: '在籍'
        }
      ],
      // card交互定义,例如本科生、研究生选择,存在state变量中,可配置额外trigger
      interactions: {
        shareAble: true, //支持分享
        //排列方式：默认按flex-end排列，flex-start|flex-end|space-between
        align: 'flex-end',
        //默认change会触发 state trigger，值记录到state里，并重新请求数据集
        actions: [
          {
            field: 'identity',
            component: 'ButtonGroup',
            options: [
              { label: '全部', value: '' },
              { label: '本科生', value: 'bks' },
              { label: '研究生', value: 'yjs' }
            ],
            props: {} //可不配
          }
        ]
      }
    },
    children: [
      {
        componentName: 'Box',
        props: {
          sx: ''
        },
        children: [
          {
            componentName: '@yiban/charts.Bar',
            props: {
              sortable: true, //是否支持排序
              data: '{{dataset[state.title]}}',
              summary: [
                {
                  title: '当前{{`${state.identity.title}`}}就业率为',
                  value: '{{dataset.摘要[state.identity.title]就业率为}}'
                }
              ]
            },
            children: [] //可支持嵌套,如有特殊场景
          },
          {
            componentName: '@yiban/charts.Bar',
            props: {
              sortable: true, //是否支持排序
              data: '{{dataset[state.title]}}',
              summary: [
                {
                  title: '当前{{`${state.identity.title}`}}就业率为',
                  value: '{{dataset.摘要[state.identity.title]就业率为}}'
                }
              ]
            },
            children: [] //可支持嵌套,如有特殊场景
          }
        ]
      }
    ]
  }
}
