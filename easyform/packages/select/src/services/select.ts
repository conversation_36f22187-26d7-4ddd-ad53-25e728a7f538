import { createRequest, IRequestOptions } from '@sui/runtime'

export const SCHE_PRE = `${(window as any).EDU_URL_PREFIX || ''}/api/v1`

const scheR = createRequest({
  prefix: SCHE_PRE
})

const scheRequest = (options: IRequestOptions) => {
  return new Promise((resolve, reject) => {
    scheR(options)
      .then((res) => {
        resolve(res)
      })
      .catch((err) => {
        reject(err)
      })
  })
}

//查询组织机构
export function getStudentList(category: any, data: any) {
  return scheRequest({
    url: `/selector/org/${category}`,
    method: 'GET',
    params: data
  })
}

//查询字典表数据
export function getGroupList(category: any) {
  return scheRequest({
    url: `/selector/dic/${category}`,
    method: 'GET'
  })
}

//查询成员数据
export function getMember(category: any, data: any) {
  return scheRequest({
    url: `/selector/member/${category}`,
    method: 'GET',
    params: data
  })
}
