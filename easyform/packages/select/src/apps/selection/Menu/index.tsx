import React, { useState, useEffect, useMemo, useRef } from 'react'
import { useRequest, useMemoizedFn } from 'ahooks'
import { Select, Tree, Menu, Form, Button, Checkbox, Divider, Input, Empty, Spin, Pagination, Modal } from 'antd'
import { getStudentList, getGroupList, getMember } from '@/services/select'
import type { DataNode } from 'antd/es/tree'
import { produce } from 'immer'
import { find, remove, difference, uniq } from 'lodash'
import { styled } from '@yiban/system'

const { Option } = Select
const CheckboxGroup = Checkbox.Group

const StyledSelect = styled(Select)<any>(({ theme }) => ({
  '& .ant-select-arrow': {
    display: 'none'
  }
}))

const StyledMenuDiv = styled('div')<any>(() => ({
  display: 'flex',
  flexWrap: 'wrap',
  marginTop: '16px',
  '.left': {
    width: '180px',
    marginRight: '16px',
    height: '480px',
    overflowY: 'auto',
    overflowX: 'hidden',
    border: '1px solid #d9d9d9',
    borderRadius: '4px',
    backgroundColor: '#fff'
  },
  '.spin': {
    flex: 1,
    height: '480px',
    width: '100%',
    border: '1px solid #d9d9d9',
    borderRadius: '4px',
    backgroundColor: '#fff',
    display: 'flex',
    justifyContent: 'center',
    alignItems: 'center'
  },
  '.right': {
    flex: 1,
    height: '480px',
    overflowY: 'auto',
    overflowX: 'hidden',
    border: '1px solid #d9d9d9',
    borderRadius: '4px',
    backgroundColor: '#fff',
    width: '100%',
    padding: '16px 32px',
    display: 'flex',
    flexDirection: 'column',
    '.selecttree': {
      display: 'flex',
      height: '88%',
      border: '1px solid #d9d9d9',
      borderRadius: '4px',
      '.tree': {
        height: 'auto',
        overflowY: 'auto',
        padding: '10px',
        flex: 1
      },
      '.subTree': {
        display: 'flex',
        flexDirection: 'column',
        // overflowY: 'auto',
        height: 'auto',
        padding: '10px',
        flex: 1,
        borderLeft: '1px solid #d9d9d9',
        '.user-list': {
          flex: 1,
          display: 'flex',
          flexDirection: 'column',
          overflowY: 'auto',
          '.user-list-header': {
            height: '43px',
            padding: '9px',
            borderBottom: '1px solid #d9d9d9',
            display: 'flex',
            justifyContent: 'space-between',
            alignItems: 'center',
            '.checkbox': {
              width: '50%'
            }
          },
          '.user-list-content': {
            margin: '6px',
            position: 'relative',
            overflow: 'hidden',
            display: 'flex',
            flexDirection: 'column',
            flex: 1,
            '.list': {
              flex: 1,
              overflowY: 'auto',
              overflowX: 'hidden',
              marginTop: '6px',
              marginBottom: '6px',
              '.checkbox-item': {
                display: 'block',
                marginBottom: '4px',
                position: 'relative',
                lineHeight: '28px',
                padding: '0 4px',
                width: '100%',
                '&:hover': {
                  backgroundColor: '#f4f4f4'
                },
                '&:last-child': {
                  marginBottom: '0'
                },
                '.ant-checkbox + span': {
                  paddingRight: '8px',
                  paddingLeft: '8px'
                },
                '.ant-checkbox-wrapper': {
                  lineHeight: '22px'
                },
                '.ant-checkbox-wrapper:after': {
                  position: 'absolute',
                  display: 'block',
                  left: '0',
                  right: '0',
                  top: '0',
                  bottom: '0',
                  width: '100%',
                  overflow: 'hidden'
                },
                '.checkbox-item-title': {
                  display: 'block'
                },

                '.item-title-suffix': {
                  fontStyle: 'normal',
                  fontSize: '12px',
                  color: '#666',
                  float: 'right'
                }
              }
            }
          }
        },
        ' .list-pagination': {
          display: 'flex',
          justifyContent: 'center',
          textAlign: 'center'
        }
      }
    },
    '.button': {
      display: 'flex',
      alignItems: 'center',
      flexDirection: 'column',
      width: '100%',
      paddingTop: '20px'
    }
  }
}))

function getDefaultValue(arr1: any[], arr2: any[]) {
  const mergedArray = []
  for (const item of arr1) {
    mergedArray.push({
      value: item.code,
      label: item.name
    })
  }
  for (const item of arr2) {
    mergedArray.push({
      value: item.code,
      label: item.name
    })
  }
  return mergedArray
}

//将object转换为带有type的数组
// function transformType(inputObject: any) {
//   const transformedData: { code: any; name: any; type: any; nodetype?: any }[] = []
//   function processNode(node: any, type: any) {
//     for (const key in node) {
//       if (Array.isArray(node[key])) {
//         node[key].forEach((item: any) => {
//           const transformedItem: any = {
//             code: item.code,
//             name: item.name,
//             type: type,
//             nodeType: key !== '0' ? key : ''
//           }
//           transformedData.push(transformedItem)
//         })
//       } else {
//         Array.isArray(node) &&
//           node?.map((n: any) => {
//             const transformedItem: any = {
//               code: n.code,
//               name: n.name,
//               type: type
//             }
//             transformedData.push(transformedItem)
//           })
//       }
//     }
//   }
//   for (const type in inputObject) {
//     processNode(inputObject[type], type)
//   }
//   return transformedData
// }
function transformType(inputObject: any) {
  const transformedData: { code: any; name: any; type: any; nodetype?: any }[] = []
  function processNode(node: any, type: any) {
    if (Array.isArray(node)) {
      node?.map((n: any) => {
        const transformedItem: any = {
          code: n.code,
          name: n.name,
          type: type
        }
        transformedData.push(transformedItem)
      })
    } else {
      for (const key in node) {
        if (Array.isArray(node[key])) {
          node[key].forEach((item: any) => {
            const transformedItem: any = {
              code: item.code,
              name: item.name,
              type: type,
              nodeType: key !== '0' ? key : ''
            }
            transformedData.push(transformedItem)
          })
        }
      }
    }
  }
  for (const Type in inputObject) {
    processNode(inputObject[Type], Type)
  }
  console.log('transformedData--', transformedData)
  return transformedData
}
//将selectedOrg和selectPersons转化为participantDetail的格式
const NodeTypeMap: Record<string, any> = {
  clazz: 'clazz'
}
const returnData = (data: any) => {
  const obj: Record<string, any> = {}
  data.selectedOrg?.forEach((o: any) => {
    switch (o?.type) {
      case 'student': {
        if (!obj.student) {
          obj.student = {}
        }
        const _key = NodeTypeMap[o.nodeType] || 'org'
        if (!obj.student[_key]) {
          obj.student[_key] = []
        }
        obj.student[_key].push({ code: o.code, name: o.name })
        // obj.student[_key].push({ code: o.key || o.code, name: o.name })
        break
      }
      case 'jzg': {
        if (!obj.jzg) {
          obj.jzg = {}
        }
        const _key = NodeTypeMap[o.nodeType] || 'org'
        if (!obj.jzg[_key]) {
          obj.jzg[_key] = []
        }
        // obj.jzg[_key].push({ code: o.key || o.code, name: o.name })
        obj.jzg[_key].push({ code: o.code, name: o.name })

        break
      }
      case 'group': {
        if (!obj.group) {
          obj.group = []
        }
        obj.group.push({ code: o.code, name: o.name })
        // obj.group.push({ code: o.key || o.code, name: o.title || o.name })
        break
      }
      case 'pos': {
        if (!obj.pos) {
          obj.pos = []
        }
        obj.pos.push({ code: o.code, name: o.name })
        // obj.pos.push({ code: o.key || o.code, name: o.title || o.name })
        break
      }
      case 'posset': {
        if (!obj.posset) {
          obj.posset = []
        }
        obj.posset.push({ code: o.key || o.code, name: o.title || o.name })
        // obj.posset.push({ code: o.key || o.code, name: o.title || o.name })
        break
      }
      case 'org': {
        if (!obj.org) {
          obj.org = []
        }
        obj.org.push({ code: o.code, name: o.name })
        break
      }
      case 'clazz': {
        if (!obj.clazz) {
          obj.clazz = []
        }
        obj.clazz.push({ code: o.code, name: o.name })
        break
      }
      case 'cat': {
        if (!obj.cat) {
          obj.cat = []
        }
        obj.cat.push({ code: o.code, name: o.name })
        break
      }
      case 'extra': {
        if (!obj.extra) {
          obj.extra = []
        }
        obj.extra.push({ code: o.code, name: o.name })
        break
      }
      case 'expr': {
        if (!obj.expr) {
          obj.expr = []
        }
        obj.expr.push({ code: o.code, name: o.name })
        break
      }
    }
  })
  data.selectPersons?.forEach((o: any) => {
    switch (o.type) {
      case 'student': {
        if (!obj.student) {
          obj.student = {}
        }
        const _key = 'user'
        if (!obj.student[_key]) {
          obj.student[_key] = []
        }
        obj.student[_key].push({ code: o.code || o.key, name: o.name })
        break
      }
      case 'jzg': {
        if (!obj.jzg) {
          obj.jzg = {}
        }
        const _key = 'user'
        if (!obj.jzg[_key]) {
          obj.jzg[_key] = []
        }
        obj.jzg[_key].push({ code: o.code || o.key, name: o.name })
        break
      }
      case 'user': {
        console.log(123321)
        if (!obj.user) {
          obj.user = []
        }
        obj.user.push({ code: o.code, name: o.name })
        break
      }
    }
  })
  return obj
}

//将participantDetail转换成slect需要的格式
const name = {
  student: '学生',
  jzg: '教职工',
  group: '组',
  pos: '岗位',
  posset: '职称',
  clazz: '班级',
  user: '用户',
  org: '机构'
}
const orgName = {
  org: '机构',
  clazz: '班级',
  user: '用户'
}
function transformDefaultValue(obj: any) {
  const result: any[] = []
  for (const key in obj) {
    const Key = key as keyof typeof name
    const orgArray = obj[key]?.org
    if (Key === 'student' || Key === 'jzg') {
      const value = obj[Key]
      const newData: any[] = []
      // 遍历原始对象
      for (const subkey in value) {
        const type = subkey as keyof typeof orgName // 获取类型名称
        const elements = value[subkey] // 获取类型的元素数组
        elements.map((e: any) => {
          newData.push({
            value: e.code,
            label: `${name[Key]}:[${orgName[type]}:${e.name}]`
          })
        })
      }
      result.push(...newData)
    } else {
      Array.isArray(obj[Key]) &&
        obj[Key]?.map((o: any) => {
          result.push({
            value: o.code,
            // label: `${name[Key]}:${o.name}`
            label: name[Key] ? `${name[Key]}:${o.name}` : `${o.name}`
          })
        })
    }
  }
  return result
}

//传入extend时做处理
function extendDetail(data: any, code: any) {
  let foundData: any
  let foundCategory: any
  const result: { [key: string]: any[] } = {}
  if (data) {
    const findData = null
    Object.keys(data.data).some((key) => {
      foundData = data.data[key].find((item: any) => item.code === code)
      // 如果找到匹配的数据，结束循环
      if (foundData) {
        foundCategory = key
        return true
      }
    })
    if (foundData) {
      result[foundCategory] = [foundData]
      console.log('result---', result)
      return result
    }
  }
}

export type CategoryItem = {
  key: string
  label: string
}

interface SelectMenuProps {
  MenuItem?: CategoryItem[] // 根据权限自定义类别菜单
  onChange?: (value: any) => void
  value?: any // 初始值
  isModal?: boolean
  modalWidth?: any
  placeholder?: string
  orgCode?: any //是否要根据院系查看
  extend?: {
    categories: CategoryItem[]
    data: Record<string, any>
  } // 附加自定义菜单
  disabled?: boolean
}

export default React.forwardRef<any, SelectMenuProps>(
  (
    {
      onChange,
      value,
      MenuItem,
      isModal = false,
      modalWidth = 800,
      placeholder = '请选择人员',
      orgCode,
      extend,
      disabled = false
    },
    ref
  ) => {
    const [form] = Form.useForm()
    const { Search } = Input
    const [menu, setMenu] = React.useState<any>(null)
    const [subMenu, setSubMenu] = React.useState<string | null>(null)
    const [selectedCard, setSelectesCard] = React.useState<string | null>(null)
    const initialStore = {
      options: [],
      begin: 1,
      length: 10,
      total: 0,
      keyword: '',
      current: 1
    }

    const [treelist, setTreelist] = useState<any>({
      expandedKeys: [],
      page: initialStore,
      selectPersons: transformType(value)?.filter((v: any) => v?.nodeType === 'user' || v?.type === 'user') || [],
      selectedOrg: transformType(value)?.filter((v: any) => v?.nodeType !== 'user' && v?.type !== 'user') || [],
      selectedValue: transformDefaultValue(value),
      // selectPersons:
      //   (Array.isArray(value?.selectValue) && value?.selectValue?.filter((item: any) => item.category === 'person')) ||
      //   [],
      // selectedOrg:
      //   (Array.isArray(value?.selectValue) && value?.selectValue?.filter((item: any) => item.category === 'org')) || [],
      // selectedValue: value?.selectValue || []
      participantDetail: value || {}
    })
    const [pageNo, setPageNo] = useState(1)
    const [modalOpen, setModalOpen] = useState<boolean>(false)
    // 记录岗位父级id
    const [posList, setPosList] = useState<any>([])
    useEffect(() => {
      setTreelist({
        ...treelist,
        expandedKeys: [],
        page: initialStore,
        selectPersons: transformType(value)?.filter((v: any) => v?.nodeType === 'user' || v?.type === 'user') || [],
        selectedOrg: transformType(value)?.filter((v: any) => v?.nodeType !== 'user' && v?.type !== 'user') || [],
        selectedValue: transformDefaultValue(value),
        participantDetail: value || {}
      })
      // eslint-disable-next-line react-hooks/exhaustive-deps
    }, [value])
    const items = [
      {
        label: '学生',
        key: '学生'
      },
      {
        label: '教职工',
        key: '教职工'
      },
      {
        label: '组',
        key: '组'
      },
      {
        label: '岗位',
        key: '岗位'
      },
      {
        label: '职称',
        key: '职称'
      }
    ]

    const groupClick = [
      {
        key: '组',
        value: 'group',
        type: 'group'
      },
      {
        key: '岗位',
        value: 'position',
        type: 'pos'
      },
      {
        key: '职称',
        value: 'positionSet',
        type: 'posset'
      }
    ]

    const updateTreeData = (list: DataNode[], key: React.Key, children: DataNode[]): DataNode[] =>
      list.map((node) => {
        if (node.key === key) {
          return {
            ...node,
            children
          }
        }
        if (node.children) {
          return {
            ...node,
            children: updateTreeData(node.children, key, children)
          }
        }
        return node
      })
    //点击 学生/教职工 获取
    const { run: runGetStudentList, loading: studentListLoading } = useRequest(getStudentList, {
      manual: true,
      onSuccess: (res: any, params: any) => {
        const data: React.SetStateAction<any[]> = []
        res.data.items.map((item: any) => {
          data.push({
            key: item.code,
            code: item.code,
            title: `${item.name}(${item.childNum})`,
            name: item.name,
            isLeaf: item.hasChild ? false : true,
            children: [],
            type: params?.[0] === 'xs_orgTree' ? 'student' : params?.[0] === 'orgTree' ? 'jzg' : '',
            // nodeType: params?.[0] === 'xs_orgTree' ? '学院' : params?.[0] === 'orgTree' ? '机构' : ''
            nodeType: item.nodeType === 'class' ? 'clazz' : 'org',
            hasChild: item.hasChild
          })
        })
        setTreelist({ ...treelist, treedata: data, category: params?.[0] })
      }
    })
    const { run: runGetClassList, loading: classListLoading } = useRequest(getStudentList, {
      manual: true,
      onSuccess: (res: any, params: any) => {
        const data: React.SetStateAction<any[]> = []
        if (params[1].orgCode) {
          res.data.items.map((item: any) => {
            data.push({
              key: item.code,
              code: item.code,
              title: `${item.name}(${item.childNum})`,
              name: item.name,
              isLeaf: item.hasChild ? false : true,
              children: [],
              type: params?.[0] === 'xs_orgTree' ? 'student' : params?.[0] === 'orgTree' ? 'jzg' : '',
              nodeType: 'clazz',
              hasChild: item.hasChild
            })
          })
          const children = updateTreeData(treelist.treedata, params[1].orgCode, data)
          setTreelist({ ...treelist, treedata: children, category: params?.[0] })
        }
        if (params[1].parent) {
          const isPos = params[0] === 'position' && params[1].category === 'position'
          if (params[0] === 'position' && params[1].category === 'org') {
            setPosList(res.data.items)
          }
          console.log('posList====', posList)
          const fatherName = isPos && posList.find((p: any) => p.code === params[1].parent)?.name
          console.log('fatherName==', fatherName)
          res.data.items.map((item: any) => {
            data.push({
              key: item.code,
              code: item.code,
              title: isPos
                ? `${item.name}(${fatherName})`
                : params[0] === 'position'
                ? item.name
                : `${item.name}(${item.childNum})`,
              name: isPos ? `${item.name}(${fatherName})` : item.name,
              isLeaf: item.hasChild || (params[0] === 'position' && params[1].category === 'org') ? false : true,
              children: [],
              type:
                params?.[0] === 'xs_orgTree'
                  ? 'student'
                  : params?.[0] === 'orgTree'
                  ? 'jzg'
                  : params[0] === 'position'
                  ? 'pos'
                  : '',
              nodeType: 'org',
              hasChild: item.hasChild,
              checkable: params[0] === 'position' && params[1].category === 'org' ? false : true
            })
          })
          const children = updateTreeData(treelist.treedata, params[1].parent, data)
          setTreelist({ ...treelist, treedata: children, category: params?.[0] })
        }
        //选择'岗位'时
        if (params[0] === 'position' && params[1].category === 'org' && !params[1].parent) {
          res.data.items.map((item: any) => {
            data.push({
              key: item.code,
              code: item.code,
              title: item.name,
              name: item.name,
              isLeaf: item.hasChild ? false : true,
              children: [],
              type: 'pos',
              checkable: false,
              hasChild: item.hasChild
            })
          })
          setTreelist({ ...treelist, treedata: data, category: 'org' })
        }
      }
    })

    console.log('treelist==', treelist)

    //点击 组/岗位/职称 获取
    const { run: runGetGroupList, loading: groupListLoading } = useRequest(getGroupList, {
      manual: true,
      onSuccess: (res: any, params: any) => {
        console.log('params==', params)
        const data: React.SetStateAction<any[]> = []
        res.data.items.map((item: any) => {
          data.push({
            key: item.code,
            code: item.code,
            name: item.name,
            title: item.name,
            type: groupClick.find((item) => item.value === params?.[0])?.type
          })
        })
        setTreelist({ ...treelist, treedata: data, category: params?.[0] })
      }
    })

    //查询成员数据
    const { run: runGetMember } = useRequest(getMember, {
      manual: true,
      onSuccess: (res: any, params: any) => {
        const newData = res.data.items.map((item: any) => ({
          ...item,
          type: params?.[0] === 'xs_ss' ? 'student' : params?.[0] === 'jzg_ss' ? 'jzg' : '',
          nodeType: 'user'
        }))
        setTreelist((prevTreelist: any) => ({
          ...prevTreelist,
          page: {
            ...prevTreelist.page,
            options: newData,
            total: res.data.totalNum,
            isMember: true
          }
        }))
      }
    })
    // useEffect(() => {
    //   setTreelist({ ...treelist, expandedKeys: [], page: initialStore })
    //   // eslint-disable-next-line react-hooks/exhaustive-deps
    // }, [menu])
    console.log('menu===', menu)
    useEffect(() => {
      setSearchValue('')
      if (menu === '学生') {
        setTreelist({ ...treelist, expandedKeys: [], page: initialStore })
        orgCode
          ? runGetStudentList('xs_orgTree', { category: 'xs_ss', orgCode: orgCode })
          : runGetStudentList('xs_orgTree', { category: 'xs_ss' })
        orgCode ? runGetMember('xs_ss', { orgCode: orgCode }) : runGetMember('xs_ss', {})
      } else if (menu === '教职工') {
        if (orgCode) {
          setTreelist({ ...treelist, expandedKeys: [], page: initialStore, treedata: [] })
          runGetMember('jzg_ss', {
            orgCode: orgCode,
            pageNo: initialStore.begin,
            pageSize: initialStore.length
          })
          // runGetMember('jzg_ss', {})
        } else {
          setTreelist({ ...treelist, expandedKeys: [], page: initialStore })
          runGetStudentList('orgTree', { category: 'xs_ss' })
          runGetMember('jzg_ss', {})
        }
      } else if (menu === '岗位') {
        setTreelist({ ...treelist, expandedKeys: [], page: initialStore })
        runGetClassList('position', { category: 'org' })
      } else if (menu === '组' || menu === '职称') {
        setTreelist({ ...treelist, expandedKeys: [], page: initialStore })
        const category = groupClick.find((item) => item.key === menu)?.value
        runGetGroupList(category)
      } else if (extend) {
        console.log('extend++', extend)
        console.log('menu+++', menu)
        setTreelist({
          ...treelist,
          expandedKeys: [],
          page: initialStore,
          selectedKeys: [],
          treedata: extend.data?.[menu]?.map((d: any) => ({
            key: d.code,
            title: d.name,
            code: d.code,
            name: d.name,
            type: menu
          }))
        })
      } else {
        setTreelist({ ...treelist, treedata: [], page: initialStore })
      }
      // eslint-disable-next-line react-hooks/exhaustive-deps
    }, [menu])

    // useEffect(() => {
    //   typeof onChange === 'function' && onChange(treelist?.participantDetail)
    //   // eslint-disable-next-line react-hooks/exhaustive-deps
    // }, [treelist?.participantDetai])

    //点击一级菜单
    const handleSelectMenu = (item: any) => {
      console.log('item===', item)
      setMenu(item?.key)
      setSelectedKey(null)
    }

    const [selectedKey, setSelectedKey] = React.useState<string | null>(null)
    //点击班级时传claCode
    const [isClazz, setIsClazz] = React.useState<boolean>(false)
    const handleClickTree = (selectedKeys: any, info: any) => {
      // console.log('info==', info)
      // console.log('selectedKeys==', selectedKeys)
      setSelectedKey(selectedKeys[0])
      setPageNo(1)
      setTreelist({ ...treelist, selectedKeys: selectedKeys })
      setSearchValue('')
      if (menu === '学生') {
        if (info.node.nodeType === 'clazz') {
          setIsClazz(true)
          runGetMember('xs_ss', { claCode: selectedKeys[0], pageNo: initialStore.begin, pageSize: initialStore.length })
        } else {
          setIsClazz(false)
          runGetMember('xs_ss', { orgCode: selectedKeys[0], pageNo: initialStore.begin, pageSize: initialStore.length })
        }
      } else if (menu === '教职工') {
        setIsClazz(false)
        runGetMember('jzg_ss', { orgCode: selectedKeys[0], pageNo: initialStore.begin, pageSize: initialStore.length })
      }
    }
    const handleCheckTree = (checkedKeys: any, info: any) => {
      console.log('info=====', info)
      console.log(2222222222)
      const checked = info?.checked
      const org = find(info?.checkedNodes, (o: any) => {
        console.log('ooooo', o)
        return o.code === info?.node?.key
      })
      console.log('org---', org)
      if (checked) {
        const orgs = [...treelist.selectedOrg]?.concat([org])
        console.log('orgs===', orgs)
        const detail = returnData({ ...treelist, selectedOrg: uniq(orgs) })
        setTreelist({
          ...treelist,
          selectedOrg: uniq(orgs),
          participantDetail: detail
        })
      } else {
        const newOrg = treelist.selectedOrg.filter((o: any) => o.code !== info.node.key)
        console.log('info.node--', info.node)
        console.log('newOrg--', newOrg)
        const detail = returnData({ ...treelist, selectedOrg: newOrg })
        setTreelist({
          ...treelist,
          selectedOrg: newOrg,
          participantDetail: detail
        })
        // const selectedOrg = [...treelist.selectedOrg]
        // const unSelects = remove(selectedOrg, (p: any) => {
        // return p.key === info?.node?.key
        // })
        // setTreelist({ ...treelist, selectedOrg: selectedOrg })
      }
    }

    //选择框

    const [indeterminate, setIndeterminate] = React.useState(false)
    const [checkAll, setCheckAll] = React.useState<boolean>(false)

    //全选
    const onCheckAllChange = (e: any) => {
      setIndeterminate(false)
      setCheckAll(e.target.checked)
      let selectPerson = []
      if (!e.target.checked) {
        selectPerson = difference(treelist?.selectPersons, treelist?.page?.options)
        console.log('selectPerson+', selectPerson)
      } else {
        selectPerson = uniq([...treelist.selectPersons].concat(treelist?.page?.options))
        console.log('selectPerson-', selectPerson)
      }
      setTreelist({ ...treelist, selectPersons: uniq(selectPerson) })
    }

    //展开
    const onExpand = (expandedKeys: React.Key[], info: any) => {
      console.log('info====', info)
      const positions = info.node.pos.split('-')
      // 计算节点级别（层级）
      const level = positions.length - 1
      setTreelist({ ...treelist, expandedKeys: expandedKeys })
      if (level === 1) {
        if (menu === '学生' || menu === '教职工') {
          const data = treelist?.treedata?.find((item: any) => item.key === info.node.key)
          if (info.expanded && data.children.length === 0) {
            const orgTreeKey = menu === '学生' ? 'xs_orgTree' : 'orgTree'
            const queryParams =
              menu === '学生' ? { category: 'xs_ss', orgCode: info.node.key } : { parent: info.node.key }
            info.node.hasChild && runGetClassList(orgTreeKey, queryParams)
          }
        } else if (menu === '岗位') {
          runGetClassList('position', { category: 'org', parent: info.node.key })
        }
      } else {
        if (menu === '学生' || (menu === '教职工' && info.expanded)) {
          runGetClassList('orgTree', { parent: info.node.key })
        } else if (menu === '岗位') {
          if (info.node.hasChild === false && info.node.isLeaf === false && info.expanded) {
            runGetClassList('position', { category: 'position', parent: info.node.key })
          } else {
            runGetClassList('position', { category: 'org', parent: info.node.key })
          }
        }
      }
      // if (level === 2) {
      //   if (menu === '学生' || menu === '教职工') {
      //     info.expanded && runGetClassList('orgTree', { parent: info.node.key })
      //   } else if (menu === '岗位') {
      //     info.expanded && runGetClassList('position', { category: 'org', parent: info.node.key })
      //   }
      // }
      // if (level === 3) {
      //   if (info.node.hasChild === false && info.node.isLeaf === false) {
      //     console.log('ttttt')
      //     info.expanded && runGetClassList('position', { category: 'position', parent: info.node.key })
      //   }
      // }
    }
    // const onExpand = (expandedKeys: React.Key[], info: any) => {
    //   console.log('info====', info)
    //   const positions = info.node.pos.split('-')
    //   // 计算节点级别（层级）
    //   const level = positions.length - 1
    //   setTreelist({ ...treelist, expandedKeys: expandedKeys, level: level })
    //   if (level === 1) {
    //     const data = treelist?.treedata?.find((item: any) => item.key === info.node.key)
    //     if (info.expanded && data.children.length === 0) {
    //       const orgTreeKey = menu === '学生' ? 'xs_orgTree' : 'orgTree'
    //       const queryParams =
    //         menu === '学生' ? { category: 'xs_ss', orgCode: info.node.key } : { parent: info.node.key }
    //       runGetClassList(orgTreeKey, queryParams)
    //     }
    //   }
    //   if (level === 2 && menu === '教职工') {
    //     runGetClassList('orgTree', { parent: info.node.key })
    //   }
    // }

    //右侧选择器
    //搜索
    const [searchValue, setSearchValue] = useState<any>()
    const onSearch = (value: string) => {
      setSearchValue(value)
      console.log('0000', value)
      if (menu === '学生') {
        console.log('selectedKey==', selectedKey)
        if (isClazz) {
          runGetMember('xs_ss', {
            // claCode: selectedKey || '',
            claCode: selectedKey ? selectedKey : orgCode || '',
            keyword: value,
            pageNo: initialStore.begin,
            pageSize: initialStore.length
          })
        } else {
          runGetMember('xs_ss', {
            // orgCode: selectedKey || '',
            orgCode: selectedKey ? selectedKey : orgCode || '',
            keyword: value,
            pageNo: initialStore.begin,
            pageSize: initialStore.length
          })
        }
      } else if (menu === '教职工') {
        runGetMember('jzg_ss', {
          orgCode: orgCode ? orgCode : selectedKey || '',
          keyword: value,
          pageNo: initialStore.begin,
          pageSize: initialStore.length
        })
      }
    }
    console.log('searchValue==', searchValue)
    //翻页
    const onPageChange = (pageNo: any, pageSize: any) => {
      setPageNo(pageNo)
      if (menu === '学生') {
        if (isClazz) {
          runGetMember('xs_ss', {
            // claCode: selectedKey || '',
            claCode: selectedKey ? selectedKey : orgCode || '',
            keyword: searchValue ? searchValue : '',
            pageNo: pageNo,
            pageSize: pageSize
          })
        } else {
          runGetMember('xs_ss', {
            // orgCode: selectedKey || '',
            orgCode: selectedKey ? selectedKey : orgCode || '',
            keyword: searchValue ? searchValue : '',
            pageNo: pageNo,
            pageSize: pageSize
          })
        }
      } else if (menu === '教职工') {
        runGetMember('jzg_ss', {
          orgCode: orgCode ? orgCode : selectedKey || '',
          keyword: searchValue ? searchValue : '',
          pageNo: pageNo,
          pageSize: pageSize
        })
      }
    }
    //选择框
    const handleCheckedChange = (e: any) => {
      const checked = e?.target?.checked
      const person = find(treelist?.page?.options, (o) => {
        return o.code === e?.target?.value
      })
      if (checked) {
        const persons = [...treelist.selectPersons]?.concat([person])
        // const newTreelist = { ...treelist, selectPersons: persons }
        // setTreelist({ ...newTreelist, participantDetail: returnData(newTreelist) })
        // setIndeterminate(persons && persons.length < 10)
        const detail = returnData({ ...treelist, selectPersons: uniq(persons) })
        setTreelist({ ...treelist, selectPersons: uniq(persons), participantDetail: detail })
      } else {
        // const selectPersons = [...treelist.selectPersons]
        // const newTreelist = { ...treelist, selectPersons: selectPersons }
        // setTreelist({ ...newTreelist, participantDetail: returnData(newTreelist) })
        const newPerson = treelist.selectPersons.filter((o: any) => o.code !== e?.target?.value)
        const detail = returnData({ ...treelist, selectPersons: newPerson })
        setTreelist({
          ...treelist,
          selectPersons: newPerson,
          participantDetail: detail
        })
      }
    }

    //已选择的人员
    const handleSelectClick = () => {
      setCheckAll(false)
      setMenu(null)
      const mergedValue = treelist?.selectPersons
        .map((person: any) => ({
          value: person.code,
          // label: `${person.type}:${person.name}[${person.orgName[0]}]`,
          label: person.name,
          category: 'person',
          ...person
        }))
        .concat(
          treelist?.selectedOrg.map((org: any) => ({
            value: org.key,
            // label: org.nodeType ? `${org.type}[${org.nodeType}:${org.title}]` : `${org.type}[${org.title}]`,
            label: org.name || org.title,
            category: 'org',
            ...org
          }))
        )
      const defaultValue = transformDefaultValue(treelist.participantDetail)
      setTreelist({ ...treelist, selectedValue: defaultValue })
      const updatedMergedValue = mergedValue.map((item: any) => {
        // 使用正则表达式去除括号及其内容
        item.label = item.label.replace(/\([^)]*\)/, '').trim()
        return item
      })
      // typeof onChange === 'function' && onChange(returnData(treelist))
      console.log('participantDetail==', returnData(treelist))
      setModalOpen(false)
    }

    //删除已选择的人员
    //单个
    const deleteSelect = (value: any) => {
      const newSlectedOrg = treelist.selectedOrg.filter((item: any) => item.code !== value)
      const newSelectPersons = treelist.selectPersons.filter((item: any) => item.code !== value)
      const newTreeList = {
        ...treelist,
        selectedOrg: newSlectedOrg,
        selectPersons: newSelectPersons,
        selectedValue: treelist?.selectedValue.filter((item: any) => item.value !== value)
      }
      setTreelist({ ...newTreeList, participantDetail: returnData(newTreeList) })
      // setTreelist({
      //   ...newTreeList,
      //   participantDetail: extend ? { ...returnData(newTreeList), ...extend?.data } : returnData(newTreeList)
      // })
      typeof onChange === 'function' && onChange(returnData(newTreeList))
    }
    //全部
    const clearSelect = () => {
      setTreelist({ ...treelist, selectedOrg: [], selectPersons: [], selectedValue: [], participantDetail: {} })
      typeof onChange === 'function' && onChange({})
    }

    console.log('treelist==', treelist)
    // console.log('value+++', value)
    // console.log('orgCode===', orgCode)

    return (
      <div>
        <div>
          <StyledSelect
            mode='tags'
            style={{ width: '100%' }}
            placeholder={placeholder}
            open={false}
            allowClear={true}
            showSearch
            value={treelist.selectedValue}
            onClear={clearSelect}
            onDeselect={deleteSelect}
            onClick={() => !disabled && setModalOpen(true)}
            disabled={disabled}
          />
        </div>
        {isModal ? (
          <Modal open={modalOpen} onCancel={() => setModalOpen(false)} footer={null} width={modalWidth}>
            <StyledMenuDiv>
              <Menu
                className='left'
                style={{
                  width: 160
                }}
                mode='vertical'
                items={
                  extend && MenuItem
                    ? MenuItem.concat(extend.categories)
                    : extend
                    ? items.concat(extend.categories)
                    : MenuItem || items
                }
                // items={MenuItem || items}
                selectedKeys={menu}
                onSelect={handleSelectMenu}
              />
              {(menu && treelist?.treedata) || treelist.page.options?.length > 0 ? (
                studentListLoading || groupListLoading ? (
                  <Spin className='spin' />
                ) : (
                  <div className='right'>
                    <div className='selecttree'>
                      {treelist?.treedata?.length > 0 && (
                        <div className='tree'>
                          <Spin spinning={classListLoading}>
                            <Tree
                              checkable
                              checkStrictly
                              defaultExpandParent={false}
                              style={{ minWidth: 200 }}
                              treeData={treelist?.treedata}
                              onSelect={handleClickTree}
                              onCheck={handleCheckTree}
                              onExpand={onExpand}
                              expandedKeys={treelist?.expandedKeys}
                              checkedKeys={treelist?.selectedOrg?.map((o: any) => {
                                return o.code
                              })}
                            />
                          </Spin>
                        </div>
                      )}
                      {treelist.page.isMember && (
                        <div className='subTree'>
                          <div className='list-search'>
                            <Search
                              // placeholder='请输入关键词'
                              placeholder={searchValue ? searchValue : '请输入关键词'}
                              allowClear
                              onSearch={(value) => {
                                onSearch(value)
                              }}
                              style={{ width: '100%' }}
                            />
                          </div>
                          <div className='user-list'>
                            {/* <div className='user-list-header'>
                        <div className='checkbox'>
                          <Checkbox indeterminate={indeterminate} onChange={onCheckAllChange} checked={checkAll}>
                            全选
                          </Checkbox>
                        </div>
                      </div> */}
                            <div className='user-list-content'>
                              <div className='list'>
                                {(treelist.page.options?.length === 0 || !treelist.page.options) && (
                                  <Empty description={'暂无数据'} />
                                )}
                                {treelist.page.options?.length > 0 && (
                                  <CheckboxGroup
                                    value={treelist?.selectPersons?.map((p: any) => {
                                      return p.code
                                    })}
                                  >
                                    {treelist.page.options?.map((o: any) => {
                                      return (
                                        <div className='checkbox-item' key={o.code}>
                                          <Checkbox value={o.code} onChange={handleCheckedChange}>
                                            <div className='checkbox-item-title'>
                                              {o.name}
                                              <i className='item-title-suffix'>({o.code})</i>
                                            </div>
                                          </Checkbox>
                                        </div>
                                      )
                                    })}
                                  </CheckboxGroup>
                                )}
                              </div>
                            </div>
                          </div>
                          <div className='list-pagination'>
                            {treelist.page.options && (
                              <Pagination
                                simple
                                defaultCurrent={treelist?.page?.begin || initialStore.begin}
                                defaultPageSize={treelist?.page?.length || initialStore.length}
                                total={treelist?.page?.total || 0}
                                onChange={onPageChange}
                                current={pageNo}
                              />
                            )}
                          </div>
                        </div>
                      )}
                    </div>
                    <div className='button'>
                      <Button
                        block
                        type='primary'
                        onClick={() => {
                          handleSelectClick()
                          typeof onChange === 'function' && onChange(treelist.participantDetail)
                        }}
                      >
                        确认
                      </Button>
                    </div>
                  </div>
                )
              ) : (
                <div className='right'>
                  <Empty description={'暂无数据'} />
                </div>
              )}
            </StyledMenuDiv>
          </Modal>
        ) : (
          <StyledMenuDiv>
            <Menu
              className='left'
              style={{
                width: 160
              }}
              mode='vertical'
              items={
                extend && MenuItem
                  ? MenuItem.concat(extend.categories)
                  : extend
                  ? items.concat(extend.categories)
                  : MenuItem || items
              }
              // items={MenuItem || items}
              selectedKeys={menu}
              onSelect={handleSelectMenu}
            />
            {(menu && treelist?.treedata) || treelist.page.options?.length > 0 ? (
              studentListLoading || groupListLoading ? (
                <Spin className='spin' />
              ) : (
                <div className='right'>
                  <div className='selecttree'>
                    {treelist?.treedata?.length > 0 && (
                      <div className='tree'>
                        <Spin spinning={classListLoading}>
                          <Tree
                            checkable
                            checkStrictly
                            defaultExpandParent={false}
                            style={{ minWidth: 200 }}
                            treeData={treelist?.treedata}
                            onSelect={handleClickTree}
                            onCheck={handleCheckTree}
                            onExpand={onExpand}
                            expandedKeys={treelist?.expandedKeys}
                            checkedKeys={treelist?.selectedOrg?.map((o: any) => {
                              return o.code
                            })}
                          />
                        </Spin>
                      </div>
                    )}
                    {treelist.page.isMember && (
                      <div className='subTree'>
                        <div className='list-search'>
                          <Search
                            // placeholder='请输入关键词'
                            placeholder={searchValue ? searchValue : '请输入关键词'}
                            allowClear
                            onSearch={(value) => {
                              onSearch(value)
                            }}
                            style={{ width: '100%' }}
                          />
                        </div>
                        <div className='user-list'>
                          {/* <div className='user-list-header'>
                        <div className='checkbox'>
                          <Checkbox indeterminate={indeterminate} onChange={onCheckAllChange} checked={checkAll}>
                            全选
                          </Checkbox>
                        </div>
                      </div> */}
                          <div className='user-list-content'>
                            <div className='list'>
                              {(treelist.page.options?.length === 0 || !treelist.page.options) && (
                                <Empty description={'暂无数据'} />
                              )}
                              {treelist.page.options?.length > 0 && (
                                <CheckboxGroup
                                  value={treelist?.selectPersons?.map((p: any) => {
                                    return p.code
                                  })}
                                >
                                  {treelist.page.options?.map((o: any) => {
                                    return (
                                      <div className='checkbox-item' key={o.code}>
                                        <Checkbox value={o.code} onChange={handleCheckedChange}>
                                          <div className='checkbox-item-title'>
                                            {o.name}
                                            <i className='item-title-suffix'>({o.code})</i>
                                          </div>
                                        </Checkbox>
                                      </div>
                                    )
                                  })}
                                </CheckboxGroup>
                              )}
                            </div>
                          </div>
                        </div>
                        <div className='list-pagination'>
                          {treelist.page.options && (
                            <Pagination
                              simple
                              defaultCurrent={treelist?.page?.begin || initialStore.begin}
                              defaultPageSize={treelist?.page?.length || initialStore.length}
                              total={treelist?.page?.total || 0}
                              onChange={onPageChange}
                              current={pageNo}
                            />
                          )}
                        </div>
                      </div>
                    )}
                  </div>
                  <div className='button'>
                    <Button
                      block
                      type='primary'
                      onClick={() => {
                        handleSelectClick()
                        typeof onChange === 'function' && onChange(treelist.participantDetail)
                      }}
                    >
                      确认
                    </Button>
                  </div>
                </div>
              )
            ) : (
              <div className='right'>
                <Empty description={'暂无数据'} />
              </div>
            )}
          </StyledMenuDiv>
        )}
      </div>
    )
  }
)
