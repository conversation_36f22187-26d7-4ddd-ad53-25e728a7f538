import '@/index.css'
import React from 'react'
import { Box, styled } from '@mui/system'
import { Typography } from '@/components'
import img from './bg1.png'
interface IDefaultTemplate {
  title?: string
  bgImg?: string
  children?: React.ReactNode
  desc?: string
  imgs?: string[]
}
export default React.forwardRef<any, IDefaultTemplate>(
  ({ title, children, desc, imgs, bgImg =img }, ref) => {
    console.log(bgImg)
    return (
      <Box
        className='h-screen'
        ref={ref}
        sx={{
          p: 2,
          backgroundImage: `url(${bgImg})`,
          backgroundSize: 'cover'
        }}
      >
        <Typography sx={{ mb: 1.5 }} variant='h1' align='center' color='primary'>
          {title || ''}
        </Typography>
        {desc && (
          <Typography sx={{ mb: 1.5, whiteSpace: 'pre-wrap', lineHeight: 1.8 }} variant='subtitle1'>
            {desc}
          </Typography>
        )}
        <Box sx={{ bgcolor: '#FFF' }}>{children}</Box>
      </Box>
    )
  }
)


