import '@/index.css'
import React from 'react'
import { Box, styled } from '@mui/system'
import { Typography } from '@/components'
import { Avatar, Form, Button } from 'antd-mobile'
import { title } from 'process'
import  bgImage from './header-bar-fdy.png'
interface IDefaultTemplate {
  title?: string
  bgImg?: string
  children?: React.ReactNode
}
const Root = styled('div')({
  display: 'flex',
  flexDirection: 'column',
  margin: '0 auto',
  height: '100%'
})
const Header = styled('div')({
  backgroundSize: 'cover',
  padding: '16px'
})

const Footer = styled('div')({
  boxShadow: '5px 5px 8px 5px #dddfde',
  backgroundColor: '#fff',
  
})
const Btn = styled(Button)({
  '--background-color': ' #6F6DEA',
  '--border-radius': '20px',
  padding:'2px 160px'
})
export default React.forwardRef<any, IDefaultTemplate>(({ children, bgImg =bgImage,title='表单模板' }, ref) => {
  console.log(bgImg)
  return (
    <Root className='h-screen' ref={ref}>
      <Header sx={{ backgroundImage: `url(${bgImg})`, backgroundSize: 'cover' }}>
        <Box sx={{ p: 1, }} className='flex justify-center'>
          <Typography variant='h6'>{title}</Typography>
          
        </Box>
      </Header>

      <Box sx={{ bgcolor: '#FFF', p: 2, flex: 1, overflowY: 'scroll' }}>
        <Box>{children}</Box>
      </Box>
      <Footer className='flex justify-center items-center'>
        <Box>
          <Form
            layout='horizontal'
            footer={
              <Btn block type='submit' size='large'>
                <Typography color='#fff'>提交</Typography>
              </Btn>
            }
          ></Form>
        </Box>
      </Footer>
    </Root>
  )
})
