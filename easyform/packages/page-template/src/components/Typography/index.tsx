import React from 'react'
import { styled } from '@mui/system'

interface ITypography {
  children?: React.ReactNode
  align?: 'center' | 'inherit' | 'justify' | 'left' | 'right'
  component?: React.ElementType
  noWrap?: boolean
  variant?: 'body1' | 'body2' | 'subtitle1' | 'subtitle2' | 'h1' | 'h6'
  sx?: any
  className?: string
  color?: 'primary' | 'textPrimary' | 'secondary' | 'textSecondary' | 'success' | 'error' | string
  [key: string]: any
}

const colorMapping: any = {
  primary: 'primary.main',
  textPrimary: 'text.primary',
  secondary: 'secondary.main',
  textSecondary: 'text.secondary',
  success: 'success.main',
  error: 'error.main'
}
const getValueByPath = (path: string, target: any) => {
  const p = path.split('.')
  return p.reduce((p, c) => p && p[c], target)
}
const transformColor = (color?: string, theme?: any) => {
  return color && colorMapping[color] ? getValueByPath(colorMapping[color], theme.palette) : color
}

const TypographyRoot = styled('span')<ITypography>(({ theme, align, noWrap, variant, color }) => ({
  ...(noWrap && {
    overflow: 'hidden',
    textOverflow: 'ellipsis',
    whiteSpace: 'nowrap'
  }),
  ...(variant && theme && (theme.typography as any)[variant]),
  textAlign: align,
  color: transformColor(color, theme)
}))

export default React.forwardRef<any, ITypography>(({ children, component = 'p', color, ...other }, ref) => {
  // const Component = component || 'span'
  const StyledRoot = React.useMemo(
    () =>
      styled(component as any)<ITypography>(({ theme, align, noWrap, variant, color }) => ({
        ...(noWrap && {
          overflow: 'hidden',
          textOverflow: 'ellipsis',
          whiteSpace: 'nowrap'
        }),
        color: transformColor(color, theme),
        ...(variant && theme && (theme.typography as any)[variant]),
        textAlign: align
      })),
    [component]
  )
  return (
    <StyledRoot color={color} as={component} {...other}>
      {children}
    </StyledRoot>
  )
})
