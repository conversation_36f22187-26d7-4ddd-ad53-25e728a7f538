import dayjs from 'dayjs'

const FriendlyTimeThreshold = {
  NOW: 1000 * 60 * 2, // 2分钟,
  HOUR: 1000 * 60 * 60, // 1小时
  DAY: 24 * 60 * 60 * 1000, // 1天
  MONTH: 24 * 60 * 60 * 1000 * 30, //1月
  YEAR: 24 * 60 * 60 * 1000 * 365 //1年
}

const getFriendlyTimeDiff = (diffTimestamp: number, sufix = '前') => {
  if (diffTimestamp < FriendlyTimeThreshold.NOW) {
    return '刚刚'
  }
  if (diffTimestamp < FriendlyTimeThreshold.HOUR) {
    return Math.floor(diffTimestamp / (60 * 1000)) + '分钟' + sufix
  }
  if (diffTimestamp < FriendlyTimeThreshold.DAY) {
    return Math.floor(diffTimestamp / (60 * 60 * 1000)) + '小时' + sufix
  }
  if (diffTimestamp < FriendlyTimeThreshold.MONTH) {
    return Math.floor(diffTimestamp / (24 * 60 * 60 * 1000)) + '天' + sufix
  }
  if (diffTimestamp < FriendlyTimeThreshold.YEAR) {
    return Math.floor(diffTimestamp / (24 * 60 * 60 * 1000 * 30)) + '个月' + sufix
  }
  return Math.floor(diffTimestamp / (24 * 60 * 60 * 1000 * 365)) + '年' + sufix
}

const getFriendlyTime = (date: string) => {
  const diffTimestamp = dayjs().valueOf() - dayjs(date).valueOf()
  return getFriendlyTimeDiff(diffTimestamp)
}

const getAcitvityDate = (start?: string, end?: string) => {
  if (end && start) {
    return dayjs(start).format('YYYY-MM-DD HH:mm') + ' 至 ' + dayjs(end).format('YYYY-MM-DD HH:mm')
  } else if (start) {
    return dayjs(start || '').format('YYYY-MM-DD HH:mm') + ' 开始'
  } else {
    return ''
  }
}

const getFrindlyRemainTime = (end: string) => {
  const diffTimestamp = dayjs(end).valueOf() - dayjs().valueOf()
  return getFriendlyTimeDiff(diffTimestamp, '后')
}
const getRemainClock = (diffTimeStamp: number) => {
  if (diffTimeStamp <= 0) {
    return ''
  }
  // const h = Math.floor(diffTimeStamp / (3600 * 1000))
  // const hStr = h < 10 ? `0${h}` : `${h}`
  const m = Math.floor((diffTimeStamp % (3600 * 1000)) / (1 * 60 * 1000))
  const mStr = m < 10 ? `0${m}` : `${m}`
  const s = Math.floor((diffTimeStamp % (60 * 1000)) / 1000)
  const sStr = s < 10 ? `0${s}` : `${s}`
  if (diffTimeStamp < 3600 * 1000) {
    return `${mStr}:${sStr}`
  }
}

const Activity_State = {
  START_WAIT: 'start_wait', // 未开始
  START_WILL: 'start_will', //即将开始
  START: 'start', // 进行中
  END_WILL: 'end_will', // 即将结束
  END: 'end', // 已结束
  JOINED: 'joined' //已参加
}
type ActivityState = typeof Activity_State
export type TActivityState = ActivityState[keyof ActivityState]

const getActivityState = (start?: string, end?: string, state?: string, isJoin?: boolean) => {
  // if (isJoin) {
  //   return Activity_State.JOINED
  // }
  if (state === Activity_State.START) {
    if (end && dayjs(end).valueOf() - dayjs().valueOf() < 3600 * 1000) {
      return Activity_State.END_WILL
    }
    return Activity_State.START
  }
  if (state === Activity_State.END) {
    return Activity_State.END
  }
  if (start && dayjs().isBefore(start)) {
    /**
     * <1小时 即将开始
     */
    if (dayjs(start).valueOf() - dayjs().valueOf() < 3600 * 1000) {
      return Activity_State.START_WILL
    }
    return Activity_State.START_WAIT
  }
  return Activity_State.START_WAIT
}

const formatDate = (date: string | number, format = 'YYYY-MM-DD') => {
  return dayjs(date).format(format)
}

export {
  getFriendlyTime,
  getAcitvityDate,
  getFrindlyRemainTime,
  getActivityState,
  getRemainClock,
  Activity_State,
  formatDate
}
