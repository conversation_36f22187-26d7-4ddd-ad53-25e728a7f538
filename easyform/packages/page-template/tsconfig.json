{"compilerOptions": {"target": "esnext", "lib": ["dom", "dom.iterable", "esnext"], "allowJs": false, "strict": true, "skipLibCheck": true, "esModuleInterop": true, "experimentalDecorators": true, "allowSyntheticDefaultImports": true, "forceConsistentCasingInFileNames": true, "noFallthroughCasesInSwitch": true, "module": "esnext", "moduleResolution": "node", "resolveJsonModule": true, "declaration": true, "jsx": "react-jsx", "baseUrl": "./", "outDir": "./", "paths": {"@/*": ["src/*"], "@@/*": ["src/.sui/*"]}, "jsxImportSource": "@emotion/react"}, "exclude": ["node_modules", "lib", "es", "dist", "build", "typings", "**/__test__", "test", "docs", "tests"]}