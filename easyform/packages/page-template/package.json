{"name": "@yiban/page-template", "version": "0.1.0", "license": "MIT", "main": "lib/index.js", "module": "es/index.js", "types": "lib/index.d.ts", "files": ["es", "lib", "dist"], "dependencies": {"@babel/runtime": "^7.16.7", "@emotion/react": "^11.10.5", "@emotion/styled": "^11.10.5", "@mui/system": "^5.10.13", "@sui/provider": "^3.0.5", "@sui/sui": "^3.0.85", "ahooks": "^3.7.2", "antd-mobile": "^5.25.1", "antd-mobile-icons": "^0.3.0", "axios": "^1.4.0", "dayjs": "^1.11.6", "react": "^17.0.2", "react-dom": "^17.0.2"}, "peerDependencies": {"@sui/provider": "^3.0.5", "react": "^16.9.0 || ^17.0.0", "react-dom": "^16.9.0 || ^17.0.0"}, "scripts": {"start": "sui start", "dev": "sui start -p 3030", "build": "sui build --library", "lib:build": "army-build", "lib:publish": "snpm publish --library", "lib:republish": "snpm republish --library", "test": "sui test", "publish-minio": "sui build --library && snpm publish --minio"}, "browserslist": {"production": ["> 0.1% and not dead", "IE > 10"], "development": ["last 1 chrome version", "last 1 firefox version", "last 1 safari version"]}, "devDependencies": {"@testing-library/jest-dom": "^5.14.1", "@testing-library/react": "^12.0.0", "@testing-library/user-event": "^13.2.1", "@types/jest": "^27.0.1", "@types/node": "^16.7.13", "@types/react": "^17.0.1", "@types/react-dom": "^17.0.1", "army-build": "^1.x", "autoprefixer": "^10.4.0", "eslint-config-custom": "*", "postcss": "^8.4.5", "tailwind-config": "*", "tailwindcss": "^3.0.5", "typescript": "4.6.3"}}