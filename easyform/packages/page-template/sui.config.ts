import { ISuiConfig } from '@sui/sui'
export default {
  // default sui config see http://170.18.10.145:7002/package/@sui/sui
  moduleFederation: {
    shared: {
      react: { singleton: true },
      'react-dom': { singleton: true },
      '@mui/system': { singleton: true, requiredVersion: false },
      'antd-mobile': { singleton: true, requiredVersion: false },
      '@sui/provider': {
        singleton: true,
        requiredVersion: false
      }
    }
  }
} as ISuiConfig
