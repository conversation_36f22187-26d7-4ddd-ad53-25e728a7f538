const { readFileSync } = require('fs')
const path = require('path')
const { getDevConfig } = require('./util')
const getCookie = () => readFileSync(path.join(__dirname, '../', 'cookie.txt')).toString().trim()

const { prefixUrl = '', serverHost = '' } = getDevConfig()

module.exports = {
	compress: false,
	proxy: {
		[`${prefixUrl}/_api`]: {
			target: serverHost,
			changeOrigin: true,
			pathRewrite: {
				// '^/edu/_api|/_api': ''
				[`^${prefixUrl}/_api`]: ''
			},
			onProxyReq(proxyReq) {
				proxyReq.setHeader('Cookie', getCookie())
			}
		},
		'/_baapi': {
			target: 'https://xhyb.xhu.edu.cn',
			changeOrigin: true,
			pathRewrite: {
				'^/_baapi': ''
			},
			onProxyReq(proxyReq) {
				proxyReq.setHeader('<PERSON><PERSON>', getCookie())
			}
		},
		'/easyform-api/': {
			target: serverHost,
			changeOrigin: true,
			onProxyReq(proxyReq) {
				proxyReq.setHeader('Cookie', getCookie())
			}
		},
		'/data-api/': {
			target: serverHost,
			changeOrigin: true,
			onProxyReq(proxyReq) {
				proxyReq.setHeader('Cookie', getCookie())
			}
		},
		'/op-api/': {
			target: serverHost,
			changeOrigin: true,
			onProxyReq(proxyReq) {
				proxyReq.setHeader('Cookie', getCookie())
			}
		},
		[`${prefixUrl}/api`]: {
			target: serverHost,
			changeOrigin: true,
			onProxyReq(proxyReq) {
				proxyReq.setHeader('Cookie', getCookie())
			}
		},

		'/sui/': {
			// target: 'http://***********:59001',
			target: serverHost,
			changeOrigin: true
		},
		'/xhu/api/v1': {
			target: 'https://xhyb.xhu.edu.cn',
			changeOrigin: true,
			onProxyReq(proxyReq) {
				proxyReq.setHeader('Cookie', getCookie())
				// proxyReq.setHeader('x-sudy-uid', '33202011980120');
				// proxyReq.setHeader('x-sudy-uid', '3120200671149');
			}
		},
		'/_wp3services/generalQuery': {
			target: 'https://www.xhu.edu.cn',
			changeOrigin: true
		},
		'/partyMemberManage':{
			target: 'http://************:8085',
			changeOrigin: true,
			headers: {
				Host: "************:8085",
				Referer: "http://************:8085"
			}
		}
	}
}
