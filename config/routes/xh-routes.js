module.exports = [
  {
    path: '/new',
    component: '@/layouts/BasicLayout',
    children: []
  },
  {
    path: '/portrait',
    hideInMenu: true,
    component: '@/pages/portrait2',
    access: [3, 4, 5, 6, 7, 8, 9, 11]
  },
  {
    path: '/pro-portrait',
    hideInMenu: true,
    component: '@/pages/portrait',
    access: [3, 4, 5, 6, 7, 8, 9, 11]
  },
  {
    path: '/task-todo/:id',
    hideInMenu: true,
    component: '@/pages/task-center/undo/simple',
    wrappers: ['@/layouts/simple']
  },
  {
    path: '/task-approve',
    hideInMenu: true,
    component: '@/pages/task-center/approve/simple',
    wrappers: ['@/layouts/simple']
  },
  {
    path: '/',
    component: '@/layouts/BasicLayout',
    children: [
      {
        index: true,
        component: '@/pages/home',
        name: '首页2222',
        icon: 'home'
      },
      {
        path: 'ezform',
        component: '@/pages/ezform',
        name: '万能表单',
        access: [5, 3, 6, 4, 8]
      },
      // {
      //   icon: 'idcard',
      //   path: 'student-profile',
      //   name: '成长档案',
      //   component: '@/pages/data-center/portraits',
      //   access: [4, 9, 6, 7, 8, 3, 5, 11]
      // },
      {
        name: '数据中心',
        hideInMenu: true,
        icon: 'database',
        access: [4, 9, 6, 7, 8, 3, 5, 10, 11],
        children: [
          {
            path: 'data-center',
            name: '校情总览',
            component: '@/pages/data-center/overview',
            access: [4, 9, 6, 3, 7, 5, 10]
          },
          {
            name: '思政指数',
            access: [4, 9, 6, 3, 7, 11],
            children: [
              // {
              //   path: 'overview',
              //   name: '指数总览',
              //   component: '@/pages/data-center/sizheng'
              // },
              {
                path: 'data-sixiang',
                name: '思想引领',
                component: '@/pages/data-center/sixiang',
                access: [4, 9, 6, 3, 7, 11]
              },
              {
                path: 'data-study',
                name: '学业指数',
                component: '@/pages/data-center/study',
                access: [4, 9, 6, 3, 7, 11]
              },
              {
                path: 'data-health',
                name: '健康指数',
                component: '@/pages/data-center/health',
                access: [4, 9, 6]
              },
              {
                path: 'data-secondClass',
                name: '第二课堂',
                component: '@/pages/data-center/secondClass',
                access: [4, 9, 6]
              },
              {
                path: 'data-chuangxin',
                name: '创新能力',
                component: '@/pages/data-center/chuangxin',
                access: [4, 9, 6]
              },
              {
                path: 'data-jiuye',
                name: '就业深造',
                component: '@/pages/data-center/jiuye',
                access: [4, 9, 6]
              },
              {
                path: 'data-biaozhang',
                name: '荣誉表彰',
                component: '@/pages/data-center/biaozhang',
                access: [4, 9, 6]
              },
              {
                path: 'data-jiangqin',
                name: '奖勤助贷',
                component: '@/pages/data-center/jiangqin',
                access: [4, 9, 6]
              },
              {
                path: 'data-guanli',
                name: '管理服务',
                component: '@/pages/data-center/guanli',
                access: [4, 9, 6]
              }
            ]
          },
          {
            name: '专题和查询',
            access: [4, 9, 6, 3, 8, 7, 5, 11],
            children: [
              {
                path: 'data-statements',
                name: '综合查询',
                component: '@/pages/data-center/statements',
                access: [4, 9, 6, 3, 8, 7, 5, 11]
              },
              // {
              //   path: 'data-query',
              //   name: '综合查询',
              //   hideInMenu: true, // 菜单隐藏，但是可以直接访问
              //   component: '@/pages/data-center/ComprehensiveQuery',
              //   access: ['canReadData', 'canReadDataSatements']
              // },
              {
                path: 'data-xqzc',
                name: '学期注册',
                component: '@/pages/data-center/data-topic/xqzc',
                access: [4, 9, 6]
              },
              {
                path: 'data-jqlx',
                name: '假期留校',
                component: '@/pages/data-center/data-topic/jqlx',
                access: [4, 9, 6]
              }
            ]
          }
        ]
      },
      /**
       * 下面是新版数据中心
       */
      {
        name: '数据中心',
        // hideInMenu: true,
        icon: 'database',
        access: [4, 9, 6, 7, 8, 3, 5, 10, 11],
        children: [
          {
            path: 'pro-data-center',
            name: '数据总览',
            component: '@/pages/xh-data-center/overview',
            access: [4, 9, 6, 8, 3, 7, 10]
          },
          {
            name: '数据看板',
            access: [4, 9, 6, 3, 8, 7, 11],
            children: [
              {
                path: 'pro-data-sixiang',
                name: '思想引领',
                component: '@/pages/xh-data-center/sxyl',
                access: [4, 9, 6, 3, 8, 7, 11]
              },
              {
                path: 'pro-data-study',
                name: '学业指数',
                component: '@/pages/xh-data-center/xyzs',
                access: [4, 9, 6, 3, 8, 7, 11]
              },
              {
                path: 'pro-data-health',
                name: '健康指数',
                component: '@/pages/xh-data-center/jkzs',
                access: [4, 9, 6, 3, 8, 11]
              },
              {
                path: 'pro-data-secondClass',
                name: '第二课堂',
                component: '@/pages/xh-data-center/dekt',
                access: [4, 9, 6, 3, 8, 11]
              },
              {
                path: 'pro-data-chuangxin',
                name: '创新能力',
                component: '@/pages/xh-data-center/cxnl',
                access: [4, 9, 6, 3, 8, 11]
              },
              {
                path: 'pro-data-jiuye',
                name: '就业深造',
                component: '@/pages/xh-data-center/jysz',
                access: [4, 9, 6, 3, 8, 11]
              },
              {
                path: 'pro-data-biaozhang',
                name: '荣誉表彰',
                component: '@/pages/xh-data-center/rybz',
                access: [4, 9, 6, 3, 8, 11]
              },
              {
                path: 'pro-data-jiangqin',
                name: '奖勤助贷',
                component: '@/pages/xh-data-center/jqzd',
                access: [4, 9, 6, 3, 8, 11]
              },
              {
                path: 'pro-data-guanli',
                name: '管理服务',
                component: '@/pages/xh-data-center/glfw',
                access: [4, 9, 6, 3, 8, 11]
              }
            ]
          },
          {
            name: '学生综合查询',
            path: 'pro-data-statements',
            access: [4, 9, 6, 3, 8, 7, 5, 11],
            component: '@/pages/data-center/newstatements'
          }

          // {
          //   path: 'warning',
          //   name: '综合预警',
          //   access: [4, 9, 6, 3, 7, 5, 10],
          //   children: [
          //     {
          //       index: true,
          //       component: '@/pages/warning'
          //     },
          //     {
          //       path: 'morewarning',
          //       hideInMenu: true,
          //       name: '更多预警',
          //       component: '@/pages/warning/morewarning',
          //       access: [4, 9, 6, 3, 7, 5, 10]
          //     }
          //   ]
          // }
        ]
      },
      {
        name: '数据中心',
        hideInMenu: true,
        icon: 'database',
        access: [4, 9, 6, 7, 8, 3, 5, 10, 11],
        children: [
          {
            path: 'standard-data-center',
            name: '校情总览',
            component: '@/pages/data-center2/overview',
            access: [4, 9, 6, 3, 7, 5, 10]
          },
          {
            name: '思政指数',
            access: [4, 9, 6, 3, 7, 11],
            children: [
              // {
              //   path: 'overview',
              //   name: '指数总览',
              //   component: '@/pages/data-center/sizheng'
              // },
              {
                path: 'standard-data-sixiang',
                name: '思想引领',
                component: '@/pages/data-center2/sxyl',
                access: [4, 9, 6, 3, 7, 11]
              },
              // {
              //   path: 'data-study',
              //   name: '学业指数',
              //   component: '@/pages/data-center/study',
              //   access: [4, 9, 6, 3, 7, 11]
              // },
              // {
              //   path: 'data-health',
              //   name: '健康指数',
              //   component: '@/pages/data-center/health',
              //   access: [4, 9, 6]
              // },
              {
                path: 'standard-data-secondClass',
                name: '第二课堂',
                component: '@/pages/data-center2/dekt',
                access: [4, 9, 6]
              },
              // {
              //   path: 'data-chuangxin',
              //   name: '创新能力',
              //   component: '@/pages/data-center/chuangxin',
              //   access: [4, 9, 6]
              // },
              // {
              //   path: 'data-jiuye',
              //   name: '就业深造',
              //   component: '@/pages/data-center/jiuye',
              //   access: [4, 9, 6]
              // },
              {
                path: 'standard-data-biaozhang',
                name: '荣誉表彰',
                component: '@/pages/data-center2/rybz',
                access: [4, 9, 6]
              },
              {
                path: 'standard-data-jiangqin',
                name: '奖勤助贷',
                component: '@/pages/data-center2/jqzd',
                access: [4, 9, 6]
              },
              {
                path: 'standard-data-guanli',
                name: '管理服务',
                component: '@/pages/data-center2/glfw',
                access: [4, 9, 6]
              }
            ]
          },
          {
            name: '专题和查询',
            access: [4, 9, 6, 3, 8, 7, 5, 11],
            children: [
              {
                path: 'standard-data-statements',
                name: '学生综合查询',
                component: '@/pages/data-center/statements',
                access: [4, 9, 6, 3, 8, 7, 5, 11]
              },
              // {
              //   path: 'data-query',
              //   name: '综合查询',
              //   hideInMenu: true, // 菜单隐藏，但是可以直接访问
              //   component: '@/pages/data-center/ComprehensiveQuery',
              //   access: ['canReadData', 'canReadDataSatements']
              // },
              {
                path: 'standard-data-xqzc',
                name: '学期注册',
                component: '@/pages/data-center/data-topic/xqzc',
                access: [4, 9, 6]
              },
              {
                path: 'standard-data-jqlx',
                name: '假期留校',
                component: '@/pages/data-center/data-topic/jqlx',
                access: [4, 9, 6]
              }
            ]
          }
        ]
      },
      {
        name: '任务中心',
        icon: 'file-sync',
        hideInMenu: true,
        children: [
          {
            path: 'task-management',
            name: '任务管理',
            hideInMenu: true,
            access: ['admin', 'departAdmin'], // 只系统管理员或学工管理员可以直接管理任务
            children: [
              {
                index: true,
                component: '@/pages/task-center/management'
              },
              {
                path: ':id',
                name: '任务详情',
                hideInMenu: true,
                component: '@/pages/task-center/management/detail'
              },
              {
                path: 'data-detail',
                hideInMenu: true,
                children: [
                  {
                    hideInMenu: true,
                    path: ':id',
                    component: '@/pages/task-center/management/data-detail',
                    name: '数据明细'
                  }
                ]
              }
            ]
          },
          {
            path: 'task-my',
            name: '我的任务',
            access: ['canCreateTask'],
            children: [
              {
                index: true,
                component: '@/pages/task-center/my'
              },
              {
                path: ':id',
                name: '任务详情',
                hideInMenu: true,
                component: '@/pages/task-center/my/detail'
              },
              {
                path: 'data-detail',
                hideInMenu: true,
                children: [
                  {
                    hideInMenu: true,
                    path: ':id',
                    component: '@/pages/task-center/my/data-detail',
                    name: '数据明细'
                  }
                ]
              }
            ]
          },
          {
            name: '我的待办',
            path: 'task-undo',
            children: [
              {
                index: true,
                component: '@/pages/task-center/undo'
              },
              {
                path: ':id',
                name: '待办详情',
                hideInMenu: true,
                component: '@/pages/task-center/undo/detail'
              }
            ]
          },
          {
            path: 'task-approve/:id',
            access: ['admin', 'departAdmin'],
            name: '任务审核',
            hideInMenu: true,
            component: '@/pages/task-center/approve/detail'
          }
        ]
      },
      {
        name: '活动中心',
        icon: 'flag',
        children: [
          {
            path: 'activity',
            name: '活动管理',
            access: ['admin', 'departAdmin'], // 只有系统管理和学工管理员可以管理
            children: [
              {
                index: true,
                component: '@/pages/ezform/activity'
              },
              {
                path: ':id',
                name: '活动参与详情',
                hideInMenu: true,
                component: '@/pages/ezform/activity/data'
              }
            ]
          },
          {
            path: 'activity-my',
            name: '我发起的',
            access: ['canCreateActivity'],
            children: [
              {
                index: true,
                component: '@/pages/ezform/my'
              },
              {
                path: ':id',
                name: '活动参与详情',
                hideInMenu: true,
                component: '@/pages/ezform/my/data'
              }
            ]
          },
          {
            path: 'activity-joined',
            name: '我参与的',
            component: '@/pages/ezform/joined'
          }
        ]
      },

      {
        name: '通讯录',
        icon: 'contacts',
        hideInMenu: true,
        children: [
          {
            path: 'contacts-school',
            name: '校内通讯录',
            component: '@/pages/contacts/school'
          }
        ]
      },

      {
        name: '消息中心',
        icon: 'wechat',
        access: [4, 6],
        children: [
          {
            path: 'wechat-data',
            name: '流量数据',
            component: '@/pages/wechat/data',
            hideInMenu: true
          },
          {
            path: 'wechat-message',
            name: '消息管理',
            component: '@/pages/wechat/message',
            hideInMenu: true
          },
          {
            path: 'wechat-template',
            name: '消息模板',
            hideInMenu: true,
            children: [
              {
                index: true,
                component: '@/pages/wechat/template'
              },
              {
                path: ':id',
                name: '模板详情',
                hideInMenu: true,
                component: '@/pages/wechat/template/detail'
              }
            ]
          },
          {
            path: 'wechat-leave-message',
            name: '公众号留言',
            component: '@/pages/wechat/wechat-leave-message'
          },
          {
            path: 'post-messages',
            name: '群发消息',
            component: '@/pages/wechat/post-messages'
          },
          {
            path: 'my-record',
            name: '我的消息',
            children: [
              {
                index: true,
                component: '@/pages/wechat/send-record'
              },
              {
                path: ':id',
                name: '查看详情',
                hideInMenu: true,
                component: '@/pages/wechat/send-record/detail'
              }
            ]
          },
          {
            path: 'send-record',
            name: '发送记录',
            children: [
              {
                index: true,
                component: '@/pages/wechat/send-record'
              },
              {
                path: ':id',
                name: '查看详情',
                hideInMenu: true,
                component: '@/pages/wechat/send-record/detail'
              }
            ]
          }
        ]
      },
      {
        name: '系统',
        icon: 'setting',
        access: ['admin'],
        children: [
          {
            path: 'settings-safety',
            name: '安全配置',
            hideInMenu: true,
            children: [
              {
                index: true,
                component: '@/pages/setting-center/safety'
              },
              {
                path: 'profile',
                name: '个人账号',
                component: '@/pages/setting-center/profile'
              },
              {
                path: 'login',
                name: '安全登录',
                component: '@/pages/setting-center/login'
              },
              {
                path: 'no-authrequired',
                name: '登录免认证',
                component: '@/pages/setting-center/no-authrequired'
              }
            ]
          },
          {
            path: 'settings-admin',
            name: '管理员配置',
            component: '@/pages/setting-center/admin',
            hideInMenu: true
          },
          {
            path: 'settings-homepage',
            name: '首页配置',
            component: '@/pages/setting-center/homepage'
          },
          {
            name: '用户中心',
            // icon: 'team',
            access: ['admin'], // 系统管理员和学工管理员有权限
            children: [
              {
                name: '总览',
                hideInMenu: true,
                component: '@/pages/overview',
                path: 'personnel-overview'
              },
              {
                resourceCode: 'perMan',
                name: '人员管理',
                path: 'personnel-management',
                children: [
                  {
                    index: true,
                    component: '@/pages/personnel-management'
                  },
                  {
                    name: '增加人员',
                    hideInMenu: true,
                    path: 'personnel-management-add',
                    component: '@/pages/personnel-management/person-management-add'
                  },
                  {
                    name: '修改人员',
                    hideInMenu: true,
                    path: 'personnel-management-edit',
                    component: '@/pages/personnel-management/person-management-edit'
                  },
                  {
                    name: '属性同步标记',
                    hideInMenu: true,
                    path: 'personnel-management-mark',
                    component: '@/pages/personnel-management/person-mark'
                  },
                  {
                    name: '查看人员详情',
                    hideInMenu: true,
                    path: 'person-management-details',
                    component: '@/pages/personnel-management/person-management-details'
                  },
                  {
                    path: 'person-management-recycle',
                    component: '@/pages/personnel-management/person-management-recycle'
                  },
                  {
                    path: 'person-management-organization-recycle',
                    component: '@/pages/personnel-management/person-management-organization-recycle'
                  },
                  {
                    path: 'person-management-person-recycle',
                    component: '@/pages/personnel-management/person-management-person-recycle'
                  }
                ]
              },
              {
                resourceCode: 'orgMan',
                name: '组织机构管理',
                path: 'organization-management',
                children: [
                  {
                    resourceCode: 'orgManm',
                    path: 'organ-management',
                    name: '机构管理',
                    component: '@/pages/organization-management/organ-management'
                  },
                  {
                    resourceCode: 'speMan',
                    path: 'specialized-management',
                    name: '专业管理',
                    component: '@/pages/organization-management/specialized-management'
                  },
                  {
                    resourceCode: 'admMan',
                    path: 'administrationclass-management',
                    name: '行政班管理',
                    children: [
                      {
                        index: true,
                        component: '@/pages/organization-management/administrationclass-management'
                      },
                      {
                        path: 'setting',
                        name: '负责人管理',
                        hideInMenu: true,
                        component: '@/pages/organization-management/administrationclass-management/setting'
                      }
                    ]
                  },
                  {
                    resourceCode: 'groMan',
                    path: 'group-management',
                    name: '组管理',
                    children: [
                      {
                        index: true,
                        component: '@/pages/organization-management/group-management'
                      },
                      {
                        path: 'groupauth',
                        name: '组管理授权',
                        hideInMenu: true,
                        component: '@/pages/organization-management/group-management/groupauth'
                      },
                      {
                        path: 'groupset',
                        name: '设置',
                        hideInMenu: true,
                        component: '@/pages/organization-management/group-management/groupset'
                      }
                    ]
                  },
                  {
                    resourceCode: 'sysPos',
                    path: 'post-management-syspost',
                    name: '正式岗位管理',
                    component: '@/pages/organization-management/post-management'
                  },
                  // {
                  //   resourceCode: 'busPos',
                  //   path: 'post-management-businesspost',
                  //   name: '业务岗位管理',
                  //   component: '@/pages/organization-management/post-management/list/businesspost.js'
                  // },
                  {
                    resourceCode: 'posGroMan',
                    path: 'post-group-management',
                    name: '岗位集管理',
                    component: '@/pages/organization-management/post-group-management'
                  }
                ]
              },
              // {
              //   resourceCode: 'datConf',
              //   name: '数据审核',
              //   path: 'data-auditing',
              //   children: [
              //     {
              //       resourceCode: 'orgAud',
              //       name: '机构数据审核',
              //       path: 'organ-auditing',
              //       component: '@/pages/data-auditing/organ-auditing'
              //     },
              //     {
              //       resourceCode: 'perAud',
              //       name: '人员数据审核',
              //       path: 'personnel-auditing',
              //       component: '@/pages/data-auditing/personnel-auditing'
              //     },
              // {
              //   resourceCode: "facAud",
              //   name: '人脸照审核', path: 'facephotos-auditing',
              //   children: [
              //     { index: true, component: '@/pages/data-auditing/facephotos-auditing' },
              //     { path: 'detail', hideInMenu: true, name: "详情", component: '@/pages/data-auditing/facephotos-auditing/detail' }
              //   ],

              // },
              //     {
              //       resourceCode: 'pasAud',
              //       name: '密码申诉审核',
              //       path: 'password-auditing',
              //       component: '@/pages/data-auditing/password-auditing'
              //     }
              //   ]
              // },

              // {
              //   resourceCode: 'useManPer',
              //   name: '用户管理授权',
              //   path: 'user-management-permission',
              //   children: [
              //     {
              //       resourceCode: 'perManPer',
              //       name: '人员管理授权',
              //       path: 'person-permission',
              //       children: [
              //         {
              //           index: true,
              //           component: '@/pages/usermanage-pageconfig'
              //         },
              //         {
              //           path: 'addpage',
              //           name: '页面配置', //  hideInMenu: true,
              //           hideInMenu: true,
              //           component: '@/pages/usermanage-pageconfig/addpage/index'
              //         },
              //         {
              //           path: 'personauth',
              //           name: '授权',
              //           hideInMenu: true,
              //           component: '@/pages/usermanagement-permission'
              //         }
              //       ]
              //     },
              //     {
              //       resourceCode: 'orgManPer',
              //       name: '机构管理授权',
              //       path: 'org-permission',
              //       component: '@/pages/usermanagement-permission/orgpermission'
              //     }
              //   ]
              // },
              {
                resourceCode: 'datMan',
                name: '数据标准管理',
                path: 'datadictionary-managemen',
                children: [
                  {
                    index: true,
                    component: '@/pages/usercenter-setting/datadictionary-managemen'
                  },
                  {
                    path: 'add',
                    name: '新增',
                    hideInMenu: true,
                    component: '@/pages/usercenter-setting/datadictionary-managemen/change'
                  },
                  {
                    path: 'child',
                    name: '新增子分类',
                    hideInMenu: true,
                    component: '@/pages/usercenter-setting/datadictionary-managemen/child'
                  },
                  {
                    path: 'editchild',
                    name: '修改子分类',
                    hideInMenu: true,
                    component: '@/pages/usercenter-setting/datadictionary-managemen/child'
                  },
                  {
                    path: 'change',
                    name: '修改',
                    hideInMenu: true,
                    component: '@/pages/usercenter-setting/datadictionary-managemen/change'
                  },
                  {
                    path: 'detail',
                    name: '详情',
                    hideInMenu: true,
                    component: '@/pages/usercenter-setting/datadictionary-managemen/detailCom'
                  },
                  {
                    path: 'persontypeset',
                    name: '人员类型属性设置',
                    hideInMenu: true,
                    component: '@/pages/usercenter-setting/datadictionary-managemen/persontypeset'
                  }
                ]
              },
              // {
              //   resourceCode: 'datSynMan',
              //   name: '数据同步管理',
              //   path: 'data-synchronization',
              //   component: '@/pages/data-synchronization'
              // },
              {
                resourceCode: 'perRec',
                name: '回收站',
                path: 'personnal-recycle',
                children: [
                  {
                    index: true,
                    component: '@/pages/personnel-management/person-management-recycle'
                  },
                  {
                    path: 'person-management-organization-recycle',
                    component: '@/pages/personnel-management/person-management-organization-recycle'
                  },
                  {
                    path: 'person-management-person-recycle',
                    component: '@/pages/personnel-management/person-management-person-recycle'
                  }
                ]
              }
            ]
          },
          {
            name: '应用中心',
            icon: 'appstore-add',
            access: ['admin'],
            children: [
              {
                path: 'app-management',
                name: '应用管理',
                component: '@/pages/app-center2/management'
              },
              {
                path: 'app-release',
                name: '应用发布',
                component: '@/pages/app-center2/release'
              }
            ]
          },
          {
            name: '万能表单',
            // hideInMenu: true,
            // icon: 'form',
            access: ['admin'],
            children: [
              {
                path: 'template',
                name: '模板管理',
                component: '@/pages/ezform/template'
              }
            ]
          },
          {
            path: 'calendar',
            name: '日程和课表',
            component: '@/pages/calendar'
          },
          {
            path: 'resource-management',
            name: '资源管理',
            component: '@/pages/system/resources'
          },
          {
            path: 'groups',
            name: '群组管理',
            children: [
              {
                path: 'classgroups',
                name: '班级群',
                component: '@/pages/system/studentsgroups'
              }
            ]
          },
          {
            path: 'indicator-design',
            name: '指数设计',
            hideInMenu: true,
            component: '@/pages/system/indicator'
          },
          {
            name: '数据中心',
            access: [4, 6, 9],
            children: [
              {
                path: 'standard-data-canonicalModel',
                name: '数据管理',
                component: '@/pages/data-center/data-topic/canonicalModel',
                access: [4, 6, 9]
              },
              {
                path: 'cardconfig',
                name: '看板配置',
                component: '@/pages/system/cardconfig',
                access: [4, 6, 9]
              }
            ]
          },
          {
            name: '运维中心',
            path: 'op',
            children: [
              {
                path: 'cluster',
                name: '集群',
                component: '@/pages/system/op/cluster'
              },
              // {
              //   path: 'service',
              //   name: '服务',
              //   component: '@/pages/system/op/cluster'
              // },
              {
                path: 'app',
                name: '应用',
                component: '@/pages/system/op/app'
              }
              // {
              //   path: 'log',
              //   name: '日志',
              //   component: '@/pages/system/op/cluster'
              // }
            ]
          }
        ]
      },
      {
        name: '访问统计',
        icon: 'fund',
        hideInMenu: true,
        access: ['admin'],
        path: 'access-stats',
        component: '@/pages/stats'
      },
      {
        path: '*',
        component: '@/pages/404'
      }
    ]
  }
]
