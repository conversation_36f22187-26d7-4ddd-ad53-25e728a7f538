<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <meta http-equiv="X-UA-Compatible" content="IE=edge" />
    <meta charset="utf-8" />
    <meta name="apple-mobile-web-app-capable" content="yes" />
    <meta
      name="viewport"
      content="width=device-width,initial-scale=1.0,user-scalable=no,maximum-scale=1.0,viewport-fit=cover"
    />
    <title>育人平台</title>
    <style>
      * {
        margin: 0;
        padding: 0;
      }
      body {
        --color-danger: #f44336;
        --color-success: #4caf50;
        --color-warning: hsl(45, 93%, 51%);
        --box-size: 40vw;
        background-color: #5454ed;
        color: #fff;
        font-size: 14px;
      }
      div {
        box-sizing: border-box;
      }
      .container {
        height: 100vh;
        position: relative;
        background-size: cover;
        background-position: 50%;
        background-repeat: no-repeat;
        display: grid;
        grid-template-columns: 1fr 4vw 1fr;
        column-gap: 4vw;
      }
      .container > div {
        display: flex;
        align-items: center;
        justify-content: center;
      }
      .container > .desc-wrapper {
        justify-self: end;
      }
      .container > .code-wrapper {
        /* justify-content: center; */
        /* flex-direction: column; */
        justify-self: start;
      }
      .arrow {
        width: 100%;
      }

      .bg {
        background-image: url('./bg.jpeg');
      }
      .box {
        border-radius: 8px;
        border: 1px solid #fff;
        width: var(--box-size);
        height: var(--box-size);
        max-width: 480px;
        max-height: 480px;
        min-width: 320px;
        min-height: 320px;
        background-color: rgba(144, 144, 255, 0.2);
        backdrop-filter: blur(3px);
      }
      .flex {
        display: flex;
      }

      .box-bg {
        width: 70%;
        position: absolute;
        pointer-events: none;
        top: 50%;
        right: 0;
        transform: translateY(-50%);
      }
      .flex-col-box {
        display: flex;
        flex-direction: column;
        justify-content: center;
        height: 100%;
      }

      .title-box {
        font-size: 16px;
        font-weight: 500;
        color: #000;
        padding: 8px 32px;
        display: flex;
        justify-content: center;
        align-items: center;
        border-top-right-radius: 32px;
        border-bottom-right-radius: 32px;
        background-color: #ffec5d;
      }
      .text {
        color: inherit;
        font-size: inherit;
        padding: 8px 32px;
        z-index: 100;
      }
      .bg-code {
        width: 100%;
        height: 100%;
        padding: 12px;
        background-image: url('./code-border.png');
        background-size: cover;
        background-position: center;
        background-repeat: no-repeat;
      }
      .qrcode-wrapper {
        border: 2px solid #000;
        background-color: #fff;
        padding: 16px;
        width: 100%;
        height: 100%;
      }
      .qrcode {
        /* background-image: url('./code.png'); */
        background-size: cover;
        background-repeat: no-repeat;
        background-position: center;
        width: 100%;
        height: 100%;
      }
      .info {
        padding: 8px 32px;
        font-size: 14px;
        color: #fff;
        display: flex;
        align-items: center;
        justify-content: center;
        background-color: var(--color-danger);
        align-self: center;
        border-radius: 16px;
        margin-top: 24px;
      }
      .logo {
        position: absolute;
        top: 32px;
        left: 32px;

        display: flex;
        align-items: center;
      }
      .logo > span {
        font-size: 24px;
        font-weight: 500;
        color: #fff;
        margin-left: 8px;
        letter-spacing: 2px;
      }
      .mt-48 {
        margin-top: 48px;
      }
      @media screen and (max-width: 900px) {
        .container {
          grid-template-columns: 1fr;
          padding-top: 60px;
        }
        .container > .desc-wrapper {
          justify-self: center;
        }
        .container > .code-wrapper {
          justify-self: center;
          margin-top: 0;
        }
        .arrow {
          width: 60px;
          transform: rotate(90deg);
        }
        .logo {
          top: 12px;
          left: 50%;
          transform: translate(-50%, 0);
        }
        .mt-48 {
          margin-top: 0;
        }
      }
    </style>
  </head>
  <body>
    <div class="container bg">
      <div class="logo">
        <svg xmlns="http://www.w3.org/2000/svg" width="48" height="48" viewBox="0 0 70.066 60.059">
          <g id="组_5" data-name="组 5" transform="translate(-958.534 185.792)">
            <circle
              id="椭圆_2"
              data-name="椭圆 2"
              cx="11.121"
              cy="11.121"
              r="11.121"
              transform="translate(982.455 -185.792)"
              fill="#fff"
            />
            <path
              id="路径_4"
              data-name="路径 4"
              d="M1097.954,1441.663H1098c-.031,0-.06-.005-.091-.005C1097.925,1441.658,1097.938,1441.663,1097.954,1441.663Z"
              transform="translate(-113.817 -1567.396)"
              fill="#f2f2f2"
            />
            <path
              id="路径_5"
              data-name="路径 5"
              d="M1139.087,1408.551h-16.844a7.135,7.135,0,0,0-5.759,2.622l-9.1,12.43,10.341,14.126c1.28,1.748-.319,3.933-2.878,3.934h1.918a7.137,7.137,0,0,0,5.76-2.622l5.695-7.78h0l13.746-18.776C1143.246,1410.736,1141.646,1408.551,1139.087,1408.551Z"
              transform="translate(-113.817 -1567.396)"
              fill="#f2f2f2"
            />
            <path
              id="路径_6"
              data-name="路径 6"
              d="M1096.058,1439.074l5.29-7.226h0l6.036-8.245-3.405-4.651h0l-5.695-7.779a7.138,7.138,0,0,0-5.76-2.622h-16.843c-2.56,0-4.16,2.185-2.88,3.934l4.306,5.881h0l15.136,20.675a7.11,7.11,0,0,0,5.666,2.617C1096.251,1441.632,1095.223,1440.214,1096.058,1439.074Z"
              transform="translate(-113.817 -1567.396)"
              fill="#f2f2f2"
            />
            <path
              id="路径_7"
              data-name="路径 7"
              d="M1098,1441.663h0Z"
              transform="translate(-113.817 -1567.396)"
              fill="#f2f2f2"
            />
            <path
              id="路径_8"
              data-name="路径 8"
              d="M1117.725,1437.729l-10.341-14.126-6.036,8.245h0l-5.29,7.226c-.835,1.14.193,2.558,1.851,2.584.031,0,.06.005.091.005h16.847C1117.406,1441.662,1119.005,1439.477,1117.725,1437.729Z"
              transform="translate(-113.817 -1567.396)"
              fill="#f2f2f2"
            />
            <path
              id="路径_9"
              data-name="路径 9"
              d="M1117.725,1437.729,1107.4,1423.62v-.017l-.006.009-.006-.009v.017l-10.33,14.109c-1.279,1.748.32,3.933,2.879,3.934h14.914C1117.406,1441.662,1119.005,1439.477,1117.725,1437.729Z"
              transform="translate(-113.817 -1567.396)"
              fill="#eaeaea"
            />
          </g>
        </svg>
        <span>育人平台</span>
      </div>
      <div class="desc-wrapper">
        <div class="box">
          <img src="./yuren.png" class="box-bg" />
          <div class="flex-col-box">
            <div style="margin-bottom: 16px" class="flex">
              <div class="title-box">未认证用户</div>
            </div>
            <div class="text">打开手机微信，扫右侧二维码</div>
            <div class="text">按照手机提示进行“用户认证”，完善个人信息</div>
            <div style="margin: 32px 0 16px 0" class="flex">
              <div style="background-color: #48fcf5" class="title-box">已认证用户</div>
            </div>
            <div class="text">打开手机微信，扫右侧二维码，直接登录网页端</div>
          </div>
        </div>
      </div>
      <div>
        <img class="arrow" src="./arrow.png" />
      </div>
      <div class="code-wrapper">
        <div class="flex-col-box">
          <div style="padding: 48px" class="box mt-48">
            <div class="bg-code">
              <div class="qrcode-wrapper">
                <div class="qrcode" id="qrcode"></div>
              </div>
            </div>
          </div>
          <div class="info" id="qrcodeState">等待扫码</div>
        </div>
      </div>
    </div>
    <script type="text/javascript">
      let contextPath = '{{.contextPath}}'
      if(contextPath.length > 0){
          contextPath = "/"+contextPath
      }

           reqClientId();
           function reqClientId() {
               window.fetch(contextPath+"/v2/weixin/getClientId", {
                   method: "GET"
               }).then(resp => {
                   return resp.json();
               }).then(json => {
                   if (json.code != 200) {
                       return alert(json.msg);
                   }
                   const clientId=json.data.client_id
                   //var expires = new Date();
                   //expires.setTime(expires.getTime()+60*60*1000); //max-age=60*60
                   //document.cookie="clientId="+clientId+"; path=/; expires="+expires.toGMTString();
                   //成功
                   //document.getElementById("clientid").textContent=json.data.client_id;
                   //监听SSE
                   listenSSE(clientId);
                   //获取二维码
                   reqQrcode(clientId);
               })
           }

           function listenSSE(clientId){
               const socket = new EventSource(contextPath+'/v1/wechat/stream/'+clientId );
               let targetUrl={{.targetUrl}};
               if (targetUrl==''){
                   targetUrl="https://yibanng.sudytech.cn/";
               }
               if (targetUrl.indexOf("?")>0){
                   targetUrl+="&ybClientId="+clientId;
               }else{
                   targetUrl+="?ybClientId="+clientId;
               }
               socket.onmessage = function (event) {
                   // console.log("message")
                   const arr=event.data.split(",");
                   const qrcodeState = document.getElementById("qrcodeState")
                   if (arr.length===2){
                       const code=arr[0]
                       if (code==="1"){
                           qrcodeState.textContent = "已扫码，等待确认。";
                           qrcodeState.style.backgroundColor="var(--color-warning)"
                        //    qrcodeState.style.borderColor="var(--color-warning)"
                       }else if(code==="2"){
                           qrcodeState.textContent = "扫码完成，即将跳转到原应用。";
                           qrcodeState.style.backgroundColor="var(--color-success)"
                        //    qrcodeState.style.borderColor="var(--color-success)"
                           setTimeout(function (){
                               let connStr="?"
                               if (targetUrl.indexOf("?")>=0){
                                   connStr="&"
                               }
                               // targetUrl=targetUrl +connStr+ "open_id="+arr[1];
                               targetUrl=targetUrl +connStr+arr[1];
                               // console.log(targetUrl)
                               location.href=targetUrl
                           },1000)
                       }else{
                           qrcodeState.textContent = "扫码取消，即将刷新页面。";
                           qrcodeState.style.backgroundColor="var(--color-danger)"
                        //    qrcodeState.style.borderColor="var(--color-danger)"
                           setTimeout(function (){
                               location.reload();
                           },1000)
                       }
                   }


               };
           }

           function reqQrcode(clientId) {
               const appId='{{.appId}}';
               let data = {"app_id": appId, "client_id": clientId}
               window.fetch(contextPath+"/v2/weixin/getQrCodeUrl", {
                   method: "POST",
                   body: JSON.stringify(data)
               }).then(resp => {
                   return resp.json();
               }).then(json => {
                   if (json.code != 200) {
                       return alert(json.msg);
                   }
                   document.getElementById("qrcode").style.backgroundImage='url('+json.data.img+')'
                 //   document.getElementById("qrcode").src = json.data.img;
                   // queryQrcodeState(clientId);

               })
           }
    </script>
  </body>
</html>
